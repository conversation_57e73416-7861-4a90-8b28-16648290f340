package cn.keking.config;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class RedisConfigTest {

    @Autowired
    private RedissonClient redissonClient;

    @Test
    public void testRedisConnection() {
        System.out.println("Redis client created successfully: " + redissonClient);
        // 简单的连接测试
        try {
            redissonClient.getBucket("test").set("hello");
            String value = (String) redissonClient.getBucket("test").get();
            System.out.println("Redis test value: " + value);
        } catch (Exception e) {
            System.err.println("Redis connection failed: " + e.getMessage());
        }
    }
}
