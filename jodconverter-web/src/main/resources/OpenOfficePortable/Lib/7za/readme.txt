7-Zip Command line version 4.65
-------------------------------

7-Zip is a file archiver with high compression ratio.
7za.exe is a standalone command line version of 7-Zip.

7-Zip Copyright (C) 1999-2009 <PERSON>.

Features of 7za.exe: 
  - High compression ratio in new 7z format
  - Supported formats:
      - Packing / unpacking: 7z, ZIP, GZIP, BZIP2 and TAR 
      - Unpacking only: Z
  - Highest compression ratio for ZIP and GZIP formats.
  - Fast compression and decompression
  - Strong AES-256 encryption in 7z and ZIP formats.

7za.exe is a free software distributed under the GNU LGPL.
Read license.txt for more information.

Source code of 7za.exe and 7-Zip can be found at
http://www.7-zip.org/

7za.exe can work in Windows 95/98/ME/NT/2000/XP/2003/Vista.

There is also port of 7za.exe for POSIX systems like Unix (Linux, Solaris, OpenBSD, 
FreeBSD, Cygwin, AIX, ...), MacOS X and BeOS:

http://p7zip.sourceforge.net/


  This distributive packet contains the following files:

  7za.exe       - 7-Zip standalone command line version.
  readme.txt    - This file.
  copying.txt   - GNU LGPL license.
  license.txt   - License information.
  7-zip.chm     - User's Manual in HTML Help format.


---
End of document
