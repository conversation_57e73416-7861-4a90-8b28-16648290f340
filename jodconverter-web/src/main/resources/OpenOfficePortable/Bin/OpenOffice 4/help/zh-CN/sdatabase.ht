19 SFX2_HID_TABDLG_RESET_BTN 81 将当前选项卡中所做的修改重设为打开对话框时具有的设置值。在关闭对话框时不显示确认询问。
30 sc%3AImageButton%3ARID_SCDLG_SOLVER%3ARB_VARCELL e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
2f sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_COLCELL e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
34 sc%3APushButton%3ARID_SCDLG_CONSOLIDATE%3ABTN_REMOVE 24 不经确认即删除选定元素。
2f sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_ROWCELL e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_DELDOUBLECAPS 24 不经确认即删除选定元素。
1b DBACCESS_HID_DBWIZ_PREVIOUS 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
1c WIZARDS_HID_QUERYWIZARD_NEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
2e sc%3APushButton%3ARID_SCDLG_NAMES%3ABTN_REMOVE 1e 确认后删除选定元素。
1c SFX2_HID_TABDLG_STANDARD_BTN 36 将对话框中可见的值重设为默认安装值。
11 WIZARDS_HID0_NEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND1_1 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
3b cui%3APushButton%3ARID_SVXDLG_MULTIPATH%3ABTN_DEL_MULTIPATH 24 不经确认即删除选定元素。
30 sc%3APushButton%3ARID_SCDLG_DBNAMES%3ABTN_REMOVE 1e 确认后删除选定元素。
32 sc%3AImageButton%3ARID_SCDLG_FILTER%3ARB_COPY_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
34 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_DELETE 1e 确认后删除选定元素。
1e WIZARDS_HID_QUERYWIZARD_CANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_REPEATCOL e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_DELABBREV 24 不经确认即删除选定元素。
3b sc%3AImageButton%3ARID_SCDLG_SPEC_FILTER%3ARB_CRITERIA_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
2d sd%3APushButton%3ADLG_CUSTOMSHOW%3ABTN_REMOVE 24 不经确认即删除选定元素。
2e sc%3AImageButton%3ARID_SCDLG_NAMES%3ARB_ASSIGN e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1e WIZARDS_HID_DLGTABLE_CMDCANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
21 EXTENSIONS_HID_GROUPWIZARD_CANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
20 EXTENSIONS_HID_LISTWIZARD_CANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
1b WIZARDS_HID_DLGFORM_CMDNEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_PRINTAREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
37 sc%3AImageButton%3ARID_SCDLG_CONSOLIDATE%3ARB_DATA_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_DELETE 1e 确认后删除选定元素。
34 sc%3AImageButton%3ARID_SCDLG_SOLVER%3ARB_FORMULACELL e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND1_2 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1e WIZARDS_HID_QUERYWIZARD_CREATE 24 应用所有更改并关闭向导。
37 sc%3AImageButton%3ARID_SCDLG_COLROWNAMERANGES%3ARB_DATA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1c WIZARDS_HID_DLGTABLE_CMDNEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
37 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_DELETE 1e 确认后删除选定元素。
1d WIZARDS_HID_DLGFORM_CMDCANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND2_2 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1c WIZARDS_HID_QUERYWIZARD_BACK 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
1b CUI_HID_MEASURE_CTL_PREVIEW 1b 预览当前选定内容。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND2_1 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
31 cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_DELETE 1e 确认后删除选定元素。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND3_1 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
2c starmath%3APushButton%3ARID_SYMBOLDIALOG%3A2 36 应用修改或选定的值，但不关闭对话框。
1c WIZARDS_HID_DLGTABLE_CMDPREV 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
34 sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_FORMULARANGE e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
15 SW_HID_MD_GLOS_DELETE 1e 确认后删除选定元素。
11 WIZARDS_HID0_PREV 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
2c sd%3APushButton%3ADLG_COPY%3ABTN_SET_DEFAULT 36 将对话框中可见的值重设为默认安装值。
30 sc%3AImageButton%3ARID_SCDLG_DBNAMES%3ARB_DBAREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
13 WIZARDS_HID0_CANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
37 sc%3AImageButton%3ARID_SCDLG_CONSOLIDATE%3ARB_DEST_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
33 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_REMOVE 1e 确认后删除选定元素。
21 WIZARDS_HID_DLGIMPORT_0_CMDCANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
32 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_DEL 1e 确认后删除选定元素。
34 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_DELETE 1e 确认后删除选定元素。
37 sc%3AImageButton%3ARID_SCDLG_SPEC_FILTER%3ARB_COPY_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
21 WIZARDS_HID_DLGREPORT_0_CMDCANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
33 sc%3APushButton%3ARID_SCDLG_AUTOFORMAT%3ABTN_REMOVE 1e 确认后删除选定元素。
1e EXTENSIONS_HID_LISTWIZARD_NEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
2a sw%3APushButton%3ADLG_BIB_BASE%3APB_DELETE 24 不经确认即删除选定元素。
31 basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_DELETE 1e 确认后删除选定元素。
37 sc%3AImageButton%3ARID_SCDLG_COLROWNAMERANGES%3ARB_AREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1b WIZARDS_HID_DLGFORM_CMDPREV 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_DELETE 1e 确认后删除选定元素。
22 EXTENSIONS_HID_LISTWIZARD_PREVIOUS 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_REPEATROW e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
17 DBACCESS_HID_DBWIZ_NEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
23 EXTENSIONS_HID_GROUPWIZARD_PREVIOUS 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
36 sc%3AImageButton%3ARID_SCDLG_PIVOT_LAYOUT%3ARB_OUTAREA e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_DELETE 1e 确认后删除选定元素。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND3_2 e7 单击缩小图标，将对话框缩小至输入字段的大小。这样，就可以更方便地在工作表中标记所需的引用。图标将自动转换为最大化图标。单击此图标，对话框将恢复为原始大小。
1e WIZARDS_HID_DLGTABLE_CMDFINISH 24 应用所有更改并关闭向导。
1f EXTENSIONS_HID_GROUPWIZARD_NEXT 9c 单击下一步按钮，“向导”将采用当前对话框的设置并转到下一步。如果到达了最后一步，对应位置的按钮变为创建。
17 SVX_HID_GALLERY_PREVIEW 1b 预览当前选定内容。
31 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_DELETE 1e 确认后删除选定元素。
43 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_REPLACE%3APB_DELETE_REPLACE 24 不经确认即删除选定元素。
1c WIZARDS_HID_FAXWIZARD_CANCEL 3f 单击取消关闭对话框，不保存所做的任何修改。
34 cui%3APushButton%3ARID_OFADLG_OPTIONS_TREE%3APB_BACK 36 将修改后的值重设为 $[officename] 默认值。
39 sc%3APushButton%3ARID_SCDLG_COLROWNAMERANGES%3ABTN_REMOVE 1e 确认后删除选定元素。
1f WIZARDS_HID_DLGREPORT_0_CMDPREV 57 在对话框中查看选择在上一步中所做的选择。当前设置保持不变。
2b sw%3APushButton%3ATP_STD_FONT%3APB_STANDARD 36 将对话框中可见的值重设为默认安装值。
1f SID_EXPLORERCONTENT_OPEN_OBJECT 45 使用打开命令可以在一个新任务中打开选定的对象。
19 SVX_HID_GALLERY_ACTUALIZE 2a 更新窗口或选定对象中的视图。
1f SID_EXPLORERCONTENT_OPEN_FOLDER 45 使用打开命令可以在一个新任务中打开选定的对象。
18 SID_EXPLORERCONTENT_OPEN 45 使用打开命令可以在一个新任务中打开选定的对象。
1b SID_EXPLORERCONTENT_DESTROY cf 借助这个菜单命令您可以删除当前选中的对象。若您同时选中了多个对象，那么它们就会被同时删除。一般来说在程序执行删除任务之前会进行安全性查询。
19 SID_EXPLORERCONTENT_PASTE 36 将移动到剪贴板上的元素插入到文档中。
1a SID_EXPLORERCONTENT_RENAME 2a 启用选定的对象以进行重命名。
18 SID_EXPLORERCONTENT_COPY 27 将选定内容复制到剪贴板中。
1d SVX_HID_GALLERY_MN_BACKGROUND 3c 在此您可以将选中的图形当作背景颜色插入。
1c SFX2_HID_DID_SAVE_PACKED_XML 55 默认情况下，$[officename] 以 OpenDocument 文件格式装入和保存文件。
e .uno%3AHelpTip 57 启用鼠标指针位置上图标名称的显示及其他“帮助”内容的显示。
13 .uno%3AHelperDialog 9a 允许您激活自动“帮助助理”。您也可以通过“选项”对话框中的$[officename] - 常规 - 帮助助理来激活“帮助助理”。
11 .uno%3AActiveHelp 45 启用鼠标指针位置上菜单和图标的简要描述的显示。
33 svtools%3ANumericField%3ADLG_EXPORT%3ANF_RESOLUTION 3c 输入图像分辨率。从列表框中选择度量单位。
2d svtools%3AMetricField%3ADLG_EXPORT%3AMF_SIZEY f 定义高度。
32 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_LEVEL2 72 如果输出设备支持彩色位图、调色板图形和经过压缩的图形，则选择“级别 2 ”选项。
32 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_LEVEL1 8d 在这个级别中，压缩不可用。如果 PostScript 打印机不提供“级别 2 ”的对应功能，请选择“级别 1 ”选项。
2c svtools%3ARadioButton%3ADLG_EXPORT%3ARB_TEXT 3a 以 ASCII 文本格式导出。比二进制文件要大。
31 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_RLE_ENCODING 2e 对 BMP 图形应用 RLE （行程编码）。
2f svtools%3ACheckBox%3ADLG_EXPORT%3ACB_INTERLACED 2a 指定是否以交错模式存储图形。
2e svtools%3ARadioButton%3ADLG_EXPORT%3ARB_BINARY 39 以二进制格式导出文件。比文本文档要小。
2d svtools%3AMetricField%3ADLG_EXPORT%3AMF_SIZEX f 定义宽度。
39 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COLOR_FORMAT1 1b 导出带颜色的文件。
35 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_EPS_PREVIEW_EPSI 97 指定是否将 EPSI 格式的单色预览图形与 PostScript 文件一同导出。此格式仅含有来自 7 位 ASCII 编码中的可打印字符。
34 svtools%3ANumericField%3ADLG_EXPORT%3ANF_COMPRESSION a5 为导出设置压缩比率和质量。从低质量小尺寸到高质量大尺寸中选择。高压缩比率代表着低质量，低压缩比率代表着高质量。
2e svtools%3AListBox%3ADLG_EXPORT%3ALB_RESOLUTION 3c 输入图像分辨率。从列表框中选择度量单位。
3b svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COMPRESSION_LZW 5b LZW 压缩是指使用基于表格的查找算法将文件压缩成一个较小的文件。
39 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COLOR_FORMAT2 1b 导出带灰度的文件。
36 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_SAVE_TRANSPARENCY 95 指定是否将图片背景以透明方式保存。在 GIF 图像中只有对象是可见的。使用“取色器”在图片中设置透明颜色。
2f svtools%3AListBox%3ADLG_EXPORT%3ALB_COLOR_DEPTH 37 从 8 位灰度或 24 位真彩色选择颜色深度。
29 svtools%3AListBox%3ADLG_EXPORT%3ALB_SIZEX 15 指定度量单位。
35 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_EPS_PREVIEW_TIFF 54 指定是否将 TIFF 格式的预览图像与实际 PostScript 文件一同导出。
3c svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COMPRESSION_NONE 18 指定不使用压缩。
2c sc%3AListBox%3ARID_SCDLG_IMPORTOPT%3ALB_FONT 2b 从导入/导出选项中选择字符集。
32 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_SAVESHOWN ab 默认为启用，数据将按显示的保存，包括应用的数字格式。如果未选中此复选框，将与该软件旧版本中一样，保存原始数据内容。
31 sc%3AComboBox%3ARID_SCDLG_IMPORTOPT%3AED_FIELDSEP 3c 选择或输入用于分隔数据字段的字段分隔符。
33 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_FIXEDWIDTH 2d 按照固定宽度导出所有数据字段。
30 sc%3AComboBox%3ARID_SCDLG_IMPORTOPT%3AED_TEXTSEP 3c 选择或输入包含每个数据字段的文字分隔符。
31 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_QUOTEALL a2 在“文字分隔符”对话框中设置的开头和结尾引号字符。如果未选中，只有那些包含“字段分隔符”的文本单元格被引用。
2e sc%3AListBox%3ARID_SCDLG_IMPORTOPT%3ADDLB_FONT 27 指定用于文字导出的字符集。
29 sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_TYPE1 54 在预览窗口中选择列，并且选择应用到导入的数据的数据类型。
3a sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_DETECT_SPECIAL_NUMBER 96 当这个选项被启用，则电子表格会自动探测所有的数字格式，包括特殊数字格式，比如日期、时间和科学计数法。
2f sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_SEMICOLON 24 使用分号将数据分隔成列。
26 sc%3AEdit%3ARID_SCDLG_ASCII%3AED_OTHER 72 使用您指定的自定义分隔符将数据分隔成列。注意：数据中必须含有自定义的分隔符。
29 sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_TAB 2a 将制表符分隔的数据分隔为列。
33 sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_QUOTED_AS_TEXT 96 当这个选项被启用，值全部被引用（开头和结尾字符等同于文字分隔符）的字段和单元格将被以文本的方式导入。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_COMMA 2a 将使用逗号分隔的数据分隔为列
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_ASONCE 3f 合并连续出现的分隔符，删除空白的数据字段。
2c sc%3AComboBox%3ARID_SCDLG_ASCII%3ACB_TEXTSEP 60 选择一个用于分隔文本数据的字符。您也可以在文字框中输入一个字符。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_OTHER 72 使用您指定的自定义分隔符将数据分隔成列。注意：数据中必须含有自定义的分隔符。
2d sc%3ARadioButton%3ARID_SCDLG_ASCII%3ARB_FIXED 3c 将宽度固定的数据（相等字符数）分隔成列。
2f sc%3ANumericField%3ARID_SCDLG_ASCII%3ANF_AT_ROW 21 指定开始进行导入的行。
2e sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_CUSTOMLANG 27 决定数字字符串如何被导入。
31 sc%3ARadioButton%3ARID_SCDLG_ASCII%3ARB_SEPARATED 27 选择用来分隔数据的分隔符。
2b sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_CHARSET 2a 指定导入文件所使用的字符集。
22 sc%3AModalDialog%3ARID_SCDLG_ASCII 2d 设置带分隔符的数据的导入选项。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_SPACE 2d 将使用空格分隔的数据分隔成列。
31 sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_SCALE 36 指定是否将打印输出分布在多个页面上。
2f sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_CUT 4c 指定超出最大可打印范围的部分将被剪除并且不被打印\。
32 sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_POSTER 36 指定是否将打印输出分布在多个页面上。
25 sd%3AModalDialog%3ADLG_PRINT_WARNINGS 5d 当页面设置与定义的打印区域不匹配时，将出现打印选项警告对话框。
2d sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_LANGUAGE 5d 如果尚未定义文字语言，请指定文字的语言。此设置仅在导入时可用。
2b sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_CR 2a 生成“回车”作为段落换行符。
2d sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_CRLF 3f 生成“回车”和“换行”。此选项为默认选项。
2c sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_CHARSET 33 指定用于导出或导入的文件的字符集。
2b sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_LF 2a 生成“换行”作为段落分隔符。
29 sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_FONT 8a 通过设置一种默认字体，您可以指定一种应该用特殊字体显示的文本。只有在导入时才能选择默认字体。
1b SVX_HID_GALLERY_MN_ADD_LINK 2d 将选定的图形作为一个链接插入。
15 SVX_HID_GALLERY_TITLE 30 为选定的图片库对象指定一个标题。
1a SVX_HID_GALLERY_MN_ADDMENU 45 定义如何将一个选定的图形对象插入到一个文档中。
16 SVX_HID_GALLERY_MN_ADD 39 将选定图形对象的副本直接插入到文档中。
19 SVX_HID_GALLERY_MN_DELETE 21 在确认后删除选定图形。
1a SVX_HID_GALLERY_MN_PREVIEW 27 预览命令显示了选定的图形。
21 SW_HID_MN_READONLY_SAVEBACKGROUND 1e 允许保存网页的背景。
1e SW_HID_MN_READONLY_LOADGRAPHIC 51 如果您已经关闭图形显示，请选择加载图形命令以图形可见。
1e SW_HID_MN_READONLY_COPYGRAPHIC 2a 将选定的图形复制到剪贴板上。
1d SW_HID_MN_READONLY_GRAPHICOFF 2d 将文档中的所有图形设为不可见。
1c SW_HID_MN_READONLY_PLUGINOFF 18 禁用插入的插件。
1b SW_HID_MN_READONLY_COPYLINK 36 将鼠标指针所指的链接复制到剪贴板上。
1e SW_HID_MN_READONLY_SAVEGRAPHIC 33 打开一个用于保存选定图形的对话框。
22 CUI_HID_GALLERY_PROPERTIES_GENERAL 15 显示主题名称。
2c private%3Afactory%2Fswriter%2FGlobalDocument 1b 创建新的主控文档。
28 private%3Afactory%2Fswriter%3Fslot=21053 1d 创建新的 XForms 文档。
1b private%3Afactory%2Fswriter 32 创建新的文本文档 ($[officename] Writer)。
19 private%3Afactory%2Fscalc 36 创建新的电子表格文档 ($[officename] Calc)。
d .uno%3ANewDoc 48 如果要通过模板创建文档，请选择新建 - 模板和文档。
28 private%3Afactory%2Fswriter%3Fslot=21052 8c 打开“名片”对话框，您可以在其中为您的名片设置选项，然后创建一个新的文本文档 ($[officename] Writer)。
21 private%3Afactory%2Fswriter%2Fweb 1b 创建新的 HTML 文档。
2f service%3Acom.sun.star.sdb.DatabaseWizardDialog 36 打开“数据库向导”以创建数据库文件。
19 private%3Afactory%2Fsmath 30 创建新的公式文档 ($[officename] Math)。
19 private%3Afactory%2Fsdraw 30 创建新的绘图文档 ($[officename] Draw)。
1b SFX2_HID_TBXCONTROL_FILENEW 48 创建新的 $[officename] 文档。单击箭头以选择文档类型。
10 .uno%3AAddDirect 24 创建新的 $[officename] 文档。
28 private%3Afactory%2Fsimpress%3Fslot=6686 60 创建新的演示文稿 ($[officename] Impress)。将显示“演示文稿向导”对话框。
28 private%3Afactory%2Fswriter%3Fslot=21051 9c 打开“标签”对话框，您可以在其中为您的标签设置选项，然后为这些标签 ($[officename] Writer) 创建一个新的文本文档。
b slot%3A5500 48 使用现有模板创建一个新文档，或打开一个示例文档。
2b private%3Afactory%2Fsdatabase%3FInteractive 36 打开“数据库向导”以创建数据库文件。
1e SVT_HID_TEMPLATEDLG_TB_PREVIEW 27 用于预览选定的模板或文档。
1c SVT_HID_TEMPLATEDLG_TB_PRINT 1e 打印选定模板或文档。
1c SVT_HID_TEMPLATEDLG_FILEVIEW 9c 列出选定类别的可用模板或文档。选择某个模板或文档，然后单击打开。要预览文档，请单击右侧框上方的预览图标。
3d svtools%3APushButton%3ADLG_DOCTEMPLATE%3ABTN_DOCTEMPLATE_EDIT 21 打开选定模板进行编辑。
3f svtools%3APushButton%3ADLG_DOCTEMPLATE%3ABTN_DOCTEMPLATE_MANAGE 36 添加、删除或重新排列模板或示例文档。
1b SVT_HID_TEMPLATEDLG_TB_PREV 2d 转到上一级文件夹（如果存在）。
1b SVT_HID_TEMPLATEDLG_TB_BACK 24 转到对话框的上一个窗口。
1c SVT_HID_TEMPLATEDLG_ICONCTRL 84 类别显示在模板和文档对话框左侧的框中。单击某个类别将在标题框中显示与该类别相关联的文件。
1e SVT_HID_TEMPLATEDLG_TB_DOCINFO 27 显示选定模板或文档的属性。
13 .uno%3AInsertLabels 3f 允许您创建标签。标签是在文本文档中创建的。
13 SW_HID_LABEL_INSERT 21 创建新文档以进行编辑。
24 sw%3AListBox%3ATP_LAB_LAB%3ABOX_MAKE 21 选择要使用的纸张品牌。
29 sw%3ARadioButton%3ATP_LAB_LAB%3ABTN_SHEET 1e 在单张纸上打印标签。
27 sw%3AListBox%3ATP_LAB_LAB%3ALB_DATABASE 2a 选择用作标签数据源的数据库。
25 sw%3ACheckBox%3ATP_LAB_LAB%3ABOX_ADDR 62 创建一个含有您的回信地址的标签。"标签文字"框中现有的文字将被覆盖。
e SW_HID_LAB_LAB 33 指定标签文本并选择标签的纸张大小。
2a sw%3AImageButton%3ATP_LAB_LAB%3ABTN_INSERT 66 选择所需的数据库字段，然后单击此框左侧的箭头，将字段插入标签文字框中
24 sw%3AListBox%3ATP_LAB_LAB%3ALB_TABLE 3f 选择包含要在标签中使用的字段的数据库表格。
2d sw%3AMultiLineEdit%3ATP_LAB_LAB%3AEDT_WRITING 48 输入要在标签上显示的文本。也可以插入数据库字段。
26 sw%3AListBox%3ATP_LAB_LAB%3ALB_DBFIELD 66 选择所需的数据库字段，然后单击此框左侧的箭头，将字段插入标签文字框中
24 sw%3AListBox%3ATP_LAB_LAB%3ABOX_TYPE c3 选择要使用的大小格式。可用的格式取决于您在商标列表中选择的商标。要使用自定义的标签格式，请选择用户，然后单击格式选项卡来定义格式。
28 sw%3ARadioButton%3ATP_LAB_LAB%3ABTN_CONT 24 在连续的纸张上打印标签。
2a sw%3AMetricField%3ATP_LAB_FMT%3AFLD_HEIGHT 5a 显示标签或名片的高度。如果您定义了自定义格式，请在此输入值。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_VDIST 78 显示两张上下相邻的标签或名片的上边缘之间的距离。要定义自定义格式，请在此输入值。
28 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_LEFT 78 显示页面左边缘与首张标签或名片左边缘之间的距离。要定义自定义格式，请在此输入值。
28 sw%3AComboBox%3ADLG_SAVE_LABEL%3ACB_MAKE 1e 输入或选择所需品牌。
29 sw%3ANumericField%3ATP_LAB_FMT%3AFLD_ROWS 3f 输入要布满整个页面高度的标签或名片的数目。
2c sw%3ANumericField%3ATP_LAB_FMT%3AFLD_COLUMNS 3c 输入要布满整个页面宽度的标签或名片数目。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_HDIST 69 显示相邻标签或名片的左边缘之间的距离。要定义自定义格式，请在此输入值。
24 sw%3AEdit%3ADLG_SAVE_LABEL%3AED_TYPE 1e 输入或选择标签类型。
26 sw%3APushButton%3ATP_LAB_FMT%3APB_SAVE 27 保存当前标签或名片的格式。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_UPPER 81 显示页面上边缘与首张标签或名片上边缘之间的距离。如果您要定义自定义格式，请在此输入值。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_WIDTH 5a 显示标签或名片的宽度。如果您定义了自定义格式，请在此输入值。
e SW_HID_LAB_FMT 1b 设置纸张格式选项。
28 sw%3ANumericField%3ATP_LAB_PRT%3AFLD_COL 3c 输入要在页面的一行中放置的标签或名片数。
29 sw%3APushButton%3ADLG_SYNC_BTN%3ABTN_SYNC 57 将左上方的标签或名片的内容复制到页面上其余的标签或名片上。
2b sw%3APushButton%3ATP_LAB_PRT%3ABTN_PRTSETUP 23 打开 打印机设置 对话框。
28 sw%3ACheckBox%3ATP_LAB_PRT%3ACB_SYNCHRON 8d 如果单击标签同步化按钮，则可以只编辑一个标签或名片，而页面上其余的标签或名片的内容将随之更新。
2a sw%3ARadioButton%3ATP_LAB_PRT%3ABTN_SINGLE 2a 在一页上打印一个标签或名片。
28 sw%3ANumericField%3ATP_LAB_PRT%3AFLD_ROW 2a 输入页面上标签或名片的行数。
28 sw%3ARadioButton%3ATP_LAB_PRT%3ABTN_PAGE 24 创建一整页的标签或名片。
e SW_HID_LAB_PRT 54 设置用于标签或名片的其他选项，包括文字同步和打印机设置。
19 .uno%3AInsertBusinessCard 21 设计和创建自己的名片。
1e SW_HID_BUSINESS_FMT_PAGE_SHEET 15 分页打印名片。
1d SW_HID_BUSINESS_FMT_PAGE_TYPE c6 选择要使用的大小格式。可用的格式取决于您在商标列表中所做的选择。要使用自定义的大小格式，请选择 [用户]，然后单击格式选项卡来定义格式。
1e SW_HID_BUSINESS_FMT_PAGE_BRAND 21 选择要使用的纸张商标。
1d SW_HID_BUSINESS_FMT_PAGE_CONT 21 在连续纸张上打印名片。
35 sw%3AListBox%3ATP_VISITING_CARDS%3ALB_AUTO_TEXT_GROUP 48 选择一个名片类别，然后在内容列表中单击一种版式。
1c SW_HID_BUSINESS_CARD_CONTENT 6c 在自动图文集 - 区域框中选择一个名片类别，然后在内容列表中单击相应的版式。
2c sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FIRSTNAME_2 21 输入备用联系人的名字。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_STATE 22 输入您所在的国家/地区。
2b sw%3AEdit%3ATP_PRIVATE_DATA%3AED_SHORTCUT_2 27 输入备用联系人的姓名缩写。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_NAME_2 21 输入备用联系人的姓氏。
2b sw%3AEdit%3ATP_PRIVATE_DATA%3AED_PROFESSION 15 输入您的职业。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_WWW 25 输入您的 Internet 主页地址。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_PHONE 21 输入您的住宅电话号码。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_MOBILE 1b 输入您的手机号码。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_WWW 28 输入公司 Internet 主页的地址。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_MOBILE 21 输入您的移动电话号码。
2a sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COMP_EXT 24 输入公司的其他详细信息。
27 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_PHONE 1e 输入公司的电话号码。
27 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_STATE 2b 输入公司所在国家/地区的名称。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_SLOGAN 1b 输入公司的广告语。
1b SFX2_HID_FILEDLG_INSERT_BTN 39 将选定文件插入到当前文档中光标所在处。
33 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_NO 36 单击以取消删除此对话框中列出的文件。
1c SVT_HID_FILEVIEW_MENU_DELETE 4e 要删除文件，请在该文件上单击鼠标右键，然后选择删除。
47 fpicker%3AMenuButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_STANDARD 27 显示默认用户目录中的文件。
49 fpicker%3AImageButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_NEWFOLDER 15 创建新的目录。
18 SVT_HID_EXPLORERDLG_FILE 18 打开或导入文件。
43 fpicker%3AListBox%3ADLG_SVT_EXPLORERFILE%3ALB_EXPLORERFILE_FILETYPE 61 选择要打开的文件类型，或者选择所有文件 (*)以列出目录中的所有文件。
34 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_YES 30 单击以删除此对话框中列出的文件。
19 SFX2_HID_FILEDLG_STANDARD 30 显示所在目录中含有的文件和目录。
b .uno%3AOpen 18 打开或导入文件。
18 SVT_HID_FILEOPEN_VERSION 45 如果所选文件存在多个版本，请选择要打开的版本。
19 SVT_HID_FILEOPEN_READONLY 1e 以只读模式打开文件。
34 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_ALL 27 单击以删除所有选定的文件。
1c SVT_HID_FILEVIEW_MENU_RENAME 54 要重命名文件，请在该文件上单击鼠标右键，然后选择重命名。
41 fpicker%3AMenuButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_UP 5a 在目录分层结构中上移一个目录。缓慢单击以查看更高一级的目录。
23 FPICKER_HID_FILEDLG_AUTOCOMPLETEBOX 68 输入文件的名称或路径。您也可以输入以协议名称 ftp、http 或 https 开头的 URL。
20 SFX2_HID_FILEDLG_FOLDER_FILENAME 68 输入文件的名称或路径。您也可以输入以协议名称 ftp、http 或 https 开头的 URL。
19 SFX2_HID_FILEDLG_PATH_BTN 18 选择指示的路径。
1e SFX2_HID_FILEDLG_PATH_FILENAME 24 输入或从列表中选择路径。
2e uui%3AListBox%3ADLG_FILTER_SELECT%3ALB_FILTERS 2d 为要打开的文件选择导入过滤器。
f .uno%3ACloseDoc 2a 关闭当前文档，但不退出程序。
b .uno%3ASave 15 保存当前文档。
21 SVT_HID_FILESAVE_SAVEWITHPASSWORD 4e 使用密码保护文件，用户在打开文件之前必须输入该密码。
19 SVT_HID_FILESAVE_FILETYPE 2a 为要保存的文档选择文件格式。
20 SVT_HID_FILESAVE_CREATEDIRECTORY 18 创建一个新目录。
18 SVT_HID_FILESAVE_FILEURL 37 输入文件的文件名或路径。也可以输入 URL
d .uno%3ASaveAs 66 将当前文档保存到其他位置，或者以不同的文件名或文件类型保存当前文档。
20 SVT_HID_FILESAVE_CUSTOMIZEFILTER 45 用于设置某些类型的数据文件的电子表格保存选项。
18 SVT_HID_FILESAVE_LEVELUP 5a 在目录分层结构中上移一个目录。缓慢单击以查看更高一级的目录。
21 SVT_HID_FILESAVE_DEFAULTDIRECTORY 27 显示默认用户目录中的文件。
d SID_SAVEASDOC 66 将当前文档保存到其他位置，或者以不同的文件名或文件类型保存当前文档。
19 SVT_HID_FILESAVE_FILEVIEW 2d 显示所在目录包含的文件和目录。
1a SVT_HID_FILESAVE_SELECTION 88 仅将 %PRODUCTNAME Draw 和 Impress 中选定的图形对象导出为其他格式。如果取消选择此框，则导出整个文档。
17 SVT_HID_FILESAVE_DOSAVE f 保存文件。
17 SVT_HID_FILESAVE_DIALOG 66 将当前文档保存到其他位置，或者以不同的文件名或文件类型保存当前文档。
f .uno%3AExportTo 45 以不同的名称和格式将当前文档保存到指定的位置。
1c .uno%3ASetDocumentProperties 5a 显示当前文件的属性，包括统计信息（例如字数和文件创建日期）。
14 SFX2_HID_DOCINFODESC 21 含有文档的描述性信息。
27 sfx2%3AEdit%3ATP_DOCINFODESC%3AED_TITLE 18 输入文档的标题。
32 sfx2%3AMultiLineEdit%3ATP_DOCINFODESC%3AED_COMMENT 27 输入有助于识别文档的注释。
27 sfx2%3AEdit%3ATP_DOCINFODESC%3AED_THEMA 57 输入文档的主题。使用文档主题可以将内容相近的文档分为一组。
2a sfx2%3AEdit%3ATP_DOCINFODESC%3AED_KEYWORDS 7e 输入用于索引文档内容的字词。关键字之间必须用逗号隔开。关键字可以包含空白字符或分号。
2e sfx2%3APushButton%3ATP_DOCINFODOC%3ABTN_DELETE 83 将编辑时间重设为零，创建日期设为当前日期和时间，版本号设为 1，并删除修改日期和打印日期。
31 sfx2%3ACheckBox%3ATP_DOCINFODOC%3ACB_USE_USERDATA 9e 保存文件的同时保存用户的全名。可以通过选择 %PRODUCTNAME - 首选项工具 - 选项 - $[officename] - 用户数据来编辑用户名称。
2a sfx2%3AEdit%3ATP_DOCINFODOC%3AED_FILE_NAME 12 显示文件名。
13 SFX2_HID_DOCINFODOC 24 含有当前文件的基本信息。
1e SFX2_HID_CTRL_CUSTOMPROPERTIES a8 输入自定义内容。您可以更改每一行的名称、类型和内容。您可以添加或删除行。这些项将会作为元数据导出到其他文件格式。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO2 a8 输入自定义内容。您可以更改每一行的名称、类型和内容。您可以添加或删除行。这些项将会作为元数据导出到其他文件格式。
14 SFX2_HID_DOCINFOUSER 33 您可以为文档指定自定义的信息字段。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO1 a8 输入自定义内容。您可以更改每一行的名称、类型和内容。您可以添加或删除行。这些项将会作为元数据导出到其他文件格式。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO3 a8 输入自定义内容。您可以更改每一行的名称、类型和内容。您可以添加或删除行。这些项将会作为元数据导出到其他文件格式。
31 sfx2%3APushButton%3ATP_CUSTOMPROPERTIES%3ABTN_ADD 36 单击以向“属性”列表中添加一个新行。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO4 a8 输入自定义内容。您可以更改每一行的名称、类型和内容。您可以添加或删除行。这些项将会作为元数据导出到其他文件格式。
f SW_HID_DOC_STAT 24 显示当前文件的统计信息。
1e sc%3ATabPage%3ARID_SCPAGE_STAT 24 显示当前文件的统计信息。
27 sfx2%3AEdit%3ATP_DOCINFORELOAD%3AED_URL 29 输入要打开的文件的 URL 地址。
33 sfx2%3ANumericField%3ATP_DOCINFORELOAD%3AED_FORWARD 4b 输入将浏览器重定向到不同的文件之前，要等待的秒数。
37 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_RELOADUPDATE 78 经过在秒数框中输入的秒数之后，重新加载 HTML 页。要查看结果，请在浏览器中打开此页。
2d sfx2%3AComboBox%3ATP_DOCINFORELOAD%3ACB_FRAME 67 如果当前的 HTML 页中含有框对象，请选择要在其中加载文件的目标框 的名称。
33 sfx2%3APushButton%3ATP_DOCINFORELOAD%3APB_BROWSEURL 30 定位要打开的文件，然后单击打开。
38 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_FORWARDUPDATE 3c 经过在秒数框中输入的秒数之后，加载页面。
32 sfx2%3ANumericField%3ATP_DOCINFORELOAD%3AED_RELOAD 2d 输入重新加载页面前等待的秒数。
37 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_NOAUTOUPDATE 21 用户必须手动刷新页面。
16 SFX2_HID_DOCINFORELOAD 2d 设置 HTML 页的更新和重定向选项。
27 SFX2_HID_SECURITYTAB_OPEN_FILE_READONLY 30 选择允许此文档仅以只读模式打开。
2d HID_DLG_PASSWORD_TO_OPEN_MODIFY_FILE_READONLY 30 选择允许此文档仅以只读模式打开。
30 HID_DLG_PASSWORD_TO_OPEN_MODIFY_PASSWORD_TO_OPEN 27 键入密码。密码区分大小写。
2a HID_SECURITYTAB_CONFIRM_PASSWORD_TO_MODIFY 15 重新输入密码。
32 HID_DLG_PASSWORD_TO_OPEN_MODIFY_PASSWORD_TO_MODIFY 27 键入密码。密码区分大小写。
38 HID_DLG_PASSWORD_TO_OPEN_MODIFY_CONFIRM_PASSWORD_TO_OPEN 15 重新输入密码。
23 SFX2_HID_SECURITYTAB_RECORD_CHANGES 51 选择启用记录修改。此操作与编辑 - 修改 - 记录的作用相同。
1f SFX2_HID_SECURITYTAB_PROTECTION ae 使用密码保护修改记录状态。如果当前文档的修改记录受保护，按钮名称为取消保护。单击取消保护并输入正确的密码以禁用保护。
22 HID_SECURITYTAB_PASSWORD_TO_MODIFY 27 键入密码。密码区分大小写。
28 HID_SECURITYTAB_CONFIRM_PASSWORD_TO_OPEN 15 重新输入密码。
3a HID_DLG_PASSWORD_TO_OPEN_MODIFY_CONFIRM_PASSWORD_TO_MODIFY 15 重新输入密码。
20 HID_SECURITYTAB_PASSWORD_TO_OPEN 27 键入密码。密码区分大小写。
13 .uno%3ATemplateMenu 48 允许您管理和编辑模板，还可将当前文件保存为模板。
16 SFX2_HID_ORGANIZE_EDIT 21 打开选定模板进行编辑。
18 SFX2_HID_ORGANIZE_DELETE 18 删除当前的选择。
1f SFX2_HID_ORGANIZE_PRINTER_SETUP 33 修改选定文档的打印机和打印机设置。
1c SFX2_HID_CTL_ORGANIZER_RIGHT 81 显示可用的模板类别或打开的 $[officename] 文件。要改变列表内容，在下面的框中选择模板或文档。
1b SFX2_HID_CTL_ORGANIZER_LEFT 81 显示可用的模板类别或打开的 $[officename] 文件。要改变列表内容，在下面的框中选择模板或文档。
10 .uno%3AOrganizer 51 打开模板管理对话框，可以在其中管理模板和定义默认模板。
36 sfx2%3APushButton%3ADLG_ORGANIZE%3ABTN_ADDRESSTEMPLATE 3f 找到文档列表中要添加的文件，然后单击打开。
18 SFX2_HID_ORGANIZE_RESCAN 15 更新列表内容。
19 SFX2_HID_ORGANIZE_COPY_TO 15 导出选定模板。
15 SFX2_HID_ORGANIZE_NEW 1b 创建新的模板类别。
21 SFX2_HID_ORGANIZE_STDTEMPLATE_DEL 4b 选择 $[officename] 文档类型，将默认模板重设为原始模板。
2b sfx2%3AMenuButton%3ADLG_ORGANIZE%3ABTN_EDIT 36 含有管理和编辑模板与文档所需的命令。
17 SFX2_HID_ORGANIZE_PRINT 30 打印此文件所用样式的名称和属性。
2c sfx2%3AListBox%3ADLG_ORGANIZE%3ALB_RIGHT_TYP 3f 选择模板或文档，修改上面列表中显示的内容。
21 SFX2_HID_ORGANIZE_STDTEMPLATE_ADD 5a 创建同一类型的 $[officename] 新文档时，使用选定模板作为默认模板。
2c sfx2%3APushButton%3ADLG_ORGANIZE%3ABTN_FILES 3f 找到文档列表中要添加的文件，然后单击打开。
1b SFX2_HID_ORGANIZE_COPY_FROM 7e 导入附加模板。要导入模板，请在列表中选择模板文件夹，单击命令按钮，然后选择导入模板。
2b sfx2%3AListBox%3ADLG_ORGANIZE%3ALB_LEFT_TYP 3f 选择模板或文档，修改上面列表中显示的内容。
3a svtools%3AComboBox%3ADLG_ADDRESSBOOKSOURCE%3ACB_DATASOURCE 1e 选择通讯簿的数据源。
22 SVT_HID_ADDRTEMPL_FIELD_ASSIGNMENT 3c 在数据表格中选择与通讯簿条目对应的字段。
35 svtools%3AComboBox%3ADLG_ADDRESSBOOKSOURCE%3ACB_TABLE 21 选择通讯簿的数据表格。
49 svtools%3APushButton%3ADLG_ADDRESSBOOKSOURCE%3APB_ADMINISTATE_DATASOURCES 2a 向通讯簿源列表添加新数据源。
18 .uno%3AAddressBookSource 2d 编辑通讯簿的字段指定和数据源。
28 sfx2%3AEdit%3ADLG_DOC_TEMPLATE%3AED_NAME 15 输入模板名称。
32 sfx2%3AListBox%3ADLG_DOC_TEMPLATE%3ALB_STYLESHEETS 1e 列出可用的模板类别。
2e sfx2%3AListBox%3ADLG_DOC_TEMPLATE%3ALB_SECTION 2a 选择一个类别，以保存新模板。
15 .uno%3ASaveAsTemplate 21 将当前文档另存为模板。
32 sfx2%3APushButton%3ADLG_DOC_TEMPLATE%3ABT_ORGANIZE 45 打开模板管理对话框，可在其中管理或创建新模板。
2e sfx2%3APushButton%3ADLG_DOC_TEMPLATE%3ABT_EDIT 24 打开选定模板以进行编辑。
13 .uno%3AOpenTemplate 3c 打开对话框，可以在其中选择要编辑的模板。
20 .HelpID%3Avcl%3APrintDialog%3AOK 69 打印当前文档、选择的内容或指定的页面。您也可以为当前文档设置打印选项。
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A2 39 使用特定的因子来缩小或放大打印的公式。
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A1 33 指定打印时您不再想进一步缩放页面。
2f .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ARowsBox f 选择行数。
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A0 24 指定是否以原始颜色打印。
c HID_PRINTDLG 69 打印当前文档、选择的内容或指定的页面。您也可以为当前文档设置打印选项。
37 .HelpID%3Avcl%3APrintDialog%3APageContentType%3AListBox 2d 选择文档中的哪一部分进行打印。
2e .HelpID%3Avcl%3APrintDialog%3APageRange%3AEdit b3 要打印某范围内的页面，使用格式 3-6 。要打印单页，使用格式 7;9;11 。如果要同时打印某范围内的页面和单页，可使用格式 3-6;8;10;12 。
35 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APagesPerSheet 1b 每张纸上打印多页。
3c .HelpID%3Avcl%3APrintDialog%3APrintPaperFromSetup%3ACheckBox 48 指定纸张来源是否使用打印机设置中定义的纸张来源。
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A0 33 打印公式，但不调整当前的字体大小。
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A2 33 仅打印当前文档中选定的区域或对象。
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A0 2d 指定打印输出时如何缩放幻灯片。
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A1 36 根据打印输出使用的页面格式调整公式。
38 .HelpID%3Avcl%3APrintDialog%3AIsPrintDateTime%3ACheckBox 2a 指定是否打印当前日期和时间。
2f .HelpID%3Avcl%3APrintDialog%3ABorder%3ACheckBox 33 对打印输出中的公式区域应用细边框。
36 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ASheetMarginBox 33 设置打印输出的页与纸张边缘的间距。
2e .HelpID%3Avcl%3APrintDialog%3AJobPage%3ACopies 21 输入您希望打印的页数。
36 .HelpID%3Avcl%3APrintDialog%3APrintProspect%3ACheckBox 3f 选择“小册子”选项，以小册子格式打印文档。
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A0 15 打印整个文档。
35 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APageMarginBox 30 设置每张纸上单独页面之间的间距。
38 .HelpID%3Avcl%3APrintDialog%3APrintEmptyPages%3ACheckBox 15e 如果此选项被启用，则会打印自动插入的空白页。执行双面打印时此方法是最佳的选择。例如：在某本书中，“章节”段落样式被设置为始终以奇数页开始。如果前一章在奇数页结束，则 %PRODUCTNAME 会插入一个空白页作为偶数页。该选项用来控制是否打印该偶数页。
31 .HelpID%3Avcl%3APrintDialog%3AOptPage%3AToReverse 15 以逆序打印页。
30 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AOrderBox 1e 选择页面打印的顺序。
31 .HelpID%3Avcl%3APrintDialog%3ATitleRow%3ACheckBox 36 指定是否在打印输出中包括文档的名称。
38 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APagesPerSheetBox 24 设置每张纸上打印多少页。
38 .HelpID%3Avcl%3APrintDialog%3APrintHiddenText%3ACheckBox 33 启用该选项则打印已标识为隐藏的文本
3d .HelpID%3Avcl%3APrintDialog%3APrintTextPlaceholder%3ACheckBox 60 启用该选项则打印文字占位符。禁用该选项，则文字占位符打印为空白。
3b .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AListBox 33 “指定在何处打印批注（如有有）。”
37 .HelpID%3Avcl%3APrintDialog%3APrintScale%3ANumericField 39 使用特定的因子来缩小或放大打印的公式。
3d .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AFixedText 2d 指定在何处打印批注（如果有）。
32 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AColumnsBox f 选择列数。
3c .HelpID%3Avcl%3APrintDialog%3APrintPageBackground%3ACheckBox 5a 指定是否打印插入页面背景（格式 - 页面 - 背景）中的颜色和对象。
38 .HelpID%3Avcl%3APrintDialog%3APrintProspectRTL%3AListBox 54 对于小册子打印，您可以选择从左到右或从右到左的页面顺序。
3b .HelpID%3Avcl%3APrintDialog%3APrintLeftRightPages%3AListBox 1e 指定哪些页将被打印。
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A2 24 指定是否以黑白方式打印。
2e .HelpID%3Avcl%3APrintDialog%3AOptPage%3AToFile 7e 打印到文件而非打印到打印机。当启用此选项时，“打印”按钮将被重命名为“打印到文件”...
3a .HelpID%3Avcl%3APrintDialog%3ASlidesPerPageOrder%3AListBox 2d 指定怎样在打印页上排列幻灯片。
32 .HelpID%3Avcl%3APrintDialog%3AJobPage%3AProperties 5d 打开打印机属性对话框。根据您选择的打印机，打印机属性会有不同。
31 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ABorderBox 18 为每页加上边框。
32 .HelpID%3Avcl%3APrintDialog%3AJobPage%3ADetailsBtn 2e 显示/隐藏所选打印机的详细信息。
35 .HelpID%3Avcl%3APrintDialog%3ASlidesPerPage%3AListBox 2a 设置每张纸上打印几张幻灯片。
32 .HelpID%3Avcl%3APrintDialog%3AOptPage%3ASingleJobs 72 勾选该选项，则不再依赖打印机来创建逐份打印，但是为每份副本创建一个打印任务。
3c .HelpID%3Avcl%3APrintDialog%3APrintProspectInclude%3AListBox 21 选择打印哪些小册子页。
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A2 57 指定是否缩小超出打印输出边缘的对象尺寸来适应打印机的纸张。
36 .HelpID%3Avcl%3APrintDialog%3APrintControls%3ACheckBox 39 指定是否打印文本文档中的窗体控件字段。
36 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AOrientationBox 18 选择纸张的方向。
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A3 78 指定以平铺格式打印页。如果页或幻灯片比纸张小，那么一张纸上会打印多个页或幻灯片。
c .uno%3APrint 69 打印当前文档、选择的内容或指定的页面。您也可以为当前文档设置打印选项。
34 .HelpID%3Avcl%3APrintDialog%3AIsPrintName%3ACheckBox 2a 指定是否打印文档每页的名字。
40 .HelpID%3Avcl%3APrintDialog%3APrintPicturesAndObjects%3ACheckBox 44 指定是否打印文本文档中的图形和图像或 OLE 对象。
34 .HelpID%3Avcl%3APrintDialog%3AFormulaText%3ACheckBox 4e 指定是否在打印输出的底端打印包括“命令”窗口的内容。
33 .HelpID%3Avcl%3APrintDialog%3AJobPage%3APrinterList e1 列表中显示系统已安装的打印机。选择当前打印任务要使用的打印机。点击打印机详细信息按钮即可查看到所选打印机的更多信息。点击“属性”按钮可修改打印机属性。
38 .HelpID%3Avcl%3APrintDialog%3APrintBlackFonts%3ACheckBox 2a 指定是否始终以黑色打印文本。
2f .HelpID%3Avcl%3APrintDialog%3AJobPage%3ACollate 24 保留原始文档的页面顺序。
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A1 27 指定是否将彩色打印成灰度。
36 .HelpID%3Avcl%3APrintDialog%3AIsPrintHidden%3ACheckBox 2a 指定是否打印当前已隐藏的页。
3c .HelpID%3Avcl%3APrintDialog%3AIsIncludeEmptyPages%3ACheckBox 5a 如果勾选了该选项，则无单元格内容或绘图对象的空白页，不打印。
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A1 24 仅打印页框中指定的页面。
42 svtools%3APushButton%3ADLG_SVT_PRNDLG_PRNSETUPDLG%3ABTN_PROPERTIES 3c 针对当前文档，修改操作系统的打印机设置。
39 svtools%3AListBox%3ADLG_SVT_PRNDLG_PRNSETUPDLG%3ALB_NAMES 78 列出操作系统中已经安装的打印机。要修改默认打印机，请从列表中选择一个打印机名称。
32 svtools%3AModalDialog%3ADLG_SVT_PRNDLG_PRNSETUPDLG 2d 选择用于当前文档的默认打印机。
13 .uno%3APrinterSetup 2d 选择用于当前文档的默认打印机。
17 .uno%3ASendMailDocAsOOo 74 在默认电子邮件程序中打开新窗口，并将当前文档作为附件。使用 OpenDocument 文件格式。
11 .uno%3ASendToMenu 36 将当前文档的副本发送给其他应用程序。
16 .uno%3ASendMailDocAsMS 71 在默认电子邮件程序中打开新窗口，并将当前文档作为附件。使用 Microsoft 文件格式。
f .uno%3ASendMail 6c 在默认电子邮件程序中打开新窗口，并将当前文档作为附件。使用当前文件格式。
24 SW_HID_SEND_MASTER_CTRL_EDIT_FILEURL 51 选择用于把源文档分成子文档所使用的段落样式或大纲级别。
19 SVT_HID_FILESAVE_TEMPLATE 51 选择用于把源文档分成子文档所使用的段落样式或大纲级别。
28 SW_HID_SEND_MASTER_CTRL_CONTROL_FILEVIEW 51 选择用于把源文档分成子文档所使用的段落样式或大纲级别。
28 SW_HID_SEND_MASTER_CTRL_LISTBOX_TEMPLATE 51 选择用于把源文档分成子文档所使用的段落样式或大纲级别。
13 .uno%3ANewGlobalDoc 98 从当前的 Writer 文档创建一个主控文档。源文档中出现选定段落样式或大纲级别的地方，都会创建一个新的子文档。
b .uno%3AQuit 3c 关闭所有 $[officename] 程序并提示您保存更改。
e .uno%3ASaveAll 30 保存所有已修改的 $[officename] 文档。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_OPEN 27 在只读窗口中打开所选版本。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_VIEW 24 显示所选版本的全部注释。
31 sfx2%3AMultiLineEdit%3ADLG_COMMENTS%3AME_VERSIONS 6f 当保存新版本时，在此处输入注释。如果单击显示打开此对话框，则无法编辑注释。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_SAVE 8f 将文档的当前状态另存为新版本。如果需要，保存新版本之前，还可以在 插入版本注释 对话框中输入注释。
2f sfx2%3ACheckBox%3ADLG_VERSIONS%3ACB_SAVEONCLOSE 60 如果您对文档进行了修改，当关闭文档时 $[officename] 会自动保存新版本。
14 .uno%3AVersionDialog 78 在同一个文件中，保存并管理当前文档的多个版本。还可以打开、删除和比较以前的版本。
2d sfx2%3APushButton%3ADLG_VERSIONS%3APB_COMPARE 2a 比较在各个版本中所做的修改。
21 sfx2%3AModalDialog%3ADLG_VERSIONS 6c 列出当前文档的现有版本，以及这些版本的创建日期、时间、作者和相关的注释。
2c sfx2%3APushButton%3ADLG_VERSIONS%3APB_DELETE 15 删除所选版本。
15 .uno%3ARecentFileList 51 列出最近打开的文件。要打开列表中的文件，请单击其名称。
b .uno%3AUndo 90 撤消键入的最后一个命令或最后一个条目。要选择想撤消的命令，请单击“标准”栏上撤消图标旁边的箭头。
14 SVX_HID_IMAPDLG_UNDO 90 撤消键入的最后一个命令或最后一个条目。要选择想撤消的命令，请单击“标准”栏上撤消图标旁边的箭头。
b .uno%3ARedo 87 恢复上一个由撤消命令撤消的操作。要选择想撤消的撤消步骤，请单击标准栏上恢复图标旁边的箭头。
14 SVX_HID_IMAPDLG_REDO 87 恢复上一个由撤消命令撤消的操作。要选择想撤消的撤消步骤，请单击标准栏上恢复图标旁边的箭头。
13 .uno%3ARepeatAction 43 重复执行上一个命令。此命令适用于 Writer 和 Calc。
d .uno%3ARepeat 43 重复执行上一个命令。此命令适用于 Writer 和 Calc。
a .uno%3ACut 24 将选定内容移到剪贴板中。
b .uno%3ACopy 2a 将选定的内容复制到剪贴板上。
c .uno%3APaste 57 在光标的位置插入剪贴板的内容，并替换任何选定的文本或对象。
17 SC_HID_SC_REPLCELLSWARN 57 在光标的位置插入剪贴板的内容，并替换任何选定的文本或对象。
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_ADD 0 
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_NOOP 0 
30 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSATTRS 0 
30 cui%3AListBox%3AMD_PASTE_OBJECT%3ALB_INSERT_LIST 30 选择粘贴剪贴板内容时采用的格式。
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_SKIP_EMPTY 0 
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_SUB 0 
33 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSFORMULAS 0 
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_DOWN 0 
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_MUL 0 
30 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSNOTES 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSSTRINGS 0 
2e sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSALL 0 
31 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_TRANSPOSE 0 
11 CUI_HID_PASTE_DLG 21 显示剪贴板内容的来源。
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_DIV 0 
2c sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_LINK 0 
33 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSDATETIME 0 
13 .uno%3APasteSpecial 4e 将剪贴板上的内容插入到当前文件中，格式可以由您指定。
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_NONE 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSNUMBERS 0 
33 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_RIGHT 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSOBJECTS 0 
15 .uno%3AInsertContents 0 
15 .uno%3APasteClipboard 4e 将剪贴板上的内容插入到当前文件中，格式可以由您指定。
10 .uno%3ASelectAll 3c 选择当前文件、框架或文本对象的全部内容。
d .uno%3ASelect 3c 选择当前文件、框架或文本对象的全部内容。
34 svx%3AListBox%3ARID_SVXDLG_SEARCH%3ALB_CALC_SEARCHIN 76 查找您在公式和固定（非计算）数值中指定的字符。例如，您可以查找包含 'SUM' 的公式。
36 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_REPLACE_ALL 2a 替换所有要替换的文字或格式。
11 .uno%3ADownSearch 2a 单击以向下搜索下一个匹配项。
34 svx%3ARadioButton%3ARID_SVXDLG_SEARCH%3ARB_CALC_ROWS 1b 从左到右查找整行。
2f svx%3AMoreButton%3ARID_SVXDLG_SEARCH%3ABTN_MORE 5a 显示更多或更少查找选项。再次单击该按钮可隐藏展开的查找选项。
33 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_NOFORMAT 63 单击“查找”或“替换”框，然后单击此按钮以删除基于格式的查找条件。
41 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_JAP_MATCH_FULL_HALF_WIDTH 1e 区分半角和全角字符。
2e svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_REGEXP 27 允许您在查找中使用通配符。
f .uno%3AFindText 4c 在当前文档中键入要搜索的文本。请按 Enter 以搜索文本。
40 cui%3APushButton%3ARID_SVXDLG_SEARCHFORM%3APB_SOUNDSLIKESETTINGS 39 设置在日文中使用的类似符号的查找选项。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_SELECTIONS 2d 仅在选定的文本或单元格内查找。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_MATCH_CASE 1b 区分字符的大小写。
33 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_WHOLE_WORDS 33 查找与查找文本相同的全字或单元格。
2e svx%3AListBox%3ARID_SVXDLG_SEARCH%3ALB_REPLACE 57 输入替换文字，或从列表中选择一个最近输入的替换文字或样式。
17 .uno%3ASearchProperties 33 查找或替换当前文档中的文字或格式。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_ALL_SHEETS 39 在当前电子表格文件的所有工作表中查找。
39 cui%3ACheckBox%3ARID_SVXDLG_SEARCHFORM%3ACB_HALFFULLFORMS 1e 区分半角和全角字符。
2f svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_LAYOUTS cc 查找使用您指定的样式排版的文本。选择此复选框，然后从查找内容列表中选择一个样式。要指定用来替换的样式，请从“替换为”列表中选择一个样式。
32 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_REPLACE 42 替换查找到的选定文字或格式，然后查找下一处。
13 .uno%3ASearchDialog 33 查找或替换当前文档中的文字或格式。
37 svx%3ARadioButton%3ARID_SVXDLG_SEARCH%3ARB_CALC_COLUMNS 1b 从上到下查找整列。
28 svx_ComboBox_RID_SVXDLG_SEARCH_ED_SEARCH 4e 输入要查找的文字，或从列表中选择一个先前的查找内容。
2d SVX%3ALISTBOX%3ARID_SVXDLG_SEARCH%3ALB_SEARCH 4e 输入要查找的文字，或从列表中选择一个先前的查找内容。
31 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_SEARCH 48 查找并选中文档中下一个符合查找条件的文字或格式。
35 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_SEARCH_ALL 75 查找并选择要在文档（仅限“文字”和“表格”文档）中查找的文本或格式的所有实例。
37 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_JAP_SOUNDS_LIKE 89 让您为在日语文本中使用的相似符号指定查找选项。选中此复选框，然后单击 ... 按钮以指定查找选项。
2f svx%3AComboBox%3ARID_SVXDLG_SEARCH%3AED_REPLACE 57 输入替换文字，或从列表中选择一个最近输入的替换文字或样式。
2e svx%3AComboBox%3ARID_SVXDLG_SEARCH%3AED_SEARCH 4e 输入要查找的文字，或从列表中选择一个先前的查找内容。
f .uno%3AUpSearch 2a 单击以向上搜索下一个匹配项。
35 svx%3APushButton%3ARID_SVXDLG_SEARCH%3APB_JAP_OPTIONS 39 设置在日文中使用的类似符号的查找选项。
39 cui%3ACheckBox%3ARID_SVXDLG_SEARCHFORM%3ACB_SOUNDSLIKECJK 89 让您为在日语文本中使用的相似符号指定查找选项。选中此复选框，然后单击 ... 按钮以指定查找选项。
31 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_BACKWARDS 54 从当前光标所在位置开始查找，并返回文件的开始处继续查找。
1d CUI_HID_SEARCH_APPROXSETTINGS 71 查找与查找内容文本类似的条目。选中此复选框，然后单击 ... 按钮以定义类似选项。
3b cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_OTHER 2d 输入查找项中可以交换的字符数。
37 cui%3ACheckBox%3ARID_SVXDLG_SEARCHSIMILARITY%3ACB_RELAX 3c 查找与类似查找设置的任意组合相匹配的项。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_SIMILARITY 71 查找与查找内容文本类似的条目。选中此复选框，然后单击 ... 按钮以定义类似选项。
3d cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_SHORTER 33 输入词语可以少于查找条目的字符数。
3c cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_LONGER 36 输入词语最多可以超出查找项的字符数。
34 svx%3APushButton%3ARID_SVXDLG_SEARCH%3APB_SIMILARITY 1e 设置类似查找的选项。
15 CUI_HID_SEARCH_APPROX 71 查找与查找内容文本类似的条目。选中此复选框，然后单击 ... 按钮以定义类似选项。
1b CUI_HID_SEARCHATTR_CTL_ATTR 1b 选择要查找的属性。
12 CUI_HID_SEARCHATTR 1b 选择要查找的属性。
34 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_ATTRIBUTE ea 选择要查找的文本属性。例如，如果查找字体属性，将会找到不使用默认字体的文本的所有实例。将找到具有直接编码字体属性的所有文本，以及样式切换字体属性的所有文本。
31 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_FORMAT 5a 查找指定的文字格式特征，例如字体类型、字体效果和文字方向等。
11 SW_HID_NAVI_TBX23 33 将导航列表中所选条目下移一个位置。
11 SW_HID_NAVI_TBX20 36 在主控文档中插入文件、索引或新文档。
1c SW_HID_GLBLTREE_INS_NEW_FILE 21 创建并插入新的子文档。
18 SW_HID_GLBLTREE_UPD_LINK 15 更新所有链接。
11 SW_HID_NAVI_TBX18 9c 编辑导航列表中所选组件的内容。如果选择了文件，将打开此文件以进行编辑。如果选择了索引，将打开索引对话框。
11 SW_HID_NAVI_TBX21 87 将链接文件内容的副本保存在主控文档中。这样可以确保在无法访问链接文件时，当前内容仍然可用。
16 SW_HID_GLBLTREE_INSERT 36 在主控文档中插入文件、索引或新文档。
17 SW_HID_GLBLTREE_UPD_SEL 15 更新所选内容。
16 SW_HID_GLBLTREE_UPDATE 24 单击并选择要更新的内容。
1e SW_HID_NAVIGATOR_GLOB_TREELIST 8d 导航列出主控文档的主要组件。当把鼠标指针置于列表中某子文档的名称上时，将显示子文档的完整路径。
13 SW_HID_GLBLTREE_DEL 24 从导航列表中删除所选项。
11 SW_HID_NAVI_TBX22 33 将导航列表中所选条目上移一个位置。
19 SW_HID_GLBLTREE_EDIT_LINK 24 修改选定文件的链接属性。
16 SW_HID_GLBLTREEUPD_ALL 15 更新所有内容。
18 SW_HID_GLBLTREE_INS_FILE 36 在主控文档中插入一个或多个现有文件。
11 SW_HID_NAVI_TBX19 24 单击并选择要更新的内容。
18 SW_HID_GLBLTREE_INS_TEXT 90 在能够输入文本的主控文档中插入一个新的段落。在“导航”中，不能在一个现有的文本条目旁边插入文本。
17 SW_HID_GLBLTREE_UPD_IDX 15 更新所有索引。
17 SW_HID_GLBLTREE_INS_IDX 2a 在主控文档中插入索引或目录。
14 SW_HID_GLBLTREE_EDIT 9c 编辑导航列表中所选组件的内容。如果选择了文件，将打开此文件以进行编辑。如果选择了索引，将打开索引对话框。
33 cui%3ARadioButton%3AMD_UPDATE_BASELINKS%3ARB_MANUAL 30 只有在单击更新按钮时才更新链接。
36 cui%3ARadioButton%3AMD_UPDATE_BASELINKS%3ARB_AUTOMATIC a5 打开文件时自动更新链接的内容。源文件中的任何修改都将在含有此链接的文件中显示出来。链接的图形文件只能手动更新。
15 CUI_HID_LINKDLG_TABLB 5d 双击列表中的链接，打开文件对话框，以便为此链接选择另一个对象。
11 .uno%3ALinkDialog ae 您可以编辑当前文档中每个链接的属性，包括指向源文件的路径。如果当前文档中不含有指向其他文件的链接，则无法使用此命令。
36 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_BREAK_LINK 72 断开源文件和当前文档之间的链接。源文件最近一次更新的内容将保留在当前文档中。
36 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_UPDATE_NOW 63 更新所选链接，以便在当前文档中显示的链接文件是最近一次保存的版本。
12 .uno%3AManageLinks ae 您可以编辑当前文档中每个链接的属性，包括指向源文件的路径。如果当前文档中不含有指向其他文件的链接，则无法使用此命令。
39 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_CHANGE_SOURCE 21 修改选定链接的源文件。
10 .uno%3AEditLinks ae 您可以编辑当前文档中每个链接的属性，包括指向源文件的路径。如果当前文档中不含有指向其他文件的链接，则无法使用此命令。
14 .uno%3APlugInsActive 136 允许您编辑文件中的 插件。选择此命令可启用或禁用此功能。启用此功能后，会在此命令的旁边显示一个选中标记，您可以在其上下文菜单中找到用于编辑插件的命令。禁用此功能后，可以在其上下文菜单中找到用于控制插件的命令。
13 .uno%3AOriginalSize 2a 将对象的大小调整为原始大小。
12 .uno%3AObjectMenue 4e 允许您编辑通过插入 - 对象命令插入到文件中的选定对象。
38 cui%3APushButton%3AMD_INSERT_OBJECT_IFRAME%3ABT_FILEOPEN 4b 找到要在选定的浮动框架中显示的文件，然后单击打开。
2d cui%3AEdit%3AMD_INSERT_OBJECT_IFRAME%3AED_URL 80 输入要在浮动框架中显示的文件的路径和名称。您也可以单击 ... 按钮，然后找到要显示的文件。
41 cui%3ACheckBox%3AMD_INSERT_OBJECT_IFRAME%3ACB_MARGINHEIGHTDEFAULT 15 应用默认间距。
3e cui%3ANumericField%3AMD_INSERT_OBJECT_IFRAME%3ANM_MARGINHEIGHT 84 输入浮动框架的上、下边缘与框架内容之间的垂直间隔。浮动框架内、外的文档必须均为 HTML 文档。
3d cui%3ANumericField%3AMD_INSERT_OBJECT_IFRAME%3ANM_MARGINWIDTH 84 输入浮动框架的左、右边缘与框架内容之间的水平间隔。浮动框架内、外的文档必须均为 HTML 文档。
33 cui%3AEdit%3AMD_INSERT_OBJECT_IFRAME%3AED_FRAMENAME 70 输入浮动框架的名称。名称中不能含有空格、特殊字符，也不能以下划线 ( _ ) 开头。
3d cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_FRMBORDER_ON 1e 显示浮动框架的边框。
3c cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGON 21 显示浮动框架的滚动条。
3e cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_FRMBORDER_OFF 1e 隐藏浮动框架的边框。
3d cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGOFF 21 隐藏浮动框架的滚动条。
3e cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGAUTO 5a 如果当前活动浮动框架在需要时可能会具有滚动条，请选中此选项。
15 SVX_HID_IMAPDLG_MACRO 3c 用于指定单击浏览器中选定热点时执行的宏。
1a SVX_HID_IMAPDLG_POLYINSERT 3c 在热点轮廓上单击鼠标的位置添加一个接点。
16 SVX_HID_IMAPDLG_ACTIVE 4b 禁用或启用选定热点的超链接。被禁用的热点是透明的。
18 SVX_HID_IMAPDLG_POLYMOVE 2a 用于移动选定热点的各个接点。
18 SVX_HID_IMAPDLG_FREEPOLY 122 绘制一个基于任意多边形的热点。单击此图标并移动到要绘制热点的位置。绘制一个不规则线条，然后释放鼠标键以形成一个封闭的形状。绘制完毕后可以输入热点的地址和文本，然后选择将要在其中打开该 URL 的框架。
16 SVX_HID_IMAPDLG_CIRCLE 9e 在图形中拖动的位置绘制一个椭圆热点。之后，您可以输入热点的地址和文本，然后选择将要在其中打开该 URL 的框架。
15 SVX_HID_IMAPDLG_APPLY 2a 将所做的修改应用到图像映射。
2a svx%3AComboBox%3ARID_SVXDLG_IMAP%3ACBB_URL 3a 输入当单击选定热点时所要打开文件的 URL。
1a SVX_HID_IMAPDLG_POLYDELETE 18 删除选定的接点。
14 SVX_HID_IMAPDLG_OPEN 5f 以 MAP-CERN、MAP-NCSA 或 SIP StarView ImageMap 文件格式加载现有的图像映射。
18 SVX_HID_IMAPDLG_PROPERTY 24 用于定义选定热点的属性。
14 SVX_HID_IMAPDLG_POLY 197 在图形中绘制一个多边形热点。单击此图标，在图形中拖动，然后单击以定义多边形的一条边。移动到下一条边的端点处，然后单击鼠标。重复此操作，直到绘制完多边形的所有边。绘制完毕后，双击鼠标以形成封闭的多边形。绘制完毕后可以输入热点的地址和文本，然后选择将要在其中打开该 URL 的 Frame。
16 SVX_HID_IMAPDLG_SELECT 2d 在图像映射中选择要编辑的热点。
15 .uno%3AImageMapDialog a1 允许将 URL 附加到单个图形或一组图形中的特定区域（称为热点）。图像映射由一组热点组成，其中包含一个或多个热点。
27 svx%3AEdit%3ARID_SVXDLG_IMAP%3AEDT_TEXT 4b 输入当鼠标停留在浏览器中的热点上时所要显示的文本。
18 SVX_HID_IMAPDLG_POLYEDIT 39 用于通过编辑接点来修改选定热点的形状。
14 SVX_HID_IMAPDLG_RECT a4 在图形中拖动的位置绘制一个长方形热点。绘制完毕后可以输入热点的地址和文本，然后选择将要在其中打开该 URL 的 Frame。
18 SVX_HID_IMAPDLG_GRAPHWND 0 
16 SVX_HID_IMAPDLG_SAVEAS 53 以 MAP-CERN、MAP-NCSA 或 SIP StarView ImageMap 文件格式保存图像映射。
26 SVX%3AMODALDIALOG%3ARID_SVXDLG_IMAPURL 1e 列出选定热点的属性。
34 SVX_MULTILINEEDIT_RID_SVXDLG_IMAPURL_EDT_DESCRIPTION 18 输入热点的说明。
2a SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_NAME 18 输入图像的名称。
34 SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_URLDESCRIPTION 45 输入当鼠标停在浏览器中的热点上时要显示的文字。
29 SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_URL 34 输入单击选定热点时打开的文件的 URL。
31 SVX%3ACOMBOBOX%3ARID_SVXDLG_IMAPURL%3ACBB_TARGETS 8f 输入要在其中打开 URL 的目标窗体的名称。您也可以从列表中选择一个所有浏览器都能识别的标准窗体名称。
12 .uno%3AChangesMenu 30 列出可用于跟踪文件中修改的命令。
16 .uno%3ATraceChangeMode 36 跟踪当前文档中每处修改的作者和日期。
13 .uno%3ATrackChanges 36 跟踪当前文档中每处修改的作者和日期。
12 SC_HID_CHG_PROTECT 6f 防止用户停用记录修改功能，或者防止用户在未输入密码的情况下接受或拒绝修改。
1d .uno%3AProtectTraceChangeMode 6f 防止用户停用记录修改功能，或者防止用户在未输入密码的情况下接受或拒绝修改。
12 .uno%3AShowChanges 21 显示或隐藏记录的修改。
19 .uno%3AShowTrackedChanges 21 显示或隐藏记录的修改。
16 CUI_HID_REDLINING_EDIT 21 输入已记录修改的注释。
14 .uno%3ACommentChange 21 输入已记录修改的注释。
1c .uno%3ACommentChangeTracking 21 输入已记录修改的注释。
1b .uno%3AAcceptTrackedChanges 21 接受或拒绝记录的修改。
14 .uno%3AAcceptChanges 21 接受或拒绝记录的修改。
19 SC_HID_SC_CHANGES_COMMENT 1e 编辑选定修改的注释。
13 SW_HID_SORT_COMMENT 36 按照附加到修改的注释对列表进行排序。
20 SVX_HID_REDLINING_VIEW_PB_ACCEPT 3c 接受选定的修改，并取消文档中的突出显示。
15 SC_HID_SC_SORT_AUTHOR 1e 列出进行修改的用户。
1e SVX_HID_REDLINING_VIEW_DG_VIEW a2 列出文档中已记录的修订。当您选中列表中的某个条目时，会突出显示文档中的修订。要对列表进行排序，请单击列标题。
14 SC_HID_SORT_POSITION 5d 按照修改在文档中的位置对列表进行降序排序。这是默认的排序方法。
10 SW_HID_SORT_DATE 27 按照日期和时间对列表排序。
1e SVX_HID_REDLINING_VIEW_PB_UNDO 1e 编辑选定修改的注释。
12 SW_HID_SORT_ACTION 2a 按照修改类型对列表进行排序。
13 SW_HID_EDIT_COMMENT 1e 编辑选定修改的注释。
23 SVX_HID_REDLINING_VIEW_PB_ACCEPTALL 3f 接受所有修改，并取消其在文档中的突出显示。
13 SC_HID_SC_SORT_DATE 27 列出所做修改的日期和时间。
1b SVX_HID_REDLINING_VIEW_PAGE 1e 接受或拒绝单个修改。
19 .uno%3AAcceptTracedChange 3c 接受选定的修改，并取消文档中的突出显示。
15 SC_HID_SC_SORT_ACTION 21 列出文档中所做的修改。
12 SW_HID_SORT_AUTHOR 1b 列表按照作者排序。
23 SVX_HID_REDLINING_VIEW_PB_REJECTALL 48 拒绝所有修改，并取消这些修改在文档中的突出显示。
19 .uno%3ARejectTracedChange 42 拒绝选定的修改，并取消其在文档中的突出显示。
20 SVX_HID_REDLINING_VIEW_PB_REJECT 42 拒绝选定的修改，并取消其在文档中的突出显示。
16 SC_HID_SC_SORT_COMMENT 21 列出附加到修改的注释。
21 SVX_HID_REDLINING_FILTER_IB_CLOCK 30 在相应的框中输入当前日期和时间。
32 sc%3AImageButton%3ARID_SCDLG_SIMPLEREF%3ARB_ASSIGN 30 按照输入的关键字筛选修改的注释。
21 SVX_HID_REDLINING_FILTER_LB_AUTOR 3f 按照您从列表中选择的作者姓名筛选修改列表。
22 SVX_HID_REDLINING_FILTER_IB_CLOCK2 30 在相应的框中输入当前日期和时间。
21 SVX_HID_REDLINING_FILTER_DF_DATE2 33 按照指定的日期和时间筛选修改列表。
23 SVX_HID_REDLINING_FILTER_CB_COMMENT 30 按照输入的关键字筛选修改的注释。
21 SVX_HID_REDLINING_FILTER_ED_RANGE 30 选择您想用作过滤器的单元格区域。
21 SVX_HID_REDLINING_FILTER_TF_DATE2 33 按照指定的日期和时间筛选修改列表。
21 SVX_HID_REDLINING_FILTER_CB_RANGE 30 选择您想用作过滤器的单元格区域。
20 SVX_HID_REDLINING_FILTER_DF_DATE 33 按照指定的日期和时间筛选修改列表。
20 SVX_HID_REDLINING_FILTER_CB_DATE 33 按照指定的日期和时间筛选修改列表。
22 SVX_HID_REDLINING_FILTER_CB_ACTION 30 按照输入的关键字筛选修改的注释。
20 SVX_HID_REDLINING_FILTER_BTN_REF 30 选择您想用作过滤器的单元格区域。
23 SVX_HID_REDLINING_FILTER_ED_COMMENT 30 按照输入的关键字筛选修改的注释。
3a sc%3AImageButton%3ARID_SCDLG_HIGHLIGHT_CHANGES%3ARB_ASSIGN 30 按照输入的关键字筛选修改的注释。
22 SVX_HID_REDLINING_FILTER_LB_ACTION 30 按照输入的关键字筛选修改的注释。
20 SVX_HID_REDLINING_FILTER_LB_DATE 33 按照指定的日期和时间筛选修改列表。
21 SVX_HID_REDLINING_FILTER_CB_AUTOR 3f 按照您从列表中选择的作者姓名筛选修改列表。
20 SVX_HID_REDLINING_FILTER_TF_DATE 33 按照指定的日期和时间筛选修改列表。
15 .uno%3AMergeDocuments 90 将同一个文档的多个复本中所做的修改导入到原始文档中。对脚注、页眉、框架和字段所做的修改将被忽略。
17 .uno%3ACompareDocuments 27 比较当前文档和选定的文档。
1b .uno%3ABib%2FstandardFilter 3c 使用标准筛选提炼并结合自动筛选搜索选项。
20 EXTENSIONS_HID_BIB_BOOKTITLE_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
18 SVX_HID_GRID_TRAVEL_PREV 24 转到表格中的上一条记录。
1b EXTENSIONS_HID_BIB_ISBN_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
19 .uno%3ABib%2FInsertRecord 27 在当前表格中插入新的记录。
19 .uno%3ABib%2FDeleteRecord 15 删除当前记录。
1a EXTENSIONS_HID_BIB_URL_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1e EXTENSIONS_HID_BIB_EDITION_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1c EXTENSIONS_HID_BIB_TITLE_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
18 SVX_HID_GRID_TRAVEL_NEXT 24 转到表格中的下一条记录。
1c SVX_HID_GRID_TRAVEL_ABSOLUTE 3a 键入要显示的记录的编号，然后按 Enter 键。
1d EXTENSIONS_HID_BIB_NUMBER_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1e EXTENSIONS_HID_BIB_JOURNAL_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1d EXTENSIONS_HID_BIB_VOLUME_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
12 .uno%3ABib%2Fquery 152 键入要查找的信息，然后按 Enter 键。要修改查找的筛选选项，请缓慢单击自动筛选图标，然后选择其他数据字段。在查找中，您可以使用通配符 % 或 * 表示任意多个字符，使用 _ 或 ? 表示一个字符。要显示表格中的所有记录，请清除此框，然后按 Enter 键。
15 SVX_HID_FM_DELETEROWS 1b 选定记录即被删除。
1b EXTENSIONS_HID_BIB_YEAR_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1d EXTENSIONS_HID_BIB_EDITOR_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1c EXTENSIONS_HID_BIB_MONTH_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
23 EXTENSIONS_HID_BIB_HOWPUBLISHED_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
18 SVX_HID_GRID_TRAVEL_LAST 24 转到表格的最后一条记录。
1c EXTENSIONS_HID_BIB_PAGES_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
16 .uno%3ABib%2Fsdbsource 2a 选择用于文献数据库的数据源。
21 EXTENSIONS_HID_BIB_REPORTTYPE_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
14 .uno%3ABib%2FMapping 99 用于将列标题映射到其他数据源中的数据字段。要为文献目录定义其他数据源，请单击记录对象栏上的数据源按钮。
1c .uno%3ABibliographyComponent 45 在文献目录数据库中插入、删除、编辑和管理记录。
24 EXTENSIONS_HID_BIB_ORGANIZATIONS_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
17 .uno%3ABib%2FautoFilter 81 缓慢单击以选择要使用查找关键字框中输入的条目来查找的数据字段。您只能查找一个数据字段。
21 EXTENSIONS_HID_BIB_IDENTIFIER_POS 54 为记录输入缩写名称。缩写名称显示在记录列表的标识符列中。
19 .uno%3ABib%2FremoveFilter 3c 单击删除筛选图标来显示表格中的全部记录。
1e EXTENSIONS_HID_BIB_CHAPTER_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1d EXTENSIONS_HID_BIB_ANNOTE_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
19 SVX_HID_GRID_TRAVEL_FIRST 21 转到表格的第一条记录。
1e EXTENSIONS_HID_BIB_ADDRESS_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1d EXTENSIONS_HID_BIB_SCHOOL_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
20 EXTENSIONS_HID_BIB_PUBLISHER_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
19 EXTENSIONS_HID_BIB_DB_TBX 45 在文献目录数据库中插入、删除、编辑和管理记录。
24 EXTENSIONS_HID_BIB_AUTHORITYTYPE_POS 8f 选择您想创建的记录的类型。$[officename] 将根据您在这里选择的类型，在对应记录的类型列中插入一个编号。
1d EXTENSIONS_HID_BIB_SERIES_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
1d EXTENSIONS_HID_BIB_MAPPINGDLG 99 用于将列标题映射到其他数据源中的数据字段。要为文献目录定义其他数据源，请单击记录对象栏上的数据源按钮。
1b EXTENSIONS_HID_BIB_NOTE_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
17 SVX_HID_GRID_TRAVEL_NEW 24 在当前表格中插入新记录。
22 EXTENSIONS_HID_BIB_INSTITUTION_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
13 .uno%3ABib%2Fsource 63 列出当前数据库中的可用表格。单击列表中的某名称来显示此表格的记录。
1d EXTENSIONS_HID_BIB_AUTHOR_POS 69 为选定记录输入附加信息。如果需要，也可以在表格中相应的字段内输入信息。
27 SVX_METRICFIELD_RID_SVXDLG_ZOOM_ED_USER 48 输入显示文档所用的显示比例。在此框中输入百分比。
2d cui%3AMetricField%3ARID_SVXDLG_ZOOM%3AED_USER 48 输入显示文档所用的显示比例。在此框中输入百分比。
13 SVX_HID_MNU_ZOOM_75 26 以实际大小的 75% 显示文档。
e .uno%3AView100 1e 以实际大小显示文档。
b SID_VIEW200 27 以实际大小的两倍显示文档。
31 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_OPTIMAL 33 根据文档中文本的宽度调整显示大小。
b SID_VIEW050 27 以实际大小的一半显示文档。
13 SVX_HID_MNU_ZOOM_50 27 以实际大小的一半显示文档。
2d cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_100 1e 以实际大小显示文档。
1b SVX_HID_MNU_ZOOM_PAGE_WIDTH 51 显示文档页面的完整宽度。可能无法看到页面的上、下边缘。
1b SVX_HID_MNU_ZOOM_WHOLE_PAGE 21 在屏幕上显示整个页面。
14 SVX_HID_MNU_ZOOM_100 1e 以实际大小显示文档。
2a SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_SINGLE 4e 单个页面视图布局显示的页面彼此上下紧挨，但从不并排。
14 SVX_HID_MNU_ZOOM_200 27 以实际大小的两倍显示文档。
34 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_PAGE_WIDTH 51 显示文档页面的完整宽度。可能无法看到页面的上、下边缘。
34 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_WHOLE_PAGE 21 在屏幕上显示整个页面。
b .uno%3AZoom 2f 缩小或放大 %PRODUCTNAME 的屏幕显示。
25 SVX_CHECKBOX_RID_SVXDLG_ZOOM_CHK_BOOK 7b 在书本模式视图布局，您会看见如在开放工作簿中并排的两页。第一页是右页，页码为奇数。
2d SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_AUTOMATIC 6f 自动视图布局按并排方式显示页面，在显示比例允许的范围内显示尽可能多的页面。
14 SVX_HID_MNU_ZOOM_150 29 以实际大小的 1.5 倍显示文档。
18 SVX_HID_MNU_ZOOM_OPTIMAL 33 根据文档中文本的宽度调整显示大小。
2a SVX_METRICFIELD_RID_SVXDLG_ZOOM_ED_COLUMNS 54 在列视图布局，您可以看见给定列数的并排页面。请输入列数。
28 SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_USER 48 输入显示文档所用的显示比例。在此框中输入百分比。
2b SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_COLUMNS 54 在列视图布局，您可以看见给定列数的并排页面。请输入列数。
2e cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_USER 48 输入显示文档所用的显示比例。在此框中输入百分比。
19 .uno%3AFunctionBarVisible 1b 显示或隐藏标准栏。
1a .uno%3AShowImeStatusWindow 2e 显示或隐藏输入法 (IME) 状态窗口。
15 .uno%3AToolBarVisible 1b 显示或隐藏工具栏。
17 .uno%3AStatusBarVisible 33 显示或隐藏位于窗口下边缘的状态栏。
15 .uno%3ATaskBarVisible 33 显示或隐藏位于窗口下边缘的状态栏。
11 .uno%3AFullScreen 7b 在 Writer 或 Calc 中显示或隐藏菜单和工具栏。要退出全屏模式，请单击打开/关闭全屏幕按钮。
1a SFX2_HID_FULLSCREENTOOLBOX 7d 在 Writer 和 Calc 中，您也可以通过使用快捷键 CommandCtrl+Shift+J 在正常模式和全屏模式之间切换。
13 .uno%3AColorControl 72 显示或隐藏颜色栏。要修改显示的颜色表，请选择格式 - 区域，然后单击颜色选项卡。
18 SVX_HID_COLOR_CTL_COLORS 141 单击要使用的颜色。要修改当前文件中对象的填充颜色，请选择该对象，然后单击一种颜色。要修改选定对象的线条颜色，请在一种颜色上单击鼠标右键。要修改文字对象中文字的颜色，请双击文字对象，选择所需的文字，然后单击一种颜色。
18 .uno%3AAvailableToolbars 2d 打开子菜单可显示和隐藏工具栏。
18 .cmd%3ARestoreVisibility 96 选择视图 - 工具栏 - 重设可以将工具栏重设为默认的上下文相关行为。此时，某些工具栏将根据上下文自动显示。
22 .uno%3ADeleteAllAnnotationByAuthor 30 删除当前文档中此作者的所有批注。
17 .uno%3ADeleteAnnotation 15 删除当前批注。
11 .uno%3ADeleteNote 15 删除当前批注。
17 .uno%3AInsertAnnotation 15 插入一个批注。
16 .uno%3AShowAnnotations 4a 使用视图 - 批注显示或隐藏所有批注（Calc 中不可用）。
1a .uno%3ADeleteAllAnnotation 27 删除当前文档中的所有批注。
15 .uno%3ADeleteAllNotes 27 删除当前文档中的所有批注。
13 .uno%3ADeleteAuthor 30 删除当前文档中此作者的所有批注。
b .uno%3AScan 27 在文档中插入一个扫描图像。
12 .uno%3ATwainSelect 1e 选择要使用的扫描仪。
14 .uno%3ATwainTransfer 60 扫描图像，然后将扫描结果插入文档。扫描对话框由扫描仪制造商提供。
13 .uno%3AInsertSymbol 33 插入包含在已安装字体中的特殊字符。
d .uno%3ABullet 33 插入包含在已安装字体中的特殊字符。
2e cui%3AListBox%3ARID_SVXDLG_CHARMAP%3ALB_SUBSET 27 选择当前字体的 Unicode 类别。
32 cui%3APushButton%3ARID_SVXDLG_CHARMAP%3ABTN_DELETE 30 清除当前选定的要插入的特殊字符。
1b CUI_HID_CHARMAP_CTL_SHOWSET 36 单击要插入的特殊字符，然后单击确定。
2c cui%3AListBox%3ARID_SVXDLG_CHARMAP%3ALB_FONT 3f 选择一种字体，以显示与其相关联的特殊字符。
16 .uno%3AInsertHyperlink 83 显示或隐藏超链接地址栏，您可以在此栏中插入或编辑 URL，或者使用关键字在 Internet 上进行搜索。
1a SVT_HID_FILEDLG_PREVIEW_CB 24 显示选定图形文件的预览。
14 .uno%3AInsertGraphic 27 将图片插入到当前的文档中。
17 SVT_HID_FILEDLG_LINK_CB 2d 将选定的图形文件作为链接插入。
15 HID_IMPGRF_CB_PREVIEW 24 显示选定图形文件的预览。
1f SVT_HID_FILEOPEN_IMAGE_TEMPLATE 1e 选择图形的框架样式。
1d SVX_HID_OFA_HYPERLINK_URL_TXT 2d 将选定的图形文件作为链接插入。
11 .uno%3AObjectMenu 5a 将某对象插入文档。对于影片和声音，使用插入 - 影片和声音替代。
13 .uno%3AInsertObject 5d 将 OLE 对象插入当前文档。OLE 对象将以链接或嵌入式对象的形式插入。
13 .uno%3AInsertPlugin 27 在当前文档中插入一个插件。
12 .uno%3AInsertSound 27 在当前文档中插入声音文件。
12 .uno%3AInsertVideo 27 在当前文档中插入视频文件。
1b .uno%3AInsertObjectStarMath 27 将公式插入到当前的文档中。
11 .uno%3AInsertMath 27 将公式插入到当前的文档中。
20 .uno%3AInsertObjectFloatingFrame 6f 在当前文档中插入浮动框架。浮动框架用在 HTML 文档中用来显示另一个文档的内容。
1c .uno%3AViewDataSourceBrowser 56 列出在 %PRODUCTNAME 中注册的数据库，并允许您管理数据库的内容。
11 .uno%3ADataImport 56 列出在 %PRODUCTNAME 中注册的数据库，并允许您管理数据库的内容。
12 .uno%3AGraphicMenu 24 选择要插入的图片的来源。
1d .uno%3AStandardTextAttributes 3f 从所选内容中删除直接格式和字符样式的格式。
11 .uno%3ASetDefault 3f 从所选内容中删除直接格式和字符样式的格式。
16 .uno%3AResetAttributes 3f 从所选内容中删除直接格式和字符样式的格式。
11 .uno%3AFontDialog 2d 修改选定字符的字体和字体格式。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_LANG 81 设置对选定文本或键入的文本进行拼写检查时使用的语言。可用的语言模块前会有一个复选标记。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_STYLE 1b 选择要应用的格式。
36 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_STYLE 1b 选择要应用的格式。
36 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_STYLE 1b 选择要应用的格式。
33 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_LANG 81 设置对选定文本或键入的文本进行拼写检查时使用的语言。可用的语言模块前会有一个复选标记。
36 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_SIZE 66 输入或选择要采用的字体大小。对于可缩放大小的字体，还可以输入小数值。
3c cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_STYLE_NOCJK 1b 选择要应用的格式。
3b cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_NAME_NOCJK 51 输入要使用的已安装字体名称，或者从列表中选择一种字体。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_NAME 51 输入要使用的已安装字体名称，或者从列表中选择一种字体。
3a cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_LANG_NOCJK 81 设置对选定文本或键入的文本进行拼写检查时使用的语言。可用的语言模块前会有一个复选标记。
3c cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_SIZE_NOCJK 66 输入或选择要采用的字体大小。对于可缩放大小的字体，还可以输入小数值。
19 CUI_HID_SVXPAGE_CHAR_NAME 24 指定要应用的格式和字体。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_LANG 81 设置对选定文本或键入的文本进行拼写检查时使用的语言。可用的语言模块前会有一个复选标记。
36 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_SIZE 66 输入或选择要采用的字体大小。对于可缩放大小的字体，还可以输入小数值。
34 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_NAME 51 输入要使用的已安装字体名称，或者从列表中选择一种字体。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_NAME 51 输入要使用的已安装字体名称，或者从列表中选择一种字体。
35 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_SIZE 66 输入或选择要采用的字体大小。对于可缩放大小的字体，还可以输入小数值。
31 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_COLOR2 81 为选定文本设置颜色。如果选择自动，则文本颜色在浅色背景下为黑色，而在深色背景下为白色。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_EMPHASIS 3f 选择要在整个选定文本上方或下方显示的字符。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_STRIKEOUT 27 选择选定文本的删除线样式。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_POSITION 24 指定显示强调标记的位置。
3d cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_UNDERLINE_COLOR 1b 选择下划线的颜色。
39 cui%3ATriStateBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_OUTLINE 51 显示选定字符的大纲视图。此效果并不是对所有字体都有效。
10 .uno%3AFontColor ba 单击为所选字符应用当前字体颜色。您也可以单击此处，然后拖动一定区域来更改文本颜色。单击图标旁的箭头可打开“字体颜色”工具栏。
38 cui%3ATriStateBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_SHADOW 48 为选定字符添加阴影，阴影投射在字符的下侧和右侧。
c .uno%3AColor ba 单击为所选字符应用当前字体颜色。您也可以单击此处，然后拖动一定区域来更改文本颜色。单击图标旁的箭头可打开“字体颜色”工具栏。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_OVERLINE 5a 选择您要应用的上划线样式。要只为字应用上划线，请选中逐字框。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_EFFECTS2 a5 选择选定文本要应用的浮雕效果。凸出的浮雕效果使字符看起来像是高出页面。凹进的浮雕效果使字符看起来像是陷入页面。
1c CUI_HID_SVXPAGE_CHAR_EFFECTS 21 指定要使用的字体效果。
3e cui%3ACheckBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_INDIVIDUALWORDS 33 仅对字词应用选定的效果，忽略空格。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_FONTCOLOR 81 为选定文本设置颜色。如果选择自动，则文本颜色在浅色背景下为黑色，而在深色背景下为白色。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_UNDERLINE 5a 选择要应用的下划线样式。如果仅对字词加下划线，请选中逐字框。
f .uno%3AOverline 93 为所选文本设置上划线或删除所选文本的上划线。如果光标没有定位在字中，新输入的文本就会被设置上划线。
3c cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_OVERLINE_COLOR 1b 为上划线选择颜色。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_RELIEF a5 选择选定文本要应用的浮雕效果。凸出的浮雕效果使字符看起来像是高出页面。凹进的浮雕效果使字符看起来像是陷入页面。
31 cui%3AEdit%3ARID_SVXPAGE_NUMBERFORMAT%3AED_FORMAT 54 显示选定格式的数字格式码。您也可以输入一个自定义的格式。
3b cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ACB_SOURCEFORMAT 39 使用含有图表数据的单元格中的数字格式。
3d cui%3ANumericField%3ARID_SVXPAGE_NUMBERFORMAT%3AED_LEADZEROES 39 输入小数点前面最多可以显示的零的位数。
38 cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ABTN_THOUSAND 48 插入千位分隔符。所用分隔符的类型取决于语言设置。
1e CUI_HID_NUMBERFORMAT_LB_FORMAT 27 选择选定单元格的显示内容。
14 CUI_HID_NUMBERFORMAT 2a 为选定的单元格指定格式选项。
32 cui%3AEdit%3ARID_SVXPAGE_NUMBERFORMAT%3AED_COMMENT 3f 为选定的数字格式输入注释，然后在框外单击。
1d CUI_HID_NUMBERFORMAT_TBI_INFO 27 向选定的数字格式添加注释。
36 cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ABTN_NEGRED 2a 将负数的字体颜色修改为红色。
3b cui%3ANumericField%3ARID_SVXPAGE_NUMBERFORMAT%3AED_DECIMALS 24 输入要显示的小数点位数。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_LANGUAGE cb 为选定的字段指定语言设置。如果将语言设置为自动，$[officename] 将自动应用与系统默认语言相关联的数字格式。请选择任意语言以确定所选字段的设置。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_CATEGORY 4b 从列表中选择一个类别，然后在格式框中选择格式样式。
1f CUI_HID_NUMBERFORMAT_TBI_REMOVE 1e 删除选定的数字格式。
1c CUI_HID_NUMBERFORMAT_TBI_ADD 3c 将输入的数字格式码加入到自定义的类别中。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_CURRENCY 63 选择一种货币，然后滚动到格式列表的顶部查看与该货币对应的格式选项。
1e .uno%3ATableNumberFormatDialog 2a 为选定的单元格指定格式选项。
19 .uno%3AInsertHyperlinkDlg 33 指定新的超链接或编辑选定的超链接。
f SW_HID_CHAR_URL 33 指定新的超链接或编辑选定的超链接。
27 sw%3AComboBox%3ATP_CHAR_URL%3ALB_TARGET 66 输入用于打开超链接文件的框架的名称，或者从列表中选择一个预设的框架。
28 sw%3APushButton%3ATP_CHAR_URL%3APB_EVENT 2a 指定单击超链接时触发的事件。
21 sw%3AEdit%3ATP_CHAR_URL%3AED_TEXT 21 输入超链接的显示文字。
2b sw%3AListBox%3ATP_CHAR_URL%3ALB_NOT_VISITED bd 从列表中为未访问过的链接选择格式样式。要向列表中加入样式或修改列表中的样式，请关闭此对话框并单击格式工具栏上的样式和格式图标。
20 sw%3AEdit%3ATP_CHAR_URL%3AED_URL 34 输入单击超链接时要打开的文件的 URL。
21 sw%3AEdit%3ATP_CHAR_URL%3AED_NAME 1b 输入超链接的名称。
26 sw%3APushButton%3ATP_CHAR_URL%3APB_URL 31 找到要链接的文件，然后单击 打开。
27 sw%3AListBox%3ATP_CHAR_URL%3ALB_VISITED c0 从列表中为已经访问过的链接选择格式样式。要向列表中加入样式或修改列表中的样式，请关闭此对话框并单击格式工具栏上的样式和格式图标。
3e cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AMF_SCALE_WIDTH 5d 输入字体宽度的百分比，将根据此数值来水平拉伸或压缩选定的文字。
3b cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_FONTSIZE 4e 输入一个数值，将根据此数值来减小选定文字的字体大小。
3b cui%3ACheckBox%3ARID_SVXPAGE_CHAR_POSITION%3ACB_PAIRKERNING 30 自动调节特定字母组合的字符间距。
3a cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_HIGHLOW 66 输入选定文字相对于基线的上升或下降值。百分之百表示与字体的高度相等。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_POSITION%3ALB_KERNING2 87 指定选定文字中各个字符之间的间距。要拉伸或压缩间距，请在按照框中输入文字被拉伸或压缩的值。
37 cui%3ACheckBox%3ARID_SVXPAGE_CHAR_POSITION%3ACB_HIGHLOW 3f 自动设置选定文字相对于基线的上升或下降值。
39 cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_LOWPOS 48 减小选定文字的字体大小，并将文字降低到基线以下。
3a cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_HIGHPOS 48 减小选定文字的字体大小，并将文字上升到基线以上。
12 .uno%3ASuperScript 48 减小选定文字的字体大小，并将文字上升到基线以上。
3c cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_NORMALPOS 1e 删除上标或下标格式。
1d CUI_HID_SVXPAGE_CHAR_POSITION 39 指定字符的位置、显示比例、旋转和间距。
3b cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_KERNING2 54 输入一个数值，将根据此数值扩大或缩小选定文字的字符间距。
10 .uno%3ASubScript 48 减小选定文字的字体大小，并将文字降低到基线以下。
39 cui%3AListBox%3ARID_SVXPAGE_CHAR_TWOLINES%3AED_ENDBRACKET 63 选择用于定义双行区域结尾的字符。要选择自定义字符，请选择其他字符。
3b cui%3AListBox%3ARID_SVXPAGE_CHAR_TWOLINES%3AED_STARTBRACKET 63 选择用于定义双行区域开头的字符。要选择自定义字符，请选择其他字符。
38 cui%3ACheckBox%3ARID_SVXPAGE_CHAR_TWOLINES%3ACB_TWOLINES 3f 允许您在当前文档的选定区域中写入双行文本。
3c cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_FORBIDDEN 63 禁止列表中的字符用于行首或行末。这些字符将被重置到上一行或下一行。
3f cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_SCRIPT_SPACE 39 在亚洲文字与西文及混合文字间插入空格。
3c cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_HANG_PUNC 6c 禁止逗号和句号导致换行。即使这些字符已位于页边距中，也仍将被保留在行末。
10 .uno%3AEditStyle 36 修改当前段落的格式，例如缩进和对齐。
16 .uno%3AParagraphDialog 36 修改当前段落的格式，例如缩进和对齐。
37 cui%3AListBox%3ARID_SVXPAGE_STD_PARAGRAPH%3ALB_LINEDIST 27 指定段落中文字之间的行距。
42 cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LINEDISTPERCENT 15 输入行距的值。
34 cui%3ACheckBox%3ARID_SVXPAGE_STD_PARAGRAPH%3ACB_AUTO 30 输入选定段落上方要留出的间距值。
3d cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LEFTINDENT 114 输入段落与页边距之间的间距。如果希望段落延伸至页边距内，请输入负数。对于文字方向从左向右的语言，段落的左边将相对于左边距缩进。对于文字方向从右向左的语言，段落的右边将相对于右边距缩进。
3d cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_BOTTOMDIST 2d 输入选定段落下方要留出的间距。
41 cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LINEDISTMETRIC 15 输入行距的值。
3e cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_RIGHTINDENT 11a 输入段落与右页边距之间的间距。如果希望段落延伸至右页边距内，请输入负数。对于文字方向从左向右的语言，段落的右边将相对于右边距缩进。对于文字方向从右向左的语言，段落的左边将相对于左边距缩进。
1c CUI_HID_FORMAT_PARAGRAPH_STD 2d 设置段落的缩进选项和间距选项。
3a cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_TOPDIST 30 输入选定段落上方要留出的间距值。
3e cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_FLINEINDENT 120 按照您输入的缩进量缩进段落的第一行。要创建一个悬挂缩进，请在“文字之前”输入一个正值并在“第一行”输入一个负值。要在使用编号或项目符号的段落中缩进第一行，请选择“格式 - 项目符号和编号 - 位置”。
3e cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_OTHER 45 您可以指定一个字符，用来填充制表位左侧的空白。
3c cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_LEFT 45 将文字的左边缘与制表位对齐，并将文字向右扩展。
11 CUI_HID_TABULATOR 27 设置制表位在段落中的位置。
3f cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_POINTS 27 用点填充制表位左侧的空白。
43 cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_UNDERSCORE 30 绘制线条以填充制表位左侧的空白。
3d cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_RIGHT 51 将文字的右边缘与制表位对齐，并将文字向制表位左侧扩展。
3e cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_CENTER 27 将文字的中心与制表位对齐。
41 cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_DASHLINE 2d 用短划线填充制表位左侧的空白。
32 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_NEW 30 在当前段落中加入您定义的制表位。
3f cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_DECIMAL 57 将数字的小数点与制表位的中心对齐，文字与制表符的左侧对齐。
35 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_DELALL 63 删除在位置下定义的全部制表位。设置固定间隔的左制表位为默认制表位。
36 cui%3AEdit%3ARID_SVXPAGE_TABULATOR%3AED_FILLCHAR_OTHER 45 您可以指定一个字符，用来填充制表位左侧的空白。
3b cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_NO 4b 不插入填充字符，或者删除制表位左侧现有的填充字符。
33 cui%3AMetricBox%3ARID_SVXPAGE_TABULATOR%3AED_TABPOS f8 选择制表位的类型，输入新的度量单位，然后单击新建。如果需要，您还可以指定用于制表符的度量单位（cm 表示厘米，" 表示英寸）。您设置的第一个制表符左侧的现有制表符将被删除。
37 cui%3AEdit%3ARID_SVXPAGE_TABULATOR%3AED_TABTYPE_DECCHAR 3f 输入想要在小数制表时用作小数分隔符的字符。
31 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_LINESTYLE 51 单击要应用的边框样式，此样式将应用于预览中选定的边框。
1a CUI_HID_BORDER_CTL_PRESETS 2a 选择要应用的预定义边框样式。
36 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AED_SHADOWSIZE 18 输入阴影的宽度。
1a CUI_HID_BORDER_CTL_SHADOWS 27 为选定的边框选择阴影样式。
2d cui%3ACheckBox%3ARID_SVXPAGE_BORDER%3ACB_SYNC 4e 输入新的间距后，四个边框应用相同的到内容的间距设置。
33 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_SHADOWCOLOR 18 选择阴影的颜色。
2f cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_TOP 30 输入上边框与选定内容之间的间距。
30 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_LEFT 30 输入左边框与选定内容之间的间距。
31 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_RIGHT 30 输入右边框与选定内容之间的间距。
32 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_BOTTOM 30 输入下边框与选定内容之间的间距。
31 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_LINECOLOR 24 为选定边框选择线条颜色。
e CUI_HID_BORDER 3b 为 Writer 或 Calc 中选定的对象设置边框选项。
36 cui%3APushButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_BROWSE 3c 找到要作为背景的图形文件，然后单击打开。
35 cui%3ACheckBox%3ARID_SVXPAGE_BACKGROUND%3ABTN_PREVIEW 27 显示或隐藏选定图形的预览。
39 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_POSITION 45 选择此选项，然后在位置网格中的某个位置处单击。
22 CUI_HID_BACKGROUND_CTL_BGDCOLORSET 4e 单击要作为背景的颜色。要删除背景颜色，请单击不填充。
35 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_TILE 39 重复图形，使其覆盖选定对象的整个背景。
1f CUI_HID_BACKGROUND_CTL_POSITION 45 选择此选项，然后在位置网格中的某个位置处单击。
32 cui%3ACheckBox%3ARID_SVXPAGE_BACKGROUND%3ABTN_LINK 30 在当前文件中链接或嵌入图形文件。
33 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_TBL_BOX 30 在当前文件中链接或嵌入图形文件。
34 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_PARA_BOX 30 在当前文件中链接或嵌入图形文件。
35 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_AREA 33 拉伸图形以填充选定对象的整个背景。
39 cui%3AMetricField%3ARID_SVXPAGE_BACKGROUND%3AMF_COL_TRANS 30 在当前文件中链接或嵌入图形文件。
18 SVX_HID_POPUP_COLOR_CTRL 78 单击颜色。单击“无填充”以删除背景或突出显示的颜色。单击“自动”以重置字体颜色。
34 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_SELECTOR 21 选择要应用的背景类型。
12 CUI_HID_BACKGROUND 1e 设置背景颜色或图形。
3f cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_LEFTALIGN 21 将段落与左页边距对齐。
42 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_JUSTIFYALIGN 24 将段落与左右页边距对齐。
1e CUI_HID_FORMAT_PARAGRAPH_ALIGN 30 相对于页边距设置段落的对齐方式。
38 cui%3ACheckBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ACB_EXPAND 7c 为使用复杂文字版式 (CTL) 的段落指定文字方向。只有启用了复杂文字版式时，此功能才可用。
41 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_CENTERALIGN 24 将页面中的段落内容居中。
3e cui%3AListBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ALB_TEXTDIRECTION 7c 为使用复杂文字版式 (CTL) 的段落指定文字方向。只有启用了复杂文字版式时，此功能才可用。
40 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_RIGHTALIGN 21 将段落与右页边距对齐。
2b sw%3AListBox%3ATP_COLUMN%3ALB_TEXTDIRECTION 7c 为使用复杂文字版式 (CTL) 的段落指定文字方向。只有启用了复杂文字版式时，此功能才可用。
39 cui%3AListBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ALB_LASTLINE 7c 为使用复杂文字版式 (CTL) 的段落指定文字方向。只有启用了复杂文字版式时，此功能才可用。
31 sw%3AListBox%3ATP_FORMAT_TABLE%3ALB_TEXTDIRECTION 7c 为使用复杂文字版式 (CTL) 的段落指定文字方向。只有启用了复杂文字版式时，此功能才可用。
32 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_RIGHT 11a 如果选择了保持显示比例选项，输入正值将剪切图形的右边缘，输入负值将在图形的右侧加入空白区域。如果选择了保持图像大小选项，输入正值将增加图形的水平显示比例，输入负值将缩小图形的水平显示比例。
36 cui%3ARadioButton%3ARID_SVXPAGE_GRFCROP%3ARB_SIZECONST e7 裁剪时保持图形的原件大小，因此只会修改图形的显示比例。要缩小图形的显示比例，请选择此选项并在裁剪框中输入负值。要增大图形的显示比例，请在裁剪框中输入正值。
36 cui%3ARadioButton%3ARID_SVXPAGE_GRFCROP%3ARB_ZOOMCONST 51 裁剪时保持图形原有的显示比例，因此只会修改图形的大小。
33 cui%3APushButton%3ARID_SVXPAGE_GRFCROP%3APB_ORGSIZE 27 将选定图形恢复为原始大小。
37 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_HEIGHTZOOM 33 输入选定图形的高度，用百分比表示。
36 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_WIDTHZOOM 33 输入选定图形的宽度，用百分比表示。
31 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_LEFT 11a 如果选择了保持显示比例选项，输入正值将剪切图形的左边缘，输入负值将在图形的左侧加入空白区域。如果选择了保持图像大小选项，输入正值将增加图形的水平显示比例，输入负值将缩小图形的水平显示比例。
33 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_BOTTOM 11a 如果选择了保持显示比例选项，输入正值将剪切图形的下边缘，输入负值将在图形的下方加入空白区域。如果选择了保持图形大小选项，输入正值将增加图形的垂直显示比例，输入负值将缩小图形的垂直显示比例。
33 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_HEIGHT 1e 输入所选图形的高度。
32 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_WIDTH 1e 输入所选图形的宽度。
30 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_TOP 11a 如果选择了保持显示比例选项，输入正值将剪切图形的上边缘，输入负值将在图形的上方加入空白区域。如果选择了保持图形大小选项，输入正值将增加图形的垂直显示比例，输入负值将缩小图形的垂直显示比例。
2b sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_BASE 6c 选择一个现有样式作为建立新样式的基础，或者不做选择，以定义自定义的样式。
2c sfx2%3ACheckBox%3ATP_MANAGE_STYLES%3ACB_AUTO 9c 当您在文档中使用某个样式为段落应用了直接格式时，请更新该样式。使用了该样式的所有段落的格式会自动被更新。
31 sfx2%3AMultiLineEdit%3ATP_MANAGE_STYLES%3AED_DESC 30 描述当前样式中所使用的相关格式。
16 SFX2_HID_MANAGE_STYLES 24 设置用于选定样式的选项。
2b sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_NEXT eb 选择一个现有的样式，对当前样式之后的文档内容采用该样式。对于段落样式，下一个样式将用于您按 Enter 键之后建立的段落。对于页面样式，下一个样式将用于建立的新页面。
28 sfx2%3AEdit%3ATP_MANAGE_STYLES%3AED_NAME 93 显示选定样式的名称。如果要创建或修改自定义样式，请为样式输入一个名称。您不能修改预定义样式的名称。
2d sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_REGION 6f 显示当前样式的类别。如果要创建或修改新样式，请从列表中选择“自定义样式”。
2b cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_VERT 2a 垂直居中打印页面上的单元格。
2e cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_REGISTER 9f 选择“段落样式”（将用于选定页面样式中排列文本时的参考）。参考样式中指定的字体高度设置垂直页面网格的间距。
2f cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_REGISTER 45 将选定“页面样式”中的文本与垂直页面网格对齐。
34 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_TOP_MARGIN 36 输入页面上边缘与文档文本之间的间距。
32 cui%3ARadioButton%3ARID_SVXPAGE_PAGE%3ARB_PORTRAIT 30 在纸张上纵向显示和打印当前文档。
2c cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_ADAPT 63 调整绘图对象的大小使其适应所选的纸张格式。绘图对象的排序保持不变。
36 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_RIGHT_MARGIN 99 输入页面右边缘与文档文字之间的间距。如果使用的是镜像页面版式，请输入文字外边缘与页面外边缘之间的间距。
35 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_LEFT_MARGIN 99 输入页面左边缘与文档文字之间的间距。如果使用的是镜像页面版式，请输入文字内边缘与页面内边缘之间的间距。
36 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_PAPER_HEIGHT 63 显示所选页面格式的高度。要定义自定义格式，请在此处输入一个高度值。
2c cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_LAYOUT 4b 指定当前样式是否显示奇数页、偶数页或是两者都显示。
2b cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_HORZ 2a 水平居中打印页面上的单元格。
2f cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_TEXT_FLOW 2a 选择文档中要使用的文本方向。
33 cui%3ARadioButton%3ARID_SVXPAGE_PAGE%3ARB_LANDSCAPE 30 在纸张上横向显示和打印当前文档。
13 CUI_HID_FORMAT_PAGE 4b 用于定义单页或多页文档的页面版式、编号和纸张格式。
37 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_BOTTOM_MARGIN 36 输入页面下边缘与文档文本之间的间距。
35 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_PAPER_WIDTH 5a 显示所选纸张格式的宽度。要自行定义格式，请在此处输入宽度值。
33 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_NUMBER_FORMAT 48 请选择您想要在当前页面样式上使用的页面编号格式。
30 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_PAPER_TRAY cf 选择打印机的纸张来源。如果需要，可以为不同的页面样式指定不同的纸张来源。例如，为“第一页”样式指定不同的纸盘，并在纸盘中装入公司的信头纸。
30 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_PAPER_SIZE 72 选择预定义的纸张大小，或者通过在高度和宽度框中输入纸张尺寸来创建自定义格式。
32 svx%3APushButton%3ARID_SVXPAGE_HEADER%3ABTN_EXTRAS 36 定义页眉的边框、背景颜色或背景图案。
33 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_RMARGIN 39 输入页面右边缘和页眉右边缘之间的间距。
2f svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_SHARED 2d 奇数页和偶数页共享相同的内容。
15 SVX_HID_FORMAT_HEADER 6c 在当前页面样式中添加页眉。页眉是指页面顶部区域，可在此处添加文本或图形。
33 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_LMARGIN 39 输入页面左边缘与页眉左边缘之间的间距。
33 svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_DYNSPACING 57 忽略间距设置，并允许页眉延伸到页眉和文档文字之间的区域中。
15 SC_HID_SC_HEADER_EDIT 1e 添加或编辑页眉文本。
32 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_HEIGHT 1b 输入所需页眉高度。
30 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_DIST 3f 输入页眉下边缘和文档文本上边缘之间的间距。
2f svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_TURNON 27 在当前页面样式中添加页眉。
33 svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_HEIGHT_DYN 33 自动调整页眉高度以适应输入的内容。
32 svx%3APushButton%3ARID_SVXPAGE_FOOTER%3ABTN_EXTRAS 36 定义页脚的边框、背景颜色或背景模式。
32 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_HEIGHT 1e 输入所需的页脚高度。
2f svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_SHARED 2d 奇数页和偶数页共享相同的内容。
2f svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_TURNON 27 在当前页面样式中添加页脚。
15 SVX_HID_FORMAT_FOOTER 75 在当前页面样式中添加页脚。页脚是指页面底部边缘区域，可以在此处添加文本或图形。
33 svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_DYNSPACING 54 覆盖间距设置，并允许页脚延伸到页脚和文档文本之间的区域。
33 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_RMARGIN 39 输入页面右边缘与页脚右边缘之间的间距。
15 SC_HID_SC_FOOTER_EDIT 1e 添加或编辑页脚文本。
33 svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_HEIGHT_DYN 36 将页脚自动调整为适合输入内容的高度。
30 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_DIST 3f 输入文档文本下边缘与页脚上边缘之间的间距。
33 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_LMARGIN 39 输入页面左边缘与页脚左边缘之间的间距。
1b .uno%3AChangeCaseToKatakana 39 将选定的亚洲语言字符修改为片假名字符。
1c .uno%3AChangeCaseToHalfWidth 36 将选定的亚洲语言字符修改为半角字符。
1a SID_TRANSLITERATE_SENTENCE 3c 将选定西文字符的首个字符修改为大写字符。
18 .uno%3ATransliterateMenu 7b 更改选定字符的字符大小写。如果光标在单词内，但无选定的文本，则该单词为选定的字符。
18 .uno%3AChangeCaseToUpper 30 将选定的西文字符修改为大写字符。
18 .uno%3AChangeCaseToLower 30 将选定的西文字符修改为小写字母。
1f .uno%3AChangeCaseToSentenceCase 3c 将选定西文字符的首个字符修改为大写字符。
1b .uno%3AChangeCaseToHiragana 39 将选定的亚洲语言字符修改为平假名字符。
1c .uno%3AChangeCaseToFullWidth 36 将选定的亚洲语言字符修改为全角字符。
1c .uno%3AChangeCaseToTitleCase 48 将选定西文字符每个单词的首个字符更改为大写字符。
1d .uno%3AChangeCaseToToggleCase 2d 切换所有选定西文字符的大小写。
18 SID_TRANSLITERATE_TOGGLE 2d 切换所有选定西文字符的大小写。
1c SID_TRANSLITERATE_CAPITALIZE 48 将选定西文字符每个单词的首个字符更改为大写字符。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_3 75 显示在当前文件中选定的主文字。如果需要，您可以在此处输入新的文本来修改主文字。
2f svx%3APushButton%3ARID_SVXDLG_RUBY%3APB_STYLIST 57 打开“样式和格式”窗口，可以在其中选择拼音文字的字符样式。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_4 30 输入要用作主文字发音指导的文本。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_2 30 输入要用作主文字发音指导的文本。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_2 75 显示在当前文件中选定的主文字。如果需要，您可以在此处输入新的文本来修改主文字。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_1 75 显示在当前文件中选定的主文字。如果需要，您可以在此处输入新的文本来修改主文字。
2d svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_POSITION 27 选择要加入拼音文本的位置。
2b svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_ADJUST 2a 选择拼音文本的水平对齐方式。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_1 30 输入要用作主文字发音指导的文本。
11 .uno%3ARubyDialog 45 允许您在亚洲文字上方添加拼注，以用作发音指导。
2f svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_CHAR_STYLE 24 选择拼音文本的字符样式。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_3 30 输入要用作主文字发音指导的文本。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_4 75 显示在当前文件中选定的主文字。如果需要，您可以在此处输入新的文本来修改主文字。
15 .uno%3AAlignFrameMenu 21 将选定的对象相互对齐。
16 .uno%3AObjectAlignLeft 87 对齐选定对象的左边缘。如果在 Draw 或 Impress 中仅选定了一个对象，对象的左边缘将与左页边距对齐。
10 .uno%3AAlignLeft 87 对齐选定对象的左边缘。如果在 Draw 或 Impress 中仅选定了一个对象，对象的左边缘将与左页边距对齐。
12 .uno%3AAlignCenter 96 水平居中对齐选定的对象。如果在 Draw 或 Impress 中只选择了一个对象，则将该对象的中心与页面的水平中心对齐。
1c .uno%3AAlignHorizontalCenter 96 水平居中对齐选定的对象。如果在 Draw 或 Impress 中只选择了一个对象，则将该对象的中心与页面的水平中心对齐。
11 .uno%3AAlignRight 8d 对齐选定对象的右边缘。如果在 Impress 或 Draw 中只选定了一个对象，则对象的右边缘将与右侧页边距对齐。
17 .uno%3AObjectAlignRight 8d 对齐选定对象的右边缘。如果在 Impress 或 Draw 中只选定了一个对象，则对象的右边缘将与右侧页边距对齐。
f .uno%3AAlignTop 93 垂直对齐选定对象的上边缘。如果在 Draw 或 Impress 中只选定了一个对象，则对象的上边缘将与上方页边距对齐。
e .uno%3AAlignUp 93 垂直对齐选定对象的上边缘。如果在 Draw 或 Impress 中只选定了一个对象，则对象的上边缘将与上方页边距对齐。
1a .uno%3AAlignVerticalCenter 96 垂直居中对齐选定的对象。如果在 Draw 或 Impress 中只选择了一个对象，则将该对象的中心与页面的垂直中心对齐。
12 .uno%3AAlignMiddle 96 垂直居中对齐选定的对象。如果在 Draw 或 Impress 中只选择了一个对象，则将该对象的中心与页面的垂直中心对齐。
12 .uno%3AAlignBottom 93 垂直对齐选定对象的下边缘。如果在 Draw 或 Impress 中只选定了一个对象，则对象的下边缘将与下方页边距对齐。
10 .uno%3AAlignDown 93 垂直对齐选定对象的下边缘。如果在 Draw 或 Impress 中只选定了一个对象，则对象的下边缘将与下方页边距对齐。
f .uno%3ALeftPara 2a 将选定的段落与左页边距对齐。
10 .uno%3ARightPara 2a 将选定的段落与右页边距对齐。
11 .uno%3ACenterPara 2d 使选定的段落在页面上居中对齐。
12 .uno%3AJustifyPara a2 将选定的段落与左页边距和右页边距对齐。如果需要，还可以通过选择格式 - 段落 - 对齐来指定段落最后一行的对齐选项。
11 .uno%3AMergeCells 42 将选定的表格单元格的内容合并到一个单元格中。
10 .uno%3ASplitCell 54 按照您输入的单元格数，水平或垂直地拆分一个或一组单元格。
12 .uno%3ACellVertTop 33 将单元格内容与单元格的上边缘对齐。
15 .uno%3ACellVertCenter 3f 单元格内容居中对齐在单元格顶端和底端之间。
15 .uno%3ACellVertBottom 33 将单元格内容与单元格的下边缘对齐。
b .uno%3ABold ae 将选定的文本设为粗体。如果光标位于词中，则整个词将变为粗体。如果选定的文字或光标所在的词已经是粗体，则取消粗体格式。
d .uno%3AItalic b7 将选定的文本变为斜体。如果光标位于某个字词中，则整个字词将变为斜体。如果选择的文字或光标所在的字词已经是斜体，则取消斜体。
16 .uno%3AUnderlineDouble 27 为选定的文本加上双下划线。
10 .uno%3AUnderline 2d 为选定的文本添加或删除下划线。
10 .uno%3AStrikeout 6c 为选定的文本加上删除线。如果光标位于某个字词中，则为整个字词加上删除线。
f .uno%3AShadowed 63 为选定文字添加阴影。如果光标位于某个字词中，则为整个字词添加阴影。
15 .uno%3ADistributeRows 4e 调整选定行的高度，使其与选定范围内最高行的高度一致。
11 .uno%3ASpacePara1 39 对当前段落应用单行行距。这是默认设置。
12 .uno%3ASpacePara15 32 将当前段落的行距设置为 1.5 倍行距。
11 .uno%3ASpacePara2 30 将当前段落的行距设置为双倍行距。
18 .uno%3ADistributeColumns 4e 调整选定列的宽度，使其与选定范围内最宽列的宽度一致。
33 sfx2%3AComboBox%3ADLG_NEW_STYLE_BY_EXAMPLE%3ALB_COL 1b 输入新样式的名称。
18 .uno%3AStyleNewByExample 1b 输入新样式的名称。
29 sw%3AEdit%3ADLG_SWDLG_STRINPUT%3AED_INPUT 3f 输入新的自动套用格式的名称，然后单击确定。
1b SD_HID_SD_NAMEDIALOG_OBJECT 42 输入选定对象的名称。“助手”中将显示该名称。
13 .uno%3ARenameObject 4b 为选定对象指定名称，以便在“导航”中快速查找对象。
10 .uno%3ANameGroup 4b 为选定对象指定名称，以便在“导航”中快速查找对象。
1e SW_HID_FORMAT_NAME_OBJECT_NAME 42 输入选定对象的名称。“助手”中将显示该名称。
32 cui%3AEdit%3ARID_SVXDLG_OBJECT_NAME%3ANTD_EDT_NAME 42 输入选定对象的名称。“助手”中将显示该名称。
1d .uno%3AObjectTitleDescription 84 为选定对象指定标题和说明。导出文档时，这些标题和说明可供辅助工具访问，并将作为替代标记。
39 cui%3AEdit%3ARID_SVXDLG_OBJECT_TITLE_DESC%3ANTD_EDT_TITLE 6c 输入标题文本。此简称在 HTML 格式中显示为替代标记。辅助工具可以读取此文本。
41 cui%3AMultiLineEdit%3ARID_SVXDLG_OBJECT_TITLE_DESC%3ANTD_EDT_DESC c6 输入说明文本。可以输入较长的说明文本，以便向使用屏幕阅读器软件的用户描述一个复杂对象或一组对象。对于辅助工具，此说明显示为替代标记。
11 .uno%3AFormatLine 2a 设置用于选定线条的格式选项。
37 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMF_SYMBOL_HEIGHT 18 输入符号的高度。
39 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_LINE_WIDTH 96 选择线条的宽度。您可以附加度量单位。线条宽度为零时将在输出媒体上产生一条宽度为一个像素的极细的线条。
10 .uno%3ALineWidth 96 选择线条的宽度。您可以附加度量单位。线条宽度为零时将在输出媒体上产生一条宽度为一个像素的极细的线条。
30 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_EDGE_STYLE 91 Select the shape to be used at the corners of the line. In case of a small angle between lines, a mitered shape is replaced with a beveled shape.
36 cui%3AMenuButton%3ARID_SVXPAGE_LINE%3AMB_SYMBOL_BITMAP 2a 选择图表中要使用的符号样式。
11 CUI_HID_LINE_LINE 75 为选定线条或要绘制的线条设置格式选项。您也可以为线条加上箭头或者修改图表符号。
37 cui%3ATriStateBox%3ARID_SVXPAGE_LINE%3ATSB_CENTER_START 33 将箭头的中心放在选定线条的端点上。
38 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_END_WIDTH 18 输入箭头的宽度。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_LINE_TRANSPARENT 59 输入线条的透明度，其中，100% 表示完全透明，0% 表示完全不透明。
11 .uno%3AXLineStyle 1b 选择要使用的线型。
3a cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_START_WIDTH 18 输入箭头的宽度。
2f cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_END_STYLE 2a 选择用应用于选定线条的箭头。
33 cui%3ACheckBox%3ARID_SVXPAGE_LINE%3ACBX_SYNCHRONIZE 84 如果您输入不同的宽度、选择不同的线条箭头样式或使线条箭头居中，两个箭头设置都会自动更新。
35 cui%3ATriStateBox%3ARID_SVXPAGE_LINE%3ATSB_CENTER_END 33 将箭头的中心放在选定线条的端点上。
31 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_START_STYLE 2a 选择用应用于选定线条的箭头。
11 .uno%3AXLineColor 18 选择线条的颜色。
2f cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_CAP_STYLE 52 Select the style of the line end caps. The caps are added to inner dashes as well.
33 cui%3ACheckBox%3ARID_SVXPAGE_LINE%3ACB_SYMBOL_RATIO 42 输入新的高度值或宽度值时，符号比例保持不变。
36 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMF_SYMBOL_WIDTH 18 输入符号的宽度。
2b cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_COLOR 18 选择线条的颜色。
30 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_LINE_STYLE 1b 选择要使用的线型。
34 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_MODIFY 75 使用当前设置更新选定线条样式。要修改选定线条样式的名称，请在提示时输入新名称。
35 cui%3ANumericField%3ARID_SVXPAGE_LINE_DEF%3ANUM_FLD_2 45 输入一个点或一条短划线在一个序列中出现的次数。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_LENGTH_1 15 输入划线长度。
30 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_TYPE_2 2a 选择要采用的划线和点的组合。
30 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_TYPE_1 2a 选择要采用的划线和点的组合。
33 cui%3AImageButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_SAVE 36 保存当前的线条样式表，以便日后加载。
34 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_LINESTYLES 21 选择要创建的线条样式。
29 cui%3AEdit%3ARID_SVXDLG_NAME%3AEDT_STRING f 输入名称。
37 cui%3ACheckBox%3ARID_SVXPAGE_LINE_DEF%3ACBX_SYNCHRONIZE 2a 相对于线条长度自动调整条目。
35 cui%3ANumericField%3ARID_SVXPAGE_LINE_DEF%3ANUM_FLD_1 45 输入一个点或一条短划线在一个序列中出现的次数。
33 cui%3AImageButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_LOAD 18 导入线条样式表。
31 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_ADD 2d 使用当前设置创建新的线条样式。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_DISTANCE 24 输入点与划线之间的间距。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_LENGTH_2 15 输入划线长度。
10 CUI_HID_LINE_DEF 30 编辑或创建短划线或点状线条样式。
35 cui%3AListBox%3ARID_SVXPAGE_LINEEND_DEF%3ALB_LINEENDS 3c 在此列表框中选择一个默认的箭头样式符号。
36 cui%3AImageButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_LOAD 1b 导入箭头样式列表。
34 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_ADD 60 要定义自定义箭头样式，请在文档中选择一个绘图对象，然后单击此处。
13 CUI_HID_LINE_ENDDEF 1e 编辑或创建箭头样式。
37 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_MODIFY 24 修改所选箭头样式的名称。
2f cui%3AEdit%3ARID_SVXPAGE_LINEEND_DEF%3AEDT_NAME 24 显示选定箭头样式的名称。
36 cui%3AImageButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_SAVE 36 保存当前的箭头样式表，以便日后加载。
11 .uno%3AFormatArea 2a 设置选定绘图对象的填充属性。
37 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_Y_OFFSET 30 输入平铺位图时采用的垂直偏移量。
2c cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_BITMAP 27 单击选定对象要应用的填充。
31 cui%3ARadioButton%3ARID_SVXPAGE_AREA%3ARBT_COLUMN 4e 根据输入的偏移量，相对于位图平铺来垂直偏移原始位图。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_Y_SIZE 18 输入位图的高度。
2b cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_COLOR 27 单击选定对象要应用的填充。
32 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_STRETCH 5c 拉伸位图以填充所选对象。要使用此功能，请清除选择 平铺 复选框。
30 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_SCALE db 根据宽度和高度框中输入的百分比值，相对于选定对象的大小调整位图比例。请清除此复选框，以便根据您在宽度和高度框中输入的度量单位来修改选定对象的大小。
34 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_STEPCOUNT 45 自动确定混合渐变图案中两端颜色时所用的步骤数。
10 .uno%3AFillStyle 33 选择选定绘图对象要采用的填充类型。
2e cui%3ARadioButton%3ARID_SVXPAGE_AREA%3ARBT_ROW 4e 根据输入的偏移量，相对于位图平铺来水平偏移原始位图。
32 cui%3ACheckBox%3ARID_SVXPAGE_AREA%3ACB_HATCHBCKGRD 66 在带阴影线的图案中应用背景颜色。选择此复选框，然后单击列表中的颜色。
2f cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_TILE 24 平铺位图以填充选定对象。
37 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_X_OFFSET 30 输入平铺位图时采用的水平偏移量。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_OFFSET 24 输入行或列的偏移百分比。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_X_SIZE 18 输入位图的宽度。
33 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_ORIGINAL 7b 在填充选定对象时保持位图的原始大小。要修改位图大小，请清除此复选框，然后单击相对。
2e cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_HATCHING 27 单击选定对象要应用的填充。
11 CUI_HID_AREA_AREA 2a 设置所选图形对象的填充选项。
36 cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_HATCHBCKGRDCOLOR 33 单击要用作选定填充图案背景的颜色。
39 cui%3ANumericField%3ARID_SVXPAGE_AREA%3ANUM_FLD_STEPCOUNT 3f 输入混合渐变图案中两端颜色时所用的步骤数。
2e cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_GRADIENT 27 单击选定对象要应用的填充。
39 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_COLOR_FROM 57 在从框中输入颜色密度，其中，0% 表示黑色，100 % 表示所选颜色。
33 cui%3AImageButton%3ARID_SVXPAGE_GRADIENT%3ABTN_LOAD 1b 加载其他渐变列表。
33 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_GRADIENTS 2d 选择要应用或要创建的渐变类型。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_COLOR_TO 5a 在至框中输入颜色密度，其中，0% 表示黑色，100 % 表示选择的颜色。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_CENTER_X 88 输入渐变的水平偏移量，其中 0% 表示渐变中端点颜色的当前水平位置。端点颜色是至框中所选的颜色。
31 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_ADD 5d 将自定义渐变加入到当前列表中。指定渐变的属性，然后单击此按钮。
32 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_COLOR_TO 1e 选择渐变的端点颜色。
34 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_ANGLE 24 输入选定渐变的旋转角度。
34 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_MODIFY 63 对所选渐变应用当前的渐变属性。如果需要，可以用其他名称保存该渐变。
34 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_COLOR_FROM 21 选择渐变的起始点颜色。
35 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_BORDER 72 输入一个数值，用于调整端点颜色在渐变上所占的面积。端点颜色是至框中所选颜色。
33 cui%3AImageButton%3ARID_SVXPAGE_GRADIENT%3ABTN_SAVE 33 保存当前的渐变列表，以供日后加载。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_CENTER_Y 94 输入渐变影像的垂直偏移量，其中 0% 表示渐变影像中端点颜色的当前垂直位置。端点颜色是至框中所选的颜色。
38 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_GRADIENT_TYPES 1b 选择要应用的渐变。
15 CUI_HID_AREA_GRADIENT 3c 设置渐变的属性，或者保存和加载渐变列表。
30 cui%3AImageButton%3ARID_SVXPAGE_HATCH%3ABTN_SAVE 2d 保存当前阴影表，以供日后加载。
30 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_HATCHINGS 54 列出可以使用的阴影线。单击要采用的阴影线，然后单击确定。
12 CUI_HID_AREA_HATCH 3f 设置阴影线的属性，或者保存和装入阴影列表。
31 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_LINE_COLOR 1b 选择阴影线的颜色。
14 CUI_HID_TPHATCH_CTRL 36 在网格中单击以定义阴影线的旋转角度。
31 cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_MODIFY 6f 对选定的阴影线采用当前的阴影线属性。如果需要，可以用其他名称保存该阴影线。
30 cui%3AImageButton%3ARID_SVXPAGE_HATCH%3ABTN_LOAD 1b 装入其他阴影列表。
35 cui%3AMetricField%3ARID_SVXPAGE_HATCH%3AMTR_FLD_ANGLE 48 输入阴影线的旋转角度，或者在旋转角度网格中单击。
2e cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_ADD 69 将自定的阴影线加入到当前列表中。指定自定阴影线的属性，然后单击此按钮。
30 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_LINE_TYPE 24 选择要使用的阴影线类型。
38 cui%3AMetricField%3ARID_SVXPAGE_HATCH%3AMTR_FLD_DISTANCE 21 输入阴影线之间的间距。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_IMPORT 66 找到要导入的位图，然后单击打开。该位图将被添加到可用位图列表的末尾。
38 cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_BACKGROUND_COLOR 24 选择位图图案的背景颜色。
2d cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_COLOR 54 选择一种前景颜色，然后在网格内单击以将像素添加到图案中。
31 cui%3AImageButton%3ARID_SVXPAGE_BITMAP%3ABTN_SAVE 33 保存当前的位图列表，以供日后加载。
31 cui%3AImageButton%3ARID_SVXPAGE_BITMAP%3ABTN_LOAD 1b 装入其他位图列表。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_MODIFY 7b 用当前的位图图案替换通过图案编辑器建立的位图。如果需要，可以用其他名称保存该图案。
2f cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_ADD 42 将在图案编辑器中创建的位图添加到当前列表中。
13 CUI_HID_AREA_BITMAP 8a 选择要作为填充图案使用的位图，或者建立自定像素图案。还可以输入位图，或者保存或加载位图列表。
2f cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_BITMAPS 5a 从列表中选择一个位图，然后单击确定以将图案应用于选定的对象。
38 cui%3ATriStateBox%3ARID_SVXPAGE_SHADOW%3ATSB_SHOW_SHADOW 27 为选定的绘图对象添加阴影。
34 cui%3AListBox%3ARID_SVXPAGE_SHADOW%3ALB_SHADOW_COLOR 18 选择阴影的颜色。
15 CUI_HID_TPSHADOW_CTRL 24 在要投射阴影的位置单击。
11 .uno%3AFillShadow c0 为选定的对象添加阴影。如果对象已经具有阴影，则会删除阴影。如果在没有选择对象的情况下单击此图标，则会为绘制的下一个对象添加阴影。
39 cui%3AMetricField%3ARID_SVXPAGE_SHADOW%3AMTR_FLD_DISTANCE 33 输入阴影与选定对象之间的偏移距离。
3f cui%3AMetricField%3ARID_SVXPAGE_SHADOW%3AMTR_SHADOW_TRANSPARENT 5f 输入 0%（不透明）到 100%（透明）之间的百分比，以指定阴影的透明度。
13 CUI_HID_AREA_SHADOW 42 为选定的绘图对象添加阴影，并定义阴影的属性。
3d cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_ANGLE 1e 输入渐变的旋转角度。
41 cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_GRADIENT 60 对当前的填充颜色应用透明渐变。选择此选项，然后再设置渐变的属性。
43 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_START_VALUE 60 输入渐变起点的透明度值，其中 0% 表示完全不透明，100% 表示完全透明。
3f cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_LINEAR 8b 打开颜色的透明度。选择此选项，然后在框中输入数字，其中 0% 表示完全不透明，而 100% 表示完全透明。
3c cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_OFF 1b 关闭颜色的透明度。
41 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_END_VALUE 60 输入图案终点的透明度值，其中 0% 表示完全不透明，100% 表示完全透明。
40 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_CENTER_X 27 输入渐变的水平方向偏移值。
3e cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_BORDER 5a 输入一个调整量，将根据此数值调整图案的透明区域。默认值是 0%。
3e cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRANSPARENT 6b 调整当前填充颜色的透明度。输入介于 0%（不透明）和 100%（透明）之间的数字。
40 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_CENTER_Y 27 输入渐变的垂直方向偏移值。
41 cui%3AListBox%3ARID_SVXPAGE_TRANSPARENCE%3ALB_TRGR_GRADIENT_TYPES 2a 选择要应用的透明图案的类型。
39 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_FULL_WIDTH 3c 锁定文本到绘画对象或文本对象的足够宽度。
3c cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_WORDWRAP_TEXT 4e 使双击自定义形状后添加的文字换行以适应形状内部大小。
3e cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_HEIGHT 4b 如果对象小于文本，请将对象的高度扩展到文本的高度。
3d cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_WIDTH 4b 如果对象小于文本，请将对象的宽度扩展到文本的宽度。
15 .uno%3ATextAttributes 51 为选定的绘图对象或文字对象中的文本设置版式和锁定属性。
39 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_BOTTOM 54 输入绘图对象或文字对象的下边缘与文本的下边框之间的间隔。
1d CUI_HID_TEXTATTR_CTL_POSITION 2a 在要放置文本锁定的位置单击。
3c cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_SIZE 45 调整自定义形状大小以适应双击形状后输入的文字。
3a cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_FIT_TO_SIZE 45 根据绘图对象或文字对象的整个区域修改文本大小。
15 CUI_HID_PAGE_TEXTATTR 51 为选定的绘图对象或文字对象中的文本设置版式和锁定属性。
36 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_TOP 54 输入绘图对象或文字对象的上边缘与文本的上边框之间的间隔。
38 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_RIGHT 54 输入绘图对象或文字对象的右边缘与文本的右边框之间的间隔。
37 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_LEFT 54 输入绘图对象或文字对象的左边缘与文本的左边框之间的间隔。
36 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_CONTOUR 45 调整文本方向，使其与选定绘图对象的轮廓相匹配。
16 .uno%3ATransformDialog 39 修改大小、移动、旋转、倾斜选定的对象。
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_WIDTH 1e 输入选定对象的宽度。
35 cui%3AListBox%3ARID_SVXPAGE_POSITION_SIZE%3ALB_ORIENT 0 
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_POS_X 45 输入对象相对于网格中选定基点要移动的横向距离。
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_POS_Y 45 输入对象相对于网格中选定基点要移动的纵向距离。
13 CUI_HID_TPSIZE_CTRL 60 单击网格中的基点，然后在宽度和高度框中输入选定对象的新尺寸大小。
17 CUI_HID_TPPOSITION_CTRL 99 单击网格中的基点，然后在位置 Y 和位置 X 框中输入对象相对于选定基点的移动量。基点相当于对象上的选择控点。
3f cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_SIZEPROTECT 1e 防止修改对象的大小。
42 cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_AUTOGROW_WIDTH 30 根据选定对象的宽度修改文本大小。
3e cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_POSPROTECT 2d 防止修改选定对象的位置或大小。
3e cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_HEIGHT 1e 输入选定对象的高度。
43 cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_AUTOGROW_HEIGHT 30 根据选定对象的高度修改文本大小。
35 cui%3AListBox%3ARID_SVXPAGE_POSITION_SIZE%3ALB_ANCHOR 0 
36 cui%3ACheckBox%3ARID_SVXPAGE_POSITION_SIZE%3ACBX_SCALE 33 修改选定对象的大小时比例保持不变。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_ANGLE 24 输入选定对象的旋转角度。
18 CUI_HID_TPROTATION_CTRL2 40 单击鼠标以指定与 45 度成倍数关系的旋转角度。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_POS_Y 36 输入从页面上边缘到透视点的垂直间距。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_POS_X 3c 输入从页面左边缘到透视点之间的水平间距。
13 CUI_HID_TRANS_ANGLE 18 旋转选定的对象。
18 CUI_HID_TPROTATION_CTRL1 27 在要放置透视点的位置单击。
36 cui%3AMetricField%3ARID_SVXPAGE_SLANT%3AMTR_FLD_RADIUS 30 输入转换成圆角所使用的圆的半径。
13 CUI_HID_TRANS_SLANT 45 倾斜选定的对象，或者将矩形对象的角转换为圆角。
35 cui%3AMetricField%3ARID_SVXPAGE_SLANT%3AMTR_FLD_ANGLE 1b 输入倾斜轴的角度。
30 cui%3ACheckBox%3ARID_SVXPAGE_CAPTION%3ACB_LAENGE 30 单击此处将以最佳方式显示单折线。
33 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_ANSATZ 39 选择相对于图例框延伸图例线的起始位置。
2f cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_ANSATZ 39 选择相对于图例框延伸图例线的起始位置。
18 CUI_HID_CAPTION_CTL_TYPE 30 单击要应用于选定图例的图例样式。
2f cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_WINKEL 30 单击要应用于选定图例的图例样式。
34 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_ABSTAND 42 输入要在图例线条末端和图例框之间留出的间距。
33 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_LAENGE 45 输入从图例框到线条弯曲点之间的图例线段的长度。
33 cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_ANSATZ_REL 39 选择相对于图例框延伸图例线的起始位置。
f .uno%3AFlipMenu 27 水平或垂直翻转选定的对象。
11 .uno%3AMirrorVert 2d 将选定的对象从上向下垂直翻转。
1b .uno%3AObjectMirrorVertical 2d 将选定的对象从上向下垂直翻转。
1d .uno%3AObjectMirrorHorizontal 2d 将选定的对象从左向右水平翻转。
11 .uno%3AMirrorHorz 2d 将选定的对象从左向右水平翻转。
17 .uno%3AArrangeFrameMenu 24 修改选定对象的堆叠顺序。
12 .uno%3AArrangeMenu 24 修改选定对象的堆叠顺序。
15 .uno%3AObjectPosition 24 修改选定对象的堆叠顺序。
13 .uno%3ABringToFront 4e 将选定对象移到堆叠顺序的顶层，使其位于其他对象之前。
17 .uno%3AObjectForwardOne 45 将选定对象上移一层，使其更接近堆叠顺序的顶层。
e .uno%3AForward 45 将选定对象上移一层，使其更接近堆叠顺序的顶层。
f .uno%3ABackward 45 将选定对象下移一层，使其更接近堆叠顺序的底层。
14 .uno%3AObjectBackOne 45 将选定对象下移一层，使其更接近堆叠顺序的底层。
11 .uno%3ASendToBack 4e 将选定对象移到堆叠顺序的底部，使其位于其他对象之后。
1c .uno%3ASetObjectToForeground 24 将选定对象移到文字之前。
1c .uno%3ASetObjectToBackground 27 将选定的对象移到文本之后。
11 .uno%3AAnchorMenu 24 设置选定对象的锁定选项。
16 .uno%3ASetAnchorToPage 24 将选定的项锁定到当前页。
16 .uno%3ASetAnchorToPara 27 将选定的项锁定到当前段落。
16 .uno%3ASetAnchorToCell 21 将选定项锁定到单元格。
17 .uno%3ASetAnchorToFrame 27 将选定的项锁定到周围框架。
16 .uno%3ASetAnchorToChar 93 将选定的项当作字符锁定在当前文字中。如果选定项的高度大于当前字体的大小，则会增加该项所在行的高度。
16 .uno%3ASetAnchorAsChar 93 将选定的项当作字符锁定在当前文字中。如果选定项的高度大于当前字体的大小，则会增加该项所在行的高度。
1d .uno%3AToggleObjectBezierMode 2a 用于修改选定绘图对象的形状。
21 SVX_HID_FONTWORK_TBI_SHADOW_SLANT 7f 为选定对象中的文本添加倾斜的阴影。单击此按钮，然后在间距 X和间距 Y框中输入阴影的尺寸。
1d SVX_HID_FONTWORK_TBI_SHOWFORM 36 显示或隐藏文本基线或选定对象的边缘。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_DISTANCE 39 输入文本基线与各字符的基线之间的间距。
21 SVX_HID_FONTWORK_TBI_STYLE_ROTATE 3f 使用选定对象的上边缘或下边缘作为文本基线。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_SHADOW_X 39 输入文本字符与阴影边缘之间的水平间距。
22 SVX_HID_FONTWORK_TBI_SHADOW_NORMAL 76 为选定对象中的文本添加阴影。单击此按钮，然后在间距 X和间距 Y框中输入阴影的尺寸。
1c SVX_HID_FONTWORK_TBI_OUTLINE 2d 显示或隐藏文本中各字符的边缘。
3b svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_TEXTSTART 48 输入文本基线的起始位置与文本起始位置之间的间距。
24 SVX_HID_FONTWORK_TBI_ADJUST_AUTOSIZE 30 根据文字基线的长度调整文本大小。
22 SVX_HID_FONTWORK_TBI_ADJUST_CENTER 2a 将文本在文本基线上居中对齐。
1a SVX_HID_FONTWORK_CTL_FORMS 2d 单击要用作文字的基准线的形状。
36 svx%3AListBox%3ARID_SVXDLG_FONTWORK%3ACLB_SHADOW_COLOR 1e 选择文本阴影的颜色。
21 SVX_HID_FONTWORK_TBI_ADJUST_RIGHT 2a 将文本与文本基线的右端对齐。
1e SVX_HID_FONTWORK_TBI_STYLE_OFF 15 删除基线格式。
1f SVX_HID_FONTWORK_TBI_SHADOW_OFF 1e 删除文本的阴影效果。
21 SVX_HID_FONTWORK_TBI_STYLE_SLANTX 27 水平倾斜文本对象中的字符。
20 SVX_HID_FONTWORK_TBI_ADJUST_LEFT 2a 将文本与文本基线的左端对齐。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_SHADOW_Y 39 输入文本字符与阴影边缘之间的垂直间距。
22 SVX_HID_FONTWORK_TBI_ADJUST_MIRROR 8d 将文字排列方向反向，然后在水平或垂直向上翻转文字。要使用此命令，首先必须对文字采用不同的基线。
21 SVX_HID_FONTWORK_TBI_STYLE_SLANTY 27 垂直倾斜文本对象中的字符。
22 SVX_HID_FONTWORK_TBI_STYLE_UPRIGHT 69 将选定对象的上边缘或下边缘用作文本基线，保留各字符原始的垂直对齐方式。
f .uno%3AFontWork 57 对于使用以前的“艺术字”对话框创建的对象，编辑艺术字效果。
10 .uno%3AGroupMenu 69 组将选定的对象组合在一起，以便可以将它们作为单个对象进行移动或格式化。
12 .uno%3AFormatGroup 45 对选定对象进行组合，使其可以作为单个对象移动。
14 .uno%3AFormatUngroup 2a 将选定的组合拆分成单个对象。
11 .uno%3AEnterGroup 78 打开选定组合以编辑各个对象。如果选定组合中含有嵌套组合，可以对子组合重复此命令。
11 .uno%3ALeaveGroup 3f 退出组合，因此无法再编辑组合中的单个对象。
38 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_LEFT 1b 从右向左滚动文字。
29 cui%3ATabPage%3ARID_SVXPAGE_TEXTANIMATION 36 为所选绘图对象中的文字加入动画效果。
38 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_AUTO a4 $[officename] 将自动确定在重复效果之前的等待时间。要手动指定延迟时间，请清除此复选框，然后在自动框中输入一个数值。
39 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_RIGHT 1b 从左向右滚动文字。
3f cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_STOP_INSIDE 27 应用效果之后文字仍然可见。
3e cui%3AMetricField%3ARID_SVXPAGE_TEXTANIMATION%3AMTR_FLD_AMOUNT 36 输入递增值，将根据此数值来滚动文字。
35 cui%3AListBox%3ARID_SVXPAGE_TEXTANIMATION%3ALB_EFFECT 6f 选择要对选定图形对象中的文字应用的动画效果。要删除动画效果，请选择无效果。
3b cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_ENDLESS 84 连续播放动画效果。要指定动画循环播放的次数，请清除此复选框，然后在连续框中输入一个数值。
40 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_START_INSIDE 3f 应用效果时，文字可见，并且位于绘图对象中。
38 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_DOWN 1b 从上向下滚动文字。
36 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_UP 1b 从下向上滚动文字。
3d cui%3AMetricField%3ARID_SVXPAGE_TEXTANIMATION%3AMTR_FLD_DELAY 30 输入重复效果之前需要等待的时间。
39 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_PIXEL 24 以像素为单位度量递增值。
3e cui%3ANumericField%3ARID_SVXPAGE_TEXTANIMATION%3ANUM_FLD_COUNT 2a 输入动画效果重复播放的次数。
1e DBACCESS_HID_BROWSER_ROWHEIGHT 27 修改当前行或选定行的高度。
10 .uno%3ARowHeight 27 修改当前行或选定行的高度。
2e sc%3ACheckBox%3ARID_SCDLG_ROW_MAN%3ABTN_DEFVAL 99 基于默认的模板调整行高大小。现有内容可能以垂直裁剪的方式显示。当您输入较多的内容时，高度不再自动增加。
31 dbaccess%3AMetricField%3ADLG_ROWHEIGHT%3AMF_VALUE 1b 输入要使用的行高。
2f sc%3AMetricField%3ARID_SCDLG_ROW_MAN%3AED_VALUE 1b 输入要使用的行高。
31 dbaccess%3ACheckBox%3ADLG_ROWHEIGHT%3ACB_STANDARD 99 基于默认的模板调整行高大小。现有内容可能以垂直裁剪的方式显示。当您输入较多的内容时，高度不再自动增加。
2f sc%3AMetricField%3ARID_SCDLG_COL_MAN%3AED_VALUE 1b 输入要使用的列宽。
2e sc%3ACheckBox%3ARID_SCDLG_COL_MAN%3ABTN_DEFVAL 27 根据当前字体自动调整列宽。
12 .uno%3AColumnWidth 27 修改当前列或选定列的宽度。
30 dbaccess%3ACheckBox%3ADLG_COLWIDTH%3ACB_STANDARD 27 根据当前字体自动调整列宽。
20 DBACCESS_HID_BROWSER_COLUMNWIDTH 27 修改当前列或选定列的宽度。
30 dbaccess%3AMetricField%3ADLG_COLWIDTH%3AMF_VALUE 1b 输入要使用的列宽。
3a cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_TXTSTACKED 15 垂直对齐文本。
11 CUI_HID_ALIGNMENT 3f 设置当前单元格或选定单元格内容的对齐选项。
36 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_SHRINK 8d 减小字体的外观尺寸，以使单元格内容适合当前的单元格宽度。无法将此命令应用于包含换行符的单元格。
34 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_HYPH 3c 启用字词连字符，为换行的文字添加连字符。
1a CUI_HID_ALIGNMENT_CTR_DIAL 2d 在刻度盘中单击以设置文字方向。
3e cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_ASIAN_VERTICAL fa 将选定单元格内的亚洲语言字符垂直排列对齐。如果单元格包含多行文字，则这些行将转换为从右至左排列的文本列。转换后文本中的西文字符将向右旋转 90 度，亚洲语言字符将不被旋转。
21 CUI_HID_ALIGNMENT_CTR_BORDER_LOCK 3c 指定从单元格的哪条边开始书写旋转的文字。
33 cui%3AListBox%3ARID_SVXPAGE_ALIGNMENT%3ALB_VERALIGN 39 选择要对单元格内容应用的垂直对齐选项。
35 cui%3AMetricField%3ARID_SVXPAGE_ALIGNMENT%3AED_INDENT 3c 根据您输入的缩进量从单元格的左边缘缩进。
33 cui%3AListBox%3ARID_SVXPAGE_ALIGNMENT%3ALB_HORALIGN 39 选择要对单元格内容应用的水平对齐选项。
34 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_WRAP 57 文本换行至单元格边框的另一行。行的数目取决于单元格的宽度。
37 cui%3ANumericField%3ARID_SVXPAGE_ALIGNMENT%3ANF_DEGREES 66 输入所选单元格内文字的旋转角度。正值向左旋转文字，负值向右旋转文字。
1c SVX_HID_GRID_NUMBEROFRECORDS a3 显示记录数。例如，"记录 7 总计 9(2)" 表示在含有 9 条记录的表格中选择了两 (2) 条记录，光标当前位于编号为 7 的记录处。
18 SVX_HID_GRID_TRAVEL_PREV 21 转到表中的上一条记录。
1c SVX_HID_GRID_TRAVEL_ABSOLUTE 37 键入要显示的记录编号，然后按 Enter 键。
17 SVX_HID_GRID_TRAVEL_NEW 24 在当前表格中插入新记录。
1b DBACCESS_HID_CTL_TABBROWSER 6f 选择数据库记录。将行或单元格拖放到文档中以插入内容。拖放列标题以插入字段。
18 SVX_HID_GRID_TRAVEL_NEXT 24 转到表格中的下一条记录。
19 SVX_HID_GRID_TRAVEL_FIRST 21 转到表中的第一条记录。
18 SVX_HID_GRID_TRAVEL_LAST 24 转到表格的最后一条记录。
20 DBACCESS_HID_BROWSER_TABLEFORMAT 21 对选定的行进行格式化。
21 DBACCESS_HID_BROWSER_COLUMNFORMAT 21 对选定的列进行格式化。
f .uno%3AWindow3D 2d 指定当前文档中三维对象的属性。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_INVERT 21 将光源设置为相反方向。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_DOUBLE_SIDED 60 封闭通过拉伸自由形线条（转换 - 变成三维）而创建的三维对象的形状。
3a svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_PERCENT_DIAGONAL 2a 输入选定三维对象的导角半径。
2d svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_GEO 114 调整选定的三维对象的形状。您只可以修改由转换平面对象创建的三维对象的形状。要将某个平面对象转换成三维对象，请选择该平面对象，单击鼠标右键，然后选择转换 - 变成三维或转换 - 变成三维旋转体。
33 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_END_ANGLE 33 输入选定三维旋转体要旋转的角度数。
33 svx%3ANumericField%3ARID_SVXFLOAT_3D%3ANUM_VERTICAL 39 输入选定三维旋转体中所用的垂直分段数。
2f svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_DEPTH 4e 输入选定三维对象的拉伸深度。此选项对三维旋转体无效。
3c svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TWO_SIDED_LIGHTING 7f 同时从对象的外面和里面照明对象。要使用周围光源，请单击此按钮，然后单击反置法线 按钮。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_FLAT 24 将三维表面渲染为多边形。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_OBJ 69 根据对象的形状对三维表面进行渲染。例如，圆形形状被渲染为具有球形表面。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_SPHERE 21 渲染成平滑的三维表面。
35 svx%3ANumericField%3ARID_SVXFLOAT_3D%3ANUM_HORIZONTAL 39 输入选定三维旋转体中所用的水平分段数。
33 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_BACKSCALE 3c 输入用于改变选定三维对象的正面大小的值。
2f svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_SLANT 30 输入阴影的投影角度（0 到 90 度）。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_SHADOW_3D 2d 添加或删除选定三维对象的阴影。
32 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_DISTANCE 39 输入照相机与选定对象的中心之间的间隔。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_REPRESENTATION 33 设置选定三维对象的阴影和阴影选项。
36 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_FOCAL_LENGTH 63 输入照相机的焦距。小焦距值对应“鱼眼”镜头，大焦距值对应远射镜头。
2e svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_SHADEMODE 11d 选择要使用的阴影模式。平形阴影为对象表面上的每个多边形指定一种颜色。郭氏阴影可以混合各个多边形中的颜色。芬氏阴影根据每个像素周围的像素来平均设置像素的颜色，这种阴影模式对处理能力的要求最高。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_8 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_5 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_2 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_AMBIENTLIGHT 1e 选择周围光线的颜色。
2c svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_LIGHT_1 1e 选择当前光源的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_4 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_COLOR 1e 选择周围光线的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_6 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
2f svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT 24 定义选定三维对象的光源。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_3 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_1 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_7 9f 单击两次打开光源，然后从列表中选择光的颜色。如果需要，您也可以从周围光线框中选择颜色来设置周围光线的颜色。
34 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_FILTER 45 使纹理产生轻微的模糊效果，以去掉不需要的斑点。
37 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_LUMINANCE 1e 将纹理转换成黑白色。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_PARALLEL_Y 2d 按平行于垂直轴的方向应用纹理。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_OBJECT_X 33 根据对象的形状和大小自动调整纹理。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_COLOR 1b 将纹理转换成彩色。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_CIRCLE_Y 2a 垂直轴方向的纹理成圆形围绕。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_CIRCLE_X 2a 水平轴方向的纹理成圆形围绕。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_MODULATE 66 应用带阴影的纹理。要定义纹理的阴影选项，请单击此对话框中的阴影按钮。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_OBJECT_Y 33 根据对象的形状和大小自动调整纹理。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_PARALLEL_X 2d 按平行于水平轴的方向应用纹理。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEXTURE 121 设置选定三维对象的表面纹理属性。在使用此功能之前，必须先对选定的对象应用表面纹理。要快速应用表面纹理，请打开图片库，按住 Shift+Ctrl 组合键（Mac: Shift+Command 组合键），并将某个图像拖到选定的三维对象上。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_REPLACE 1e 应用不带阴影的纹理。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_EMISSION_COLOR 21 选择对象要反射的颜色。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_EMISSION 1e 选择照明对象的颜色。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_MAT_COLOR 1e 选择照明对象的颜色。
2e svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_COLOR 21 选择对象要应用的颜色。
40 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_MAT_SPECULAR_INTENSITY 21 输入发光点效果的强度。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_SPECULAR_COLOR 21 输入发光点效果的强度。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_SPECULAR 21 选择对象要反射的颜色。
32 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_MATERIAL 24 修改选定三维对象的颜色。
32 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_FAVORITES 5a 选择预定义的颜色图案，或者选择自定义来定义自定义的颜色图案。
38 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_TOP 48 分布选定的对象，以使各对象上边缘之间的间距相等。
1a .uno%3ADistributeSelection 81 将三个或三个以上的选定对象沿水平轴或垂直轴进行平均分布。还可以平均分布对象之间的间距。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_LEFT 48 分布选定的对象，以使各对象左边缘之间的间距相等。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_NONE 24 不在垂直方向上分布对象。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_BOTTOM 48 分布选定的对象，以使各对象下边缘之间的间距相等。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_CENTER 4b 分布选定的对象，以使各对象垂直中心之间的间距相等。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_CENTER 4b 分布选定的对象，以使各对象水平中心之间的间距相等。
3d cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_DISTANCE 45 水平分布选定的对象，以使各对象之间的间距相等。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_NONE 24 不在水平方向上分布对象。
3d cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_DISTANCE 45 垂直分布选定的对象，以使各对象之间的间距相等。
3a cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_RIGHT 48 分布选定的对象，以使各对象右边缘之间的间距相等。
1c CUI_HID_SPLDLG_BUTTON_CHANGE 66 使用当前建议替换未知词。如果您不只修改了拼错的字词，则替换整个句子。
38 cui%3AMultiLineEdit%3ARID_SVXDLG_SPELLCHECK%3AED_NEWWORD 75 突出显示句子中拼写错误的词。编辑该单词或句子，或者单击以下文字框中的一个建议。
f .uno%3ASpelling 81 检查文档或当前选定文本中的拼写错误。如果安装了语法检查扩展，该对话框也会检查语法错误。
1f CUI_HID_SPLDLG_BUTTON_CHANGEALL 2a 使用当前建议替换所有未知词。
1f CUI_HID_SPLDLG_BUTTON_IGNOREALL 4b 跳过整个文档中出现的所有未知词，继续进行拼写检查。
1f .uno%3ASpellingAndGrammarDialog 81 检查文档或当前选定文本中的拼写错误。如果安装了语法检查扩展，该对话框也会检查语法错误。
32 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_UNDO 66 单击以撤销当前句子中前一次修改。再次单击以撤销相同句子中的以前修改。
39 cui%3ACheckBox%3ARID_SVXDLG_SPELLCHECK%3ACB_CHECK_GRAMMAR 57 将语法检查首先应用到所有的拼写错误，之后是所有的语法错误。
1c CUI_HID_SPLDLG_BUTTON_IGNORE 33 忽略此未知单词，继续进行拼写检查。
35 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_OPTIONS 69 打开一个对话框，您能够从中选择用户定义的词典，也能够设置拼写检查规则。
37 cui%3AMenuButton%3ARID_SVXDLG_SPELLCHECK%3AMB_ADDTODICT 2d 将未知词汇添加到自定义词典中。
36 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_AUTOCORR 69 打开一个对话框，您能够从中选择用户定义的词典，也能够设置拼写检查规则。
35 cui%3AListBox%3ARID_SVXDLG_SPELLCHECK%3ALB_SUGGESTION 72 列出用于替换拼错字词的建议字词。选择您想使用的字词，然后单击修改或全部修改。
20 CUI_HID_SPLDLG_BUTTON_IGNORERULE 69 在执行语法检查时，单击“忽略规则”可以忽略当前被标记为语法错误的规则。
33 cui%3AListBox%3ARID_SVXDLG_SPELLCHECK%3ALB_LANGUAGE 24 指定用于拼写检查的语言。
17 .uno%3AMoreDictionaries 30 使用默认浏览器打开词典扩展页面。
13 .uno%3ALanguageMenu 42 打开子菜单，您可以在其中选择语言特定的命令。
18 .uno%3AChineseConversion 90 将选定的中文文本从一种中文书写系统转换为另一种中文书写系统。如果没有选择文本，则会转换整篇文档。
2b svx%3AEdit%3ARID_SVXDLG_THESAURUS%3AED_REPL ae 当您单击“替换”按钮时，“替换为”文本框中的单个字词或多个字词将会替换文档中的原始字词。还可以在此框中直接键入文本。
33 svx%3AImageButton%3ARID_SVXDLG_THESAURUS%3ABTN_LEFT 39 重新调用“当前字词”文本框的以前内容。
10 .uno%3AThesaurus 3c 打开对话框用同义词或相关项替换当前字词。
1c CUI_HID_CT_THES_ALTERNATIVES bd 单击“替换”列表中的任何条目以将相关术语复制到“替换为”文本框。双击任何条目以将相关术语复制到“当前字词”文本框并查询该术语。
16 .uno%3AThesaurusDialog 3c 打开对话框用同义词或相关项替换当前字词。
2f svx%3AComboBox%3ARID_SVXDLG_THESAURUS%3ACB_WORD 8a 双击“替换”列表中的某行显示选择的当前字词或相关术语。还可以在此框中直接键入文本以查询文本。
35 svx%3AMenuButton%3ARID_SVXDLG_THESAURUS%3AMB_LANGUAGE 27 为同义词词典选择一种语言。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_1 63 选择此复选框，可以用您在替换为框中指定的颜色替换当前的“源颜色”。
e .uno%3ABmpMask 5d 打开“取色器”对话框，通过它可以替换位图和元文件图形中的颜色。
2d svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_TRANS 36 选择用于替换当前图像透明区域的颜色。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_3 8e 列出可用的替换颜色。要修改当前的颜色列表，请取消选择图像，选择格式 - 区域，然后单击 颜色选项卡。
1a SVX_HID_BMPMASK_CTL_QCOL_1 90 显示选定图像需要替换的颜色。要设置源颜色，请单击此处，单击“取色器”，然后单击选定图像中的颜色。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_4 63 选择此复选框，可以用您在替换为框中指定的颜色替换当前的“源颜色”。
1b SVX_HID_BMPMASK_TBI_PIPETTE 6f 选择四个源颜色框中之一。将鼠标指针移到选定的图像上，然后单击要替换的颜色。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_2 8e 列出可用的替换颜色。要修改当前的颜色列表，请取消选择图像，选择格式 - 区域，然后单击 颜色选项卡。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_1 8e 列出可用的替换颜色。要修改当前的颜色列表，请取消选择图像，选择格式 - 区域，然后单击 颜色选项卡。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_3 c0 设置在替换源图像中的源颜色时要用到的偏差。要替换与选定颜色类似的颜色，请输入一个较低的值。要替换较大范围的颜色，请输入较高的值。
1b SVX_HID_BMPMASK_CTL_PIPETTE 7b 显示当前鼠标指针正下方所选定的图像中的颜色。仅当选择了取色器工具时，此功能才可用。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_4 8e 列出可用的替换颜色。要修改当前的颜色列表，请取消选择图像，选择格式 - 区域，然后单击 颜色选项卡。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_1 c0 设置在替换源图像中的源颜色时要用到的偏差。要替换与选定颜色类似的颜色，请输入一个较低的值。要替换较大范围的颜色，请输入较高的值。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_4 c0 设置在替换源图像中的源颜色时要用到的偏差。要替换与选定颜色类似的颜色，请输入一个较低的值。要替换较大范围的颜色，请输入较高的值。
2f svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_TRANS 39 用您选择的颜色替换当前图像的透明区域。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_3 63 选择此复选框，可以用您在替换为框中指定的颜色替换当前的“源颜色”。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_2 63 选择此复选框，可以用您在替换为框中指定的颜色替换当前的“源颜色”。
30 svx%3APushButton%3ARID_SVXDLG_BMPMASK%3ABTN_EXEC 50 用在 替换为 框中指定的颜色替换当前图像中选定的源颜色。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_2 c0 设置在替换源图像中的源颜色时要用到的偏差。要替换与选定颜色类似的颜色，请输入一个较低的值。要替换较大范围的颜色，请输入较高的值。
15 .uno%3AAutoCorrectDlg 36 设置用于在键入时自动替换文字的选项。
1c CUI_HID_OFAPAGE_AUTOCORR_CLB 45 选择用于自动更正键入错误的选项，然后单击确定。
20 CUI_HID_OFAPAGE_AUTOCORR_OPTIONS 45 选择用于自动更正键入错误的选项，然后单击确定。
1f CUI_HID_OFAPAGE_AUTOFMT_OPTIONS 45 选择用于自动更正键入错误的选项，然后单击确定。
36 cui%3APushButton%3ARID_OFAPAGE_AUTOFMT_APPLY%3APB_EDIT 21 修改选定自动更正选项。
1e CUI_HID_OFAPAGE_AUTOFORMAT_CLB 45 选择用于自动更正键入错误的选项，然后单击确定。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_REPLACE%3ACB_TEXT_ONLY 75 保存替换为框中的条目，但不包括格式。进行替换时，文本使用与文档文本相同的格式。
1f CUI_HID_OFACTL_AUTOCORR_REPLACE 135 列出输入时自动替换字词或缩写的条目。要添加条目，请在替换和替换为框中输入文本，然后单击新建。要编辑条目，请选择要编辑的条目，修改替换为框中的文本，然后单击替换。要删除条目，请选择要删除的条目，然后单击删除。
40 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_REPLACE%3APB_NEW_REPLACE 2a 添加或替换替换表格中的条目。
36 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_REPLACE%3AED_REPLACE c1 输入要用来替换替换框中的文本的替换文本、图形、框架或 OLE 对象。如果文档中包含选定文本、图形、框架或 OLE 对象，则此处已输入相关信息。
20 CUI_HID_OFAPAGE_AUTOCORR_REPLACE 4e 编辑用于自动更正或替换文档中的字词或缩写的替换表格。
34 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_REPLACE%3AED_SHORT 2d 输入键入时要替换的字词或缩写。
39 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3AED_DOUBLE_CAPS d5 键入以两个大写字母开始的字词或缩写，对于这些内容，您不希望 $[officename] 将其更正为仅开头一个字母大写。例如，输入 PC，$[officename] 就不会将 PC 修改为 Pc。
19 CUI_HID_AUTOCORR_LANGUAGE 48 选择一种语言，将针对这种语言创建或编辑替换规则。
3a cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ACB_AUTOCAPS 104 将以两个大写字母开始的字词或缩写自动添加到相应的例外列表中。仅当在此对话框的选项 选项卡中选择[T] 列中的更正字首双重大写字母选项或设置每个句子首字母为大写选项时，此功能才有效。
1f CUI_HID_OFAPAGE_AUTOCORR_EXCEPT 45 指定不需要 $[officename] 自动更正的缩写或字母组合。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_NEWABBREV 2a 将当前条目添加到例外列表中。
34 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3AED_ABBREV bc 键入一个缩写，后面加一个句号，然后单击新增。这样，对于缩写结尾处句号之后的文字，$[officename] 就不会自动将其第一个字母改为大写了。
37 cui%3AListBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ALB_ABBREV 2d 列出不需要进行自动更正的缩写。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ACB_AUTOABBREV 104 将以两个大写字母开始的字词或缩写自动添加到相应的例外列表中。仅当在此对话框的选项 选项卡中选择[T] 列中的更正字首双重大写字母选项或设置每个句子首字母为大写选项时，此功能才有效。
3c cui%3AListBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ALB_DOUBLE_CAPS 8d 列出以两个大写字母开始而不需要自动更正的字词或缩写。字段中列出了所有以两个大写字母开始的字词。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_NEWDOUBLECAPS 2a 将当前条目添加到例外列表中。
3b cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_ENDQUOTE 75 选择特殊字符以便在选择格式 - 自动更正 - 应用时自动替换文档中当前位于字尾的引号。
1c CUI_HID_OFAPAGE_QUOTE_SW_CLB 43 当键入 [T] 或修改当前文本 [M] 时，选择应用替换。
3a cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_STD 24 将引号重设为默认的符号。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_STARTQUOTE 75 选择特殊字符以便在选择格式 - 自动更正 - 应用时自动替换文档中当前位于字首的引号。
39 cui%3ACheckbox%3ARID_OFAPAGE_AUTOCORR_QUOTE%3ACB_SGL_TYPO 4b 使用您指定的特殊字符自动替换单引号的系统默认符号。
3a cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_DBL_STD 24 将引号重设为默认的符号。
35 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_QUOTE%3ACB_TYPO 4b 使用您指定的特殊字符自动替换单引号的系统默认符号。
3f cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_ENDQUOTE 75 选择特殊字符以便在选择格式 - 自动更正 - 应用时自动替换文档中当前位于字尾的引号。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_STARTQUOTE 75 选择特殊字符以便在选择格式 - 自动更正 - 应用时自动替换文档中当前位于字首的引号。
18 SW_HID_LINGU_IGNORE_WORD 3c 忽略当前文档中突出显示的字词的所有实例。
21 EDITENG_HID_EDITENG_SPELLER_START 1f 打开拼写检查 对话框。
19 SW_HID_LINGU_SPELLING_DLG 1f 打开拼写检查 对话框。
1a SW_HID_LINGU_WORD_LANGUAGE 66 如果在另一个词典中找到了突出显示的字词，可以修改这个字词的语言设置。
27 EDITENG_HID_EDITENG_SPELLER_AUTOCORRECT 95 要始终替换突出显示的词，请单击列表中的词。词汇对存储在“工具”-“自动更正”-“替换”中的替换表格中。
22 EDITENG_HID_EDITENG_SPELLER_IGNORE 3c 忽略当前文档中突出显示的字词的所有实例。
15 SW_HID_LINGU_ADD_WORD 33 将突出显示的字词添加到自定义词典。
14 SW_HID_LINGU_REPLACE 6c 单击该单词可替换突出显示的单词。使用“自动更正”子菜单可进行永久性替换。
15 SW_HID_LINGU_AUTOCORR 95 要始终替换突出显示的词，请单击列表中的词。词汇对存储在“工具”-“自动更正”-“替换”中的替换表格中。
1a SW_HID_LINGU_PARA_LANGUAGE 72 如果在另一个词典中找到了突出显示的字词，可以修改这个字词所在段落的语言设置。
23 EDITENG_HID_EDITENG_SPELLER_ADDWORD 33 将突出显示的字词添加到自定义词典。
42 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_REMOVE_LIST e9 如果启用，则会在关闭当前文档时清除列表。如果禁用，则在关闭当前文档后，当前“字词补充完整”列表将可用于其他文档。在您退出 %PRODUCTNAME 之前，列表将一直保持可用。
46 cui%3ANumericField%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ANF_MAX_ENTRIES 45 输入要在“字词补充完整”列表中存储的最多字数。
41 cui%3AListBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ADCB_EXPAND_KEY 36 选择接受自动补充完整的字词所用的键。
42 cui%3AMultiListBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ALB_ENTRIES cf 列出收集的字词。关闭当前文档之前该列表一直有效。要使列表可用于当前会话中的其他文档，请禁用“关闭文档时，从列表中删除从该文档收集的字词”。
3e cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_COLLECT 8a 将常用的词添加到列表中。要从“字词补充完整”列表中删除字词，请选择该字词，然后单击删除条目。
3d cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_AS_TIP 36 以帮助提示的形式显示补充完整的字词。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_ACTIV 87 存储常用的字词，并在您键入与所存储字词的前三个字母匹配的三个字母后，自动补充完整这些字词。
40 cui%3APushButton%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3APB_ENTRIES 4b 从“字词补充完整”列表中删除选定的一个或多个字词。
43 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_APPEND_SPACE 4d 如果没有在字词后添加标点，$[officename] 会添加一个空格。
46 cui%3ANumericField%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ANF_MIN_WORDLEN 3f 输入字词补充完整功能要求的字词的最小字长。
36 SVX_CHECKBOX_RID_OFAPAGE_SMARTTAG_OPTIONS_CB_SMARTTAGS 45 使“智能标记”可以计算并在您的文本文档中显示。
38 SVX_PUSHBUTTON_RID_OFAPAGE_SMARTTAG_OPTIONS_PB_SMARTTAGS 94 要配置某“智能标记”，选择“智能标记”的名称，然后单击“属性”。 不是全部的“智能标记”都可以配置。
14 .uno%3AOutlineBullet 60 为当前段落添加编号或项目符号，并允许您编辑编号或项目符号的格式。
20 .uno%3ABulletsAndNumberingDialog 60 为当前段落添加编号或项目符号，并允许您编辑编号或项目符号的格式。
17 CUI_HID_VALUESET_BULLET 27 单击要使用的项目符号样式。
1a CUI_HID_VALUESET_SINGLENUM 21 单击要使用的编号样式。
14 CUI_HID_VALUESET_NUM 21 单击要使用的大纲样式。
17 CUI_HID_VALUESET_NUMBMP 27 单击要用作项目符号的图形。
31 cui%3ACheckBox%3ARID_SVXPAGE_PICK_BMP%3ACB_LINKED 69 如果启用，则图形将以链接的形式插入。如果不启用，则图形将嵌入到文档中。
3b cui%3ANumericField%3ARID_SVXPAGE_NUM_OPTIONS%3ANF_ALL_LEVEL 2a 为当前级别输入新的起始编号。
30 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_FMT 24 选择选定级别的编号样式。
36 cui%3APushButton%3ARID_SVXPAGE_NUM_OPTIONS%3APB_BULLET 40 打开特殊字符 对话框，可在其中选择项目符号。
33 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_ORIENT 1e 选择图形的对齐选项。
33 cui%3ACheckBox%3ARID_SVXPAGE_NUM_OPTIONS%3ACB_RATIO 1e 保持图形的大小比例。
36 cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_WIDTH 18 输入图形的宽度。
36 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_BUL_COLOR 24 选择当前编号样式的颜色。
3d cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_BUL_REL_SIZE 54 输入相对于当前段落的字体高度调整项目符号字符大小的幅度。
37 cui%3AMultiListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_LEVEL 2a 选择要定义格式化选项的级别。
37 cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_HEIGHT 18 输入图形的高度。
34 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_CHARFMT 2a 为当前级别输入新的起始编号。
36 cui%3AMenuButton%3ARID_SVXPAGE_NUM_OPTIONS%3AMB_BITMAP 4b 选择要作为项目符号的图形，或查找要使用的图形文件。
37 cui%3ANumericField%3ARID_SVXPAGE_NUM_OPTIONS%3AED_START 2a 为当前级别输入新的起始编号。
30 cui%3AEdit%3ARID_SVXPAGE_NUM_OPTIONS%3AED_SUFFIX 82 输入要在列表编号后显示的字符或文字。要创建使用样式 "1.)" 的编号列表，请在此框中输入 "1.)"。
30 cui%3AEdit%3ARID_SVXPAGE_NUM_OPTIONS%3AED_PREFIX 36 输入要在列表编号前显示的字符或文本。
2e sw%3AMetricField%3ATP_NUM_POSITION%3AMF_INDENT 45 输入编号图标的左边缘与文字的左边缘之间的间隔。
29 sw%3AListBox%3ATP_NUM_POSITION%3ALB_ALIGN 117 设置编号符号的对齐方式。选择“左对齐”将编号符号的左边缘直接对齐在“对齐”位置。选择“右对齐”将编号符号的右边缘直接对齐在“对齐”位置。选择“居中对齐”将编号符号居中对齐在“对齐”位置。
33 cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_ALIGN 117 设置编号符号的对齐方式。选择“左对齐”将编号符号的左边缘直接对齐在“对齐”位置。选择“右对齐”将编号符号的右边缘直接对齐在“对齐”位置。选择“居中对齐”将编号符号居中对齐在“对齐”位置。
2e sw%3AMultiListBox%3ATP_NUM_POSITION%3ALB_LEVEL 1b 选择要修改的级别。
38 cui%3AMultiListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_LEVEL 1b 选择要修改的级别。
39 cui%3APushButton%3ARID_SVXPAGE_NUM_POSITION%3APB_STANDARD 2d 将缩进值和间距值重设为默认值。
35 cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_ALIGN_2 117 设置编号符号的对齐方式。选择“左对齐”将编号符号的左边缘直接对齐在“对齐”位置。选择“右对齐”将编号符号的右边缘直接对齐在“对齐”位置。选择“居中对齐”将编号符号居中对齐在“对齐”位置。
3c cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_BORDERDIST d2 输入要在页面左边距（或文本对象的左边缘）和编号符号左边缘之间留出的间距量。如果当前段落样式使用缩进，则会将此处输入的缩进量加到原有缩进量上。
3c cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_ALIGNED_AT 4b 输入到左边距的距离，编号符号将会在左边距中被对齐。
39 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_LISTTAB 66 如果您选择编号后跟一个制表位，您可以输入一个非负值作为制表位的位置。
3f cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_LABEL_FOLLOWED_BY 39 选择编号后跟的元素：制表位、空格或无。
37 cui%3ACheckBox%3ARID_SVXPAGE_NUM_POSITION%3ACB_RELATIVE 3f 相对于层次结构列表中的上一级来缩进当前级。
2f sw%3APushButton%3ATP_NUM_POSITION%3APB_STANDARD 2d 将缩进值和间距值重设为默认值。
2f sw%3AMetricField%3ATP_NUM_POSITION%3AMF_NUMDIST 2d 将缩进值和间距值重设为默认值。
3b cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_INDENT_AT 66 输入从左边距到带编号的段落中的所有行（第一行以下）的开始位置的距离。
38 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_INDENT 45 输入编号图标的左边缘与文字的左边缘之间的间隔。
39 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_NUMDIST 2d 将缩进值和间距值重设为默认值。
2d sw%3ACheckBox%3ATP_NUM_POSITION%3ACB_RELATIVE 3f 相对于层次结构列表中的上一级来缩进当前级。
32 sw%3AMetricField%3ATP_NUM_POSITION%3AMF_BORDERDIST d2 输入要在页面左边距（或文本对象的左边缘）和编号符号左边缘之间留出的间距量。如果当前段落样式使用缩进，则会将此处输入的缩进量加到原有缩进量上。
39 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_RENAME 36 打开一个可以给选定脚本改名的对话框。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_APPEND 5a 找到要添加到当前列表中的 $[officename] Basic 库，然后单击“打开”。
2f basctl%3AEdit%3ARID_DLG_NEWLIB%3ARID_ED_LIBNAME 21 输入新库或模块的名称。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_NEWLIB f 创建新库。
31 basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_NEWDLG 27 打开编辑器并创建新对话框。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_NEWLIB 24 将记录的宏保存在新库中。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_DEL 30 创建一个新宏，或删除所选定的宏。
37 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_EDIT 2d 打开操作系统默认的脚本编辑器。
36 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_RUN 4e 要执行脚本，先在列表中选择该脚本，然后单击“运行”。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_ORG 6c 打开宏管理器对话框，可以在其中添加、编辑或删除现有的宏模块、对话框和库。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_NEWMOD 27 将记录的宏保存在新模块中。
33 basctl%3AEdit%3ARID_MACROCHOOSER%3ARID_ED_MACRONAME 57 显示选定宏的名称。要创建或修改宏的名称，请在此处输入名称。
33 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_PASSWORD 2a 指定或编辑选定程序库的密码。
1d BASCTL_HID_BASICIDE_LIBS_TREE 3f 列出当前应用程序和所有打开文档现有的宏库。
31 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_EDIT 30 打开选定的宏或对话框以进行编辑。
20 BASCTL_HID_BASICIDE_MODULES_TREE 21 列出现有的宏和对话框。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_ASSIGN 7b 打开自定义对话框，在其中您能够将选定的宏指定给一个菜单命令、一个工具栏或一个事件。
18 BASCTL_HID_BASICIDE_LIBS 93 列出可以在其中打开或保存宏的库和模块。要将宏与特定文档保存在一起，请打开该文档，然后打开此对话框。
12 CUI_HID_SCRIPTSBOX 7e 从 "user"、"share" 或打开的文档中选择一个宏或脚本，要查看可用的宏或脚本，请双击某个条目。
2e basctl%3AListBox%3ARID_TP_LIBS%3ARID_LB_BASICS 39 选择包含要管理的宏库的应用程序或文档。
39 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_CREATE 12 创建新脚本，
1a BASCTL_HID_BASICIDE_MACROS 45 列出包含在从宏的来源列表里所选择的模块中的宏。
2f basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_EDIT 39 打开 $[officename] Basic 编辑器，修改选定库。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_NEWDLG 27 打开编辑器并创建新对话框。
2f basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_EDIT 57 启动 $[officename] Basic 编辑器并打开选定的宏或对话框以进行编辑。
12 .uno%3AMacroDialog 24 打开一个对话框以管理宏。
36 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_DEL 1e 提示删除选定的脚本。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_NEWMOD 2a 打开编辑器并创建一个新模块。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_RUN 1b 运行或保存当前宏。
34 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_EDIT 57 启动 $[officename] Basic 编辑器并打开选定的宏或对话框以进行编辑。
15 .uno%3AMacroSignature 54 为宏添加或从宏删除数字签名。也可以使用此对话框查看证书。
27 .uno%3AMacroOrganizer%3FTabId%3Ashort=1 3c 打开“宏管理器”的“对话框”选项卡页面。
f .uno%3ARunMacro 2a 打开可在其中启动宏的对话框。
14 .uno%3AStopRecording 12 停止记录宏。
14 .uno%3AMacroRecorder f 记录新宏。
10 SVX_HID_PASSWORD 21 使用密码保护选定的库。
30 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_NEW_PASSWD 1e 输入选定库的新密码。
30 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_OLD_PASSWD 21 输入选定库的当前密码。
33 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_REPEAT_PASSWD 24 再次输入选定库的新密码。
44 .uno%3AScriptOrganizer%3FScriptOrganizer.Language%3Astring=BeanShell 36 打开一个对话框，您可在其中管理脚本。
16 .uno%3AScriptOrganizer 54 打开一个带有对话框链接的子菜单，您可在其中管理宏和脚本。
45 .uno%3AScriptOrganizer%3FScriptOrganizer.Language%3Astring=JavaScript 36 打开一个对话框，您可在其中管理脚本。
2e basctl%3ACheckBox%3ARID_DLG_LIBS%3ARID_CB_REPL 1e 用当前库替换同名库。
2d basctl%3ACheckBox%3ARID_DLG_LIBS%3ARID_CB_REF 68 以只读文件的方式添加选定的库。每次启动 %PRODUCTNAME 时，都会重新加载该库。
20 BASCTL_HID_BASICIDE_LIBSDLG_TREE 51 输入要附加的库的名称或路径。还可以从列表中选择一个库。
16 .uno%3AConfigureDialog 51 自定义 $[officename] 菜单、快捷键、工具栏和为事件指定的宏。
12 .uno%3ALoadToolBox 51 自定义 $[officename] 菜单、快捷键、工具栏和为事件指定的宏。
2d cui%3AListBox%3ARID_SVXPAGE_MENUS%3ALB_SAVEIN 45 在您希望添加菜单的位置选择应用程序或打开文档。
19 CUI_HID_SELECTOR_COMMANDS 57 选择一个命令，然后单击添加或将该命令拖放到自定义对话框中。
35 cui%3AImageButton%3AMD_MENU_ORGANISER%3ABTN_MENU_DOWN 21 将所选项下移一个位置。
37 cui%3APushButton%3ARID_SVXPAGE_MENUS%3ABTN_ADD_COMMANDS 7b 打开“添加命令”对话框。选择一个命令，然后单击添加或将该命令拖放到自定义对话框中。
31 cui%3AMenuButton%3ARID_SVXPAGE_MENUS%3ABTN_CHANGE 24 使用其他命令打开子菜单。
1b CUI_HID_SELECTOR_CATEGORIES 57 选择一个命令，然后单击添加或将该命令拖放到自定义对话框中。
2c cui%3AListBox%3ARID_SVXPAGE_MENUS%3ALB_MENUS 27 选择要编辑的菜单和子菜单。
37 cui%3AMenuButton%3ARID_SVXPAGE_MENUS%3ABTN_CHANGE_ENTRY 2a 打开一个包含其他命令的菜单。
33 cui%3AImageButton%3AMD_MENU_ORGANISER%3ABTN_MENU_UP 21 将所选项上移一个位置。
2e cui%3APushButton%3ARID_SVXPAGE_MENUS%3ABTN_NEW 72 打开新建菜单对话框，您既可以在其中输入新建菜单的名称，也可以选择菜单的位置。
2f cui%3AEdit%3AMD_MENU_ORGANISER%3AEDIT_MENU_NAME 73 输入菜单的名称。要将名称中的一个字母指定为快捷键，请在该字母前输入波形号 (~)。
1f CUI_HID_SVX_CONFIG_MENU_LISTBOX 57 单击箭头按钮可将选定的菜单条目在菜单中上移或下移一个位置。
34 cui%3ARadioButton%3ARID_SVXPAGE_KEYBOARD%3ARB_OFFICE 3c 显示所有 $[officename] 应用程序共用的快捷键。
33 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_RESET 21 将修改值恢复为默认值。
38 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_ACC_REMOVE 33 删除选中的元素或不询问确认的元素。
1f CUI_HID_CONFIGGROUP_ACC_LISTBOX 5d 列出可用的功能分类。要指定样式的快捷方式，请打开“样式”类别。
1b CUI_HID_ACCELCONFIG_LISTBOX 9c 列出快捷键及相关命令。要指定或修改功能列表中选定命令的快捷键，请在此列表中单击某个快捷键，然后单击修改。
38 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_ACC_CHANGE 4b 将快捷键列表中所选组合键指定给功能列表中所选命令。
22 CUI_HID_CONFIGFUNCTION_ACC_LISTBOX bd 选择要为其指定快捷键的功能，在快捷键列表中单击某个组合键，然后单击修改。如果所选功能已具有快捷键，则该快捷键会显示在键列表中。
14 CUI_HID_CONFIG_ACCEL 5c 为$[officename] 命令或者 $[officename] 基本宏指定快捷键或编辑其快捷键。
32 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_LOAD 39 用以前保存的快捷键配置替换快捷键配置。
32 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_SAVE 36 保存当前的快捷键配置，以供日后加载。
34 cui%3ARadioButton%3ARID_SVXPAGE_KEYBOARD%3ARB_MODULE 3c 显示用于当前 $[officename] 应用程序的快捷键。
17 CUI_HID_SVX_NEW_TOOLBAR 66 打开“名称”对话框，可在其中输入新工具栏的名称和选择新工具栏的位置。
1a CUI_HID_SVX_MODIFY_TOOLBAR 35 "工具栏"按钮打开具有以下命令的子菜单
13 CUI_HID_SVX_SAVE_IN 30 选择要加载配置和保存配置的位置。
1f CUI_HID_SVX_MODIFY_TOOLBAR_ITEM 32 "修改"按钮打开具有以下命令的子菜单
1c CUI_HID_SVX_NEW_TOOLBAR_ITEM 81 打开“添加命令”对话框。选择任意一个命令，然后单击添加或将该命令拖放到自定义对话框中。
2e cui%3AImageButton%3ARID_SVXPAGE_MENUS%3ABTN_UP 24 在列表中上移选定的项目。
30 cui%3AImageButton%3ARID_SVXPAGE_MENUS%3ABTN_DOWN 24 在列表中下移选定的项目。
2f cui%3APushButton%3AMD_ICONSELECTOR%3ABTN_IMPORT b9 将新图标添加到图标列表中。您可以看到一个打开文件对话框，此对话框用于将选定的一个或多个图标导入到 %PRODUCTNAME 的内部图标目录中。
2f cui%3APushButton%3AMD_ICONSELECTOR%3ABTN_DELETE 4e 单击以删除列表中选定的图标。只能删除用户定义的图标。
31 cui%3APushButton%3ARID_SVXPAGE_EVENTS%3APB_ASSIGN 33 打开宏选择器将宏指定到选定的事件。
2e cui%3AListBox%3ARID_SVXPAGE_EVENTS%3ALB_SAVEIN 50 首先在当前文档或 %PRODUCTNAME 中选择要保存事件绑定的位置。
36 cui%3APushButton%3ARID_SVXPAGE_MACROASSIGN%3APB_DELETE 21 删除选定事件的宏指定。
31 cui%3APushButton%3ARID_SVXPAGE_EVENTS%3APB_DELETE 21 删除选定事件的宏指定。
12 .uno%3ATableEvents 51 为程序事件指派宏。被指派的宏在每次事件触发时自动运行。
1a CUI_HID_SVX_MACRO_LB_EVENT 90 大列表框列出事件和指定的宏。在保存于列表框中选择位置之后，在大列表框中选择事件。然后单击指定宏。
36 cui%3APushButton%3ARID_SVXPAGE_MACROASSIGN%3APB_ASSIGN 33 打开宏选择器将宏指定到选定的事件。
1a FILTER_HID_XML_FILTER_TEST 2b 打开具有选定文件名的对话框。_
1c FILTER_HID_XML_FILTER_DELETE 3f 在确认随后显示的对话框后，删除选定的文件。
1c .uno%3AOpenXMLFilterSettings 88 打开 XML 过滤器设置对话框，您可以在其中建立、编辑、删除和测试用于导入和导出 XML 文件的过滤器。
25 FILTER_HID_XML_FILTER_SETTINGS_DIALOG 24 显示此对话框的帮助页面。
1a FILTER_HID_XML_FILTER_OPEN 57 显示打开对话框，用于打开 XSLT 过滤器软件包 (*.jar) 中的过滤器。
1a FILTER_HID_XML_FILTER_SAVE 5c 显示另存为对话框，用于将选定文件保存为 XSLT 过滤器软件包 (*.jar)。
19 FILTER_HID_XML_FILTER_NEW 2d 打开具有新过滤器名称的对话框。
1b FILTER_HID_XML_FILTER_CLOSE 15 关闭此对话框。
1a FILTER_HID_XML_FILTER_LIST 45 选择一个或多个过滤器，然后单击其中的一个按钮。
1a FILTER_HID_XML_FILTER_EDIT 2a 打开具有选定文件名的对话框。
1f FILTER_HID_XML_FILTER_EXTENSION 9e 输入打开文件（在未指定过滤器的情况下）时要使用的文件扩展名。$[officename] 通过文件扩展名来确定要使用的过滤器。
24 FILTER_HID_XML_FILTER_INTERFACE_NAME 42 输入要在文件对话框的文件类型框中显示的名称。
1a FILTER_HID_XML_FILTER_NAME 50 输入您想要在 XML 过滤器设置对话框的列表框中显示的名称。
21 FILTER_HID_XML_FILTER_DESCRIPTION 1b 输入注释（可选）。
21 FILTER_HID_XML_FILTER_APPLICATION 33 选择要与过滤器配合使用的应用程序。
23 FILTER_HID_XML_FILTER_TABPAGE_BASIC 2f 输入或编辑 XML 过滤器的一般信息。
25 FILTER_HID_XML_FILTER_IMPORT_TEMPLATE 68 输入要用于导入的文档样式名称。在文档样式中，样式被定义为显示 XML 标记。
28 FILTER_HID_XML_FILTER_EXPORT_XSLT_BROWSE 1e 打开文件选择对话框。
19 FILTER_HID_XML_FILTER_DTD 58 如果需要，请输入要使用的 DTD（文档类型定义）公共或系统标志。
21 FILTER_HID_XML_FILTER_EXPORT_XSLT 4e 如果是导出筛选，请输入要用于导出的 XSLT 样式表文件名。
2c FILTER_HID_XML_FILTER_IMPORT_TEMPLATE_BROWSE 1e 打开文件选择对话框。
20 FILTER_HID_XML_FILTER_DTD_BROWSE 1e 打开文件选择对话框。
28 FILTER_HID_XML_FILTER_IMPORT_XSLT_BROWSE 1e 打开文件选择对话框。
1d FILTER_HID_XML_FILTER_DOCTYPE 1f 输入 XML 文件的 DOCTYPE。
21 FILTER_HID_XML_FILTER_IMPORT_XSLT 4e 如果是导入筛选，请输入要用于导入的 XSLT 样式表文件名。
28 FILTER_HID_XML_FILTER_TEST_IMPORT_BROWSE 56 打开文件选择对话框。使用当前 XML 导入筛选器打开选定的文件。
2f FILTER_HID_XML_FILTER_TEST_IMPORT_TEMPLATE_FILE 3f 显示在转换选项卡页面上输入的模板的文件名。
28 FILTER_HID_XML_FILTER_TEST_EXPORT_BROWSE 72 找到要应用 XML 导出筛选器的文件。转换文件的 XML 代码显示在XML 筛选器输出 窗口中。
28 FILTER_HID_XML_FILTER_TEST_IMPORT_RECENT 36 重新打开上次使用此对话框打开的文档。
2b FILTER_HID_XML_FILTER_TEST_IMPORT_XSLT_FILE 48 显示在转换选项卡页面上输入的 XSLT 筛选器的文件名。
2d FILTER_HID_XML_FILTER_TEST_IMPORT_RECENT_FILE 36 重新打开上次使用此对话框打开的文档。
2b FILTER_HID_XML_FILTER_TEST_EXPORT_XSLT_FILE 48 显示在转换选项卡页面上输入的 XSLT 过滤器的文件名。
29 FILTER_HID_XML_FILTER_TEST_EXPORT_CURRENT c8 位于最前面且符合 XML 筛选条件的打开文件将用于测试筛选器。当前 XML 导出筛选器用于转换文件，转换后获得的 XML 代码显示在 XML 筛选器输出窗口中。
30 FILTER_HID_XML_FILTER_TEST_IMPORT_DISPLAY_SOURCE 72 打开XML 筛选器输出窗口，其中显示了选定文档的 XML 源。该文档用于测试导入筛选器。
29 FILTER_HID_XML_FILTER_TEST_VALIDATE_OUPUT 23 列出 XML 筛选的测试结果。
23 FILTER_HID_XML_FILTER_OUTPUT_WINDOW 23 列出 XML 筛选的测试结果。
23 FILTER_HID_XML_SOURCE_FILE_VALIDATE 2c 验证 XML 筛选器输出窗口的内容。
20 CUI_HID_HANGULDLG_BUTTON_OPTIONS 25 打开韩文/汉字选项对话框。
43 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_SIMPLE_CONVERSION 2a 建议的字符将代替原来的字符。
1d CUI_HID_HANGULDLG_SUGGESTIONS 27 显示词典中的所有可用替换。
18 CUI_HID_SPELLDLG_SETWORD 15 显示当前选择。
48 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_BRACKETED 39 汉字部分将显示在韩文部分后面的括号中。
48 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_BRACKETED 39 韩文部分将显示在汉字部分后面的括号中。
1e CUI_HID_HANGULDLG_EDIT_NEWWORD 2a 显示词典中的第一个替换建议。
3a cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_HANGUL_ONLY 3f 选中此选项后，将只转换韩文，而不转换汉字。
22 CUI_HID_HANGULDLG_SUGGESTIONS_LIST 27 显示词典中的所有可用替换。
35 cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA%3APB_FIND 45 在词典中查找韩文输入，并将其替换为对应的汉字。
39 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_HANJA_ONLY 3f 选中此选项后，将只转换汉字，而不转换韩文。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_BELOW 3c 汉字部分将显示为韩文部分下方的拼音文字。
43 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_REPLACE_BY_CHARACTER 69 选中此选项，可以在选定文字的字符之间逐个移动。如果未选中，则替换全字。
1f CUI_HID_HANGULDLG_BUTTON_IGNORE 48 不修改当前选择。将选定下一个要转换的字词或字符。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_BELOW 3c 韩文部分将显示为汉字部分下方的拼音文字。
22 CUI_HID_HANGULDLG_SUGGESTIONS_GRID 27 显示词典中的所有可用替换。
1f CUI_HID_HANGULDLG_BUTTON_CHANGE 51 根据格式选项的设置，用建议的字符或字词替换选定的内容。
22 CUI_HID_HANGULDLG_BUTTON_IGNOREALL 57 不修改当前选择，并且每次检测到相同的选择时，都会自动跳过。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_ABOVE 3c 汉字部分将显示为韩文部分上方的拼音文字。
1e CUI_HID_HANGULDLG_BUTTON_CLOSE 15 关闭此对话框。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_ABOVE 3c 韩文部分将显示为汉字部分上方的拼音文字。
1c .uno%3AHangulHanjaConversion 39 将选定的朝文在朝文和汉字之间相互转换。
22 CUI_HID_HANGULDLG_BUTTON_CHANGEALL 90 根据格式选项的设置，用建议的字符或字词替换选择的内容。每次检测到相同的选择时，将自动替换该选择。
3c cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_NEW 42 打开“新建词典”对话框，以在其中创建新词典。
3d cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_IGNOREPOST 3c 在搜索词典时忽略朝鲜文字末尾的位置字符。
3d cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_EDIT 5d 打开编辑自定义词典对话框，在其中您能够编辑任何用户定义的词典。
3f cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_DELETE 21 删除选定的自定义词典。
20 CUI_HID_HANGULHANJA_OPT_DICTS_LB 81 列出所有用户定义的词典。选中您想使用的词典旁的复选框。清除您不想使用的词典旁的复选框。
3b cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_NEWDICT%3AED_DICTNAME 18 输入词典的名称。
44 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_AUTOREPLACEUNIQUE 39 自动替换只包含一个建议替换字词的字词。
44 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_SHOWRECENTLYFIRST 42 上次选择的替换建议显示为列表中的第一个条目。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_4 78 为在“原始”文本框中选择的条目键入建议的替换字词。替换字词最多可以包含八个字符。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_2 78 为在“原始”文本框中选择的条目键入建议的替换字词。替换字词最多可以包含八个字符。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_3 78 为在“原始”文本框中选择的条目键入建议的替换字词。替换字词最多可以包含八个字符。
40 cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3APB_HHE_DELETE 18 删除选定的条目。
37 cui%3AListBox%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3ALB_BOOK 24 选择要编辑的自定义词典。
3d cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3APB_HHE_NEW 2d 将当前的替换定义添加到词典中。
3c cui%3AComboBox%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3ALB_ORIGINAL 63 选择要编辑的当前词典中的条目。如果需要，也可以在此框中键入新条目。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_1 78 为在“原始”文本框中选择的条目键入建议的替换字词。替换字词最多可以包含八个字符。
12 .uno%3ASpellDialog 15 手动检查拼写。
10 .uno%3ANewWindow 3f 打开一个新窗口，其中显示了当前窗口的内容。
3e xmlsecurity%3APushButton%3ARID_XMLSECDLG_DIGSIG%3ABTN_VIEWCERT 1e 打开查看证书对话框。
33 padmin%3APushButton%3ARID_PADIALOG%3ARID_PA_BTN_DEL 21 从列表中删除选定的源。
31 sfx2%3APushButton%3ATP_DOCINFODOC%3ABTN_SIGNATURE 57 此对话框可用于为文档添加和删除数字签名，也可用于查看证书。
33 padmin%3APushButton%3ARID_PADIALOG%3ARID_PA_BTN_STD 1e 打开选择证书对话框。
10 .uno%3ASignature 57 此对话框可用于为文档添加和删除数字签名，也可用于查看证书。
29 XMLSECURITY_HID_XMLSEC_CTRL_SIGNATURESDLG 24 列出当前文档的数字签名。
38 desktop%3ACheckBox%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_ALL 8d 默认情况下，只有可下载的扩展才会显示在对话框中。选中显示所有更新还可以显示其它扩展和错误信息。
46 desktop%3AMultiLineEdit%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_DESCRIPTIONS 8a 检查更新时您将看到一个进度指示器。请等待对话框中显示的某些信息，或单击“取消”中止更新检查。
21 DESKTOP_HID_DEPLOYMENT_GUI_UPDATE c9 在扩展管理器中单击检查更新按钮来检查已安装扩展的联机更新。如果只为选定的扩展检查联机更新，请单击鼠标右键打开上下文菜单，然后选择更新。
28 DESKTOP_HID_DEPLOYMENT_GUI_UPDATEINSTALL c9 在扩展管理器中单击检查更新按钮来检查已安装扩展的联机更新。如果只为选定的扩展检查联机更新，请单击鼠标右键打开上下文菜单，然后选择更新。
39 desktop%3APushButton%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_OK 4e 当您单击“安装”按钮时，将显示“下载和安装”对话框。
11 .uno%3AInsertZWSP bb 在字词中插入不可见的空格，一旦该空格成为某一行的最后一个字符，它将插入一个换行符。启用复杂文本版式 (Complex Text Layout, CTL) 后可用。
10 .uno%3AInsertRLM 94 插入文本方向标记，该标记将影响位于其后的所有文本的方向。启用复杂文本版式 (Complex Text Layout, CTL) 后可用。
17 .uno%3AInsertHardHyphen 4b 插入连字符，该连字符可使边缘字符在换行处不被断开。
10 .uno%3AInsertLRM 94 插入文本方向标记，该标记将影响位于其后的所有文本的方向。启用复杂文本版式 (Complex Text Layout, CTL) 后可用。
17 .uno%3AInsertSoftHyphen 93 在字词中插入不可见的连字符，一旦该连字符成为某一行的最后一个字符，它将会显示出来并创建一个换行符。
1d .uno%3AInsertNonBreakingSpace 45 插入空格，该空格可使边缘字符在换行处不被断开。
19 .uno%3AFormattingMarkMenu 49 打开子菜单以插入特殊格式标记。为更多命令启用 CTL。
13 .uno%3AInsertZWNBSP 94 在字词中插入不可见的空格，该空格可使字词在行尾不被断开。启用复杂文本版式 (Complex Text Layout, CTL) 后可用。
16 SVX_HID_GALLERY_WINDOW 4e 要插入图片库对象，请选择该对象，然后将其拖到文档中。
e .uno%3AGallery 51 打开图片库，可以在其中选择要插入到文档中的图形和声音。
19 SVX_HID_GALLERY_THEMELIST 33 单击主题可查看与该主题相关的对象。
18 SVX_HID_GALLERY_NEWTHEME 4e 将新主题添加到图片库中，并可选择加入到该主题的文件。
18 SVX_HID_GALLERY_ICONVIEW 2a 以图标的形式显示图片库内容。
18 SVX_HID_GALLERY_LISTVIEW 4b 将图片库的内容显示为小图标，并显示标题和路径信息。
3f cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_TAKE 27 将选定文件添加到当前主题。
42 cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_TAKEALL 36 将列表中的所有文件添加到当前主题中。
40 cui%3ACheckBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ACBX_PREVIEW 27 显示或隐藏选定文件的预览。
41 cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_SEARCH 3f 找到包含要添加的文件的目录，然后单击确定。
41 cui%3AComboBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ACBB_FILETYPE 21 选择要添加的文件类型。
42 cui%3AMultiListBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ALBX_FOUND 81 列出可用文件。选择要添加的文件，然后单击添加。要添加列表中的所有文件，请单击全部添加。
f .uno%3AGridMenu 1e 设置网格的显示属性。
14 .uno%3ASnapLinesMenu 21 指定辅助线的显示选项。
14 .uno%3AAVMediaPlayer 84 打开“媒体播放器”窗口，您能够在其中预览影片和声音文件，以及将这些文件插入到当前文档中。
14 .uno%3AInsertAVMedia 2a 在文档中插入视频或声音文件。
3b cui%3APushButton%3ARID_SVXPAGE_ONLINEUPDATE%3APB_CHANGEPATH 2d 单击选择一个文件夹以下载文件。
3b cui%3ACheckBox%3ARID_SVXPAGE_ONLINEUPDATE%3ACB_AUTODOWNLOAD 84 标记以启用自动检查更新。选择“选项”对话框中的 %PRODUCTNAME -“联机更新”禁用或启用这项功能。
36 desktop%3ACheckBox%3ATP_UPDATE_CHECK%3ACB_UPDATE_CHECK 27 您可以手动或自动检查更新。
22 EXTENSIONS_HID_CHECK_FOR_UPD_PAUSE 39 暂停下载。稍后点击“继续”以继续下载。
25 EXTENSIONS_HID_CHECK_FOR_UPD_DOWNLOAD 99 下载并将更新文件保存到桌面或者您选择的文件夹。在“选项”对话框中选择 %PRODUCTNAME -“联机更新”中的文件夹。
24 EXTENSIONS_HID_CHECK_FOR_UPD_INSTALL 1b 安装已下载的更新。
26 EXTENSIONS_HID_CHECK_FOR_UPD_DOWNLOAD2 99 下载并将更新文件保存到桌面或者您选择的文件夹。在“选项”对话框中选择 %PRODUCTNAME -“联机更新”中的文件夹。
20 EXTENSIONS_HID_CHECK_FOR_UPD_DLG b6 检查 %PRODUCTNAME 版本的可用更新。如果新版本可用，您可以选择下载更新。下载之后，如果您具有安装目录的写入权限，您可以安装更新。
23 EXTENSIONS_HID_CHECK_FOR_UPD_CANCEL 30 终止下载并删除不完整的下载文件。
21 SFX2_HID_DLG_CHECKFORONLINEUPDATE b6 检查 %PRODUCTNAME 版本的可用更新。如果新版本可用，您可以选择下载更新。下载之后，如果您具有安装目录的写入权限，您可以安装更新。
23 EXTENSIONS_HID_CHECK_FOR_UPD_RESUME 18 继续暂停的下载。
2d DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_DISABLE 1b 启用或禁用该扩展。
36 desktop%3AMultiLineEdit%3ARID_DLG_LICENSE%3AML_LICENSE 72 读取许可证。必要时可单击“向下滚动”按钮向下滚动。单击“接受”继续安装扩展。
39 service%3Acom.sun.star.deployment.ui.PackageManagerDialog 5c ”扩展管理器“可以添加、删除、禁用、启用和更新 %PRODUCTNAME 扩展。
27 DESKTOP_HID_PACKAGE_MANAGER_TREELISTBOX 6c 选择要删除、启用或禁用的扩展。对于一些扩展，您也可以打开”选项“对话框。
2c DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_REMOVE 36 选择要删除的扩展，然后单击“删除”。
1b DESKTOP_HID_PACKAGE_MANAGER 5c ”扩展管理器“可以添加、删除、禁用、启用和更新 %PRODUCTNAME 扩展。
2d DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_OPTIONS 54 选择一个安装扩展，然后单击以打开此扩展的“选项”对话框。
25 DESKTOP_HID_PACKAGE_MANAGER_MENU_ITEM 5c ”扩展管理器“可以添加、删除、禁用、启用和更新 %PRODUCTNAME 扩展。
38 desktop%3APushButton%3ARID_DLG_LICENSE%3APB_LICENSE_DOWN 72 读取许可证。必要时可单击“向下滚动”按钮向下滚动。单击“接受”继续安装扩展。
13 SC_HID_PASSWD_TABLE 27 键入密码。密码区分大小写。
10 sfx.Edit.2316.26 27 键入密码。密码区分大小写。
36 uui%3AEdit%3ADLG_UUI_PASSWORD_CRT%3AED_PASSWORD_REPEAT 15 重新输入密码。
33 uui%3AEdit%3ADLG_UUI_PASSWORD_CRT%3AED_PASSWORD_CRT 27 键入密码。密码区分大小写。
2b uui%3AEdit%3ADLG_UUI_PASSWORD%3AED_PASSWORD 27 键入密码。密码区分大小写。
24 HID_DLG_PASSWORD_TO_OPEN_MODIFY_MORE 33 单击显示或隐藏文档共享的密码选项。
2c sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_CONFIRM 15 重新输入密码。
2d sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_PASSWORD 27 键入密码。密码区分大小写。
11 SC_HID_PASSWD_DOC 15 重新输入密码。
10 sfx.Edit.2316.28 15 重新输入密码。
3e filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_HIGHRES 24 可以以高分辨率打印文档。
3e filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_BROWSER 79 使用 Internet 浏览器打开跨文档链接。Internet 浏览器必须能够处理超链接中指定的文件类型。
3c filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_VISIBLE 95 选择该选项将生成一个显示页面中的文本和图形的 PDF 文件，这些文本和图形经过缩放以适合阅读器窗口的宽度。
12 .uno%3AExportToPDF 40 将当前文件保存为可移植文档格式 (PDF)1.4 版本。
31 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_ALL 5d 导出所有定义的打印区域。如果没有定义打印区域，将导出整个文档。
3b filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_DEFAULT 5f 选择该选项将生成一个根据阅读器软件的版式设置显示页面的 PDF 文件。
3b filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_NONE 18 不允许打印文档。
2c filter%3AEdit%3ARID_PDF_TAB_GENER%3AED_PAGES 21 导出在框中键入的页面。
3a filter%3ACheckBox%3ARID_PDF_TAB_OPNFTR%3ACB_PGLY_FIRSTLEFT f9 选择该选项将生成一个以连续列形式并排显示页面的 PDF 文件。对于两个以上的页面，将在左侧显示第一页。必须通过“选项”对话框中的“语言设置”-“语言”来启用复杂文本版式支持。
3f FILTER_RADIOBUTTON_RID_PDF_TAB_VPREFER_RB_VISIBLEBOOKMARKLEVELS 62 选择此选项可在阅读器打开 PDF 文件时显示选定级别及其以上的书签级别。
37 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_SELECTION 15 导出当前选择。
3f filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_INSDEL 2a 只允许插入、删除和旋转页面。
43 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_ALLOWDUPLICATEFIELDNAMES 8f 允许为生成的 PDF 文件中的多个字段使用同一个字段名称。如果禁用，将使用生成的唯一名称导出字段名称。
3d FILTER_NUMERICFIELD_RID_PDF_TAB_OPNFTR_NUM_MAGNF_INITIAL_PAGE 47 选择此选项可在阅读器打开 PDF 文件时显示给定页面。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_EXP_BMRK_TO_DEST 124 PDF 文件中的书签（引用的目标）可以定义为矩形区域。另外，对于已命名对象的书签可以使用他们的名称进行定义。启用此复选框导出文档中对象的名称作为有效的书签目标。允许从其他文档中通过名称链接到这些对象。
35 filter%3AListBox%3ARID_PDF_TAB_GENER%3ALB_FORMSFORMAT 2f 选择从 PDF 文件中提交窗体的格式。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_CNV_OOO_DOCTOPDF c4 启用此复选框将引用其他 ODF 文件的 URL 转换为具有相同名称的 PDF 文件。在引用的 URL 中，扩展名 .odt、.odp、.ods、.odg 和 .odm 会被转换为扩展名 .pdf。
35 filter%3AMetricField%3ARID_PDF_TAB_GENER%3ANF_QUALITY 24 输入 JPEG 压缩的质量级别。
36 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTNOTES 40 选择将 Writer 和 Calc 文档的批注导出为 PDF 批注。
40 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_REDUCEIMAGERESOLUTION 4e 选择重采样或者通过减少每英寸的像素数而减小图像尺寸。
3c filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_DEFAULT cb 选择该选项将生成一个显示原始大小的页面内容的 PDF 文件。如果已将阅读器软件配置为在默认情况下使用某种显示比例，则会以该显示比例来显示页面。
3b filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTEMPTYPAGES 15c 如果打开，则会将自动插入的空白页导出到 pdf 文件。对 pdf 文件执行双面打印时，此方法为最佳选择。示例：在某本书中，章节段落样式被设置为始终以奇数页开始。如果前一章在奇数页结束，则 %PRODUCTNAME 会插入一个偶数空白页。此选项控制是否导出该偶数页。
41 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_FILLFORM 1e 只允许填充窗体字段。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_OPNFULL 7a 选择该选项将生成一个 PDF 文件，该文件在位于所有其他窗口前面的全屏阅读器窗口中显示。
3d filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EMBEDSTANDARDFONTS ea 通常情况下，这14种标准 Postscript 字体不会嵌入到 PDF 文件中，因为每种 PDF 阅读器软件已经包含了这些字体。启用该选项以嵌入安装在您系统上并且在文档中使用过的标准字体。
34 filter%3APushButton%3ARID_PDF_TAB_SECURITY%3ABTN_PWD 30 单击可打开用于输入密码的对话框。
3a filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTBOOKMARKS b5 选择导出 Writer 文档的书签作为 PDF 书签。全部章节段落（工具 - 章节编号）和您在源数据文档中指定超链接的目录条目均可以创建书签。
3b FILTER_RADIOBUTTON_RID_PDF_TAB_VPREFER_RB_ALLBOOKMARKLEVELS 4d 选择此选项可在阅读器打开 PDF 文件时显示所有书签级别。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_CNTRWIN 59 选择该选项将生成一个在屏幕中央的阅读器窗口中显示的 PDF 文件。
34 FILTER_METRICFIELD_RID_PDF_TAB_OPNFTR_NUM_MAGNF_ZOOM 3e 在阅读器打开 PDF 文件时选择给定的显示比例。
3e filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_OUTLINE 50 选择该选项将生成一个显示书签面板和页面内容的 PDF 文件。
3b filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_CONTFAC 8f 选择该选项将生成一个以连续列形式并排显示页面的 PDF 文件。对于两个以上的页面，将在右侧显示第一页。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_ENAB_RELLINKFSYS 89 启用此复选框在文件系统中导出 URL 到其他的文档（作为相对 URL）。请参见”帮助“中的"相对超链接"。
33 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_RANGE 21 导出在框中键入的页面。
39 filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_WIND 77 选择该选项将生成一个 PDF 文件，该文件显示经过缩放以完全适合阅读器窗口大小的页面。
39 filter%3ACheckBox%3ARID_PDF_TAB_SECURITY%3ACB_ENAB_ACCESS 36 选择此选项可启用辅助工具的文本访问。
34 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_TAGGEDPDF 3e 选择写入 PDF 标记。这会显著增加文件的大小。
3d filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_NONE 18 不允许更改内容。
38 filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_CONT 53 选择该选项将生成一个以连续垂直列形式显示页面的 PDF 文件。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVWINCTRL 48 选择该选项将在文档处于活动状态时隐藏阅读器控件。
3a FILTER_NUMERICFIELD_RID_PDF_TAB_VPREFER_NUM_BOOKMARKLEVELS 62 选择此选项可在阅读器打开 PDF 文件时显示选定级别及其以上的书签级别。
3d filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_LOWRES 5e 只能以低分辨率 (150 dpi) 打印文档。并非所有 PDF 阅读器都支持此设置。
3a filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_WIDTH 71 选择该选项将生成一个 PDF 文件，该文件显示经过缩放以适合阅读器窗口宽度的页面。
39 filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_DISPDOCTITLE 62 选择该选项将生成一个显示时在阅读器标题栏中显示文档标题的 PDF 文件。
42 filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_APPLICATION 9c 使用当前显示文档的 PDF 阅读器应用程序打开跨文档链接。PDF 阅读器应用程序必须能够处理超链接中指定的文件类型。
3e filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_TRANSITIONEFFECTS 47 选择将 Impress 幻灯片切换效果导出为相应的 PDF 效果。
40 filter%3AComboBox%3ARID_PDF_TAB_GENER%3ACO_REDUCEIMAGERESOLUTION 21 选择图像的目标分辨率。
43 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_ANY_NOCOPY 27 除提取页面外允许所有更改。
41 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_LOSSLESSCOMPRESSION 39 选择图像的无损压缩。所有像素均被保留。
33 FILTER_RADIOBUTTON_RID_PDF_TAB_OPNFTR_RB_MAGNF_ZOOM 3e 在阅读器打开 PDF 文件时选择给定的显示比例。
3e filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_DEFAULT 5c 从 PDF 文档到其他文档的链接将会按照操作系统中指定的方式被处理。
3b filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTFORMFIELDS 45 选择创建 PDF 表单。PDF 文档用户可以填写和打印它。
34 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_ADDSTREAM 2a 被导出的文档将被嵌入PDF文件。
40 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_COMMENT 27 只允许注释和填充窗体字段。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVTOOLBAR 4b 选择该选项将在文档处于活动状态时隐藏阅读器工具栏。
39 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_PDFA_1B_SELECT d0 转换为 PDF/A-1a 格式。该格式被定义为电子文档文件格式，以用于长期保存。源文档中使用过的所有字体都会被嵌入到生成的 PDF 文件中。PDF 标记将会被写入。
3a filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_SINGPG 41 选择该选项将生成一个每次显示一页的 PDF 文件。
38 filter%3ACheckBox%3ARID_PDF_TAB_SECURITY%3ACB_ENDAB_COPY 30 选择此选项可将内容复制到剪贴板。
3f filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_PAGEONLY 44 选择该选项将生成一个只显示页面内容的 PDF 文件。
3d filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_THUMBS 53 选择该选项将生成一个显示缩略图面板和页面内容的 PDF 文件。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVMENUBAR 4b 选择该选项将在文档处于活动状态时隐藏阅读器菜单栏。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_RESINIT 5f 选择以生成一个 PDF 文件，将在一个窗口中显示该文件的整个初始页面。
3d filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_JPEGCOMPRESSION ba 选择图像的 JPEG 压缩。使用高质量级别时，几乎可以保留所有像素。使用低质量级别时，将会丢失一些像素并导致失真，但可以使文件缩小。
17 .uno%3ASendMailDocAsPDF 9f 显示“导出为 PDF”对话框，将当前文档导出为可移植文档格式 (PDF)，然后打开电子邮件发送窗口，将 PDF 作为附件发送。
1b UUI_HID_XMLSECDLG_MACROWARN 5e 启用或禁用宏。选择“选项”对话框中的 %PRODUCTNAME - 安全以设置选项。
23 SFX2_HID_WARNING_SECURITY_HYPERLINK 5e 启用或禁用宏。选择“选项”对话框中的 %PRODUCTNAME - 安全以设置选项。
43 xmlsecurity%3APushButton%3ARID_XMLSECDLG_CERTCHOOSER%3ABTN_VIEWCERT 48 打开查看证书对话框，您能够在其中检查选定的证书。
3f xmlsecurity%3APushButton%3ARID_XMLSECTP_CERTPATH%3ABTN_VIEWCERT 48 打开查看证书对话框，您能够在其中检查选定的证书。
2c XMLSECURITY_HID_XMLSEC_CTRL_CHOOSESIGNATURES 39 选择用来对当前文档进行数字签名的证书。
e .uno%3AWebHtml 82 以 HTML 格式创建当前文档的临时副本，打开系统默认 Web 浏览器，并且在 Web 浏览器显示 HTML 文件。
30 svx%3AEdit%3ARID_SVXDLG_ADD_MODEL%3AED_INST_NAME f 输入名称。
16 SVX_HID_XFORMS_TOOLBOX 2c 指定当前 XForms 文档的数据结构。
18 .uno%3AShowDataNavigator 2c 指定当前 XForms 文档的数据结构。
22 SVX_HID_XFORMS_TOOLBOX_ITEM_REMOVE 3f 删除选定的项目（元素、属性、提交或绑定）。
1d SVX_HID_XFORMS_MODELS_MENUBTN 2c 添加、重命名和删除 XForms 模型。
1a SVX_HID_XFORMS_TAB_CONTROL 2c 指定当前 XForms 文档的数据结构。
20 SVX_HID_XFORMS_TOOLBOX_ITEM_EDIT 54 打开一个对话框以编辑选定项目（元素、属性、提交或绑定）。
27 SVX_HID_XFORMS_TOOLBOX_ITEM_ADD_ELEMENT 7e 打开一个对话框，以便将新项目（元素、属性、提交或绑定）作为当前项目的子项目进行添加。
22 SVX_HID_MN_XFORMS_INSTANCES_REMOVE 33 删除当前实例。无法删除上一个实例。
3c svx%3ACheckBox%3ARID_SVXDLG_ADD_MODEL%3ACB_MODIFIES_DOCUMENT e4 启用该功能后，当您更改了绑定到模块中数据的任何窗体控件后，文档状态将会被设置为“已修改”。如果未启用该功能，任何更改都不会使文档状态被设置为“已修改”。
1d SVX_HID_MN_XFORMS_MODELS_EDIT 1f 重命名选定 Xform 模型。
1c SVX_HID_MN_XFORMS_MODELS_ADD 4c 打开“添加模型”对话框，您能够在其中添加 XForm 模型。
1a SVX_HID_XFORMS_MODELS_LIST 23 选择要使用的 XForms 模型。
20 SVX_HID_XFORMS_INSTANCES_MENUBTN 42 此按钮具有用于添加、编辑或删除实例的子菜单。
1f SVX_HID_MN_XFORMS_MODELS_REMOVE 40 删除选定的 XForm 模型。无法删除最后一个模型。
29 SVX_HID_XFORMS_TOOLBOX_ITEM_ADD_ATTRIBUTE 7e 打开一个对话框，以便将新项目（元素、属性、提交或绑定）作为当前项目的子项目进行添加。
1f SVX_HID_MN_XFORMS_INSTANCES_ADD 3c 打开对话框，您可以在其中添加一个新实例。
1e SVX_HID_MN_XFORMS_SHOW_DETAILS 27 切换详细信息的显示和隐藏。
20 SVX_HID_MN_XFORMS_INSTANCES_EDIT 3c 打开对话框，您可以在其中修改当前的实例。
1f SVX_HID_XFORMS_TOOLBOX_ITEM_ADD 7e 打开一个对话框，以便将新项目（元素、属性、提交或绑定）作为当前项目的子项目进行添加。
19 SVX_HID_XFORMS_ITEMS_LIST 24 列出属于当前实例的项目。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_READONLY 18 声明项目已计算。
20 EXTENSIONS_HID_PROP_XSD_REQUIRED 2e 指定 XForm 中是否必须包括该项目。
2e svx%3AEdit%3ARID_SVXDLG_ADD_DATAITEM%3AED_NAME 18 输入项目的名称。
37 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_CALCULATE 18 声明项目已计算。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_READONLY 1b 将项目声明为只读。
20 EXTENSIONS_HID_PROP_XSD_RELEVANT 1b 将项目声明为相关。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_RELEVANT 1b 将项目声明为相关。
20 EXTENSIONS_HID_PROP_XSD_READONLY 1b 将项目声明为只读。
38 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_CONSTRAINT 1b 将项目声明为约束。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_REQUIRED 1b 将项目声明为相关。
35 svx%3AListBox%3ARID_SVXDLG_ADD_DATAITEM%3ALB_DATATYPE 2a 选择用于选定项目的数据类型。
3a svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_CONSTRAINT 1b 将项目声明为只读。
31 svx%3AEdit%3ARID_SVXDLG_ADD_DATAITEM%3AED_DEFAULT 21 输入选定项目的默认值。
23 EXTENSIONS_HID_PROP_XSD_CALCULATION 18 声明项目已计算。
22 EXTENSIONS_HID_PROP_XSD_CONSTRAINT 1b 将项目声明为约束。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_REQUIRED 2e 指定 XForm 中是否必须包括该项目。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_RELEVANT 1b 将项目声明为约束。
3d svx%3AMultiLineEdit%3ARID_SVXDLG_ADD_CONDITION%3AED_CONDITION f 输入条件。
40 svx%3APushButton%3ARID_SVXDLG_ADD_CONDITION%3APB_EDIT_NAMESPACES 69 打开“窗体命名空间”对话框，可以在该对话框中添加、编辑或删除命名空间。
40 svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_EDIT_NAMESPACE 1e 编辑选定的命名空间。
3f svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_ADD_NAMESPACE 27 在列表中添加新的命名空间。
42 svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_DELETE_NAMESPACE 1e 删除选定的命名空间。
21 SVX_HID_XFORMS_NAMESPACEITEM_LIST 2d 列出当前为窗体定义的命名空间。
23 EXTENSIONS_HID_PROP_XSD_WHITESPACES dc 指定在处理当前数据类型的字符串时，如何对空格进行处理。可能的值为“预留”、“替换”和“折叠”。该语义遵循 http://www.w3.org/TR/xmlschema-2/#rf-whiteSpace 上的定义。
21 EXTENSIONS_UID_PROP_ADD_DATA_TYPE b4 单击该按钮将打开一个对话框，可以在该对话框中输入新的用户定义数据类型的名称。新的数据类型将继承当前选定数据类型的所有面。
20 EXTENSIONS_HID_PROP_XSD_REQUIRED 2e 指定 XForm 中是否必须包含该项目。
25 EXTENSIONS_HID_PROP_XSD_MIN_EXCLUSIVE 24 指定值的下界（不包括）。
27 EXTENSIONS_HID_PROP_XSD_FRACTION_DIGITS 45 指定十进制数据类型的值可以具有的最大小数位数。
24 EXTENSIONS_HID_PROP_XSD_TOTAL_DIGITS 42 指定十进制数据类型的值可以具有的最大总位数。
24 EXTENSIONS_UID_PROP_REMOVE_DATA_TYPE 5a 选择用户定义的数据类型，单击该按钮可删除用户定义的数据类型。
20 EXTENSIONS_HID_PROP_XSD_RELEVANT 1b 将项目声明为相关。
22 EXTENSIONS_HID_PROP_XSD_MAX_LENGTH 2a 指定字符串包含的最大字符数。
1e EXTENSIONS_HID_PROP_XSD_LENGTH 24 指定字符串包含的字符数。
1f EXTENSIONS_HID_PROP_XSD_PATTERN 10c 指定正则表达式模式。按照数据类型进行验证的字符串必须符合此模式才能有效。正则表达式的 XSD 数据类型语法与 %PRODUCTNAME 中其余位置（例如在“查找和替换”对话框中）使用的正则表达式语法不同。
21 EXTENSIONS_HID_PROP_XSD_DATA_TYPE 4b 选择一个数据类型，将按照该数据类型对控件进行验证。
20 EXTENSIONS_HID_PROP_XSD_READONLY 1b 将项目声明为只读。
25 EXTENSIONS_HID_PROP_XSD_MAX_INCLUSIVE 21 指定值的上界（包括）。
20 EXTENSIONS_HID_PROP_BINDING_NAME b1 选择或输入绑定的名称。选择现有绑定的名称可以使该绑定与窗体控件关联。输入新名称可以创建新的绑定，并将其与窗体控件关联。
22 EXTENSIONS_HID_PROP_XML_DATA_MODEL 3f 从当前文档中所有模型的列表中选择一个模型。
25 EXTENSIONS_HID_PROP_XSD_MIN_INCLUSIVE 21 指定值的下界（包括）。
23 EXTENSIONS_HID_PROP_XSD_CALCULATION 1b 声明该项目已计算。
22 EXTENSIONS_HID_PROP_XSD_CONSTRAINT 1b 将项目声明为约束。
25 EXTENSIONS_HID_PROP_XSD_MAX_EXCLUSIVE 24 指定值的上界（不包括）。
23 EXTENSIONS_HID_PROP_BIND_EXPRESSION 79 输入要将控件模型绑定到的 DOM 节点。单击 "..." 按钮可显示用于输入 XPath 表达式的对话框。
13 .uno%3APrintDefault 60 单击直接打印文件图标将使用当前的默认打印机设置打印使用中的文档。
13 .uno%3AText_Marquee a8 在当前文档中插入水平方向的动画文本。拖动文本框，然后键入或粘贴文本。要指定动画效果，请选择格式 - 文本 - 文本动画。
a .uno%3APie 157 在当前文档中绘制由椭圆弧和两条半径线设置的已填色形状。要绘制椭圆扇形，请先将椭圆拖到所需大小，然后单击以修改第一条半径线。将鼠标指针移到要设置第二条半径线的位置，然后单击。不需要在椭圆上单击。要绘制圆扇形，请在拖动时按住 Shift 键。
17 .uno%3APolygon_Unfilled e4 绘制由一组直线段组成的线条。拖动以绘制线段，单击以定义线段的终点，然后再次拖动以绘制新的线段。双击以完成线条的绘制。要建立封闭的形状，请双击线条的起点。
f .uno%3ADrawText db 在当前文档中拖动的位置绘制一个文本框（其文本为水平方向）。在文档中的任意位置将文本框拖至所需大小，然后键入或粘贴文本。旋转文本框可以获得旋转文本。
10 .uno%3ACircleCut 145 在当前文档中绘制由圆弧和一条直径线设置的已填色形状。要绘制圆缺，请先将圆拖到所需大小，然后单击以设置直径线的起点。将鼠标指针移到要设置直径线终点的位置，然后单击。不需要在圆上单击。要绘制椭圆缺，请在拖动时按住 Shift 键。
e .uno%3AEllipse a9 在当前文档中拖动的位置绘制一个椭圆。在要绘制椭圆的位置单击，然后拖成所需的大小。要绘制圆，请在拖动时按住 Shift 键。
16 .uno%3AVerticalCaption 171 在当前文档中，从拖动的位置开始到纵向文本方向的矩形图例为止绘制一条线条。拖动图例的控点来更改图例的大小。要加入文本，请单击图例的边缘，然后键入或粘贴文本。要将矩形图例修改为圆角图例，请在指针变为手形时拖动最大的角控点。只有启用亚洲语言支持时才可用
12 .uno%3ADrawCaption 159 在当前文档中，从拖动的位置开始到矩形标注（其文本方向为水平方向）为止绘制一条线条。拖动标注的控点可以修改标注的大小。要添加文本，请单击标注的边缘，然后键入或粘贴文本。要将矩形标注修改为圆角标注，请在指针变为手形时拖动最大的角控点。
a .uno%3AArc 11b 在当前文档中绘制椭圆弧。要绘制椭圆弧，请先将椭圆拖动到所需大小，然后单击以设置椭圆弧的起点。将鼠标指针移到要设置终点的位置，然后单击。不需要在椭圆上单击。要绘制圆线，请在拖动时按住 Shift 键。
18 .uno%3AFreeline_Unfilled b7 在当前文档中拖动的位置绘制一条不规则线条。要结束线条，请释放鼠标按钮。要绘制封闭形状，请在线条起点附近的位置释放鼠标按钮。
b .uno%3ARect b5 在当前文档中拖动的位置绘制一个矩形。要绘制正方形，请在拖动时按住 Shift 键。在要放置矩形一角的位置单击，然后拖成所需的大小。
16 .uno%3ABezier_Unfilled 126 绘制平滑曲线。在选定的曲线起点位置单击、拖动、释放，然后将鼠标指针移到选定的曲线终点位置并单击。移动鼠标指针并再次单击将线条加入到曲线中。双击以完成曲线的绘制。要建立封闭的形状，请双击曲线的起点。
11 .uno%3AInsertDraw 78 通过单击可打开或关闭绘图栏，可以在其中将形状、线条、文字和图例添加到当前文档中。
13 .uno%3AVerticalText 12f 在当前文档中单击或拖动的位置绘制一个文本框（其文本为垂直方向）。在文档中的任意位置单击，然后键入或粘贴文本。还可以将光标移到要添加文本的位置，拖动文本框，然后键入或粘贴文本。仅在启用亚洲语言支持时可用。
b .uno%3ALine 7d 在当前文档中拖动的位置绘制一条直线。要将线条角度限制为 45 度，请在拖动时按住 Shift 键。
12 .uno%3AFileControl 2a 创建一个用于选择文件的按钮。
10 .uno%3AScrollBar 12 创建滚动条。
11 .uno%3ASpinbutton 1b 创建数值调节按钮。
16 .uno%3AFormDesignTools 1e 打开窗体设计工具栏。
13 .uno%3AMoreControls 1e 打开更多控件工具栏。
f .uno%3AComboBox 18 创建一个组合框。
e .uno%3AListBox 12 创建列表框。
17 .uno%3AAutoControlFocus ea 如果已启动自动控件焦点，则在打开文档时，第一个窗体控件将处于选中状态。如果未启动该按钮，打开文档后文本将被选中。根据指定的轮换顺序确定先选择哪一个窗体控件。
f .uno%3AGroupBox 3c 创建用于对多个控制进行可视化组合的框架。
13 .uno%3ANumericField 1b 创建一个数字字段。
13 .uno%3AImageControl 42 创建一个图像控件。它可用于从数据库添加图像。
c .uno%3ALabel 24 创建用于显示文本的字段。
f .uno%3ACombobox 18 创建一个组合框。
14 .uno%3ANavigationBar 12 创建导航栏。
b .uno%3AGrid 2d 创建表格控件以显示数据库表格。
12 .uno%3ARadioButton 1b 创建一个选项字段。
11 .uno%3APushbutton 26 创建一个 "Push" 类型的按钮。
15 .uno%3AFormattedField 21 创建一个格式化的字段。
b .uno%3AEdit 12 创建文字框。
f .uno%3ACheckbox 18 创建一个复选框。
f .uno%3AGroupbox 3c 创建用于对多个控制进行可视化组合的框架。
13 .uno%3APatternField 1b 创建一个掩码字段。
10 .uno%3ADateField 1b 创建一个日期字段。
11 .uno%3ASpinButton 1b 创建数值调节按钮。
14 .uno%3ACurrencyField 1b 创建一个货币字段。
10 .uno%3ATimeField 1b 创建一个时间字段。
12 .uno%3ARadiobutton 1b 创建一个选项字段。
f .uno%3ACheckBox 18 创建一个复选框。
d .uno%3AConfig 48 “窗体控件”工具栏包含创建交互式窗体所需的工具。
12 .uno%3AImagebutton 21 创建显示为图像的按钮。
18 .uno%3AConvertToCurrency 2a 将选定的控件转换成货币字段。
14 .uno%3AConvertToDate 2a 将选定的控件转换成日期字段。
18 .uno%3AConvertToCheckBox 27 将选定的控件转换成复选框。
19 .uno%3AConvertToFormatted 36 将选定的控制转换成已经格式化的字段。
16 .uno%3AConvertToButton 24 将选定的控件转换成按钮。
1c .uno%3AConvertToImageControl 2a 将选定的控件转换成图像控件。
1b .uno%3AConvertToFileControl 2a 将选定的控件转换成文件选择。
15 .uno%3AConvertToFixed 24 将选定的控件转换成标签。
17 .uno%3AConvertToNumeric 2a 将选定的控件转换成数字字段。
17 .uno%3AConvertToPattern 27 选定的控件转换为掩码字段。
14 .uno%3AConvertToTime 2a 将选定的控件转换成时间字段。
15 .uno%3AConvertToCombo 27 将选定的控件转换成组合框。
15 .uno%3AConvertToRadio 2a 选定的控件被转换成选项按钮。
14 .uno%3AConvertToEdit 27 将选定的控件转换成文字框。
18 .uno%3AConvertToImageBtn 2a 将选定的控件转换成图像按钮。
18 .uno%3AChangeControlType 5a 调用子菜单，可从中选择一种控件类型来替换在文档中选择的控件。
14 .uno%3AConvertToList 27 将选定的控件转换成列表框。
14 SVX_HID_FM_CHANGECOL 5a 打开一个子菜单选择数据字段，以替换表格控件中选定的数据字段。
12 SVX_HID_FM_HIDECOL 15 隐藏选定的列。
14 SVX_HID_FM_DELETECOL 1b 删除当前选定的列。
16 SVX_HID_FM_SHOWALLCOLS 30 如果您想显示所有列，请单击全部。
18 SVX_HID_FM_SHOWCOLS_MORE 1b 调用显示列对话框。
2f cui%3AListBox%3ARID_SVX_DLG_SHOWGRIDCOLUMNS%3A1 83 在显示列对话框中，您可以选择要显示的列。按住 Shift 或 Ctrl 键（Mac: Command 键）可以选择多个列。
14 SVX_HID_FM_INSERTCOL 48 调用一个子菜单选择数据字段，以将其添加表格控件。
13 SVX_HID_FM_SHOWCOLS 45 调用一个子菜单，在里面可以选出要再次显示的列。
20 EXTENSIONS_HID_FM_PROPDLG_TABCTR 33 打开用于编辑选定控件属性的对话框。
18 .uno%3AControlProperties 33 打开用于编辑选定控件属性的对话框。
1e EXTENSIONS_HID_PROP_MAXTEXTLEN 2d 定义用户可以输入的最多字符数。
23 EXTENSIONS_HID_PROP_SHOW_SCROLLBARS 33 将指定的滚动条类型添加到文本框中。
1b EXTENSIONS_HID_PROP_ENABLED 6c 如果某个控制字段的“使用中”属性为“是”，则窗体用户可以使用该控制字段。
21 EXTENSIONS_HID_PROP_LINEINCREMENT 4b 指定用户单击滚动条上的箭头图标时要增加或减少的值。
1b EXTENSIONS_HID_PROP_TIMEMIN 2d 指定用户可以输入的最小时间值。
1b EXTENSIONS_HID_PROP_HELPURL 7a 以 URL 拼写形式指定引用帮助文档的批标签，在调用批标签时会同时调用该控制字段的帮助。
1a EXTENSIONS_HID_PROP_BORDER 52 指定字段的边框类型（“无框”、“三维显示”、“平面”)。
20 EXTENSIONS_HID_PROP_EFFECTIVEMIN 54 这里可以为控件字段规定最小值，以防止用户输入小于它的值。
1e EXTENSIONS_HID_PROP_TIMEFORMAT 2a 可以定义所需的时间显示格式。
20 EXTENSIONS_HID_PROP_HIDDEN_VALUE 2d 您可以输入隐藏控件继承的数据。
19 EXTENSIONS_HID_PROP_WIDTH 18 定义控件的宽度。
1a EXTENSIONS_HID_PROP_HEIGHT 18 定义控件的高度。
20 EXTENSIONS_HID_PROP_DEFAULT_DATE 15 设置默认日期。
1e EXTENSIONS_HID_PROP_BUTTONTYPE 39 “操作”属性决定激活按钮时发生的操作。
20 EXTENSIONS_HID_PROP_DEFAULT_TIME 15 设置默认时间。
1f EXTENSIONS_HID_PROP_BORDERCOLOR 48 指定“边框”属性设置为“平面”的控件的边框颜色。
21 EXTENSIONS_HID_PROP_SHOW_POSITION 3c 指定显示或隐藏所选导航栏控件中的定位项。
1d EXTENSIONS_HID_PROP_ECHO_CHAR 82 如果文字框用于输入密码，请输入显示字符的 ASCII 码。显示时此字符将代替用户键入的密码字符。
26 EXTENSIONS_HID_PROP_DEFAULT_SELECT_SEQ 30 指定列表框条目以标记为默认条目。
1e EXTENSIONS_HID_PROP_DATEFORMAT 2a 在此确定所需的日期读出格式。
1f EXTENSIONS_HID_PROP_ORIENTATION 3c 指定滚动条或数值调节钮的水平或垂直方向。
22 EXTENSIONS_HID_PROP_DEFAULT_BUTTON 44 默认按钮属性用于指定按 Return 键时将启动的按钮。
1c EXTENSIONS_HID_PROP_READONLY 48 确定控件字段是只读的（是），还是可编辑的（否）。
19 EXTENSIONS_HID_PROP_ALIGN 39 为控件上使用的文本或图形指定对齐选项。
1c EXTENSIONS_HID_PROP_HELPTEXT 5a 该选项用于输入帮助文本，这些文本将作为帮助提示显示在控件上。
1c EXTENSIONS_HID_PROP_DROPDOWN 48 指定组合框是可下拉的（是）还是不可下拉的（否）。
20 EXTENSIONS_HID_PROP_RECORDMARKER 51 指定是否在第一列中显示行标签，其中用箭头标记当前记录。
1b EXTENSIONS_HID_PROP_VSCROLL 33 将指定的滚动条类型添加到文本框中。
1f EXTENSIONS_HID_PROP_LITERALMASK 57 定义字符掩码。字符掩码含有初始值，并在表单下载后始终可见。
21 EXTENSIONS_HID_PROP_SPININCREMENT 42 确定每次单击数值调节钮时要增大和缩小的间隔。
20 EXTENSIONS_HID_PROP_TARGET_FRAME 51 为显示由“打开文档 /Web 页”操作打开的文档指定目标框架。
1b EXTENSIONS_HID_PROP_DATEMAX 48 指定一个日期，以限定用户输入的值不得超过此日期。
1d EXTENSIONS_HID_PROP_WORDBREAK 1b 跨多个行显示文本。
1f EXTENSIONS_HID_PROP_ANCHOR_TYPE 1e 定义控件的锁定位置。
23 EXTENSIONS_HID_PROP_SHOW_NAVIGATION 3c 指定显示或隐藏所选导航栏控件中的导航项。
19 EXTENSIONS_HID_PROP_LABEL 48 “标签”属性用于设置窗体中显示的控件字段的标签。
1b EXTENSIONS_HID_PROP_TABSTOP 44 制表位属性决定是否可以用 Tab 键来选择控件字段。
22 EXTENSIONS_HID_PROP_MULTISELECTION 2a 用于在列表框中选择多个项目。
24 EXTENSIONS_HID_PROP_DECIMAL_ACCURACY 2d 指定小数点右侧显示的数字位数。
1c EXTENSIONS_HID_PROP_ICONSIZE 3f 指定所选导航栏上的图标是小图标还是大图标。
20 EXTENSIONS_HID_PROP_VISUALEFFECT 4b 指定复选框和选项按钮显示为三维（默认值）还是平面。
1f EXTENSIONS_HID_PROP_VISIBLESIZE b1 在“数值单位”中指定滚动条翻阅块的大小。（“最大滚动值”减去“最小滚动数值”）的值/2 可得到大小为背景区域一半的翻阅块。
23 EXTENSIONS_HID_PROP_SHOW_FILTERSORT 4e 指定显示或隐藏所选“导航栏”控件中的筛选和排序项目。
22 EXTENSIONS_HID_PROP_CURRENCYSYMBOL 33 可以输入字符或字符串作为货币符号。
25 EXTENSIONS_HID_PROP_DEFAULT_SPINVALUE 21 设置控件字段的默认值。
1d EXTENSIONS_HID_PROP_POSITIONY 30 定义控件相对于锁定位置的 Y 位置。
20 EXTENSIONS_HID_PROP_DEFAULTVALUE 21 设置控件字段的默认值。
21 EXTENSIONS_HID_PROP_NAVIGATIONBAR 3f 指定是否显示该表格控件的下边框上的导航栏。
1e EXTENSIONS_HID_PROP_SCALEIMAGE 24 调整图像至适合控件大小。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MAX 24 指定滚动条控件的最大值。
1d EXTENSIONS_HID_PROP_PRINTABLE 3c 指定是否在文档的打印输出中显示控制字段。
24 EXTENSIONS_HID_PROP_CURRSYM_POSITION 57 指定在使用货币字段时，在数字之前或之后是否要显示货币符号。
23 EXTENSIONS_HID_PROP_SHOWTHOUSANDSEP 18 插入千位分隔符。
27 EXTENSIONS_HID_PROP_DEFAULT_SCROLLVALUE 1e 设置滚动条的默认值。
21 EXTENSIONS_HID_PROP_IMAGEPOSITION 39 为控件上使用的文本或图形指定对齐选项。
1d EXTENSIONS_HID_PROP_ROWHEIGHT 24 指定表格控件字段的行高。
20 EXTENSIONS_HID_PROP_REPEAT_DELAY 2d 用毫秒指定重复事件之间的延迟。
18 EXTENSIONS_HID_PROP_SPIN 6c “是”选项将控件字段转换为一个数值调节按钮，并在其中添加相应的箭头按钮。
24 EXTENSIONS_HID_PROP_EFFECTIVEDEFAULT 21 设置控件字段的默认值。
26 EXTENSIONS_HID_PROP_SHOW_RECORDACTIONS 3c 指定显示或隐藏所选导航栏控件中的操作项。
1c EXTENSIONS_HID_PROP_VALUEMIN 54 这里可以为控件字段规定最小值，以防止用户输入小于它的值。
1a EXTENSIONS_HID_PROP_REPEAT 69 指定在单击控件（如数值调节钮）并按住鼠标键时，该控件的操作是否会重复。
1f EXTENSIONS_HID_PROP_SYMBOLCOLOR 48 指定控件上的符号（例如，滚动条上的箭头）的颜色。
1d EXTENSIONS_HID_PROP_MULTILINE b5 可在控件字段中使用换行和格式化，例如文本框或标签。要手动输入换行，请按 Enter 键。选择“带有格式的多行”以输入带格式的文本。
20 EXTENSIONS_HID_PROP_AUTOCOMPLETE 2d 为组合框指定“自动填充”功能。
18 EXTENSIONS_HID_PROP_NAME 7e 在属性选项卡上，该选项指定控件字段的名称。在窗体属性选项卡上，该选项指定窗体的名称。
21 EXTENSIONS_HID_PROP_SPINVALUE_MIN 54 这里可以为控件字段规定最小值，以防止用户输入小于它的值。
1d EXTENSIONS_HID_PROP_POSITIONX 30 定义控件相对于锁定位置的 X 位置。
20 EXTENSIONS_HID_PROP_CONTROLLABEL 1b 指定控件标签的源。
17 EXTENSIONS_HID_PROP_TAG 36 指定控制字段的附加信息或描述性文字。
22 EXTENSIONS_HID_PROP_ENABLE_VISIBLE 57 定义控件是否在活动模式下可见。在设计模式下，控件始终可见。
22 EXTENSIONS_HID_PROP_LINEEND_FORMAT 54 对于文字字段，选择将文字写入数据库列时使用的行结尾代码。
20 EXTENSIONS_HID_PROP_EFFECTIVEMAX 51 为控制字段定义一个值，以限定用户输入的值不得超过此值。
20 EXTENSIONS_HID_PROP_STRICTFORMAT 51 如果启动了“检查格式”功能（是），则只接受允许的字符。
18 EXTENSIONS_HID_PROP_TEXT 2d 设置文本框或组合框的默认文本。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MIN 24 指定滚动条控件的最小值。
23 EXTENSIONS_HID_PROP_BACKGROUNDCOLOR 24 设置控件字段的背景颜色。
1b EXTENSIONS_HID_PROP_HSCROLL 33 将指定的滚动条类型添加到文本框中。
22 EXTENSIONS_HID_PROP_STRINGITEMLIST e5 定义了文档中可见的列表条目。打开此列表并键入文本。对新行使用 Shift+Enter 组合键。可以使用列表框和组合框定义文档中可见的列表条目。打开列表条目字段并键入文本。
1c EXTENSIONS_HID_PROP_TRISTATE 6a 指定复选框除了表示链接数据库的 TRUE 和 FALSE 值以外，是否也可以表示 ZERO 值。
1c EXTENSIONS_HID_PROP_VALUEMAX 51 为控制字段定义一个值，以限定用户输入的值不得超过此值。
1d EXTENSIONS_HID_PROP_IMAGE_URL 4e 图形属性用于指定要在按钮上显示的图形的路径和文件名。
20 EXTENSIONS_HID_PROP_FOCUSONCLICK 5a 如果将此选项设为“是”，在您单击该按钮时按钮开关将获得焦点。
1a EXTENSIONS_HID_PROP_TOGGLE 105 指定按钮开关是否充当切换按钮。如果将“切换”设置为“是”，当控件具有焦点后单击该按钮或按空格键，可以切换“已选定”或“未选定”控制状态。“已选定”状态的按钮显示为“已按下”。
1b EXTENSIONS_HID_PROP_TIMEMAX 48 指定一个时间，以限定用户输入的值不得超过此时间。
1d EXTENSIONS_HID_PROP_FORMATKEY 4a 指定控件的格式代码。单击 ... 按钮可以选择格式代码。
21 EXTENSIONS_HID_PROP_SPINVALUE_MAX 51 为控制字段定义一个值，以限定用户输入的值不得超过此值。
1d EXTENSIONS_HID_PROP_LINECOUNT 6c 指定下拉列表中显示的行数。该设置在“下拉”选项中选择了“是”选项后有效。
1c EXTENSIONS_HID_PROP_EDITMASK 69 定义输入掩码。通过指定字符代码，确定用户在该控制字段中可以输入的内容。
1e EXTENSIONS_HID_PROP_TARGET_URL 4e 指定在单击“打开文档 / Web 页”按钮时所打开的 URL 地址。
1c EXTENSIONS_HID_PROP_TABINDEX 56 轮换顺序属性确定当您按 Tab 键时，窗体中控件获得焦点的顺序。
29 EXTENSIONS_HID_PROP_HIDEINACTIVESELECTION 5a 指定当焦点不再在控件上时，控件上的文本选项是否保持选中状态。
1d EXTENSIONS_HID_PROP_VALUESTEP 24 指定数值调节钮的间隔值。
22 EXTENSIONS_HID_PROP_BLOCKINCREMENT 4b 指定用户在滚动条两端按钮单击时，要增加或减少的值。
22 EXTENSIONS_HID_PROP_WHEEL_BEHAVIOR 153 设置该值是否在用户滚动鼠标滚轮时发生更改。“从不”：该值不发生任何更改。“在获取焦点时”：（默认）当控件具有焦点，同时滚轮指向控件并滚动时，该值发生更改。“始终”：无论控件是否具有焦点，当滚轮指向控件并滚动时，该值都发生更改。
1b EXTENSIONS_HID_PROP_DATEMIN 2a 指定用户可以输入的最早日期。
1c EXTENSIONS_HID_PROP_TEXTTYPE b5 可在控件字段中使用换行和格式化，例如文本框或标签。要手动输入换行，请按 Enter 键。选择“带有格式的多行”以输入带格式的文本。
18 EXTENSIONS_HID_PROP_FONT 2a 为控件字段中的文本选择字体。
26 EXTENSIONS_HID_PROP_CELL_EXCHANGE_TYPE 48 选择列表框与电子表格中的链接单元格之间的链接模式
1e EXTENSIONS_HID_PROP_BOUND_CELL 75 指定一个到电子表格的链接单元格的引用。控件的有效状态或内容链接到单元格的内容。
1c EXTENSIONS_HID_PROP_REFVALUE c2 您能够输入 Web 表单的引用值，当发送表单时，该值将被提交到服务器。在数据库窗体中，该输入值将被写入到数据库字段中，并指定给控件字段。
1f EXTENSIONS_HID_PROP_BOUNDCOLUMN 82 使用索引指定链接到数据字段中的字段的表格字段或表格 SQL 查询。此属性的有效值为 1、2、3 等。
25 EXTENSIONS_HID_PROP_UNCHECKEDREFVALUE 105 电子表格中的复选框和单选按钮可绑定到当前文档中的单元格。如果启用该控件，在“引用值（开）”中输入的值将被复制到单元格。如果禁用该控件，“引用值（关）”中的值将被复制到单元格。
1e EXTENSIONS_HID_PROP_LISTSOURCE 8d 对于数据库窗体，为窗体元素的列表内容指定数据源。此字段也可用来为未连接数据库的文档定义值列表。
23 EXTENSIONS_HID_PROP_LIST_CELL_RANGE 51 输入一个包含电子表格中列表框或组合框条目的单元格区域。
21 EXTENSIONS_HID_PROP_CONTROLSOURCE 2d 指定控件引用的数据源表格字段。
21 EXTENSIONS_HID_PROP_EMPTY_IS_NULL 105 定义如何处理输入的空字符串。如果将此属性设置为“是”，则输入的长度为零的字符串将被视为 NULL 值。如果将此属性设置为“否”，则任何输入的字符串都被视为其本来的样子，不做任何转换。
22 EXTENSIONS_HID_PROP_LISTSOURCETYPE 36 确定填充列表框和组合框中列表的数据。
22 EXTENSIONS_HID_PROP_FILTERPROPOSAL ea 设计窗体时，可以在相应的属性对话框中数据选项卡中，为每个文本框设置“筛选器建议”属性。这样，在筛选模式下进行搜索时，可以从这些字段含有的所有信息中进行选择。
1c EXTENSIONS_HID_EVT_FOCUSLOST 42 当控件字段失去焦点时，发生在偏离目标时事件。
22 EXTENSIONS_HID_EVT_ACTIONPERFORMED 36 当启动某个操作时，发生执行操作事件。
1b EXTENSIONS_HID_EVT_KEYTYPED 4b 如果在控件具有焦点时按任意键，则发生按住按键事件。
1e EXTENSIONS_HID_EVT_TEXTCHANGED 4b 在对输入字段输入或修改文字时，发生文字已修改事件。
29 EXTENSIONS_HID_EVT_APPROVEACTIONPERFORMED 42 通过单击控件来触发某个操作之前，发生此事件。
1e EXTENSIONS_HID_EVT_MOUSEEXITED 45 当鼠标位于控件字段之外时，发生鼠标在外部事件。
1d EXTENSIONS_HID_EVT_MOUSEMOVED 36 当鼠标移过控件时，发生鼠标移动事件。
1f EXTENSIONS_HID_EVT_MOUSEPRESSED 5a 当鼠标指针位于控件之上并按下鼠标键时，发生已按下鼠标键事件。
1f EXTENSIONS_HID_EVT_MOUSEDRAGGED 5d 当按住某个键并同时拖动鼠标时，发生按住按键的同时移动鼠标事件。
18 EXTENSIONS_HID_EVT_KEYUP 4e 如果在控件具有焦点时释放任意键，则发生松开按键事件。
1a EXTENSIONS_HID_EVT_CHANGED 63 当控件失去焦点且控件的内容因为失去焦点而被修改时，发生已修改事件。
20 EXTENSIONS_HID_EVT_MOUSERELEASED 5a 当鼠标指针位于控件之上并松开鼠标键时，发生已释放鼠标键事件。
1f EXTENSIONS_HID_EVT_MOUSEENTERED 42 当鼠标位于控件字段中时，发生鼠标在内部事件。
1e EXTENSIONS_HID_EVT_FOCUSGAINED 3c 当控件字段获得焦点时，发生在瞄准时事件。
23 EXTENSIONS_HID_EVT_ITEMSTATECHANGED 4b 若控件字段的状态已更改，则发生项目状态已更改事件。
15 .uno%3AFormProperties 57 在此对话框中，可以为整个窗体指定数据源、事件以及其他属性。
23 EXTENSIONS_HID_PROP_SUBMIT_ENCODING 24 指定数据传送的编码类型。
21 EXTENSIONS_HID_PROP_SUBMIT_METHOD 36 指定用于传送已完成的窗体信息的方法。
1e EXTENSIONS_HID_EVT_POSITIONING 42 修改当前记录指针之前，会发生记录修改前事件。
23 EXTENSIONS_HID_EVT_APPROVEPARAMETER 57 当要加载的窗体中含有必须填写的参数时，会发生填入参数事件。
1b EXTENSIONS_HID_EVT_UNLOADED 60 在卸载窗体之后，即窗体与其数据源分离之后，会立即发生卸载时事件。
1c EXTENSIONS_HID_EVT_ROWCHANGE 42 修改当前记录后，会立即发生记录操作之后事件。
1b EXTENSIONS_HID_EVT_RELOADED 3f 窗体重新加载后，会立即发生重新加载时事件。
1b EXTENSIONS_HID_EVT_RESETTED 30 重设窗体后，会发生重设之后事件。
1d EXTENSIONS_HID_EVT_POSITIONED 48 修改当前记录指针之后，会立即发生记录修改后事件。
23 EXTENSIONS_HID_EVT_APPROVEROWCHANGE 42 在修改当前记录之前，会发生记录操作之前事件。
19 EXTENSIONS_HID_EVT_LOADED 33 窗体加载后，会立即发生加载时事件。
22 EXTENSIONS_HID_EVT_APPROVERESETTED 36 在重设窗体之前，会发生重设之前事件。
1f EXTENSIONS_HID_EVT_ERROROCCURED 57 如果在访问数据源的过程中出现错误，将激活发生一个错误事件。
1e EXTENSIONS_HID_EVT_AFTERUPDATE 5a 在用户更改的控件内容写入到数据源之后，会发生“更新后”事件。
20 EXTENSIONS_HID_EVT_CONFIRMDELETE 48 一旦数据从窗体中删除，便会立即发生确认删除事件。
1c EXTENSIONS_HID_EVT_RELOADING 42 在重新加载窗体之前，会发生重新加载之前事件。
1c EXTENSIONS_HID_EVT_SUBMITTED 3c 在发送窗体数据之前，会发生提交之前事件。
1c EXTENSIONS_HID_EVT_UNLOADING 5d 在卸载窗体之前，即窗体与其数据源分离之前，会发生卸载之前事件。
1f EXTENSIONS_HID_PROP_ALLOW_EDITS 21 指定是否可以修改数据。
25 EXTENSIONS_HID_PROP_ESCAPE_PROCESSING 34 指定是否由 %PRODUCTNAME 来分析 SQL 语句。
23 EXTENSIONS_HID_PROP_ALLOW_ADDITIONS 21 指定是否可以添加数据。
24 EXTENSIONS_HID_PROP_CURSORSOURCETYPE 74 定义是否使用现有的数据库表格或查询作为数据源，或者是否基于 SQL 指令来生成窗体。
20 EXTENSIONS_HID_PROP_MASTERFIELDS 66 如果要创建子窗体，请输入母窗体的数据字段，以确保母窗体与子窗体同步。
23 EXTENSIONS_HID_PROP_ALLOW_DELETIONS 21 确定是否可以删除数据。
1d EXTENSIONS_HID_PROP_DATAENTRY 5d 定义表单只允许添加新数据（是），还是也允许添加其他属性（否）。
20 EXTENSIONS_HID_PROP_CURSORSOURCE e6 确定用于窗体的内容。内容可以是现有表格或查询（以前在数据库中创建的表格或查询），也可以利用 SQL 语句来定义。输入内容源之前，需要在内容类型中定义正确的类型。
1e EXTENSIONS_HID_PROP_DATASOURCE 21 定义窗体引用的数据源。
1f EXTENSIONS_HID_PROP_SLAVEFIELDS 5a 如果要创建子窗体，请输入用于存储父窗体字段中可能数值的变量。
19 EXTENSIONS_HID_PROP_CYCLE 29 指定使用 Tab 键时的浏览方式。
1e EXTENSIONS_HID_PROP_NAVIGATION 3c 指定是否可以使用底部窗体栏中的导航功能。
21 EXTENSIONS_HID_PROP_SORT_CRITERIA 69 指定窗体数据的排序条件。排序条件规范遵循 SQL 规则，而不使用 ORDER BY 子句。
10 .uno%3ATabDialog 62 在轮换顺序对话框中，可以修改用户按 Tab 键时控件字段获得焦点的顺序。
15 HID_TABORDER_CONTROLS 62 列出窗体中的所有控件。可以用 Tab 键按指定顺序从上到下选择这些控件。
f .uno%3AAddField 5d 打开一个窗口，在此您可以选择一个数据库字段添加到表单或报表中。
11 SVX_HID_FIELD_SEL 75 字段选择窗口列出了在表格属性中指定为数据源的表格（或查询）中的所有数据库字段。
1e .uno%3ASwitchControlDesignMode a8 启动或关闭“设计”模式。此功能用于在设计模式与“用户”模式之间快速切换。启动时编辑窗体控件，关闭时使用窗体控件。
1d .uno%3ASwitchXFormsDesignMode a8 启动或关闭“设计”模式。此功能用于在设计模式与“用户”模式之间快速切换。启动时编辑窗体控件，关闭时使用窗体控件。
11 SVX_HID_FM_DELETE 15 删除选定条目。
13 SVX_HID_FM_NEW_FORM 1e 在文档中建立新表单。
1a .uno%3AShowPropertyBrowser 27 启动选定条目的属性对话框。
18 SVX_HID_FM_RENAME_OBJECT 1b 重命名选定的对象。
15 SVX_HID_FM_NEW_HIDDEN a8 在选定的表单中建立隐藏的控件。该控件不会显示在屏幕上，因此用户无法看到。隐藏的控件用于包含与表单一同传送的数据。
15 .uno%3AShowFmExplorer 7e 打开窗体导航。在窗体导航中，显示了当前文档的所有窗体和子窗体以及它们各自含有的控件。
e SVX_HID_FM_NEW 6c 将新元素添加到窗体中。只有在窗体导航中选定某个窗体后，才可调用添加功能。
16 SVX_HID_FORM_NAVIGATOR 72 窗体导航中包含一个列表，其中列出了所有已创建的（逻辑）窗体及其相应控件字段。
13 .uno%3AOpenReadOnly 33 用设计模式打开窗体，以便进行编辑。
11 .uno%3AUseWizards 39 指定在插入新控件时，是否自动启动向导。
15 .uno%3ADesignerDialog 60 指定显示或是隐藏“样式和格式”窗口，可在此窗口中指定和管理样式。
15 SVX_HID_STYLE_LISTBOX 42 用于为当前段落、选定段落或选定对象指定样式。
16 .uno%3AStyleApplyState 42 用于为当前段落、选定段落或选定对象指定样式。
11 .uno%3AStyleApply 42 用于为当前段落、选定段落或选定对象指定样式。
13 .uno%3ACharFontName 4b 允许您从列表中选择字体名称，或者直接输入字体名称。
11 .uno%3AFontHeight 5a 可以从列表中选择各种字体大小，也可以手动输入所需的字体大小。
1f .uno%3ATextdirectionLeftToRight 1e 指定水平的文本方向。
1f .uno%3ATextdirectionTopToBottom 1e 指定垂直的文本方向。
16 .uno%3ADecrementIndent 81 单击减少缩进图标减小当前段落或单元格内容的左缩进，并将其设置到前一个默认制表符的位置。
16 .uno%3AIncrementIndent 87 单击“增加缩进”图标增加当前段落或单元格内容的左缩进，并将其设置到后一个默认制表符的位置。
10 .uno%3ABackColor 11d 将当前的突出显示颜色应用于所选文本的背景。如果未选择任何文本，请单击突出显示图标，选择要突出显示的文本，然后再次单击突出显示图标。要修改突出显示的颜色，单击突出显示图标，然后单击所需的颜色。
16 .uno%3ABackgroundColor 84 点击打开工具栏，可以在工具栏中为段落设置背景颜色。颜色被应用到当前段落或所选定的段落上。
18 .uno%3AParaspaceIncrease 48 单击增大间隔图标可以增加选定段落之上的段落间隔。
18 .uno%3AParaspaceDecrease 48 单击缩小间距图标可以缩小选定段落上方的段落间距。
15 .uno%3ASetBorderStyle 69 单击边框图标可打开边框工具栏，在其中您可以修改工作表区域或对象的边框。
10 .uno%3ALineStyle 60 单击此图标可以打开线条样式工具栏，您能够在其中修改边框线条样式。
15 .uno%3AFrameLineColor 63 单击框线颜色图标打开边框颜色工具栏，通过它可以修改对象边框的颜色。
17 .uno%3AToggleAnchorType 2d 允许您在锁定选项之间进行切换。
14 .uno%3AOptimizeTable 42 打开一个包含可优化表格中行和列功能的工具栏。
13 .uno%3ALineEndStyle 60 打开线条箭头工具栏。可以使用显示的符号来定义选定直线的尾端样式。
1d .uno%3AToggleObjectRotateMode 18 旋转选定的对象。
12 .uno%3AObjectAlign 24 修改所选对象的对齐方式。
15 .uno%3ADecrementLevel 4e 在编号或项目符号的层次结构中，将选定的段落下移一级。
13 .uno%3AOutlineRight 4e 在编号或项目符号的层次结构中，将选定的段落下移一级。
12 .uno%3AOutlineLeft 4e 在编号或项目符号的分层结构中，将选定的段落上移一级。
15 .uno%3AIncrementLevel 4e 在编号或项目符号的分层结构中，将选定的段落上移一级。
d .uno%3AMoveUp 30 将选定段落移到上一个段落的前面。
10 .uno%3AOutlineUp 30 将选定段落移到上一个段落的前面。
12 .uno%3AOutlineDown 30 将选定段落移到下一个段落的后面。
f .uno%3AMoveDown 30 将选定段落移到下一个段落的后面。
14 .uno%3ADefaultBullet 63 为选定的段落指定项目符号，或者从采用了项目符号的段落删除项目符号。
e .uno%3AOpenUrl 9f 通过指定的 URL 地址加载文档。您可以输入新的 URL 地址、编辑 URL 地址、或者从列表中选择。显示当前文档的绝对路径。
a SID_RELOAD 2d 用上次保存的版本替换当前文档。
d .uno%3AReload 2d 用上次保存的版本替换当前文档。
e .uno%3AEditDoc 33 使您可以编辑只读文档或数据库表格。
21 SFX2_HID_HELP_TEXT_SELECTION_MODE ba 您可以在只读文本文档或帮助中启用选择光标。选择编辑 - 选择文本，或者打开只读文档的上下文菜单并选择选择文本。选择光标不会闪动。
15 .uno%3ASelectTextMode ba 您可以在只读文本文档或帮助中启用选择光标。选择编辑 - 选择文本，或者打开只读文档的上下文菜单并选择选择文本。选择光标不会闪动。
11 .uno%3ADSBEditDoc 36 打开或关闭当前数据库表格的编辑模式。
f SID_BROWSE_STOP 82 单击可中断当前加载进程，按住 Ctrl 键并单击（Mac: Command 键并单击）可中断所有正在加载的进程。
b .uno%3AStop 82 单击可中断当前加载进程，按住 Ctrl 键并单击（Mac: Command 键并单击）可中断所有正在加载的进程。
18 .uno%3AExportDirectToPDF 46 将当前文档直接导出为 PDF。不显示任何设置对话框。
15 .uno%3AStatusGetTitle 38 显示有关活动 %PRODUCTNAME Basic 文档的信息。
18 .uno%3AStatusGetPosition 65 显示光标当前在 %PRODUCTNAME Basic 文档中的位置。先指定行号，然后指定列号。
1a SVX_HID_OFA_HYPERLINK_NAME 29 为 Internet URL 或文件指定名称。
19 SVX_HID_OFA_HYPERLINK_URL 56 您可以键入一个 URL，或者通过拖动的方式从文档中插入一个 URL。
13 .uno%3ASetHyperlink 2f 从当前 URL 将超链接插入到文档中。
1c SVX_HID_OFA_HYPERLINK_SEARCH 69 单击并从子菜单中选择一个 Internet 搜索引擎。搜索条目将输入到 URL 名称框中。
1c SVX_HID_OFA_HYPERLINK_TARGET 29 为指定的 URL 选择目标框类型。
1c CUI_HID_ICCDIALOG_CANCEL_BTN 1e 关闭对话框但不保存。
1c .uno%3AOpenHyperlinkOnCursor 24 打开光标所在位置的超链接
1b CUI_HID_ICCDIALOG_RESET_BTN 33 将对话框中的条目重设为原来的状态。
1c .uno%3ACopyHyperlinkLocation 1a 复制 URL 至剪切板。
1c CUI_HID_ICCDIALOG_CHOICECTRL 27 选择要插入的超链接的类型。
14 .uno%3AEditHyperlink 1b 打开超链接对话框。
16 .uno%3AHyperlinkDialog 33 打开用于创建和编辑超链接的对话框。
16 .uno%3ARemoveHyperlink 24 删除超链接，仅保留文本。
18 CUI_HID_ICCDIALOG_OK_BTN 1b 将数据应用到文档。
3f cui%3ACheckBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ACBX_ANONYMOUS 2f 允许以匿名用户身份登录 FTP 地址。
35 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_TEXT 1e 输入超级链接的名称。
31 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_TEXT 1e 输入超级链接的名称。
48 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_INTERNET 18 创建 HTTP 超链接。
38 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ALB_FORM 3f 指定超链接是作为文本插入还是作为按钮插入。
37 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_INDICATION 30 指定超链接的可见文字或按钮标题。
1a CUI_HID_HYPERLINK_INTERNET 18 创建 HTTP 超链接。
3a cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ACB_FRAME b4 输入用于打开链接文件的框架的名称，或从列表中选择一个预定义的框架。如果将此框保留为空，将在当前浏览器窗口中打开链接文件。
3a cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ACB_FRAME b4 输入用于打开链接文件的框架的名称，或从列表中选择一个预定义的框架。如果将此框保留为空，将在当前浏览器窗口中打开链接文件。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_BROWSE 3c 打开 Web 浏览器，可以在其中加载所需的 URL。
36 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_LOGIN 2f 如果使用 FTP 地址，请指定登录名。
38 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ALB_FORM 3f 指定超链接是作为文本插入还是作为按钮插入。
1a CUI_HID_HYPERDLG_INET_PATH 88 为您单击超链接时想要打开的文件输入 URL。如果您没有指定目标框架，文件在当前文档或框架中打开。
34 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_MAIL%3ALB_FORM 3f 指定超链接是作为文本插入还是作为按钮插入。
37 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_PASSWD 2c 如果使用 FTP 地址，请指定密码。
3e cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_INDICATION 30 指定超链接的可见文字或按钮标题。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_TARGET 24 打开文档中的目标对话框。
46 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_TELNET 1a 创建 Telnet 超链接。
1e CUI_HID_HYPERLINK_MARKWND_TREE 2d 指定要跳至的目标文档中的位置。
3d cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ACB_FRAME b4 输入用于打开链接文件的框架的名称，或从列表中选择一个预定义的框架。如果将此框保留为空，将在当前浏览器窗口中打开链接文件。
36 cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_MAIL%3ACB_FRAME b4 输入用于打开链接文件的框架的名称，或从列表中选择一个预定义的框架。如果将此框保留为空，将在当前浏览器窗口中打开链接文件。
3b cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ABTN_SCRIPT 9c 打开指定宏对话框，可在此对话框中为事件（例如“鼠标在对象之上”或“触发超级链接”）指定其自身的程序代码。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_SCRIPT 9c 打开指定宏对话框，可在此对话框中为事件（例如“鼠标在对象之上”或“触发超级链接”）指定其自身的程序代码。
1f CUI_HID_HYPERLINK_MARKWND_CLOSE 51 完整输入超链接之后，请单击关闭以设置链接并退出对话框。
35 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_TEXT 1e 输入超级链接的名称。
3b cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ALB_FORM 3f 指定超链接是作为文本插入还是作为按钮插入。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_INDICATION 30 指定超链接的可见文字或按钮标题。
42 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ABTN_SCRIPT 9c 打开指定宏对话框，可在此对话框中为事件（例如“鼠标在对象之上”或“触发超级链接”）指定其自身的程序代码。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_INDICATION 30 指定超链接的可见文字或按钮标题。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_SCRIPT 9c 打开指定宏对话框，可在此对话框中为事件（例如“鼠标在对象之上”或“触发超级链接”）指定其自身的程序代码。
38 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_TEXT 1e 输入超级链接的名称。
1f CUI_HID_HYPERLINK_MARKWND_APPLY 36 在超链接对话框的目标字段中插入目标。
43 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_FTP 17 创建 FTP 超链接。
34 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_SUBJECT 45 指定主题，用于插入到新电子邮件文档的主题行中。
40 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ARB_LINKTYP_NEWS 24 将新闻地址指定到超链接。
1a CUI_HID_HYPERDLG_MAIL_PATH 5e 指定完整的收件人 URL，格式为 mailto:<EMAIL> 或 news:group.server.com。
40 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ARB_LINKTYP_MAIL 33 将指定的电子邮件地址指定到超链接。
16 CUI_HID_HYPERLINK_MAIL 33 将指定的电子邮件地址指定到超链接。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ABTN_ADRESSBOOK 24 隐藏或显示数据源浏览器。
1a CUI_HID_HYPERLINK_DOCUMENT 88 为您单击超链接时想要打开的文件输入 URL。如果您没有指定目标框架，文件在当前文档或框架中打开。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_TARGET_DOC 3c 为超链接指定一个目标到路径下指定的文档。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_BROWSE 24 打开文档中的目标对话框。
41 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_FILEOPEN 36 打开可以在其中选择文件的打开对话框。
19 CUI_HID_HYPERDLG_DOC_PATH 88 为您单击超链接时想要打开的文件输入 URL。如果您没有指定目标框架，文件在当前文档或框架中打开。
44 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ARB_EDITLATER 27 指定创建但不立即打开文档。
3f cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_PATH_NEWDOC 3a 为您单击超链接时想要打开的文件输入 URL。
45 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ALB_DOCUMENT_TYPES 21 为新文档指定文件类型。
42 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ABTN_CREATE 3c 打开选择路径对话框，在其中可以选择路径。
42 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ARB_EDITNOW 42 指定创建新文档后，立即打开该文档以进行编辑。
1d CUI_HID_HYPERLINK_NEWDOCUMENT 42 指定创建新文档后，立即打开该文档以进行编辑。
13 .uno%3APreviousPage 1b 移到文档的上一页。
d .uno%3APageUp 1b 移到文档的上一页。
f .uno%3APageDown 1b 移到文档的下一页。
f .uno%3ANextPage 1b 移到文档的下一页。
15 .uno%3AGoToStartOfDoc 18 移到文档的首页。
10 .uno%3AFirstPage 18 移到文档的首页。
13 .uno%3AGoToEndOfDoc 1e 移到文档的最后一页。
f .uno%3ALastPage 1e 移到文档的最后一页。
f .uno%3ACloseWin 15 关闭当前窗口。
21 DBACCESS_HID_BROWSER_RENAME_ENTRY be 要重命名条目，请调用此命令并输入新的名称。还可以通过选择条目并按 F2 键来实现此功能。数据库必须支持重命名，否则将无法启用此命令。
1e DBACCESS_HID_BROWSER_CLOSECONN 60 关闭与数据源的连接。请参见“选项”对话框中的 %PRODUCTNAME Base - 连接。
18 .uno%3ADSBrowserExplorer 33 打开和关闭数据源资源管理器的视图。
22 DBACCESS_HID_BROWSER_EDIT_DATABASE 33 打开选定的数据库文件以便进行编辑。
21 DBACCESS_HID_BROWSER_ADMINISTRATE c5 打开一个对话框，以便在注册数据库列表中添加/编辑/删除数据库文件。通过在“选项”对话框中选择 %PRODUCTNAME Base - 数据库可以打开同一个对话框。
f .uno%3ASortDown 2d 将选定字段中的数据按降序排序。
11 .uno%3AAutoFilter 3f 根据当前选定数据字段的内容对记录进行筛选。
17 .uno%3ARemoveFilterSort 3c 取消筛选设置并显示当前表格中的所有记录。
28 DBACCESS_HID_BROWSER_REFRESH_REBUILDVIEW 51 重建数据库表格的视图。修改表格的结构后，请使用此命令。
e .uno%3ARefresh 18 刷新显示的数据。
13 .uno%3ASbaBrwInsert 54 将所有已标记的记录的字段插入到当前文档中光标所在的位置。
32 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_TABLE_COL 33 列出所有要插入到文档中的数据库列。
37 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_DBFMT_FROM_USR 51 如果不接受特定数据字段的格式信息，则从列表中指定格式。
35 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_AS_TABLE 48 将数据源浏览器中选定的数据作为表格插入到文档中。
3b sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ALL_FROM 36 删除表格列列表框中的所有数据库字段。
3b sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ONE_FROM 36 删除表格列列表框中选定的数据库字段。
36 sw%3APushButton%3ADLG_AP_INSERT_DB_SEL%3APB_TBL_FORMAT 66 打开表格格式对话框，可以在其中定义表格属性，例如边框、背景和列宽等。
3a sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_DBFMT_FROM_DB 18 接受数据库格式。
38 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_HEADL_EMPTY 2d 将空白标题行插入到文字表格中。
3b sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_DBFMT_FROM_USR 51 如果不接受特定数据字段的格式信息，则从列表中指定格式。
39 sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ONE_TO 39 将选定的数据库字段移到表格列列表框中。
39 sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ALL_TO 3f 将所有列出的数据库字段移到表格列列表框中。
36 sw%3ACheckBox%3ADLG_AP_INSERT_DB_SEL%3ACB_TABLE_HEADON 36 指定是否为文本表格中的列插入标题行。
37 sw%3APushButton%3ADLG_AP_INSERT_DB_SEL%3APB_TBL_AUTOFMT 66 打开自动套用格式对话框，可以在其中选择插入表格时立即采用的格式样式。
39 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_HEADL_COLNMS 4b 将数据库表格的字段名称用作文本表格中每一列的标题。
36 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_TBL_DB_COLUMN 36 指定要插入到文本表格中的数据库栏位。
34 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_AS_TEXT 48 将数据源浏览器中选定的数据作为文字插入到文档中。
28 .uno%3ADataSourceBrowser%2FInsertContent 42 利用已标记的记录来更新现有数据库字段的内容。
17 .uno%3ADSBInsertContent 42 利用已标记的记录来更新现有数据库字段的内容。
11 .uno%3AFilterCrit 1b 用于设置筛选选项。
1b DBACCESS_HID_DLG_FILTERCRIT 1b 用于设置筛选选项。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL2 2a 指定一个用于筛选字段的数值。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND1 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD2 3f 指定当前表格中的字段名称，将其设置到变量。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE3 2a 指定一个用于筛选字段的数值。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP1 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOND2 44 对于下面的参数，您可以选择逻辑运算符 AND 或 OR。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE1 2a 指定一个用于筛选字段的数值。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL3 2a 指定一个用于筛选字段的数值。
1f .uno%3ADataFilterStandardFilter 2a 指定筛选表格数据的逻辑条件。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD2 3f 指定当前表格中的字段名称，将其设置到变量。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND3 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
28 sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_OP2 44 对于下面的参数，您可以选择逻辑运算符 AND 或 OR。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP3 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD3 3f 指定当前表格中的字段名称，将其设置到变量。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD3 3f 指定当前表格中的字段名称，将其设置到变量。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE2 2a 指定一个用于筛选字段的数值。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD1 3f 指定当前表格中的字段名称，将其设置到变量。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD1 3f 指定当前表格中的字段名称，将其设置到变量。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOND3 44 对于下面的参数，您可以选择逻辑运算符 AND 或 OR。
28 sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_OP1 44 对于下面的参数，您可以选择逻辑运算符 AND 或 OR。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL1 2a 指定一个用于筛选字段的数值。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP2 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND2 4e 指定比较运算符，用来链接字段名称和数值字段中的条目。
1a DBACCESS_HID_DLG_ORDERCRIT 24 指定数据显示的排序条件。
10 .uno%3AOrderCrit 24 指定数据显示的排序条件。
19 CUI_HID_SEARCH_BTN_SEARCH 18 开始或取消查找。
17 CUI_HID_SEARCH_WILDCARD 2a 查找时允许使用 * 或 ? 通配符。
13 CUI_HID_SEARCH_CASE 24 指定查找时要区分大小写。
3c cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORNULL 24 指定查找不含数据的字段。
3c cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORTEXT 45 在框中输入查找条目，或者从列表中选择查找条目。
13 CUI_HID_SEARCH_TEXT 45 在框中输入查找条目，或者从列表中选择查找条目。
10 .uno%3ARecSearch 21 查找数据库表格和窗体。
18 CUI_HID_SEARCH_STARTOVER 87 重新开始查找。向前查找是从第一条记录开始重新查找，而向后查找则从最后一条记录开始重新查找。
18 CUI_HID_SEARCH_FORMATTER 42 指定在当前文档中查找时会考虑所有的字段格式。
17 CUI_HID_SEARCH_POSITION 33 指定查找条目与字段内容之间的关系。
3a cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SINGLEFIELD 1e 查找指定的数据字段。
18 CUI_HID_SEARCH_ALLFIELDS 15 查找所有字段。
18 CUI_HID_SEARCH_BTN_CLOSE 62 关闭对话框。在退出 %PRODUCTNAME 以前，将一直保留上一次查找的相关设置。
1d CUI_HID_SEARCH_FIELDSELECTION 1e 查找指定的数据字段。
3f cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORNOTNULL 24 指定查找含有数据的字段。
17 CUI_HID_SEARCH_BACKWARD 4b 指定按相反方向查找，即从最后一条记录到第一条记录。
2f cui%3AListBox%3ARID_SVXDLG_SEARCHFORM%3ALB_FORM 21 指定要查找的逻辑窗体。
16 CUI_HID_SEARCH_REGULAR 24 使用正则表达式进行查找。
11 .uno%3AFormFilter 42 提示数据库服务器根据指定的条件筛选可见数据。
13 .uno%3AFormFiltered 3c 在表格的已过滤视图和未过滤视图之间切换。
15 .uno%3AViewFormAsGrid 30 在窗体视图中启动附加的表格视图。
1c .uno%3ADSBDocumentDataSource 3f 在数据源浏览器中显示链接到当前文档的表格。
2d .uno%3ADataSourceBrowser%2FDocumentDataSource 3f 在数据源浏览器中显示链接到当前文档的表格。
14 .uno%3ASbaExecuteSql 29 运行 SQL 查询并显示查询结果。
13 .uno%3ADBClearQuery 36 清除查询并删除设计窗口中的所有表格。
39 dbaccess%3ARadioButton%3ADLG_JOIN_TABADD%3ARB_CASE_TABLES 12 只显示表格。
20 DBACCESS_HID_JOINSH_ADDTAB_CLOSE 1e 关闭添加表格对话框。
3a dbaccess%3ARadioButton%3ADLG_JOIN_TABADD%3ARB_CASE_QUERIES 12 只显示查询。
24 DBACCESS_HID_JOINSH_ADDTAB_TABLELIST 2d 指定要插入到设计窗口中的表格。
35 dbaccess%3APushButton%3ADLG_JOIN_TABADD%3APB_ADDTABLE 1e 插入当前选定的表格。
f .uno%3AAddTable 2d 指定要插入到设计窗口中的表格。
19 .uno%3ADBChangeDesignMode 2c 显示查询的设计视图或 SQL 视图。
13 .uno%3ASbaNativeSql 7f 在本地 SQL 模式下可以输入 SQL 命令，这些命令不经过 $[officename] 的解释，直接传递到数据源中。
16 .uno%3ADBViewFunctions 4b 在查询设计窗口的设计视图下方区域中显示“函数”行。
17 .uno%3ADBViewTableNames 39 在查询设计的下方区域中显示“表格”行。
14 .uno%3ADBViewAliases 39 在查询设计的下方区域中显示“别名”行。
17 .uno%3ADBDistinctValues 4e 在当前列中利用参数 DISTINCT 扩展 SQL 查询创建的选择语句。
13 .uno%3ASelectObject 27 用于在当前文档中选择对象。
11 SW_HID_SOURCEVIEW 78 显示当前 HTML 文档的源文本。要查看新文档的 HTML 源文本，必须首先将其保存为 HTML 文档。
11 .uno%3ASourceView 78 显示当前 HTML 文档的源文本。要查看新文档的 HTML 源文本，必须首先将其保存为 HTML 文档。
14 .uno%3APageStyleName 63 显示当前页面样式。双击可以编辑样式，单击鼠标右键可以选择其他样式。
16 .uno%3AStatusPageStyle 63 显示当前页面样式。双击可以编辑样式，单击鼠标右键可以选择其他样式。
13 .uno%3ALayoutStatus 63 显示当前页面样式。双击可以编辑样式，单击鼠标右键可以选择其他样式。
10 .uno%3AStateZoom 2a 指定当前页面的显示比例系数。
11 .uno%3AInsertMode 63 显示当前的插入模式，您可以在“INSRT = 插入”和“OVER = 覆盖”之间切换。
14 .uno%3ASelectionMode 70 显示当前选择模式。您可以在 STD = 标准、EXT = 扩展、ADD = 添加、BLK = 方块之间切换。
1a .uno%3AStatusSelectionMode 70 显示当前选择模式。您可以在 STD = 标准、EXT = 扩展、ADD = 添加、BLK = 方块之间切换。
15 .uno%3AModifiedStatus a6 如果对文档所做的修改还未保存到硬盘驱动器上，则状态栏上的这个字段中将显示一个 "*"。这也适用于新建的未保存的文档。
f .uno%3AModified a6 如果对文档所做的修改还未保存到硬盘驱动器上，则状态栏上的这个字段中将显示一个 "*"。这也适用于新建的未保存的文档。
13 .uno%3AModifyStatus a6 如果对文档所做的修改还未保存到硬盘驱动器上，则状态栏上的这个字段中将显示一个 "*"。这也适用于新建的未保存的文档。
12 .uno%3ACurrentTime 15 显示当前时间。
12 .uno%3ACurrentDate 15 显示当前日期。
4d cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_MTR_WIDTH 15 定义平铺宽度。
1a .uno%3AGraphicFilterInvert 7b 对彩色图像的颜色值或灰度图像的亮度值进行互补色转换。再次应用过滤器可恢复到原效果。
4e cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_MTR_HEIGHT 15 定义平铺高度。
1b .uno%3AGraphicFilterSharpen 2a 通过应用高通过滤器锐化图像。
1b .uno%3AGraphicFilterToolbox 7e 图片栏上的此图标可以打开图形过滤器栏，在图形过滤器栏中可以对选定图片使用各种过滤器。
4f cui%3ANumericField%3ARID_SVX_GRFFILTER_DLG_POSTER%3ADLG_FILTERPOSTER_NUM_POSTER 39 指定颜色数量，图像颜色将减少到此数量。
1c .uno%3AGraphicFilterSolarize a8 打开用于定义曝光的对话框。使用曝光得到的效果类似于冲洗照片时由于光线太强而产生的效果。颜色会发生部分互补色转换。
1a .uno%3AGraphicFilterSmooth 42 通过应用低通过滤器对图像进行柔化或模糊处理。
1a .uno%3AGraphicFilterPoster 33 打开用于确定海报颜色数量的对话框。
55 cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_SOLARIZE%3ADLG_FILTERSOLARIZE_MTR_THRESHOLD 3f 指定亮度（百分比），像素将根据此亮度曝光。
4a cui%3ACheckBox%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_CBX_EDGES 21 增强或鋭化对象的边缘。
4f cui%3ACheckBox%3ARID_SVX_GRFFILTER_DLG_SOLARIZE%3ADLG_FILTERSOLARIZE_CBX_INVERT 21 指定同时转换所有像素。
19 .uno%3AGraphicFilterSepia 8a 所有像素都被设置为各自的灰度值，然后按照指定的量减少绿色通道和蓝色通道。而红色通道保持不变。
19 .uno%3AGraphicFilterSobel 60 将图像显示为炭笔素描。以黑色绘制图像的轮廓，不再显示原来的颜色。
1a .uno%3AGraphicFilterMosaic 39 将小的像素组合并为颜色相同的矩形区域。
1a .uno%3AGraphicFilterRelief 27 显示用于创建浮雕的对话框。
1a .uno%3AGraphicFilterPopart 27 将图像转换为流行艺术格式。
4b cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_SEPIA%3ADLG_FILTERSEPIA_MTR_SEPIA 94 定义时效度（用百分比表示）。时效度为 0% 时，将显示所有像素的灰度值。时效度为 100% 时，仅保留红色通道。
1f .uno%3AGraphicFilterRemoveNoise 2d 通过应用中值过滤器可去除杂色。
f .uno%3AGrafMode 87 列出选定的图形对象的视图属性。不会修改当前文件中嵌入或链接的图形对象，而只修改对象的视图。
e .uno%3AGrafRed 3e 为选定图形对象指定 RGB 颜色组件的红色比例。
10 .uno%3AGrafGreen 3e 为选定图形对象指定 RGB 颜色组件的绿色比例。
f .uno%3AGrafBlue 3e 为选定图形对象指定 RGB 颜色组件的蓝色比例。
14 .uno%3AGrafLuminance 24 指定选定图形对象的亮度。
13 .uno%3AGrafContrast 27 指定选定图形图像的对比度。
10 .uno%3AGrafGamma 52 指定选定对象视图的 Gamma 值，此设置会影响中间色调的亮度。
17 .uno%3AGrafTransparence 21 指定图形对象的透明度。
13 .uno%3AGrafAttrCrop 5a 允许剪裁已插入图片的显示。只剪裁显示，而不更改已插入的图片。
b .uno%3ACrop 3f 拖动八个剪裁控点中的任何一个以剪裁该图片。
1c .uno%3ABasicShapes.trapezoid 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3ABasicShapes.isosceles-triangle 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1c .uno%3ABasicShapes.rectangle 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3ABasicShapes.paper 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
12 .uno%3ABasicShapes 54 打开“基本形状”工具栏，利用该工具栏可在文档中插入图形。
17 .uno%3ABasicShapes.ring 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1b .uno%3ABasicShapes.pentagon 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
20 .uno%3ABasicShapes.round-quadrat 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
22 .uno%3ABasicShapes.round-rectangle 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
16 .uno%3ABasicShapes.can 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3ABasicShapes.frame 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3ABasicShapes.cross 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ABasicShapes.octagon 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3ABasicShapes.right-triangle 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1c .uno%3ABasicShapes.block-arc 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ABasicShapes.diamond 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
17 .uno%3ABasicShapes.cube 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ABasicShapes.hexagon 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
20 .uno%3ABasicShapes.parallelogram 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3ABasicShapes.circle-pie 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ABasicShapes.ellipse 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
19 .uno%3ABasicShapes.circle 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ABasicShapes.quadrat 5a 单击“基本形状”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AArrowShapes.left-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3AArrowShapes.left-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3AArrowShapes.pentagon-right 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AArrowShapes.notched-right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3AArrowShapes.left-right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3AArrowShapes.circular-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AArrowShapes.right-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1e .uno%3AArrowShapes.split-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
22 .uno%3AArrowShapes.s-sharped-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AArrowShapes.quad-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
29 .uno%3AArrowShapes.up-right-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3AArrowShapes.up-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3AArrowShapes.quad-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
20 .uno%3AArrowShapes.up-down-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
24 .uno%3AArrowShapes.split-round-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AArrowShapes.corner-right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1e .uno%3AArrowShapes.right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
29 .uno%3AArrowShapes.up-down--arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AArrowShapes.down-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AArrowShapes.striped-right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AArrowShapes.up-right-down-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1b .uno%3AArrowShapes.up-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
2b .uno%3AArrowShapes.left-right-arrow-callout 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3AArrowShapes.chevron 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3AArrowShapes.up-right-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3AArrowShapes.down-arrow 5a 单击“箭头汇总”工具栏上的图标，然后在文档中拖动来绘制形状。
12 .uno%3AArrowShapes 54 打开“箭头总汇”工具栏，利用该工具栏可在文档中插入图形。
23 .uno%3ACalloutShapes.line-callout-1 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
14 .uno%3ACalloutShapes 4e 打开“图例”工具栏，利用该工具栏可在文档中插入图形。
22 .uno%3ACalloutShapes.cloud-callout 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3ACalloutShapes.line-callout-3 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3ACalloutShapes.line-callout-2 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
22 .uno%3ACalloutShapes.round-callout 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
2e .uno%3ACalloutShapes.round-rectangular-callout 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
28 .uno%3ACalloutShapes.rectangular-callout 54 单击“图例”工具栏上的图标，然后在文档中拖动来绘制形状。
14 .uno%3AColorSettings 44 通过"颜色"工具栏，可以编辑选定对象的某些属性。
2d .uno%3AFlowChartShapes.flowchart-manual-input 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2c .uno%3AFlowChartShapes.flowchart-preparation 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AFlowChartShapes.flowchart-sort 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
31 .uno%3AFlowChartShapes.flowchart-manual-operation 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
28 .uno%3AFlowChartShapes.flowchart-display 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AFlowChartShapes.flowchart-delay 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
31 .uno%3AFlowChartShapes.flowchart-summing-junction 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2b .uno%3AFlowChartShapes.flowchart-terminator 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
33 .uno%3AFlowChartShapes.flowchart-predefined-process 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
28 .uno%3AFlowChartShapes.flowchart-process 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
16 .uno%3AFlowChartShapes 51 打开“流程图”工具栏，在这里您可以将图形插入到文档中。
2a .uno%3AFlowChartShapes.flowchart-connector 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
31 .uno%3AFlowChartShapes.flowchart-internal-storage 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
29 .uno%3AFlowChartShapes.flowchart-decision 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
28 .uno%3AFlowChartShapes.flowchart-collate 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
33 .uno%3AFlowChartShapes.flowchart-off-page-connector 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
29 .uno%3AFlowChartShapes.flowchart-document 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AFlowChartShapes.flowchart-data 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
28 .uno%3AFlowChartShapes.flowchart-extract 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
36 .uno%3AFlowChartShapes.flowchart-direct-access-storage 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
32 .uno%3AFlowChartShapes.flowchart-alternate-process 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2e .uno%3AFlowChartShapes.flowchart-magnetic-disk 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
32 .uno%3AFlowChartShapes.flowchart-sequential-access 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2c .uno%3AFlowChartShapes.flowchart-stored-data 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
26 .uno%3AFlowChartShapes.flowchart-merge 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3AFlowChartShapes.flowchart-or 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2d .uno%3AFlowChartShapes.flowchart-punched-tape 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
25 .uno%3AFlowChartShapes.flowchart-card 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
2e .uno%3AFlowChartShapes.flowchart-multidocument 57 单击“流程图”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3AFontworkGalleryFloater 63 该图标可打开“艺术字图片库”，利用该图片库可以在文档中插入艺术字。
17 .uno%3AFormatPaintbrush 8a 先选择某个文本或对象，然后单击该图标。之后单击或拖过其他文本或单击一个对象以应用相同的格式。
18 .uno%3AStarShapes.signet 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
11 .uno%3AStarShapes 57 打开“星形与旗帜”工具栏，在这里您可以将图形插入到文档中。
17 .uno%3AStarShapes.star5 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
17 .uno%3AStarShapes.star4 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
23 .uno%3AStarShapes.horizontal-scroll 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
1b .uno%3AStarShapes.doorplate 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3AStarShapes.star12 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3AStarShapes.star24 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
16 .uno%3AStarShapes.bang 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3AStarShapes.vertical-scroll 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
1f .uno%3AStarShapes.concave-star6 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
17 .uno%3AStarShapes.star8 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
17 .uno%3AStarShapes.star6 5d 单击“星形与旗帜”工具栏上的图标，然后在文档中拖动来绘制形状。
1e .uno%3ASymbolShapes.quad-bevel 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1e .uno%3ASymbolShapes.left-brace 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
13 .uno%3ASymbolShapes 54 打开“符号形状”工具栏，利用该工具栏可在文档中插入图形。
1f .uno%3ASymbolShapes.right-brace 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ASymbolShapes.smiley 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3ASymbolShapes.octagon-bevel 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3ASymbolShapes.forbidden 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
19 .uno%3ASymbolShapes.cloud 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ASymbolShapes.flower 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1a .uno%3ASymbolShapes.puzzle 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1d .uno%3ASymbolShapes.lightning 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
18 .uno%3ASymbolShapes.moon 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3ASymbolShapes.diamond-bevel 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
1e .uno%3ASymbolShapes.brace-pair 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
20 .uno%3ASymbolShapes.left-bracket 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
17 .uno%3ASymbolShapes.sun 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
21 .uno%3ASymbolShapes.right-bracket 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
20 .uno%3ASymbolShapes.bracket-pair 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
19 .uno%3ASymbolShapes.heart 5a 单击“符号形状”工具栏上的图标，然后在文档中拖动来绘制形状。
2f sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_BACKWARDS 27 从光标的当前位置向上查找。
30 sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_WRAPAROUND 3f 从光标的当前位置开始查找整个“帮助”页面。
30 sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_WHOLEWORDS f 全字匹配。
2c sfx2%3APushButton%3ARID_DLG_SEARCH%3APB_FIND 21 查找下一个查找匹配项。
22 SFX2_HID_HELP_TOOLBOXITEM_BACKWARD 15 向后转到上一页
1f SFX2_HID_HELP_TOOLBOXITEM_INDEX 1b 隐藏和显示导航窗格
18 SFX2_HID_HELP_TABCONTROL 5d “帮助”窗口的导航窗格中包含内容、索引、查找和书签等选项卡页。
15 SFX2_HID_HELP_LISTBOX 4f 在最顶部的列表框中，可选择其他 %PRODUCTNAME“帮助”模块。
2c sfx2%3AComboBox%3ARID_DLG_SEARCH%3AED_SEARCH 48 输入要查找的文本，或者从列表中选择一个文本条目。
1f SFX2_HID_HELP_TOOLBOXITEM_START 27 转到当前“帮助”主题的首页
21 SFX2_HID_HELP_TOOLBOXITEM_FORWARD 15 向前转到下一页
15 SFX2_HID_HELP_TOOLBOX 3f 工具栏中包含用于控制“帮助”系统的重要功能
1f SFX2_HID_HELP_TOOLBOXITEM_PRINT f 打印当前页
11 .uno%3AHelpOnHelp 1b 提供帮助系统的概览
26 SFX2_HID_HELP_TOOLBOXITEM_SEARCHDIALOG 21 打开在此页查找对话框。
23 SFX2_HID_HELP_TOOLBOXITEM_BOOKMARKS 18 将此页添加到书签
2f sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_MATCHCASE 18 区分大小写文本。
31 sfx2%3APushButton%3ATP_HELP_INDEX%3APB_OPEN_INDEX 21 单击以显示选定的主题。
2a sfx2%3AComboBox%3ATP_HELP_INDEX%3ACB_INDEX 4b 在“索引”中，双击某个条目，或者键入要查找的字词。
2f sfx2%3ACheckBox%3ATP_HELP_SEARCH%3ACB_FULLWORDS 6c 指定是否对输入的字词进行精确查找。在精确查找中，将不显示部分匹配的字词。
2e sfx2%3APushButton%3ATP_HELP_SEARCH%3APB_SEARCH 36 单击以开始对输入的条目进行全文查找。
2b sfx2%3ACheckBox%3ATP_HELP_SEARCH%3ACB_SCOPE 36 指定是否只在文档标题中搜索查找条目。
2c sfx2%3AComboBox%3ATP_HELP_SEARCH%3AED_SEARCH 42 在这里输入查找条目。查找时不考虑字母大小写。
2b sfx2%3AListBox%3ATP_HELP_SEARCH%3ALB_RESULT 63 列出全文查找得到的页面标题。要显示某个页面，双击与它相对应的条目。
33 sfx2%3APushButton%3ATP_HELP_SEARCH%3APB_OPEN_SEARCH 21 显示列表中选定的条目。
1e SFX2_HID_HELP_BOOKMARKS_RENAME 36 打开一个对话框，输入书签的另一个名称
1c SFX2_HID_HELP_BOOKMARKS_OPEN 1b 显示选定的帮助主题
31 sfx2%3AListBox%3ATP_HELP_BOOKMARKS%3ALB_BOOKMARKS 6c 双击一个书签或者按回车键来打开帮助中对应的页面。单击右键打开上下文菜单。
36 sfx2%3AEdit%3ADLG_HELP_ADDBOOKMARK%3AED_BOOKMARK_TITLE 4b 显示含有书签的页面的名称，也可以为书签键入新名称。
1e SFX2_HID_HELP_BOOKMARKS_DELETE 15 删除选定书签。
1e SFX2_HID_HELP_TABPAGE_CONTENTS 4e 显示主帮助主题，排列方式与文件管理器中的文件夹相似。
1a SVX_HID_POPUP_LINEEND_CTRL 15 选择一个方向。
20 .uno%3AExtrusionDirectionFloater 21 打开“拉伸方向”窗口。
1c .uno%3AExtrusionDepthFloater 21 打开“拉伸深度”窗口。
1e .uno%3AExtrusionSurfaceFloater 21 打开“拉伸表面”窗口。
18 .uno%3AExtrusionTiltLeft 1e 将所选对象左偏五度。
16 .uno%3AExtrusionTiltUp 1e 将所选对象上翘五度。
17 .uno%3AExtrusion3DColor 24 打开“拉伸颜色”工具栏。
3c svx%3AMetricField%3ARID_SVX_MDLG_EXTRUSION_DEPTH%3AMTR_DEPTH 1b 输入一个拉伸深度。
16 .uno%3AExtrusionToggle 2d 切换选定对象三维效果的开和关。
19 .uno%3AExtrusionTiltRight 1e 将所选对象右偏五度。
18 .uno%3AExtrusionTiltDown 1e 将所选对象下俯五度。
1f .uno%3AExtrusionLightingFloater 21 打开“拉伸照明”窗口。
14 .uno%3AAutoPilotMenu 57 引导您建立商务信函和私人信函、传真、会议议程和演示文稿等。
1a WIZARDS_HID_LTRWIZARD_BACK 30 允许您查看以前步骤中的选择情况。
1c WIZARDS_HID_LTRWIZARD_CREATE 54 根据您的选择，向导创建一个新的文档模板，并保存在硬盘上。
16 .uno%3AAutoPilotLetter 24 启动用于信函模板的向导。
1a WIZARDS_HID_LTRWIZARD_NEXT 2a 保存当前的设置并继续下一页。
38 service%3Acom.sun.star.wizards.letter.CallWizard%3Fstart 24 启动用于信函模板的向导。
1c EXTENSIONS_HID_ABSPILOT_NEXT 2a 保存当前的设置并继续下一页。
1f WIZARDS_HID_DLGIMPORT_0_CMDPREV 30 允许您查看以前步骤中的选择情况。
20 EXTENSIONS_HID_ABSPILOT_PREVIOUS 30 允许您查看以前步骤中的选择情况。
1f WIZARDS_HID_DLGIMPORT_0_CMDNEXT 2a 保存当前的设置并继续下一页。
23 WIZARDS_HID_LTRWIZ_OPTPRIVATELETTER 1e 指定要创建私人信函。
13 SW_HID_LETTER_PAGE1 36 指定要创建私人信函还是创建商务信函。
24 WIZARDS_HID_LTRWIZ_OPTBUSINESSLETTER 24 指定要创建商务信函模板。
23 WIZARDS_HID_LTRWIZ_LSTBUSINESSSTYLE 1e 选择信函模板的设计。
23 WIZARDS_HID_LTRWIZ_CHKBUSINESSPAPER 7b 指定是否使用已包含打印徽标、地址或页脚行的纸张。“向导”显示下一个“信头”版式页。
22 WIZARDS_HID_LTRWIZ_LSTPRIVATESTYLE 1e 选择信函模板的设计。
27 WIZARDS_HID_LTRWIZ_LSTPRIVOFFICIALSTYLE 1e 选择信函模板的设计。
28 WIZARDS_HID_LTRWIZ_OPTPRIVOFFICIALLETTER 27 指定要创建正式的私人信函。
29 WIZARDS_HID_LTRWIZ_CHKPAPERCOMPANYADDRESS 49 指定某地址已打印在信头纸上。%PRODUCTNAME 不打印地址。
1e WIZARDS_HID_LTRWIZ_NUMADDRESSY 24 设置对象与上边距的间隔。
1b WIZARDS_HID_LTRWIZ_NUMLOGOX 24 设置对象与左边距的间隔。
22 WIZARDS_HID_LTRWIZ_NUMFOOTERHEIGHT 5e 输入已打印在信头纸上的页脚区域的高度。%PRODUCTNAME 不在该区域打印。
23 WIZARDS_HID_LTRWIZ_NUMADDRESSHEIGHT 18 定义对象的高度。
1f WIZARDS_HID_LTRWIZ_NUMLOGOWIDTH 18 定义对象的宽度。
20 WIZARDS_HID_LTRWIZ_NUMLOGOHEIGHT 18 定义对象的高度。
26 WIZARDS_HID_LTRWIZ_CHKPAPERCOMPANYLOGO 46 确定已在信头纸上打印徽标。%PRODUCTNAME 不打印徽标。
1b WIZARDS_HID_LTRWIZ_NUMLOGOY 24 设置对象与上边距的间隔。
13 SW_HID_LETTER_PAGE2 30 允许您指定信头纸上已包含的元素。
22 WIZARDS_HID_LTRWIZ_NUMADDRESSWIDTH 18 定义对象的宽度。
21 WIZARDS_HID_LTRWIZ_CHKPAPERFOOTER 4c 指定页脚区域已打印在信头纸上。%PRODUCTNAME 不打印页脚。
25 WIZARDS_HID_LTRWIZ_CHKCOMPANYRECEIVER 73 指定自己的地址始终以小号字打印在收件人地址上方。%PRODUCTNAME 不以小号字打印地址。
1e WIZARDS_HID_LTRWIZ_NUMADDRESSX 24 设置对象与左边距的间隔。
1f WIZARDS_HID_LTRWIZ_CHKUSEFOOTER 21 在信函模板中包括页脚。
20 WIZARDS_HID_LTRWIZ_CHKUSESUBJECT 24 在信函模板中包括主题行。
21 WIZARDS_HID_LTRWIZ_CHKUSEGREETING 4b 在信函模板中包括信尾礼貌用语。从列表框中选择文本。
13 SW_HID_LETTER_PAGE3 2a 定义包含在信函模板中的项目。
1e WIZARDS_HID_LTRWIZ_LSTGREETING 4b 在信函模板中包括信尾礼貌用语。从列表框中选择文本。
23 WIZARDS_HID_LTRWIZ_CHKUSESALUTATION 3f 在信函模板中包括称呼。从列表框中选择称呼。
28 WIZARDS_HID_LTRWIZ_CHKUSEADDRESSRECEIVER 33 在信函模板中包括小尺寸的回信地址。
1e WIZARDS_HID_LTRWIZ_CHKUSESIGNS 33 在信函模板中包括关于商务信函的行。
1d WIZARDS_HID_LTRWIZ_CHKUSELOGO 21 在信函模板中包括徽标。
22 WIZARDS_HID_LTRWIZ_CHKUSEBENDMARKS 27 在信函模板中包括折叠标记。
20 WIZARDS_HID_LTRWIZ_LSTSALUTATION 3f 在信函模板中包括称呼。从列表框中选择称呼。
20 WIZARDS_HID_LTRWIZ_LSTLETTERNORM 4a 选择国家/地区，以便使用该国家/地区的典型信函版式。
27 WIZARDS_HID_LTRWIZ_OPTSENDERPLACEHOLDER 58 通过“选项”对话框中的 %PRODUCTNAME - “用户数据”使用地址数据。
13 SW_HID_LETTER_PAGE4 27 指定发件人和收件人的信息。
22 WIZARDS_HID_LTRWIZ_TXTSENDERSTREET 21 指定发件人的街道地址。
22 WIZARDS_HID_LTRWIZ_OPTSENDERDEFINE 2a 使用下列文字框中的地址数据。
26 WIZARDS_HID_LTRWIZ_OPTRECEIVERDATABASE 2d 地址数据库字段被插入信函模板。
29 WIZARDS_HID_LTRWIZ_OPTRECEIVERPLACEHOLDER 30 指定在信函模板中插入占位符字段。
26 WIZARDS_HID_LTRWIZ_TXTSENDERSTATE_TEXT 21 指定发件人的地址数据。
24 WIZARDS_HID_LTRWIZ_TXTSENDERPOSTCODE 21 指定发件人的地址数据。
20 WIZARDS_HID_LTRWIZ_TXTSENDERCITY 21 指定发件人的地址数据。
20 WIZARDS_HID_LTRWIZ_TXTSENDERNAME 18 指定发件人姓名。
1c WIZARDS_HID_LTRWIZ_TXTFOOTER 1b 输入页脚行的文字。
13 SW_HID_LETTER_PAGE5 2d 指定要在页脚空间中包含的信息。
25 WIZARDS_HID_LTRWIZ_CHKFOOTERNEXTPAGES 24 选择压缩第一页上的页脚。
27 WIZARDS_HID_LTRWIZ_CHKFOOTERPAGENUMBERS 21 在信函模版中包含页码。
22 WIZARDS_HID_LTRWIZ_OPTCREATELETTER 54 保存并关闭模板，然后打开一个新的基于该模板的未命名文档。
13 SW_HID_LETTER_PAGE6 36 指定您要保存文档和模版的位置和名称。
1a WIZARDS_HID_LTRWIZ_TXTPATH 53 输入模板的路径和文件名，或单击 ... 按钮选择路径和文件名。
22 WIZARDS_HID_LTRWIZ_TXTTEMPLATENAME 1e 指定文档模板的标题。
1a WIZARDS_HID_LTRWIZ_CMDPATH 53 输入模板的路径和文件名，或单击 ... 按钮选择路径和文件名。
21 WIZARDS_HID_LTRWIZ_OPTMAKECHANGES 39 保存模板，并使其处于打开状态以便编辑。
1a WIZARDS_HID_FAXWIZARD_BACK a2 单击返回按钮可以查看在以前的页面中选择的设置。单击此按钮不会修改或删除当前设置。返回按钮从第二页开始被激活。
1a WIZARDS_HID_FAXWIZARD_NEXT 6c 向导保存当前设置，并转到下一页。到达最后一页时，下一步按钮将变为不可用。
1c WIZARDS_HID_FAXWIZARD_CREATE 9c 向导将根据您的选择创建一个文档模板并将其保存。工作区域中将显示根据此模板创建的新文档，文件名为 "UntitledX"。
35 service%3Acom.sun.star.wizards.fax.CallWizard%3Fstart 1e 打开用于传真的向导。
13 .uno%3AAutoPilotFax 1e 打开用于传真的向导。
20 WIZARDS_HID_FAXWIZ_OPTPRIVATEFAX 2a 创建用于私人传真的传真模板。
10 SW_HID_FAX_PAGE1 1e 定义传真文档的样式。
23 WIZARDS_HID_FAXWIZ_LSTBUSINESSSTYLE 18 指定预定义样式。
21 WIZARDS_HID_FAXWIZ_OPTBUSINESSFAX 2d 创建用于商务型传真的传真模板。
1b WIZARDS_HID_LSTPRIVATESTYLE 18 指定预定义样式。
1b WIZARDS_HID_OPTSENDERDEFINE 33 包括问候语。从列表框中选择问候语。
19 WIZARDS_HID_CHKUSESUBJECT 15 包括公司徽标。
1b WIZARDS_HID_TXTSENDERSTREET f 包括页脚。
19 WIZARDS_HID_LSTSALUTATION 33 包括通信类型行。从列表框中选择行。
20 WIZARDS_HID_OPTSENDERPLACEHOLDER 2d 包括称呼。从列表框中选择称呼。
1c WIZARDS_HID_CHKUSESALUTATION 15 包括日期字段。
18 WIZARDS_HID_CHKUSEFOOTER 2d 包括称呼。从列表框中选择称呼。
10 SW_HID_FAX_PAGE2 21 指定要打印的传真元素。
19 WIZARDS_HID_TXTSENDERNAME 33 包括问候语。从列表框中选择问候语。
17 WIZARDS_HID_LSTGREETING 12 包括主题行。
1a WIZARDS_HID_CHKUSEGREETING 33 包括通信类型行。从列表框中选择行。
20 WIZARDS_HID_CHKFOOTERPAGENUMBERS 1e 输入发件人地址数据。
15 WIZARDS_HID_TXTFOOTER 1e 输入发件人地址数据。
22 WIZARDS_HID_OPTRECEIVERPLACEHOLDER 1e 输入发件人地址数据。
10 SW_HID_FAX_PAGE3 2d 指定传真的收件人和发件人信息。
1c WIZARDS_HID_FILETEMPLATEPATH 45 插入数据库字段以便稍后与传真文档进行邮件合并。
1e WIZARDS_HID_CHKFOOTERNEXTPAGES 1e 输入发件人地址数据。
1a WIZARDS_HID_TXTSENDERSTATE 6c 在传真模板上插入地址的占位符。稍后在传真文档中，单击占位符输入实际数据。
18 WIZARDS_HID_TXTSENDERFAX 1e 输入发件人地址数据。
19 WIZARDS_HID_TXTSENDERCITY 60 选择在下列文字框中输入地址数据。数据作为普通文字插在传真文档中。
1b WIZARDS_HID_TXTTEMPLATENAME 6c 在传真模板上插入地址的占位符。稍后在传真文档中，单击占位符输入实际数据。
1f WIZARDS_HID_OPTRECEIVERDATABASE 1e 输入发件人地址数据。
10 SW_HID_FAX_PAGE4 15 指定页脚数据。
10 SW_HID_FAX_PAGE5 21 定义模板的名称和位置。
16 .uno%3AAutoPilotAgenda 33 启动向导，帮助您创建会议议程模板。
38 service%3Acom.sun.star.wizards.agenda.CallWizard%3Fstart 33 启动向导，帮助您创建会议议程模板。
1f WIZARDS_HID_AGWIZ_1_CHK_MINUTES 36 打印可在会议期间记录会议记录的页面。
23 WIZARDS_HID_AGWIZ_1_LIST_PAGEDESIGN 24 从列表框中选择页面设计。
13 SW_HID_AGENDA_PAGE1 1e 指定议程的页面设计。
1d WIZARDS_HID_AGWIZ_2_TXT_TITLE 18 指定会议的标题。
1c WIZARDS_HID_AGWIZ_2_TXT_DATE 18 指定会议的日期。
20 WIZARDS_HID_AGWIZ_2_TXT_LOCATION 18 指定会议的地点。
1c WIZARDS_HID_AGWIZ_2_TXT_TIME 18 指定会议的时间。
13 SW_HID_AGENDA_PAGE2 33 指定会议的日期、时间、标题和地点。
1d WIZARDS_HID_AGWIZ_3_CHK_BRING 27 指定是否打印“请携带”行。
1c WIZARDS_HID_AGWIZ_3_CHK_READ 27 指定是否打印“请阅读”行。
13 SW_HID_AGENDA_PAGE3 2d 指定要在会议议程中包含的标题。
1d WIZARDS_HID_AGWIZ_3_CHK_NOTES 24 指定是否打印“批注”行。
24 WIZARDS_HID_AGWIZ_3_CHK_MEETING_TYPE 24 指定是否打印会议类型行。
22 WIZARDS_HID_AGWIZ_4_CHK_TIMEKEEPER 33 指定是否打印可输入会议主持人的行。
21 WIZARDS_HID_AGWIZ_4_CHK_NOTETAKER 33 指定是否打印可输入会议记录员的行。
21 WIZARDS_HID_AGWIZ_4_CHK_CALLED_BY 33 指定是否打印可输入会议召集者的行。
27 WIZARDS_HID_AGWIZ_4_CHK_RESOURCEPERSONS 30 指定是否打印可输入服务人员的行。
21 WIZARDS_HID_AGWIZ_4_CHK_OBSERVERS 30 指定是否打印可输入旁听人员的行。
21 WIZARDS_HID_AGWIZ_4_CHK_ATTENDEES 30 指定是否打印可输入出席人员的行。
23 WIZARDS_HID_AGWIZ_4_CHK_FACILITATOR 2a 指定是否打印可输入主席的行。
13 SW_HID_AGENDA_PAGE4 2d 指定要在会议议程上打印的名称。
1a WIZARDS_HID_AGWIZ_5_BTN_UP 1e 向上移动当前主题行。
1e WIZARDS_HID_AGWIZ_5_BTN_INSERT 33 在当前行上方插入一个新的空主题行。
21 WIZARDS_HID_AGWIZ_5_TXT_MINUTES_1 66 输入会议议程事项。使用“向上移动”和“向下移动”按钮对事项进行排序。
13 SW_HID_AGENDA_PAGE5 33 指定要在会议议程模板上打印的主题。
1e WIZARDS_HID_AGWIZ_5_BTN_REMOVE 18 删除当前主题行。
25 WIZARDS_HID_AGWIZ_5_TXT_RESPONSIBLE_1 66 输入会议议程事项。使用“向上移动”和“向下移动”按钮对事项进行排序。
1f WIZARDS_HID_AGWIZ_5_TXT_TOPIC_1 66 输入会议议程事项。使用“向上移动”和“向下移动”按钮对事项进行排序。
1c WIZARDS_HID_AGWIZ_5_BTN_DOWN 1e 向下移动当前主题行。
13 SW_HID_AGENDA_PAGE6 2d 选择会议议程模板的标题和位置。
24 WIZARDS_HID_AGWIZ_6_BTN_TEMPLATEPATH 3f 指定完整路径，包括会议议程模板的文件名称。
23 WIZARDS_HID_AGWIZ_6_OPT_MAKECHANGES 5a 创建并保存会议议程模板，然后打开该模板以便进行进一步的编辑。
24 WIZARDS_HID_AGWIZ_6_TXT_TEMPLATEPATH 3f 指定完整路径，包括会议议程模板的文件名称。
24 WIZARDS_HID_AGWIZ_6_OPT_CREATEAGENDA 63 创建并保存会议议程模板，然后打开一个新的基于该模板的会议议程文档。
24 WIZARDS_HID_AGWIZ_6_TXT_TEMPLATENAME 24 指定会议议程模板的名称。
24 sd%3APushButton%3ADLG_ASS%3ABUT_LAST 30 不删除当前设置，返回上一个步骤。
1d .uno%3AAutoPilotPresentations 60 使用向导交互创建演示文稿。利用向导，可以修改模板以满足您的需要。
24 sd%3APushButton%3ADLG_ASS%3ABUT_NEXT 2e 接受新的设置并进入/移到下一页。
16 .uno%3ANewPresentation 60 使用向导交互创建演示文稿。利用向导，可以修改模板以满足您的需要。
c slot%3A10425 60 使用向导交互创建演示文稿。利用向导，可以修改模板以满足您的需要。
18 SD_HID_SD_AUTOPILOT_OPEN e7 列出创建并保存到 Templates 目录的演示文稿，该目录在“选项”对话框中 %PRODUCTNAME - 路径下指定。要使用向导编辑演示文稿的版式和格式，请选择演示文稿，然后单击下一步。
1a SD_HID_SD_AUTOPILOT_REGION 30 列出演示文稿可以使用的模板类别。
2a sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_OPEN 2d 显示以前创建的演示文稿的列表。
26 sd%3ACheckBox%3ADLG_ASS%3ACB_STARTWITH 5a 指定只有在您使用文件 - 向导 - 演示文稿发出请求时，才启动向导。
24 sd%3ACheckBox%3ADLG_ASS%3ACB_PREVIEW 27 指定在预览窗口中显示模板。
29 sd%3APushButton%3ADLG_ASS%3APB_PAGE1_OPEN 2d 单击打开以显示文件选择对话框。
2e sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_TEMPLATE 48 打开一个列表框，其中含有多种可以修改的演示文稿。
2b sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_EMPTY 2d 创建一个新的（空白）演示文稿。
1d SD_HID_SD_AUTOPILOT_TEMPLATES 30 列出演示文稿可以使用的模板类别。
19 SD_HID_SD_AUTOPILOT_PAGE1 33 指定演示文稿类型，并选择一个模板。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM5 24 使用模板的原始页面格式。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM3 2a 创建用作透明胶片的演示文稿。
28 sd%3AListBox%3ADLG_ASS%3ALB_PAGE2_LAYOUT 54 允许您为在向导第 1 页上所选的演示文稿选择一种幻灯片设计。
21 SD_HID_SD_AUTOPILOT_PAGETEMPLATES 54 允许您为在向导第 1 页上所选的演示文稿选择一种幻灯片设计。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM4 30 创建可以打印在纸张上的演示文稿。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM2 27 创建用作幻灯片的演示文稿。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM1 27 仅创建计算机屏幕演示文稿。
19 SD_HID_SD_AUTOPILOT_PAGE2 54 允许您为在向导第 1 页上所选的演示文稿选择一种幻灯片设计。
27 sd%3ACheckBox%3ADLG_ASS%3ACB_PAGE3_LOGO 4e 指定是否在演示文稿之间的暂停期间显示 $[officename] 徽标。
2a sd%3ATimeField%3ADLG_ASS%3ATMF_PAGE3_BREAK 2a 定义演示文稿之间的暂停时间。
2b sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE3_KIOSK 3c 自动放映演示文稿，并在暂停之后重新开始。
28 sd%3AListBox%3ADLG_ASS%3ALB_PAGE3_EFFECT 1e 指定演示文稿的效果。
27 sd%3AListBox%3ADLG_ASS%3ALB_PAGE3_SPEED 18 确定效果的速度。
29 sd%3ATimeField%3ADLG_ASS%3ATMF_PAGE3_TIME 2a 定义每页演示文稿的持续时间。
19 SD_HID_SD_AUTOPILOT_PAGE3 3c 指定演示文稿的特殊效果并确定效果的速度。
2a sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE3_LIVE 57 选择默认选项，将用指定的速度以全屏显示方式来放映演示文稿。
19 SD_HID_SD_AUTOPILOT_PAGE4 27 指定您的姓名或公司的名称。
27 sd%3AEdit%3ADLG_ASS%3AEDT_PAGE4_ASKNAME 27 指定您的姓名或公司的名称。
37 sd%3AMultiLineEdit%3ADLG_ASS%3AEDT_PAGE4_ASKINFORMATION 3c 在此字段中输入要在演示文稿中加入的设想。
28 sd%3AEdit%3ADLG_ASS%3AEDT_PAGE4_ASKTOPIC 1e 指定演示文稿的主题。
2a sd%3ACheckBox%3ADLG_ASS%3ACB_PAGE5_SUMMARY 2a 创建所有演示文稿内容的摘要。
19 SD_HID_SD_AUTOPILOT_PAGE5 2a 创建所有演示文稿内容的摘要。
1d WIZARDS_HID_DLGFORM_CMDFINISH 30 单击可创建窗体而不处理其他页面。
1a WIZARDS_HID_DLGFORM_DIALOG 24 激活用于创建窗体的向导。
27 WIZARDS_HID_DLGFORM_MASTER_CMDREMOVEALL 36 单击以将所有字段移到箭头所指的框中。
22 WIZARDS_HID_DLGFORM_SUB_CMDMOVEALL 36 单击以将所有字段移到箭头所指的框中。
29 WIZARDS_HID_DLGFORM_MASTER_FIELDSSELECTED 30 显示将在新表格中包括的所有字段。
29 WIZARDS_HID_QUERYWIZARD_CMDREMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
2c WIZARDS_HID_DLGTABLE_CMDMOVEDOWN_PK_SELECTED 3c 单击可将选定的字段在列表中下移一个条目。
24 WIZARDS_HID_DLGTABLE_FIELDSAVAILABLE 39 列出选定表格或查询中数据库字段的名称。
2a WIZARDS_HID_DLGFORM_MASTER_FIELDSAVAILABLE 39 列出选定表格或查询中数据库字段的名称。
2a WIZARDS_HID_DLGFORM_MASTER_CMDMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
21 WIZARDS_HID_DLGTABLE_CMDREMOVEALL 36 单击以将所有字段移到箭头所指的框中。
26 WIZARDS_HID_DLGTABLE_CMDREMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
25 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEALL 36 单击以将所有字段移到箭头所指的框中。
29 WIZARDS_HID_DLGFORM_SUB_CMDREMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
19 WIZARDS_HID2_BTN_DOC_DOWN 3c 单击可将选定的字段在列表中下移一个条目。
24 WIZARDS_HID_DLGTABLE_CMDMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
27 WIZARDS_HID_DLGFORM_SUB_FIELDSAVAILABLE 39 列出选定表格或查询中数据库字段的名称。
24 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEUP 3c 单击可将选定的字段在列表中上移一个条目。
27 WIZARDS_HID_QUERYWIZARD_CMDMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
27 WIZARDS_HID_DLGFORM_SUB_CMDMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
2d WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
27 WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVEUP 3c 单击可将选定的字段在列表中上移一个条目。
23 WIZARDS_HID_DLGFORM_MASTER_LBTABLES 2d 指定用于创建表格的表格或查询。
21 WIZARDS_HID_QUERYWIZARD_CMDMOVEUP 3c 单击可将选定的字段在列表中上移一个条目。
2f WIZARDS_HID_QUERYWIZARD_CMDFILTERREMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
21 WIZARDS_HID_QUERYWIZARD_LSTFIELDS 39 列出选定表格或查询中数据库字段的名称。
2a WIZARDS_HID_DLGTABLE_CMDMOVEUP_PK_SELECTED 3c 单击可将选定的字段在列表中上移一个条目。
20 WIZARDS_HID_DLGTABLE_CMDMOVEDOWN 3c 单击可将选定的字段在列表中下移一个条目。
27 WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDS 39 列出选定表格或查询中数据库字段的名称。
24 WIZARDS_HID_QUERYWIZARD_CMDREMOVEALL 36 单击以将所有字段移到箭头所指的框中。
23 WIZARDS_HID_QUERYWIZARD_CMDMOVEDOWN 3c 单击可将选定的字段在列表中下移一个条目。
21 WIZARDS_HID_DLGFORM_SUB_CMDMOVEUP 3c 单击可将选定的字段在列表中上移一个条目。
26 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEDOWN 3c 单击可将选定的字段在列表中下移一个条目。
17 WIZARDS_HID2_BTN_DOC_UP 3c 单击可将选定的字段在列表中上移一个条目。
29 WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVEDOWN 3c 单击可将选定的字段在列表中下移一个条目。
1e WIZARDS_HID_DLGTABLE_CMDMOVEUP 3c 单击可将选定的字段在列表中上移一个条目。
23 WIZARDS_HID_DLGFORM_SUB_CMDMOVEDOWN 3c 单击可将选定的字段在列表中下移一个条目。
22 WIZARDS_HID_QUERYWIZARD_CMDMOVEALL 36 单击以将所有字段移到箭头所指的框中。
2c WIZARDS_HID_DLGFORM_MASTER_CMDREMOVESELECTED 3f 单击以将所有选中的字段移到箭头所指的框中。
1f WIZARDS_HID_DLGTABLE_CMDMOVEALL 36 单击以将所有字段移到箭头所指的框中。
24 WIZARDS_HID_DLGFORM_SUB_CMDREMOVEALL 36 单击以将所有字段移到箭头所指的框中。
24 WIZARDS_HID_DLGFORM_CHKCREATESUBFORM 18 选择添加子窗体。
20 WIZARDS_HID_DLGFORM_lstRELATIONS 27 选择作为子窗体基础的关系。
25 WIZARDS_HID_DLGFORM_OPTSELECTMANUALLY 33 单击添加基于手动选择字段的子窗体。
29 WIZARDS_HID_DLGFORM_OPTONEXISTINGRELATION 2d 单击添加基于现有关系的子窗体。
20 WIZARDS_HID_DLGFORM_SUB_LBTABLES 2d 指定要创建子窗体的表格或查询。
26 WIZARDS_HID_DLGFORM_SUB_FIELDSSELECTED 36 显示要包括在新的子窗体中的所有字段。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK1 6c 选择连接到主窗体字段的子窗体字段，该字段可在此列表框旁边的列表框中选择。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK4 6c 选择连接到主窗体字段的子窗体字段，该字段可在此列表框旁边的列表框中选择。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK3 6c 选择连接到主窗体字段的子窗体字段，该字段可在此列表框旁边的列表框中选择。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK3 6c 选择连接到子窗体字段的主窗体字段，该字段可在此列表框旁边的列表框中选择。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK2 6c 选择连接到子窗体字段的主窗体字段，该字段可在此列表框旁边的列表框中选择。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK1 6c 选择连接到子窗体字段的主窗体字段，该字段可在此列表框旁边的列表框中选择。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK4 6c 选择连接到子窗体字段的主窗体字段，该字段可在此列表框旁边的列表框中选择。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK2 6c 选择连接到主窗体字段的子窗体字段，该字段可在此列表框旁边的列表框中选择。
22 WIZARDS_HID_DLGFORM_CMDLEFTLABELED 39 按列对齐数据库字段，标签位于字段左侧。
21 WIZARDS_HID_DLGFORM_CMDTABLESTYLE 2a 将数据库字段以表格形式对齐。
21 WIZARDS_HID_DLGFORM_CMDALIGNRIGHT 18 标签字段右对齐。
23 WIZARDS_HID_DLGFORM_CMDTOPJUSTIFIED 2a 将标签排列在相应数据的上方。
20 WIZARDS_HID_DLGFORM_CMDALIGNLEFT 12 标签左对齐。
21 WIZARDS_HID_DLGFORM_CMDTOPLABELED 39 按列对齐数据库字段，标签位于字段上方。
25 WIZARDS_HID_DLGFORM_CHKNOMODIFICATION 1b 选择禁止编辑数据。
25 WIZARDS_HID_DLGFORM_OPTDISPLAYALLDATA 3f 创建可用于显示现有数据和输入新数据的窗体。
21 WIZARDS_HID_DLGFORM_CHKNODELETION 1b 选择禁止删除数据。
21 WIZARDS_HID_DLGFORM_CHKNOADDITION 1e 选择禁止添加新数据。
22 WIZARDS_HID_DLGFORM_OPTNEWDATAONLY 2a 创建仅用于输入新数据的窗体。
1f WIZARDS_HID_DLGFORM_CMD3DBORDER 27 指定字段边框呈现三维外观。
1f WIZARDS_HID_DLGFORM_CMDNOBORDER 18 指定字段无边框。
23 WIZARDS_HID_DLGFORM_CMDSIMPLEBORDER 1e 指定字段边框呈平型。
1d WIZARDS_HID_DLGFORM_LSTSTYLES 1e 指定窗体的页面样式。
1b WIZARDS_HID_DLGFORM_TXTPATH 15 指定窗体名称。
21 WIZARDS_HID_DLGFORM_OPTMODIFYFORM 45 保存窗体，然后以编辑模式打开，可以修改其版式。
23 WIZARDS_HID_DLGFORM_OPTWORKWITHFORM 4e 保存窗体，然后以窗体文档形式打开，可输入和显示数据。
23 DBACCESS_HID_DOCUMENT_CREATE_REPWIZ 1e 激活向导以创建报表。
1c WIZARDS_HID_DLGREPORT_DIALOG 15 选择报表属性。
26 WIZARDS_HID_DLGREPORT_1_FIELDSSELECTED 2a 显示新报表中含有的所有字段。
27 WIZARDS_HID_DLGREPORT_1_FIELDSAVAILABLE 3f 在选定的表格或查询中显示数据库字段的名称。
24 WIZARDS_HID_DLGREPORT_1_CMDREMOVEALL 36 单击以将所有字段移到箭头所指的框中。
22 WIZARDS_HID_DLGREPORT_1_CMDMOVEALL 36 单击以将所有字段移到箭头所指的框中。
27 WIZARDS_HID_DLGREPORT_1_CMDMOVESELECTED 39 单击以将选中的字段移到箭头所指的框中。
29 WIZARDS_HID_DLGREPORT_1_CMDREMOVESELECTED 39 单击以将选中的字段移到箭头所指的框中。
20 WIZARDS_HID_DLGREPORT_1_LBTABLES 2a 选择要创建报表的表格或查询。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_3 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_4 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_2 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_6 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_5 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_1 75 显示要包括在报表中的字段的名称。在右侧，可以为要打印在报表中每个字段输入标签。
20 WIZARDS_HID_DLGREPORT_2_CMDGROUP 39 单击以将选中的字段移到箭头所指的框中。
27 WIZARDS_HID_DLGREPORT_2_PREGROUPINGDEST a5 列出对报表进行分组时所依据的字段。要删除一个分组级别，请选择字段名称，然后单击 < 按钮。最多可选择四个分组级别。
22 WIZARDS_HID_DLGREPORT_2_CMDUNGROUP 39 单击以将选中的字段移到箭头所指的框中。
20 WIZARDS_HID_DLGREPORT_2_GROUPING b4 根据您在向导上一页中所做的选择列出字段。要按字段对报表进行分组，请选择字段名称，然后单击 > 按钮。最多可选择四个分组级别。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND2 27 按升序对字段内容进行排序。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND2 27 按降序对字段内容进行排序。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND3 27 按升序对字段内容进行排序。
1d WIZARDS_HID_DLGREPORT_3_SORT4 36 选择对报表进行排序所依据的其他字段。
1d WIZARDS_HID_DLGREPORT_3_SORT1 39 选择对报表进行排序所依据的第一个字段。
1d WIZARDS_HID_DLGREPORT_3_SORT3 36 选择对报表进行排序所依据的其他字段。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND4 27 按降序对字段内容进行排序。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND1 27 按升序对字段内容进行排序。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND1 27 按降序对字段内容进行排序。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND3 27 按降序对字段内容进行排序。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND4 27 按升序对字段内容进行排序。
1d WIZARDS_HID_DLGREPORT_3_SORT2 36 选择对报表进行排序所依据的其他字段。
22 WIZARDS_HID_DLGREPORT_4_PAGELAYOUT 72 定义报表的页面版式。页面版式是从指定了页眉、页脚和页面背景的模板文件加载的。
20 WIZARDS_HID_DLGREPORT_4_PORTRAIT 24 为报表选择纵向页面方向。
21 WIZARDS_HID_DLGREPORT_4_LANDSCAPE 24 为报表选择横向页面方向。
22 WIZARDS_HID_DLGREPORT_4_DATALAYOUT 60 为报表定义一组样式。样式可以指定字体、缩进、表格背景及其他内容。
26 WIZARDS_HID_DLGREPORT_5_OPTUSETEMPLATE 21 单击完成时将保存报表。
27 WIZARDS_HID_DLGREPORT_5_OPTSTATDOCUMENT 66 将报表另存为静态报表。打开静态报表时，它将始终显示创建报表时的数据。
27 WIZARDS_HID_DLGREPORT_5_OPTEDITTEMPLATE 48 单击完成时将保存报表，并会打开报表以便进行编辑。
1d WIZARDS_HID_DLGREPORT_4_TITLE 2d 指定打印在每页标题行中的标题。
26 WIZARDS_HID_DLGREPORT_5_OPTDYNTEMPLATE 57 将报表另存为模板。打开动态报表时，它将显示当前的数据内容。
34 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE1_OLD_DESIGN 66 从设计列表中加载一个现有的设计，作为“向导”后续页中操作步骤的起点。
34 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE1_NEW_DESIGN 30 在向导的后续页中创建一个新设计。
1a SD_HID_SD_HTMLEXPORT_PAGE1 30 在向导的后续页中创建一个新设计。
33 sd%3APushButton%3ADLG_PUBLISHING%3APAGE1_DEL_DESIGN 2a 从设计列表中删除选定的设计。
2d sd%3AListBox%3ADLG_PUBLISHING%3APAGE1_DESIGNS 1b 显示所有现有设计。
32 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_CHG_AUTO 45 自动根据指定时间更换页面，与演示文稿的内容无关
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_ASP 97 选择 ASP 选项时，“WebCast 导出”可创建 ASP 页面。需要注意的是，只有支持 ASP 的 Web 服务器可提供 HTML 演示文稿。
32 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_STANDARD 2d 使用导出页面创建标准 HTML 页面。
2e sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_PERL 38 WebCast 导出用于创建 HTML 页面和 Perl 脚本。
31 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_WEBCAST 4a 在 WebCast 导出中，将通过 Perl 或 ASP 支持自动生成脚本。
2e sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_ENDLESS 51 显示完最后一张幻灯片后，自动重新开始播放 HTML 演示文稿。
34 sd%3ATimeField%3ADLG_PUBLISHING%3APAGE2_DURATION_TMF 27 定义每张幻灯片的显示时间。
35 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_CHG_DEFAULT c3 幻灯片的切换时间取决于您为演示文稿中的每张幻灯片设置的时间。如果您设置为手动切换页面，则按下键盘上的任意键都会使 HTML 演示文稿换页。
30 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_FRAMES 81 创建带有框架的标准 HTML 页面。导出页面将显示在主框架中，框左侧将以超链接的形式显示目录。
2c sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_NOTES 1b 指定同时显示批注。
26 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_CGI 3b 指定生成的 Perl 脚本的 URL （绝对或相对）。
26 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_URL 5b 指定保存在 Web 服务器上创建的 HTML 演示文稿的 URL （绝对或相对）。
2e sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_CONTENT 1b 创建文档的标题页。
1a SD_HID_SD_HTMLEXPORT_PAGE2 2d 使用导出页面创建标准 HTML 页面。
28 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_INDEX 49 指定观众要查看演示文稿需输入的 URL（绝对或相对）。
2f sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_KIOSK 82 创建一个默认 HTML 演示文稿作为 kiosk 导出。在该演示文稿中，会按照指定的时间自动演示幻灯片。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_3 45 要获得显示高质量的幻灯片显示，请选择高分辨率。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_JPG 7c 将文件导出为 JPEG 文件。JPEG 文件使用可调的压缩方法进行压缩，所含的颜色可以超过 256 种。
1a SD_HID_SD_HTMLEXPORT_PAGE3 77 文件导出为 PNG 格式。PNG 文件在压缩过程中不会丢失数据，并且可以包含 256 种以上颜色。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_1 60 选择低分辨率，即使演示文稿含有很多幻灯片，生成的文件也不会太大。
30 sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE3_SLD_SOUND 4e 指定导出声音文件，将该文件定义为幻灯片转换时的效果。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_GIF 74 将文件导出为 GIF 文件。GIF 文件在压缩过程中不会丢失数据，而且最多具有 256 种颜色。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_2 3c 对于中等大小的演示文稿，请选择中分辨率。
2e sd%3AComboBox%3ADLG_PUBLISHING%3APAGE3_QUALITY c5 指定 JPEG 图形的压缩比例。选择 100% 的比例，可以获得较大的数据区域和最佳的图像质量。选择 25% 的比例，可以获得较小的文件，但图像质量较差。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_PNG 77 文件导出为 PNG 格式。PNG 文件在压缩过程中不会丢失数据，并且可以包含 256 种以上颜色。
2b sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_WWW_EDIT 3f 指定您的主页，将在出版物中插入一个超链接。
29 sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_AUTHOR 21 指定出版物作者的姓名。
30 sd%3AMultiLineEdit%3ADLG_PUBLISHING%3APAGE4_MISC 30 指定要在标题页上显示的附加文字。
2d sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_EMAIL_EDIT 1b 指定电子邮件地址。
1a SD_HID_SD_HTMLEXPORT_PAGE4 21 指定出版物作者的姓名。
1a SD_HID_SD_HTMLEXPORT_PAGE5 2d 仅插入文字超链接而不插入按钮。
2f sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE5_TEXTONLY 2d 仅插入文字超链接而不插入按钮。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_BACK 4e 打开颜色对话框，您可以在其中选择演示文稿的背景颜色。
2e sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_ALINK 57 打开颜色对话框，您可以在其中选择演示文稿中活动链接的颜色。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_LINK 51 打开颜色对话框，您可以在其中选择演示文稿的超链接颜色。
2e sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_USER 3c 允许您为某些演示文稿对象定义自己的颜色。
31 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_DEFAULT 30 使用查看者网页浏览器的默认颜色。
1a SD_HID_SD_HTMLEXPORT_PAGE6 30 从当前文档使用的样式中确定颜色。
2e sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_VLINK 5a 打开颜色对话框，您可以在其中选择演示文稿中已访问链接的颜色。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_TEXT 4e 打开颜色对话框，您可以在其中选择演示文稿的文字颜色。
33 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_DOCCOLORS 30 从当前文档使用的样式中确定颜色。
20 EXTENSIONS_HID_GRIDWIZARD_FINISH f 创建对象。
21 EXTENSIONS_HID_GROUPWIZARD_FINISH f 创建对象。
20 EXTENSIONS_HID_LISTWIZARD_FINISH f 创建对象。
27 macro%3A%2F%2F%2FImportWizard.Main.Main 51 复制文档并将其转换为 $[officename] 使用的 OpenDocument XML 格式。
1c WIZARDS_HID_DLGIMPORT_DIALOG 51 复制文档并将其转换为 $[officename] 使用的 OpenDocument XML 格式。
1f WIZARDS_HID_DLGIMPORT_0_CMDHELP 51 复制文档并将其转换为 $[officename] 使用的 OpenDocument XML 格式。
20 WIZARDS_HID_DLGIMPORT_2_CHKEXCEL 4d 将 Microsoft Excel 格式 *.xls 文档转换为 OpenDocument *.ods 文档。
22 WIZARDS_HID_DLGIMPORT_0_CHKLOGFILE 48 在工作目录中建立日志文件，以显示已经转换的文档。
25 WIZARDS_HID_DLGIMPORT_0_CHKMATHGLOBAL 97 将旧格式的 Writer 主控文档 *.sgl 文档转换为 OpenDocument *.odm 文档，以及将 Math *.smf 文档转换为 OpenDocument *.odf 文档。
1f WIZARDS_HID_DLGIMPORT_0_CHKCALC 47 将旧格式的 Calc *.sdc 文档转换为 OpenDocument *.ods 文档。
22 WIZARDS_HID_DLGIMPORT_0_CHKIMPRESS 8b 将旧格式的 Draw *.sda 文档转换为 OpenDocument *.odg 文档，以及将 Impress *.sdd 文档转换为 OpenDocument *.odp 文档。
25 WIZARDS_HID_DLGIMPORT_2_CHKPOWERPOINT 52 将 Microsoft PowerPoint 格式 *.ppt 文档转换为 OpenDocument *.odp 文档。
1f WIZARDS_HID_DLGIMPORT_2_CHKWORD 4c 将 Microsoft Word 格式 *.doc 文档转换为 OpenDocument *.odt 文档。
21 WIZARDS_HID_DLGIMPORT_0_CHKWRITER 49 将旧格式的 Writer *.sdw 文档转换为 OpenDocument *.odt 文档。
26 WIZARDS_HID_DLGIMPORT_0_OPTSODOCUMENTS 50 将旧的二进制文档转换为 $[officename] 所用的 OpenDocument 格式。
26 WIZARDS_HID_DLGIMPORT_0_OPTMSDOCUMENTS 4c 将 Microsoft Word 格式 *.doc 文档转换为 OpenDocument *.odt 文档。
2d WIZARDS_HID_DLGIMPORT_2_CMDDOCUMENTPATHSELECT 30 打开一个对话框，选择所需的路径。
26 WIZARDS_HID_DLGIMPORT_2_EDTEMPLATEPATH 2a 指定用于写入目标文件的目录。
2d WIZARDS_HID_DLGIMPORT_2_CMDTEMPLATEPATHSELECT 30 打开一个对话框，选择所需的路径。
22 WIZARDS_HID_DLGIMPORT_2_CBTEMPLATE 1b 指定要转换的模板。
26 WIZARDS_HID_DLGIMPORT_2_EDDOCUMENTPATH 2a 指定用于写入目标文件的目录。
26 WIZARDS_HID_DLGIMPORT_2_LBDOCUMENTPATH 21 指定含有源文件的目录。
26 WIZARDS_HID_DLGIMPORT_2_LBTEMPLATEPATH 21 指定含有源文件的目录。
29 WIZARDS_HID_DLGIMPORT_2_CBDOCUMENTRECURSE 45 指示在查找匹配文件时还要查找选定目录的子目录。
29 WIZARDS_HID_DLGIMPORT_2_CBTEMPLATERECURSE 45 指示在查找匹配文件时还要查找选定目录的子目录。
22 WIZARDS_HID_DLGIMPORT_2_CBDOCUMENT 1b 指示要转换的文档。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON4 6f 如果在调用换算器之前选定了单元格区域，则区域中的所有货币单元格都将被转换。
23 WIZARDS_HID_DLGCONVERT_CBTARGETOPEN 5d 打开一个对话框，您可以在该对话框中选择要保存已转换文件的目录。
21 WIZARDS_HID_DLGCONVERT_CHKPROTECT b1 指定在转换时取消对工作表的保护并在转换完成后重新启用保护。如果工作表保护设置了密码，将显示一个对话框，要求您输入密码。
25 WIZARDS_HID_DLGCONVERT_CHECKRECURSIVE 36 指定是否包括选定目录的所有子文件夹。
23 WIZARDS_HID_DLGCONVERT_CBSOURCEOPEN 36 打开用于选择所需目录或文档的对话框。
1f WIZARDS_HID_DLGCONVERT_LISTBOX1 24 显示列表中要转换的区域。
20 WIZARDS_HID_DLGCONVERT_COMBOBOX1 24 指定要换算成欧元的货币。
1d WIZARDS_HID_DLGCONVERT_CBBACK 2d 返回到“欧元换算器”的第一页。
1f WIZARDS_HID_DLGCONVERT_CBCANCEL 18 关闭欧元换算器。
1f WIZARDS_HID_DLGCONVERT_TBTARGET 36 指定要保存转换的文件的文件夹和路径。
27 WIZARDS_HID_DLGCONVERT_CHKTEXTDOCUMENTS 4c 换算 $[officename] Writer 文档中的字段和表格中的货币金额。
31 macro%3A%2F%2F%2FEuro.AutoPilotRun.StartAutoPilot 8a 将 $[officename] Calc 文档中的货币金额以及 $[officename] Writer 文档中的字段和表格中的货币金额换算成欧元。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON1 3f 具有选定单元格样式的所有单元格都会被转换。
1d WIZARDS_HID_DLGCONVERT_CBHELP 1e 启动此对话框的帮助。
1d WIZARDS_HID_DLGCONVERT_OBFILE 29 转换单个 $[officename] Calc 文件。
1d WIZARDS_HID_DLGCONVERT_DIALOG 8a 将 $[officename] Calc 文档中的货币金额以及 $[officename] Writer 文档中的字段和表格中的货币金额换算成欧元。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON3 36 活动文档中的所有货币单元格都会转换。
1f WIZARDS_HID_DLGCONVERT_TBSOURCE 33 指示要转换的单个文档的目录或名称。
20 WIZARDS_HID_DLGCONVERT_CHECKBOX1 15 转换整个文档。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON2 3f 活动电子表格中的所有货币单元格都会被转换。
1d WIZARDS_HID_DLGCONVERT_CBGOON f 开始转换。
1c WIZARDS_HID_DLGCONVERT_OBDIR 5d 转换选定目录中的所有 $[officename] Calc 和 $[officename] Writer 文档和模板。
14 .uno%3AEuroConverter 8a 将 $[officename] Calc 文档中的货币金额以及 $[officename] Writer 文档中的字段和表格中的货币金额换算成欧元。
39 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_KAB 3e 如果您已经使用了 KDE 通讯簿，请选择此选项。
44 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OUTLOOKEXPRESS 5a 如果您已经在 Microsoft Outlook Express 中使用了通讯簿，请选择此选项。
3a extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_MORK 52 如果已经在 Mozilla 或 Netscape 中使用了通讯簿，请选择此选项。
c slot%3A10934 45 此向导将现有通讯簿注册为 $[officename] 中的数据源。
1e EXTENSIONS_HID_ABSPILOT_CANCEL 27 退出向导而不执行任何修改。
49 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION_GROUPWISE 4a 如果您已经在 Groupwise 中使用了通讯簿，请选择此选项。
3a extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_LDAP 48 如果已经在 LDAP 服务器上使用地址簿，请选择此选项。
3b extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OTHER 5d 如果希望在 $[officename] 中将其他数据源注册为通讯簿，请选择此选项。
2c extensions%3ATabPage%3ARID_PAGE_SELECTABTYPE 45 此向导将现有通讯簿注册为 $[officename] 中的数据源。
1e EXTENSIONS_HID_ABSPILOT_FINISH 30 创建到数据源的连接并关闭对话框。
3d extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OUTLOOK 70 如果您已经在 Microsoft Outlook（而不是 Outlook Express）中使用了通讯簿，请选择此选项。
41 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_THUNDERBIRD 4c 如果您已经使用了 Thunderbird 中的通讯簿，请选择此选项。
21 .uno%3AAutoPilotAddressDataSource 45 此向导将现有通讯簿注册为 $[officename] 中的数据源。
44 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION_LDAP 4c 如果您已经在 Evolution LDAP 中使用通讯簿，请选择此选项。
3b extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_MACAB 3f 如果您已经使用了 OS X 通讯簿，请选择此选项。
17 EXTENSIONS_HID_ABSPILOT 45 此向导将现有通讯簿注册为 $[officename] 中的数据源。
3f extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION 4a 如果您已经在 Evolution 中使用了通讯簿，请选择此选项。
4b extensions%3APushButton%3ARID_PAGE_ADMININVOKATION%3APB_INVOKE_ADMIN_DIALOG 33 调用一个用于输入附加设置的对话框。
3d extensions%3AListBox%3ARID_PAGE_TABLESELECTION%3ALB_TABLELIST 39 指定要用作 $[officename] 通讯簿模板的表格。
1f DBACCESS_HID_DSADMIN_BROWSECONN 24 使用文件对话框指定位置。
37 extensions%3ACheckBox%3ARID_PAGE_FINAL%3ACB_REGISTER_DS be 注册在 %PRODUCTNAME 中新创建的数据库文件。然后，数据库将在数据源窗口中列出 (F4)。如果清除此复选框，数据库将仅在打开数据库文件时可用。
36 extensions%3AEdit%3ARID_PAGE_FINAL%3AET_DATASOURCENAME 18 指定数据源名称。
34 extensions%3APushButton%3ARID_PAGE_FINAL%3APB_BROWSE 24 使用文件对话框指定位置。
49 extensions%3APushButton%3ARID_PAGE_FIELDMAPPING%3APB_INVOKE_FIELDS_DIALOG 2a 打开模板：指定通讯簿对话框。
1b DESKTOP_HID_FIRSTSTART_USER a1 在文档属性、模板中以及记录文档修改时都会用到用户名。仅在计算机上找不到以前安装的 %PRODUCTNAME 时，此页面才显示。
1d DESKTOP_HID_FIRSTSTART_DIALOG 5f “启动向导”可帮助您指定 %PRODUCTNAME 的用户设置。此向导仅运行一次。
1e DESKTOP_HID_FIRSTSTART_WELCOME 5f “启动向导”可帮助您指定 %PRODUCTNAME 的用户设置。此向导仅运行一次。
20 DESKTOP_HID_FIRSTSTART_MIGRATION 50 您可以使用此页从以前安装的 %PRODUCTNAME 版本导入用户数据。
1e DESKTOP_HID_FIRSTSTART_LICENSE 8d 阅读并接受许可文本。您需要滚动到许可文本的末尾，然后才能继续。如果您拒绝此许可，将会取消安装。
35 service%3Acom.sun.star.wizards.web.CallWizard%3Fstart 48 “Web 向导”可帮助您在互联网服务器上维护 Web 站点。
16 WIZARDS_HID0_WEBWIZARD 48 “Web 向导”可帮助您在互联网服务器上维护 Web 站点。
19 WIZARDS_HID1_LST_SESSIONS 6c 选择要加载的设置，然后单击加载。要使用默认设置启动向导，请选择“默认”。
18 WIZARDS_HID1_BTN_DEL_SES 18 删除选定的设置。
1b WIZARDS_HID2_LST_DOC_EXPORT 2d 选择要导出所选文件的文件格式。
18 WIZARDS_HID2_BTN_REM_DOC 24 从列表中删除选定的文件。
1a WIZARDS_HID2_TXT_DOC_TITLE 65 输入所选文档的标题。标题在 Web 站点的索引页上显示为所选文档的超链接。
19 WIZARDS_HID2_TXT_DOC_DESC 1e 输入选定文档的说明。
1b WIZARDS_HID2_TXT_DOC_AUTHOR 27 输入所选文档的作者的姓名。
18 WIZARDS_HID2_BTN_ADD_DOC 97 打开对话框，在其中选择您要上传到 Web 站点上的文件。列出的顺序决定 Web 站点索引页上文档超链接显示的顺序。
15 WIZARDS_HID2_LST_DOCS d6 列出要发布到 Web 站点的文档。在上载文档之前，该向导可能将 %PRODUCTNAME 文档转换成 HTML、PDF，有时还会转换成 Flash 格式。所有其他文件以其原始文件格式上载。
1c WIZARDS_HID3_IL_LAYOUTS_IMG3 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG8 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG9 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG7 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG6 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG2 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG1 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG5 1b 选择索引页的版式。
1c WIZARDS_HID3_IL_LAYOUTS_IMG4 1b 选择索引页的版式。
1d WIZARDS_HID4_CHK_DISPLAY_SIZE 39 在索引页上显示以千字节表示的文件大小。
20 WIZARDS_HID4_CHK_DISPLAY_UP_DATE 33 在索引页上包括文件的最后修改日期。
1e WIZARDS_HID4_CHK_DISPLAY_PAGES 2a 在索引页上显示网站的网页数。
1f WIZARDS_HID4_CHK_DISPLAY_AUTHOR 30 在索引页上包括文档创建人的姓名。
1e WIZARDS_HID4_GRP_OPTIMAIZE_640 3c 优化网站使其适用于 640x480 像素屏幕分辨率。
1e WIZARDS_HID4_GRP_OPTIMAIZE_800 3c 优化网站使其适用于 800x600 像素屏幕分辨率。
21 WIZARDS_HID4_CHK_DISPLAY_FILENAME 2a 在索引页上包括文档的文件名。
1f WIZARDS_HID4_CHK_DISPLAY_F_ICON 2a 在索引页上显示文件格式图标。
24 WIZARDS_HID4_CHK_DISPLAY_DESCRIPTION 2d 在索引页上包括文档的摘要信息。
1f WIZARDS_HID4_GRP_OPTIMAIZE_1024 3d 优化网站使其适用于 1024x768 像素屏幕分辨率。
1f WIZARDS_HID4_CHK_DISPLAY_FORMAT 24 在索引页上显示文件格式。
20 WIZARDS_HID4_CHK_DISPLAY_CR_DATE 2d 在索引页上包括文档的创建日期。
18 WIZARDS_HID5_BTN_BACKGND 21 选择索引页的背景图像。
16 WIZARDS_HID5_BTN_ICONS 33 选择要用于索引页上导航元素的图标。
17 WIZARDS_HID5_LST_STYLES 21 选择索引页的颜色图案。
17 WIZARDS_HID_BG_BTN_NONE 21 清除索引页的背景图像。
e WIZARDS_HID_BG 29 指定 Web 向导样式的背景图像。
18 WIZARDS_HID_BG_BTN_OTHER 48 打开“打开文件”对话框选择索引页的背景图像文件。
17 WIZARDS_HID_IS_BTN_NONE 1e 清除索引页的图标集。
e WIZARDS_HID_IS 44 选择在 Web 向导中浏览 HTML 演示文稿文档的图标集。
1e WIZARDS_HID6_DATE_SITE_CREATED 48 输入索引页的创建日期。日期以 HTML 元标记格式存储。
1b WIZARDS_HID6_TXT_SITE_TITLE 50 输入索引页的标题。此元素将显示在 Web 浏览器的标题栏中。
1e WIZARDS_HID6_DATE_SITE_UPDATED 48 输入索引页的修改日期。日期以 HTML 元标记格式存储。
1b WIZARDS_HID6_TXT_SITE_EMAIL 4e 输入索引页的电子邮件地址。地址以 HTML 元标记格式存储。
1f WIZARDS_HID6_TXT_SITE_COPYRIGHT 48 输入索引页的版权声明。声明以 HTML 元标记格式存储。
1a WIZARDS_HID6_TXT_SITE_DESC 42 输入索引页的说明。说明以 HTML 元标记格式存储。
14 WIZARDS_HID7_TXT_FTP 87 将文件上载到 FTP 服务器。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
1c WIZARDS_HID7_CHK_PUBLISH_FTP 87 将文件上载到 FTP 服务器。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
15 WIZARDS_HID7_CHK_SAVE 27 保存在此向导中指定的设置。
16 WIZARDS_HID7_TXT_LOCAL 91 将索引页和文件上载到本地目录。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
1e WIZARDS_HID7_CHK_PUBLISH_LOCAL 91 将索引页和文件上载到本地目录。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
15 WIZARDS_HID7_TXT_SAVE 1e 为设置文件输入名称。
14 WIZARDS_HID7_BTN_FTP 67 打开 FTP 连接对话框，您可以在该对话框中编辑和测试 FTP 服务器的连接设置。
14 WIZARDS_HID7_BTN_ZIP 51 打开一个对话框，您可以在该对话框中指定存档文件的位置。
18 WIZARDS_HID7_BTN_PREVIEW 38 在操作系统的默认 Web 浏览器中打开网页。
14 WIZARDS_HID7_TXT_ZIP bb 将索引页和文件添加到压缩的存档文件，然后将文件上载到网站。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
16 WIZARDS_HID7_BTN_LOCAL 36 打开一个对话框，可在其中选择文件夹。
1c WIZARDS_HID7_CHK_PUBLISH_ZIP bb 将索引页和文件添加到压缩的存档文件，然后将文件上载到网站。索引页保存到指定位置。所有其他文件保存到包含索引页的 "myWebsite" 目录。
18 WIZARDS_HID_FTP_TXT_PATH 3b 输入要存储文件的 FTP 服务器上的目录位置。
14 WIZARDS_HID_FTP_TEST 26 使用当前设置测试 FTP 连接。
14 WIZARDS_HID_FTP_PASS 2f 输入访问 FTP 服务器所必需的密码。
16 WIZARDS_HID_FTP_SERVER 2d 输入 FTP 服务器的名称或 IP 地址。
18 WIZARDS_HID_FTP_BTN_PATH 5f 打开一个对话框，您可以在该对话框中指定存储文件的 FTP 服务器目录。
f WIZARDS_HID_FTP 3a 编辑和测试 Web 向导的 FTP 服务器连接设置。
18 WIZARDS_HID_FTP_USERNAME 32 输入访问 FTP 服务器所必需的用户名。
1d DBACCESS_HID_QRYDGN_ROW_TABLE 39 在此列出所选数据字段对应的数据库表格。
1b DBACCESS_HID_CTL_QRYDGNCRIT 1e 选择条件以定义查询。
26 DBACCESS_HID_QUERY_EDIT_JOINCONNECTION 12 编辑联接属性
1d DBACCESS_HID_QRYDGN_ROW_ORDER 5d 如果您单击此单元格，可以选择以下排序选项：升序、降序和不排序。
28 dbaccess%3AEdit%3ADLG_SAVE_AS%3AET_TITLE 22 输入查询/表视图的名称。
20 DBACCESS_HID_QRYDGN_ROW_FUNCTION 30 选择要在此处的查询中运行的函数。
1d DBACCESS_HID_QRYDGN_ROW_FIELD 69 输入查询中所引用的数据字段的名称。下面各行中的所有设置都是针对此字段。
2d dbaccess%3AComboBox%3ADLG_SAVE_AS%3AET_SCHEMA 37 请输入为查询/表格视图指定的架构名称。
1c DBACCESS_HID_QRYDGN_ROW_CRIT 2a 指定筛选数据字段内容的条件。
1d DBACCESS_HID_QRYDGN_ROW_ALIAS 6f 指定别名。此别名将替代字段名称显示在查询中。这样就可使用用户定义的列标签。
1a DBACCESS_HID_CTL_QRYDGNTAB 45 双击字段可以将其加入到查询。拖放可以定义关系。
1f DBACCESS_HID_QRYDGN_ROW_VISIBLE 51 如果为某个数据字段选中可见属性，在查询时将显示该字段。
20 DBACCESS_HID_DLG_QRY_RIGHT_TABLE 21 指定要连结的两个表格。
1f DBACCESS_HID_DLG_QRY_LEFT_TABLE 21 指定要连结的两个表格。
21 DBACCESS_HID_DLG_QRY_JOIN_CONTROL cb 把关键字 NATURAL 插入到定义了关系的 SQL 语句中。该关系把两个表中所有具有相同列名称的列连接起来。生成的连接表对每对命名相同的列都只保留一列。
1d DBACCESS_HID_DLG_QRY_JOINTYPE 27 指定选择的链接的链接类型。
2c dbaccess%3AEdit%3ADLG_PASSWORD%3AED_PASSWORD 12 输入新密码。
2f dbaccess%3AEdit%3ADLG_PASSWORD%3AED_OLDPASSWORD 12 输入旧密码。
33 dbaccess%3AEdit%3ADLG_PASSWORD%3AED_PASSWORD_REPEAT 18 再次输入新密码。
29 sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_USER 1b 指定新用户的名称。
22 DBACCESS_HID_TAB_ENT_FORMAT_SAMPLE 2f 显示通过 ... 按钮选择的格式代码。
1f DBACCESS_HID_TABDESIGN_TYPECELL 15 指定字段类型。
29 DBACCESS_HID_TABLEDESIGN_TABED_PRIMARYKEY 57 如果此命令具有选中标记，则表示该行中的数据字段为主关键字。
21 DBACCESS_HID_TABDESIGN_BACKGROUND 27 此区域用来定义表格的结构。
1c DBACCESS_HID_TAB_ENT_DEFAULT 27 指定新数据记录中的默认值。
23 DBACCESS_HID_TABLEDESIGN_INSERTROWS 8a 如果尚未保存表格，则在当前行的上方插入一个空行。如果已经保存表格，则在表格末尾插入一个空行。
25 DBACCESS_HID_TABLE_DESIGN_HELP_WINDOW 15 显示帮助文字。
1f DBACCESS_HID_TABDESIGN_NAMECELL 6c 指定数据字段的名称。请注意数据库的限制，如名称的长度、特殊字符和空格等。
1b DBACCESS_HID_TAB_ENT_FORMAT 2a 此按钮将打开字段格式对话框。
22 DBACCESS_HID_TABDESIGN_COMMENTCELL 18 指定可选的说明。
2c DBACCESS_HID_DLGINDEX_INDEXDETAILS_SORTORDER 15 确定排序顺序。
1c DBACCESS_HID_DLGIDX_NEWINDEX 15 创建新的索引。
28 dbaccess%3AModalDialog%3ADLG_INDEXDESIGN 3c 索引设计对话框允许您编辑当前表格的索引。
14 .uno%3ADBIndexDesign 3c 索引设计对话框允许您编辑当前表格的索引。
1d DBACCESS_HID_DLGIDX_DROPINDEX 15 删除当前索引。
1e DBACCESS_HID_DLGIDX_RESETINDEX 36 将当前索引重设为启动对话框时的设置。
32 dbaccess%3APushButton%3ADLG_INDEXDESIGN%3APB_CLOSE 12 关闭对话框。
1f DBACCESS_HID_DLGIDX_RENAMEINDEX 18 重命名当前索引。
1d DBACCESS_HID_DLGIDX_SAVEINDEX 24 在数据源中保存当前索引。
1d DBACCESS_HID_DLGIDX_INDEXLIST 66 显示可用索引。从列表中选择索引来编辑。选定索引的细节在对话框中显示。
28 DBACCESS_HID_DLGINDEX_INDEXDETAILS_FIELD 45 显示当前表格中的字段列表。您可以选择多个字段。
31 dbaccess%3ACheckBox%3ADLG_INDEXDESIGN%3ACB_UNIQUE 2d 指定当前索引是否只允许唯一值。
1c DBACCESS_HID_CTL_RELATIONTAB 54 在此可以通过公用数据字段将当前数据库中的表格链接在一起。
1d DBACCESS_HID_RELDLG_KEYFIELDS 21 定义关系的关键字字段。
2a DBACCESS_HID_RELATIONDIALOG_RIGHTFIELDCELL 3c 为链接选择的表格名称在这里显示为列名称。
14 .uno%3ADBAddRelation 33 允许您定义和修改两表格之间的关系。
29 DBACCESS_HID_RELATIONDIALOG_LEFTFIELDCELL 3c 为链接选择的表格名称在这里显示为列名称。
3b dbaccess%3ACheckBox%3ATAB_WIZ_COPYTABLE%3ACB_PRIMARY_COLUMN 3c 自动生成主键数据字段，并并在其中填写值。
3a dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_APPENDDATA 36 将要复制的表格数据追加到现有表格中。
34 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_VIEW ab 如果数据库支持视图，只有复制了表格容器中的查询后才能选择此选项。利用此选项，可以普通表格视图的形式查看并编辑查询。
37 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_DEFDATA 26 创建数据库表格的 1:1 副本。
30 dbaccess%3AEdit%3ATAB_WIZ_COPYTABLE%3AET_KEYNAME 3c 为生成的主关键字指定名称，此名称为可选。
33 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_DEF 33 仅复制表格定义，不复制相应的数据。
23 DBACCESS_HID_TAB_WIZ_TABLENAME_EDIT 18 指定副本的名称。
45 dbaccess%3AMultiListBox%3ATAB_WIZ_COLUMN_SELECT%3ALB_NEW_COLUMN_NAMES 2d 列出要包含在复制表格中的字段。
3e dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMNS_RH 57 添加或删除选定字段 （> 或< 按钮）或全部字段（<< 或 >> 按钮）。
3d dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMN_RH 57 添加或删除选定字段 （> 或< 按钮）或全部字段（<< 或 >> 按钮）。
3e dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMNS_LH 57 添加或删除选定字段 （> 或< 按钮）或全部字段（<< 或 >> 按钮）。
3d dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMN_LH 57 添加或删除选定字段 （> 或< 按钮）或全部字段（<< 或 >> 按钮）。
45 dbaccess%3AMultiListBox%3ATAB_WIZ_COLUMN_SELECT%3ALB_ORG_COLUMN_NAMES b2 列出可以包含在复制的表格中的可用数据字段。要复制某数据字段，单击字段名称，然后单击 > 按钮。要复制全部字段，单击 >> 按钮。
35 dbaccess%3APushButton%3ATAB_WIZ_TYPE_SELECT%3APB_AUTO 1b 启用自动类型识别。
19 DBACCESS_HID_TAB_ENT_TYPE 15 选择字段类型。
1d DBACCESS_HID_TAB_ENT_TEXT_LEN 21 输入数据字段的字符数。
1f DBACCESS_HID_TAB_ENT_COLUMNNAME 51 显示选定的数据字段的名称。如果需要，可以输入新的名称。
43 dbaccess%3AMultiListBox%3ATAB_WIZ_TYPE_SELECT%3ALB_NEW_COLUMN_NAMES 33 列出要包含在复制表格中的数据字段。
37 dbaccess%3ANumericField%3ATAB_WIZ_TYPE_SELECT%3AET_AUTO 2a 输入用于自动类型识别的行数。
21 DBACCESS_HID_TAB_ENT_BOOL_DEFAULT 28 选择“是/否”字段的默认值。
18 DBACCESS_HID_TAB_ENT_LEN 21 输入数据字段的字符数。
1a DBACCESS_HID_TAB_ENT_SCALE 63 输入数据字段的小数位数。此选项仅适用于数字数据字段或小数数据字段。
29 DBACCESS_HID_TAB_NAMEMATCHING_COLS_ASSIGN 84 列出目标表格中可能包含的数据字段。只有在源表格列表中选定的数据字段才会包含在目标表格中。
45 dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_DOWN_RIGHT 33 将选定的条目在列表中下移一个位置。
3f dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_DOWN 33 将选定的条目在列表中下移一个位置。
36 dbaccess%3APushButton%3ATAB_WIZ_NAME_MATCHING%3APB_ALL 27 选择列表中的所有数据字段。
3d dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_UP 33 将选定的条目在列表中上移一个位置。
28 DBACCESS_HID_TAB_NAMEMATCHING_COLS_AVAIL 144 列出源表格中的数据字段。要使目标表格中包含源表格中的数据字段，请选中该数据字段名称前面的复选框。要将源表格中数据字段的内容映射到目标表格中不同的数据字段，请单击源表格列表中的数据字段，然后单击向上方向键或向下方向键。
43 dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_UP_RIGHT 33 将选定的条目在列表中上移一个位置。
37 dbaccess%3APushButton%3ATAB_WIZ_NAME_MATCHING%3APB_NONE 24 清除列表中的所有复选框。
28 DBACCESS_HID_DSADMIN_AUTORETRIEVEENABLED 5a 启用 $[officename] 对当前 ODBC 或 JDBC 数据源的自动递增数据字段支持。
27 DBACCESS_HID_DSADMIN_AUTOINCREMENTVALUE 68 输入 SQL 命令说明符，此说明符用来指示数据源自动递增指定的整数数据字段。
1f DBACCESS_HID_DSADMIN_SQL92CHECK 6a 仅允许在数据源中使用符合 SQL92 命名规则的字符的名称。其他字符均不能使用。
22 DBACCESS_HID_DSADMIN_RETRIEVE_AUTO 50 输入返回主关键字数据字段最后一个自动递增值的 SQL 语句。
1f DBACCESS_HID_DSADMIN_USECATALOG be 使用当前“目录”中的数据源。当 ODBC 数据源作为数据库服务器时此选项十分有用。如果 ODBC 数据源作为 dBASE 驱动程序，将此复选框保留为空。
21 DBACCESS_HID_DSADMIN_ODBC_OPTIONS 4e 如果有必要，使用此文本字段输入其他驱动程序设置选项。
23 DBACCESS_HID_DSADMIN_DBASE_INDICIES 5b 打开索引对话框，在其中您可以组织当前 dBASE 数据库内的表格索引。
20 DBACCESS_HID_DSADMIN_SHOWDELETED 78 显示文件中的所有记录，包括标记为已删除的记录。如果选择此复选框，则无法删除记录。
36 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_REMOVE 38 将选定的表格索引移到 自由索引 列表中。
37 dbaccess%3AListBox%3ADLG_DBASE_INDEXES%3ALB_FREEINDEXES 2d 列出可以指定给表格的可用索引。
39 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_REMOVEALL 35 将所有表格索引移到 自由索引 列表中。
33 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_ADD 32 将选定的索引移到 表格索引 列表中。
36 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_ADDALL 33 将所有自由索引移到表格索引列表中。
38 dbaccess%3AListBox%3ADLG_DBASE_INDEXES%3ALB_TABLEINDEXES 30 列出选定的数据库表格的当前索引。
33 dbaccess%3AComboBox%3ADLG_DBASE_INDEXES%3ACB_TABLES 24 选择要索引的数据库表格。
34 dbaccess%3AMultiLineEdit%3ADLG_DIRECTSQL%3AME_STATUS 32 显示 SQL 命令的执行结果，包括错误。
32 dbaccess%3APushButton%3ADLG_DIRECTSQL%3APB_EXECUTE 36 执行您在要执行的命令框中输入的命令。
31 dbaccess%3AMultiLineEdit%3ADLG_DIRECTSQL%3AME_SQL 26 输入要执行的 SQL 管理命令。
2f dbaccess%3AListBox%3ADLG_DIRECTSQL%3ALB_HISTORY 68 列出以前执行过的 SQL 命令。要再次执行命令，请单击该命令，然后单击执行。
27 DBACCESS_HID_DSADMIN_SUPPRESS_VERSIONCL cb 某些数据库通过向修改的字段指定版本号来跟踪每个数据记录的修改。字段每被修改一次，版本号就递增 1。在数据库表格中显示数据记录的内部版本号。
d .uno%3ASortup 48 从字母表的起始字母开始，按升序排序表格名称列表。
1c DBACCESS_HID_TAB_PAGE_LBUSER 24 选择要修改其设置的用户。
1c DBACCESS_HID_TAB_PAGE_PBUSER 2a 添加访问选定数据库的新用户。
1f DBACCESS_HID_TAB_PAGE_TBLGRANTS 42 显示并允许您编辑选定用户对数据库的访问权限。
1e DBACCESS_HID_TAB_PAGE_PBCHGPWD 33 修改用于访问数据库的当前用户密码。
22 DBACCESS_HID_TAB_PAGE_PBUSERDELETE 18 删除选定的用户。
31 dbaccess%3AListBox%3ADLG_ADABASSTAT%3ALB_DATADEVS 2f 显示 DATADEVSPACE 文件的路径和名称。
34 dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_TRANSACTIONLOG 31 显示 TRANSACTIONLOG 文件的路径和名称。
2e dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_FREESIZE 3a 显示数据库的可用空间量（以 MB 为单位）。
39 dbaccess%3ANumericField%3ADLG_ADABASSTAT%3AET_MEMORYUSING 39 显示数据库的已用空间量，用百分比表示。
2a dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_SIZE 37 显示数据库的完整大小（以 MB 为单位）。
31 dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_SYSDEVSPACE 2e 显示 SYSDEVSPACE 文件的路径和名称。
24 DBACCESS_HID_DLG_ADABAS_DATADEVSPACE 22 输入数据 DEVSPACE 的路径。
26 DBACCESS_HID_DLG_ADABAS_TRANSACTIONLOG 24 输入事务日志文件的路径。
1e DBACCESS_HID_DLG_ADABAS_CONUSR 99 输入要赋予修改某些数据库参数的有限权限的用户名称。通常情况下，不能修改 control 用户名称和密码的默认设置。
2b DBACCESS_HID_DLG_ADABAS_TRANSACTIONLOG_SIZE 31 输入事务文件的大小，以 MB 为单位。
23 DBACCESS_HID_DLG_ADABAS_SYSDEVSPACE 22 输入系统 DEVSPACE 的路径。
1b DBACCESS_HID_DLG_ADABAS_USR 77 输入 Adabas 内部使用的域用户名称。通常情况下，不能修改域用户名称和密码的默认设置。
29 DBACCESS_HID_DLG_ADABAS_DATADEVSPACE_SIZE 4a 在此处输入数据库的大小，以 MB 为单位。最大为 100 MB。
1e DBACCESS_HID_DLG_ADABAS_SYSPWD f 输入密码。
1e DBACCESS_HID_DLG_ADABAS_SYSUSR 24 输入数据库管理员的名称。
25 DBACCESS_HID_DLG_ADABAS_PBSYSDEVSPACE 36 找到要保存文件的目录，然后单击确定。
26 DBACCESS_HID_DLG_ADABAS_PBDATADEVSPACE 36 找到要保存文件的目录，然后单击确定。
21 DBACCESS_HID_DLG_ADABAS_DOMAINPWD f 输入密码。
22 DBACCESS_HID_DLG_ADABAS_CACHE_SIZE 34 输入数据缓冲区的大小，以 MB 为单位。
1e DBACCESS_HID_DLG_ADABAS_CONPWD f 输入密码。
1e DBACCESS_HID_DLG_ADABAS_DBNAME 1b 键入数据库的名称。
28 DBACCESS_HID_DLG_ADABAS_PBTRANSACTIONLOG 36 找到要保存文件的目录，然后单击确定。
1b HID_DSADMIN_ESCAPE_DATETIME 31 选择以使用 ODBC 日期/时间统一格式。
1b DBACCESS_HID_DSADMIN_SCHEMA 32 允许您在 SELECT 语句中使用架构名称。
24 DBACCESS_HID_DSADMIN_ENABLEOUTERJOIN 4f 将换码序列用于外部连接。此换码序列的语法是 {oj outer-join}
1f HID_DSADMIN_PRIMARY_KEY_SUPPORT 4b 使得数据库原来用于检测其是否支持主键的启发法无效。
1c DBACCESS_HID_DSADMIN_CATALOG b2 使用当前目录中的数据源。当 ODBC 数据源作为数据库服务器时此选项十分有用。如果 ODBC 数据源作为 dBASE 驱动程序，则不要选此选项。
2a DBACCESS_HID_DSADMIN_CHECK_REQUIRED_FIELDS d2 当您在表单中添加一条新的记录或更新一条现有记录，如果将一个字段留空，但该字段与一个必填的数据库列相对应，您将会看到一个关于空字段的提醒信息。
27 DBACCESS_HID_DSADMIN_PARAMETERNAMESUBST 33 用问号 ? 替换数据源中已命名的参数。
27 DBACCESS_HID_DSADMIN_SUPPRESS_VERSIONCL 33 显示数据库表格中记录的内部版本号。
26 HID_DSADMIN_AS_BEFORE_CORRELATION_NAME 8c 某些数据库在名称及其别名之间使用关键字 "AS"，而其他数据则使用空格。启用此选项可在别名前插入 AS。
26 DBACCESS_HID_DSADMIN_BOOLEANCOMPARISON 27 选择要使用的布尔比较类型。
26 DBACCESS_HID_DSADMIN_IGNOREDRIVER_PRIV 33 忽略数据库驱动程序提供的访问权限。
25 DBACCESS_HID_DSADMIN_APPENDTABLEALIAS 32 在 SELECT 语句中追加表格名称的别名。
20 DBACCESS_HID_DSADMIN_DOSLINEENDS 73 选择该选项将使用 CR + LF 代码对结束每个文本行（DOS 和 Windows 操作系统的首选操作）。
28 DBACCESS_HID_DSADMIN_IGNOREINDEXAPPENDIX 29 使用 ASC 或 DESC 语句创建索引。
1c DBACCESS_HID_DSADMIN_CHARSET 63 选择要用来在 $[officename] 中查看此数据库的字符集。此设置不影响数据库。
24 DBACCESS_HID_DSADMIN_SPECIAL_MESSAGE 27 选择要连接的数据库的类型。
1f DBACCESS_HID_DSADMIN_DBASE_PATH 2e 输入包含 dBASE 文件的目录的路径。
2d DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLCLASS 3c 输入连接到数据源的 JDBC 驱动程序类的名称。
2e DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLDBNAME 1b 输入数据库的名称。
3c dbaccess%3ACheckBox%3APAGE_CONNECTION%3ACB_PASSWORD_REQUIRED 48 如果选中，则会要求用户输入访问数据库所需的密码。
1e DBACCESS_HID_DSADMIN_CALC_PATH 39 输入要用作数据库的电子表格文档的路径。
31 DBACCESS_HID_PAGE_DBWIZARD_JDBC_PB_TESTMYSQLCLASS 33 通过 JDBC 驱动程序类测试数据库连接。
1e DBACCESS_HID_DSADMIN_FLAT_PATH 33 输入指向文本文件所在文件夹的路径。
3b dbaccess%3APushButton%3APAGE_CONNECTION%3APB_TESTCONNECTION 27 用当前设置测试数据库连接。
20 DBACCESS_HID_DLG_DATABASE_WIZARD 4b 数据库向导可以创建包含有关数据库信息的数据库文件。
3a dbaccess%3ARadioButton%3APAGE_GENERAL%3ARB_OPENEXISTINGDOC 57 选择从最近使用的文件列表或文件选择对话框中打开数据库文件。
39 DBACCESS_HID_PAGE_DBWIZARD_GENERAL_RB_GETEXISTINGDATABASE 36 选择创建现有数据库连接的数据库文档。
36 DBACCESS_HID_PAGE_DBWIZARD_GENERAL_RB_CREATEDBDATABASE 1b 选择创建新数据库。
36 dbaccess%3APushButton%3APAGE_GENERAL%3APB_OPENDOCUMENT ae 打开文件选择对话框，您可以在其中选择数据库文件。在文件选择对话框中单击“打开”或“确定”，立即打开该文件并退出向导。
33 dbaccess%3AListBox%3APAGE_GENERAL%3ALB_DOCUMENTLIST 7a 从最近使用的文件列表中选择要打开的数据库文件。单击"完成"可立即打开文件并退出向导。
1b DBACCESS_HID_DSADMIN_DSTYPE 30 选择现有数据库连接的数据库类型。
36 DBACCESS_HID_PAGE_DBWIZARD_FINAL_RB_REGISTERDATASOURCE 112 选择在 %PRODUCTNAME 的用户副本内注册数据库。注册之后，数据库将显示在视图 - 数据源窗口中。您必须注册一个数据库，以便能够将数据库字段插入到文档中（“插入”-“字段”-“其他”）或合并到邮件中。
34 DBACCESS_HID_PAGE_DBWIZARD_FINAL_CB_STARTTABLEWIZARD 3c 选择在“数据库向导”结束后调用表格向导。
3a DBACCESS_HID_PAGE_DBWIZARD_FINAL_RB_DONTREGISTERDATASOURCE 3f 选择仅在创建的数据库文档中保留数据库信息。
32 DBACCESS_HID_PAGE_DBWIZARD_FINAL_CB_OPENAFTERWARDS 48 选择显示数据库文档，您可以在其中编辑数据库结构。
37 DBACCESS_HID_PAGE_DBWIZARD_MSACCESS_PB_MSACCESSLOCATION 27 单击可打开文件选择对话框。
37 DBACCESS_HID_PAGE_DBWIZARD_MSACCESS_ET_MSACCESSLOCATION 21 指定数据库文件的路径。
2f DBACCESS_HID_PAGE_DBWIZARD_ADABAS_PB_ADABASNAME 27 单击可打开文件选择对话框。
2f DBACCESS_HID_PAGE_DBWIZARD_ADABAS_ET_ADABASNAME 21 输入数据库文件的名称。
3e DBACCESS_HID_PAGE_DBWIZARD_AUTHENTIFICATION_ET_GENERALUSERNAME 2b 用户名最多可以包含 18 个字符。
28 DBACCESS_HID_PAGE_DBWIZARD_ADO_PB_ADOURL 27 单击可打开文件选择对话框。
46 DBACCESS_HID_PAGE_DBWIZARD_AUTHENTIFICATION_CB_GENERALPASSWORDREQUIRED 28 密码必须包含 3 到 18 个字符。
28 DBACCESS_HID_PAGE_DBWIZARD_ADO_ET_ADOURL 16 输入数据源 URL。
31 DBACCESS_HID_PAGE_DBWIZARD_DBASE_ET_DBASELOCATION 25 输入 dBASE *.dbf 文件的路径。
31 DBACCESS_HID_PAGE_DBWIZARD_DBASE_PB_DBASELOCATION 1e 打开路径选择对话框。
2a DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_JDBCURL cf 输入数据库的 URL。例如，对于 MySQL JDBC 驱动程序，输入 "jdbc:mysql://<Servername>/<name of the database>". 有关 JDBC 驱动程序的详细信息，请参阅驱动程序附带的文档。
20 DBACCESS_HID_DSADMIN_DRIVERCLASS 24 输入 JDBC 驱动程序的名称。
2c DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLPORT cf 输入数据库的 URL。例如，对于 MySQL JDBC 驱动程序，输入 "jdbc:mysql://<Servername>/<name of the database>". 有关 JDBC 驱动程序的详细信息，请参阅驱动程序附带的文档。
32 DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLHOSTSERVER 3a hostname 是运行 MySQL 数据库的计算机的名称。
29 DBACCESS_HID_PAGE_DBWIZARD_LDAP_CB_USESSL 4e 通过“安全套接字层 (SSL)”创建到 LDAP 服务器的安全连接。
20 DBACCESS_HID_DSADMIN_LDAP_BASEDN 3b 输入查找 LDAP 数据库的起点，例如，"dc=com"。
2d DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_PORTNUMBER 31 输入 LDAP 服务器的端口，通常为 389。
29 DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_BASEDN 3b 输入查找 LDAP 数据库的起点，例如，"dc=com"。
22 DBACCESS_HID_DSADMIN_LDAP_HOSTNAME 3f 输入 LDAP 服务器的名称，格式为 "ldap.server.com"。
2d DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_HOSTSERVER 3f 输入 LDAP 服务器的名称，格式为 "ldap.server.com"。
24 DBACCESS_HID_DSADMIN_LDAP_PORTNUMBER 31 输入 LDAP 服务器的端口，通常为 389。
32 DBACCESS_HID_PAGE_DBWIZARD_MYSQL_RB_CONNECTVIAJDBC 36 连接到系统级上设置的现有 JDBC 数据源。
32 DBACCESS_HID_PAGE_DBWIZARD_MYSQL_RB_CONNECTVIAODBC 36 连接到系统级上设置的现有 ODBC 数据源。
37 DBACCESS_HID_PAGE_DBWIZARD_ODBC_ET_NAMEOFODBCDATASOURCE 21 输入数据库文件的路径。
37 DBACCESS_HID_PAGE_DBWIZARD_ODBC_PB_NAMEOFODBCDATASOURCE 2d 单击打开 ODBC 数据源选择对话框：
30 DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLECLASS 22 输入数据库服务器的 URL。
2f DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLEPORT 27 输入数据库服务器的端口号。
35 DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLEHOSTSERVER 23 输入 Oracle 数据库的名称。
39 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_PB_SPREADSHEETPATH 27 单击可打开文件选择对话框。
45 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_CB_SPREADSHEETPASSWORDREQUIRED 2d 选择从数据库文档用户请求密码。
39 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_ET_SPREADSHEETPATH 30 输入电子表格文件的路径和文件名。
28 DBACCESS_HID_DSADMIN_THOUSANDS_SEPARATOR 6e 输入或选择在文本文件中用作千位分隔符的字符，例如，逗号 (1,000) 或句点 (1.000)。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_ET_LOCATIONTEXTFILE ea 输入文本文件或文件的路径。如果仅需要一个文本文件，可使用任一文件扩展名。如果输入文件夹名称，该文件夹中的文本文件必须带有可识别为文本数据库文件的扩展名 *.csv。
26 DBACCESS_HID_DSADMIN_DECIMAL_SEPARATOR 6a 输入或选择在文本文件中用作小数分隔符的字符，例如，句点 (0.5) 或逗号 (0,5)。
24 DBACCESS_HID_DSADMIN_FIELD_SEPARATOR 45 输入或选择用于分隔文本文件中的数据字段的字符。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_PB_LOCATIONTEXTFILE 27 单击可打开文件选择对话框。
23 DBACCESS_HID_DSADMIN_TEXT_SEPARATOR 45 输入或选择用于识别文本文件中的文字字段的字符。
2f DBACCESS_HID_PAGE_DBWIZARD_TEXT_ET_OWNEXTENSION 42 单击以访问自定义文件。在文字框中输入扩展名。
31 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSCSVFILES 1d 单击以访问 csv 文件。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSOTHERFILES 42 单击以访问自定义文件。在文字框中输入扩展名。
31 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSTXTFILES 1d 单击以访问 txt 文件。
f .uno%3ADBRename 69 重命名选定的对象。根据数据库的不同，某些名称、字符和名称长度可能无效。
f .uno%3ADBDelete 30 删除选定表格、查询、窗体或报表。
1f .uno%3ADBDatabasePropertiesMenu 12 打开子菜单。
d .uno%3ADBOpen 30 打开处于上次保存状态的选定对象。
16 .uno%3ADBConvertToView ba 将选定的查询转换成视图。原始查询保存在数据库文件中，并在数据库服务器上生成一个视图。必须具有写入权限才能将视图添加到数据库。
1b .uno%3ADBDSAdvancedSettings 24 打开“高级属性”对话框。
21 DBACCESS_HID_BROWSER_TABLE_DELETE 30 删除选定表格、查询、窗体或报表。
d .uno%3ADBEdit 51 打开一个窗口，可在其中编辑选定表格、查询、表单或报表。
29 .uno%3ADBNewFormAutoPilotWithPreSelection 39 为选定的表格、查询或视图启动窗体向导。
19 .uno%3ADBDSConnectionType 1b 打开连接类型向导。
15 .uno%3ADBDSProperties 27 打开“数据库属性”对话框。
2b .uno%3ADBNewReportAutoPilotWithPreSelection 36 为选定的表格、查询或视图启动报表向导
1f DBACCESS_HID_BROWSER_TABLE_EDIT 51 打开一个窗口，可在其中编辑选定表格、查询、表单或报表。
19 .uno%3ADBSendReportAsMail cf 打开默认电子邮件应用程序发送新的电子邮件。选定报表被附加为附件。您可以输入主题、收件人和邮件正文。动态报表在导出时作为数据库内容副本导出。
1b .uno%3ADBSendReportToWriter 63 将选定报表导出到文本文档。动态报表在导出时作为数据库内容副本导出。
49 dbaccess%3AImageButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_NEWFOLDER 33 单击可在数据库文件中创建新文件夹。
42 dbaccess%3AImageButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_UP 33 单击可在文件夹层次结构中向上一级。
43 dbaccess%3APushButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_SAVE 30 单击可将表单保存到数据库文件中。
40 dbaccess%3AEdit%3ADLG_COLLECTION_VIEW%3AED_EXPLORERFILE_FILENAME 24 为保存的表单输入文件名。
10 .uno%3ADBNewView 21 以设计模式打开新视图。
11 .uno%3ADBNewTable 1b 打开表格设计视图。
12 .uno%3ADBNewReport 42 为选定的表格、视图和查询启动报表构建器窗口。
12 .uno%3ADBNewFolder 60 打开一个对话框，您可以在该对话框中将新文件夹保存在数据库文件中。
10 .uno%3ADBNewForm 27 以窗体模式打开新文本文档。
14 .uno%3ADBNewQuerySql 20 以 SQL 模式打开新查询。
13 .uno%3ADBNewViewSQL 20 以 SQL 模式打开新视图。
11 .uno%3ADBNewQuery 21 以设计模式打开新查询。
28 DBACCESS_HID_BROWSER_TABLE_CREATE_DESIGN 1b 打开表格设计视图。
23 DBACCESS_HID_DSADMIN_TABLE_SELECTOR 6f 打开“表格过滤器”对话框，您可以在该对话框中指定显示或隐藏哪个数据库表格。
14 .uno%3ADBTableFilter 6f 打开“表格过滤器”对话框，您可以在该对话框中指定显示或隐藏哪个数据库表格。
12 .uno%3ADBUserAdmin 48 打开“用户管理”对话框（如果数据库支持此功能）。
12 .uno%3ADBDirectSQL 46 打开 SQL 对话框，您可以在该对话框中输入 SQL 语句。
17 .uno%3ADBRelationDesign 48 打开关系设计视图，并检查数据库连接是否支持关系。
17 .uno%3ADBDisablePreview 2d 在数据库窗口中启用或禁用预览。
10 .uno%3ADBPreview 12 打开子菜单。
d .uno%3ADBSort 12 打开子菜单。
1c .uno%3ADBDatabaseObjectsMenu 12 打开子菜单。
1b .uno%3ADBShowDocInfoPreview 33 预览窗口显示窗体或报表的文档信息。
17 .uno%3ADBShowDocPreview 27 预览显示窗体或报表的文档。
12 .uno%3ADBViewForms 3f 选择窗体容器，并在细节视图中显示所有窗体。
13 .uno%3ADBViewTables 3f 选择表格容器，并在细节视图中显示所有表格。
14 .uno%3ADBViewQueries 3f 选择查询容器，并在细节视图中显示所有查询。
16 .uno%3ADBRefreshTables f 刷新表格。
14 .uno%3ADBViewReports 3f 选择报表容器，并在细节视图中显示所有报表。
45 dbaccess%3APushButton%3ATP_SAVE_DBDOC_AS%3APB_BROWSE_SAVE_AS_LOCATION ca 选择保存位置并输入文件名以保存该新建数据库文件。默认情况下，新文件的名称会和旧文件的名称一样，同时旧文件会被用字符串 "backup" 进行重命名。
32 dbaccess%3AMultiLineEdit%3ATP_SUMMARY%3AED_CHANGES 3f 该列表显示了应用到该数据库文件的所有更改。
17 .uno%3ADBMigrateScripts 81 “数据库文档宏移植向导”可以把旧库文件的子文档中现有的宏移动到新库文件的宏存储区域中。
36 svtools%3ACheckBox%3ADLG_LOGIN%3ACB_LOGIN_SAVEPASSWORD 89 再次连接到当前 %PRODUCTNAME 会话中的同一数据源时，选择使用相同的用户名和密码而不显示其他对话框。
2e svtools%3AEdit%3ADLG_LOGIN%3AED_LOGIN_PASSWORD 24 输入连接到数据源的密码。
2e svtools%3AEdit%3ADLG_LOGIN%3AED_LOGIN_USERNAME 27 输入连接到数据源的用户名。
17 WIZARDS_HID_QUERYWIZARD 36 “查询向导”可帮助您设计数据库查询。
21 WIZARDS_HID_QUERYWIZARD_LSTTABLES 21 指定要创建查询的表格。
24 WIZARDS_HID_QUERYWIZARD_LSTSELFIELDS 30 显示要包括在新查询中的所有字段。
1d WIZARDS_HID_QUERYWIZARD_SORT2 5a 如果之前的排序字段相同，指定已创建查询排序所依据的其他字段。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND4 30 单击可按字母或数字顺序降序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND1 30 单击可按字母或数字顺序降序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND3 30 单击可按字母或数字顺序降序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND4 30 单击可按字母或数字顺序升序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND2 30 单击可按字母或数字顺序升序排序。
1d WIZARDS_HID_QUERYWIZARD_SORT4 5a 如果之前的排序字段相同，指定已创建查询排序所依据的其他字段。
1d WIZARDS_HID_QUERYWIZARD_SORT3 5a 如果之前的排序字段相同，指定已创建查询排序所依据的其他字段。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND2 30 单击可按字母或数字顺序降序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND1 30 单击可按字母或数字顺序升序排序。
1d WIZARDS_HID_QUERYWIZARD_SORT1 30 指定已创建查询排序所依据的字段。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND3 30 单击可按字母或数字顺序升序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTMATCHALL 38 选择使用逻辑 AND 根据所有条件筛选查询。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_1 1b 输入筛选条件的值。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_2 24 选择筛选条件的字段名称。
23 WIZARDS_HID_QUERYWIZARD_OPTMATCHANY 37 选择使用逻辑 OR 根据任一条件筛选查询。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_1 24 选择筛选条件的字段名称。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_1 15 选择筛选条件。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_2 15 选择筛选条件。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_3 1b 输入筛选条件的值。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_2 1b 输入筛选条件的值。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_3 15 选择筛选条件。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_3 24 选择筛选条件的字段名称。
2e WIZARDS_HID_QUERYWIZARD_LSTAGGREGATEFUNCTION_1 15 选择聚合函数。
29 WIZARDS_HID_QUERYWIZARD_BTNAGGREGATEMINUS 1b 删除最后一行控件。
2c WIZARDS_HID_QUERYWIZARD_LSTAGGREGATEFIELDS_1 18 选择数字字段名。
2f WIZARDS_HID_QUERYWIZARD_OPTAGGREGATEDETAILQUERY 21 选择显示所有查询记录。
28 WIZARDS_HID_QUERYWIZARD_BTNAGGREGATEPLUS 18 附加新的控件行。
30 WIZARDS_HID_QUERYWIZARD_OPTAGGREGATESUMMARYQUERY 27 选择仅显示聚合函数的结果。
2a WIZARDS_HID_QUERYWIZARD_LSTFILTERSELFIELDS 2a 显示用于组合查询的所有字段。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_1 15 选择组合条件。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_1 24 选择组合条件的字段名称。
28 WIZARDS_HID_QUERYWIZARD_OPTGROUPMATCHANY 37 选择使用逻辑 OR 根据任一条件组合查询。
28 WIZARDS_HID_QUERYWIZARD_OPTGROUPMATCHALL 38 选择使用逻辑 AND 根据所有条件组合查询。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_1 1b 输入组合条件的值。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_2 1b 输入组合条件的值。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_3 15 选择组合条件。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_2 15 选择组合条件。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_2 24 选择组合条件的字段名称。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_3 1b 输入组合条件的值。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_3 24 选择组合条件的字段名称。
22 WIZARDS_HID_QUERYWIZARD_TXTTITLE_1 1e 输入字段名称的别名。
26 WIZARDS_HID_QUERYWIZARD_OPTMODIFYQUERY 30 选择保存查询并打开查询进行编辑。
22 WIZARDS_HID_QUERYWIZARD_TXTSUMMARY 15 显示查询摘要。
25 WIZARDS_HID_QUERYWIZARD_TXTQUERYTITLE 15 输入查询名称。
27 WIZARDS_HID_QUERYWIZARD_OPTDISPLAYQUERY 1e 选择保存和显示查询。
2e REPORTDESIGN_CHECKBOX_RID_DATETIME_DLG_CB_DATE 84 启用“包含日期”，在报表活动区域插入一个日期字段。当报表被执行时，日期字段显示当前日期。
2e REPORTDESIGN_CHECKBOX_RID_DATETIME_DLG_CB_TIME 84 启用“包含时间”，在报表活动区域插入一个时间字段。当报表被执行时，时间字段显示当前时间。
1a .uno%3AInsertDateTimeField 69 您可以通过选择插入 - 日期和时间来打开报表构建器的“日期和时间”对话框。
14 .uno%3AGreatestWidth 5a 选择两个或更多个对象并且单击此图标将对象大小调整到最大宽度。
15 .uno%3ASmallestHeight 5a 选择两个或两个以上对象，单击此图标以重新调整对象高度至最小。
18 .uno%3ASectionAlignRight 5a 选择两个或更多个对象并且单击此图标在区域的右页边距对齐对象。
17 .uno%3ASectionShrinkTop 36 缩小选定的部分以删除顶部的空白区域。
14 .uno%3ASmallestWidth 5a 选择两个或更多个对象并且单击此图标将对象大小调整到最小宽度。
17 .uno%3ASectionAlignLeft 5a 选择两个或更多个对象并且单击此图标在区域的左页边距对齐对象。
11 .uno%3AHFixedLine 24 将水平线插入到当前区域。
16 .uno%3ASectionAlignTop 5d 选择两个或更多个对象并且单击此图标在区域的上方页边距对齐对象。
14 .uno%3ASectionShrink 3f 缩小选定的部分以删除顶部和底部的空白区域。
15 .uno%3AGreatestHeight 5a 选择两个或更多个对象并且单击此图标将对象大小调整到最大高度。
1a .uno%3ASectionShrinkBottom 36 缩小选定的部分以删除底部的空白区域。
19 .uno%3ASectionAlignBottom 5d 选择两个或更多个对象并且单击此图标在区域的下方页边距对齐对象。
11 .uno%3AVFixedLine 21 将竖线插入到当前区域。
26 REPORTDESIGN_HID_RPT_PROP_PREEVALUATED 45 如果启用预评估，只有当报表完成时才会计算函数。
28 REPORTDESIGN_HID_RPT_PROP_INITIALFORMULA 3b 为公式的计算输入初始值。通常设为 0 或 1。
12 .uno%3ANewFunction b1 在“报表导航”的上下文菜单中，您可以看到与在“报表构建器”视图中相同的命令，以及其他用来创建新的函数或将其删除的命令。
21 REPORTDESIGN_HID_RPT_PROP_FORMULA 3a 输入定义函数的公式。使用 OpenFormula 语法。
26 REPORTDESIGN_HID_REPORT_NAVIGATOR_TREE a2 单击“报表导航”中的某个条目，将选定“报表构建器”视图中对应的对象或区域。右键单击某个条目以打开上下文菜单。
28 REPORTDESIGN_HID_RPT_PROP_DEEPTRAVERSING cc 如果启用“深度遍历”，计算函数时将考虑到所有更低层次的层次结构。这将用作行编号的实例。如果没有启用“深度遍历”，只能计算首层的层次结构。
16 .uno%3AReportNavigator 6c “报表导航”显示了报表的结构。您可以使用“报表导航”将函数插入到报表中。
32 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_N 9 第 N 页
3b REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_BOTTOMPAGE 18 页面底端（页脚）
37 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_N_OF_M 15 第 N 页，共 M 页
38 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_TOPPAGE 18 页面顶端（页眉）
32 REPORTDESIGN_LISTBOX_RID_PAGENUMBERS_LST_ALIGNMENT c 对齐方式
22 EXTENSIONS_HID_PROP_VERTICAL_ALIGN c 垂直对齐
27 REPORTDESIGN_HID_RPT_PROP_RPT_POSITIONY 24 为选定的对象定义 Y 位置。
25 REPORTDESIGN_HID_RPT_PROP_PRESERVEIRI e4 对于图片，您可以指定将该图片以链接方式插入文件，也可以只将其作为 Base 文件的一个嵌入对象。嵌入选项增大了 Base 文件的大小，而链接选项无法移植到其他计算机中。
2e REPORTDESIGN_HID_RPT_PROP_PRINTWHENGROUPCHANGE 15 组合更改时打印
34 REPORTDESIGN_HID_RPT_PROP_CONDITIONALPRINTEXPRESSION 4d 如果“条件打印表达式”值为 TRUE，就会打印出选定对象。
21 REPORTDESIGN_HID_RPT_PROP_VISIBLE 66 隐藏对象不会在执行的报表中显示，但仍然会在“报表构建器”视图中显示。
23 REPORTDESIGN_HID_RPT_PROP_BACKCOLOR 45 为选定对象设定屏幕的背景颜色和打印的背景颜色。
2a REPORTDESIGN_HID_RPT_PROP_PAGEHEADEROPTION 7b 指定在哪个上下文中打印页眉：在所有页中，或者不在有报表页眉或者页脚的页中打印页眉。
23 REPORTDESIGN_HID_RPT_PROP_DATAFIELD 42 在“数据”标签页，您可以更改为显示数据内容。
22 REPORTDESIGN_HID_RPT_PROP_RPT_FONT 27 为选定的文本对象选择字体。
27 REPORTDESIGN_HID_RPT_PROP_RPT_POSITIONX 21 为选定的对象设定 X 位置
26 REPORTDESIGN_HID_RPT_PROP_KEEPTOGETHER 6c 如果与当前页不符，“放置一起”指定要打印当前对象，从新页的顶部开始打印。
26 REPORTDESIGN_HID_RPT_PROP_FORCENEWPAGE 58 “强制分页”指定是否在新页中打印当前区域和/或者下一个区域。
24 REPORTDESIGN_HID_RPT_PROP_RPT_HEIGHT 1e 为选定对象定义高度。
2a REPORTDESIGN_HID_RPT_PROP_PAGEFOOTEROPTION 7b 指定在哪个上下文中打印页脚：在所有页中，或者不在有报表页眉或者页脚的页中打印页脚。
29 REPORTDESIGN_HID_RPT_PROP_BACKTRANSPARENT 2a 指定选定对象的背景是否透明。
2b REPORTDESIGN_HID_RPT_PROP_GROUPKEEPTOGETHER 63 将组合按页或者列（默认）放置在一起。您必须也启用“放置一起”选项。
1e REPORTDESIGN_HID_RPT_FIELD_SEL c6 当您在“内容”栏中选定了一个表格并离开时会自动显示“添加字段”窗口。您还可以单击工具栏中的“添加字段”图标，或者选择查看 - 添加字段。
23 REPORTDESIGN_HID_RPT_PROP_RPT_WIDTH 21 为选定的对象设定宽度。
25 REPORTDESIGN_HID_RPT_PROP_NEWROWORCOL 82 对于多列设计，“新行”或者“新列”指定是否在新行或者列中打印当前区域和/或者下一个区域。
2d REPORTDESIGN_HID_RPT_PROP_PRINTREPEATEDVALUES 1b 指定打印重复的值。
13 .uno%3ASelectReport 6d 要显示整个报表的“数据”或者“常规”标签页，请选择“编辑”-“选择报表”。
41 reportdesign%3AListBox%3ARID_GROUPS_SORTING%3ALST_KEEPTOGETHERLST 4b 选择详细信息的级别，并据此将一个组合放置在同一页。
31 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_ORDER 15 选择排序顺序。
35 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_HEADERLST 2d 选择显示或者隐藏“组合页眉”。
28 REPORTDESIGN_HID_RPT_GROUPSORT_MOVE_DOWN 27 将选定的字段在列表中下移。
1b .uno%3ADbSortingAndGrouping 9c 在报表构建器的“排序和组合”对话框中，您可以定义在报表中排序的字段以及应该放置在一起形成一个组合的字段。
24 REPORTDESIGN_HID_RPT_FIELDEXPRESSION 2d 单击打开您可以选择字段的列表。
25 REPORTDESIGN_HID_RPT_GROUPSORT_DELETE 21 删除列表中的选定字段。
40 REPORTDESIGN_NUMERICFIELD_RID_GROUPS_SORTING_ED_GROUPINTERVALLST 30 输入组间距值，根据该值组合记录。
36 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_GROUPONLST 45 选择在每次更改值或者更改其他属性时创建新组合。
1e REPORTDESIGN_HID_RPT_GROUPSBRW 84 列出要进行排序或者组合的字段。在最上面的字段有最高的优先级，第二个字段有第二优先级等等。
35 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_FOOTERLST 2d 选择显示或者隐藏“组合页脚”。
26 REPORTDESIGN_HID_RPT_GROUPSORT_MOVE_UP 27 将选定的字段在列表中上移。
1b WIZARDS_HID_DLGTABLE_DIALOG 36 “表格向导”可帮助您创建数据库表格。
1d WIZARDS_HID_DLGTABLE_LBTABLES 81 选择一个示例表格。然后，从左侧列表框的表格中选择字段。重复此步骤，选择所需的各个字段。
20 WIZARDS_HID_DLGTABLE_OPTBUSINESS 33 选择商务类别可仅查看商务示例表格。
23 WIZARDS_HID_DLGTABLE_FIELDSSELECTED 30 显示要包括在新表格中的所有字段。
1f WIZARDS_HID_DLGTABLE_OPTPRIVATE 33 选择私人类别可仅查看私人示例表格。
1c WIZARDS_HID_DLGTABLE_COLNAME 4e 显示选定数据字段的名称。如果需要，可以输入新的名称。
23 DBACCESS_HID_TAB_AUTOINCREMENTVALUE 68 输入 SQL 命令说明符，此说明符用来指示数据源自动递增指定的整数数据字段。
1d DBACCESS_HID_TAB_ENT_REQUIRED 33 如果设置为“是”，此字段不得为空。
1c WIZARDS_HID_DLGTABLE_CMDPLUS 2a 在列表框中添加新的数据字段。
1d WIZARDS_HID_DLGTABLE_CMDMINUS 24 从列表框中删除选定字段。
25 WIZARDS_HID_DLGTABLE_LB_SELFIELDNAMES 24 选择字段以编辑字段信息。
22 DBACCESS_HID_TAB_ENT_AUTOINCREMENT 51 如果设置为“是”，则此数据字段的值将由数据库引擎生成。
24 WIZARDS_HID_DLGTABLE_CK_PK_AUTOVALUE 81 选择自动插入值，并递增每条记录的字段值。数据库必须支持自动递增，才能使用自动取值功能。
2e WIZARDS_HID_DLGTABLE_CK_PK_AUTOVALUE_AUTOMATIC 81 选择自动插入值，并递增每条记录的字段值。数据库必须支持自动递增，才能使用自动取值功能。
25 WIZARDS_HID_DLGTABLE_OPT_PK_AUTOMATIC 33 选择自动将主关键字添加为附加字段。
24 WIZARDS_HID_DLGTABLE_CHK_USEPRIMEKEY c2 选择创建主关键字。将主关键字添加到每个数据库，以便唯一识别每条记录。对于 %PRODUCTNAME 内的某些数据库系统，编辑表格时必须提供主关键字。
28 WIZARDS_HID_DLGTABLE_FIELDS_PK_AVAILABLE 48 选择字段，然后单击 > 将其添加到主关键字字段列表。
23 WIZARDS_HID_DLGTABLE_OPT_PK_SEVERAL 3c 选择根据几个现有字段的组合创建主关键字。
24 WIZARDS_HID_DLGTABLE_LB_PK_FIELDNAME 15 选择字段名称。
2a WIZARDS_HID_DLGTABLE_CMDREMOVE_PK_SELECTED 93 选择字段，然后单击 < 将其从主关键字字段列表中删除。主关键字被创建为此列表中字段的连接（从上向下）。
22 WIZARDS_HID_DLGTABLE_OPT_PK_SINGLE 45 选择使用带有唯一值（如，主关键字）的现有字段。
28 WIZARDS_HID_DLGTABLE_OPT_STARTFORMWIZARD 6c 选择基于此表格创建窗体。使用窗体向导上次使用的设置在文本文档中创建窗体。
24 WIZARDS_HID_DLGTABLE_OPT_MODIFYTABLE 39 选择保存表格设计并打开表格以输入数据。
26 WIZARDS_HID_DLGTABLE_OPT_WORKWITHTABLE 24 选择保存和编辑表格设计。
20 WIZARDS_HID_DLGTABLE_LST_CATALOG 3f 选择表格目录。（仅在数据库支持目录时可用）
1d WIZARDS_HID_DLGTABLE_TXT_NAME 15 指定表格名称。
1f WIZARDS_HID_DLGTABLE_LST_SCHEMA 3f 选择表格模式。（仅在数据库支持模式时可用）
15 .uno%3ADBReportRename 18 重命名选定报表。
12 .uno%3ADBQueryOpen 39 打开选定查询，以输入、编辑或删除记录。
13 .uno%3ADBReportEdit 27 打开选定报表，以修改版式。
14 .uno%3ADBQueryRename 18 重命名选定查询。
14 .uno%3ADBTableDelete 15 删除选定表格。
13 .uno%3ADBFormRename 18 重命名选定窗体。
12 .uno%3ADBTableOpen 39 打开选定表格，以输入、编辑或删除记录。
11 .uno%3ADBFormOpen 39 打开选定窗体，以输入、编辑或删除记录。
12 .uno%3ADBTableEdit 27 打开选定表格，以修改结构。
11 .uno%3ADBFormEdit 27 打开选定窗体，以修改版式。
14 .uno%3ADBQueryDelete 15 删除选定查询。
12 .uno%3ADBQueryEdit 27 打开选定查询，以修改结构。
15 .uno%3ADBReportDelete 15 删除选定报表。
14 .uno%3ADBTableRename 18 重命名选定表格。
13 .uno%3ADBReportOpen 3c 打开选定的报表，以输入、编辑或删除记录。
13 .uno%3ADBFormDelete 15 删除选定窗体。
3c SVX_HID_SVX_CHINESE_TRANSLATION_RB_CONVERSION_TO_TRADITIONAL 3c 单击为选定的“艺术字”对象应用字符间距。
26 .uno%3AFontworkCharacterSpacingFloater 2a 打开“艺术字字符间距”窗口。
c FontWork1TBO 3c 单击为选定的“艺术字”对象应用对齐方式。
c FontWork2TBO 27 输入“艺术字”字符间距值。
20 .uno%3AFontworkSameLetterHeights 60 在普通和所有对象高度相同之间切换选定“美工字体”对象的字符高度。
1f .uno%3AFontworkAlignmentFloater 2a 打开“艺术字对齐方式”窗口。
19 .uno%3AFontworkShapeTypes 75 打开“艺术字形状”工具栏。单击某形状可将该形状应用到所有选定的“艺术字”对象。
39 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPALLOTHERS 39 保留所有其他用户的更改，取消您的更改。
37 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPALLMINE 3f 保留您的所有更改，取消其他用户的所有更改。
32 sc%3ACheckBox%3ARID_SCDLG_SHAREDOCUMENT%3ACB_SHARE cf 启用该选项将和其他用户共享当前文档。禁用则以非共享模式使用文档。这会使自从您上次打开或保存文档后，由其他用户所做的尚未保存的编辑内容无效。
14 .uno%3AShareDocument 5a 打开“共享文档”对话框，在此您可以启用或禁用文档的协作共享。
16 SC_HID_SCDLG_CONFLICTS 9c 如果同样的内容由不同的用户进行了更改，则会打开“解决冲突”对话框。对于每一个冲突，要决定保留哪一个更改。
34 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPMINE 33 保留您的更改，取消其他用户的更改。
35 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPOTHER 39 保留其他用户所做的更改，取消您的更改。
18 .uno%3AFormFilterExecute 66 如果您在窗体筛选器工具栏上单击应用窗体式筛选器图标，则会应用筛选器。
15 .uno%3AFormFilterExit 5a 如果单击窗体筛选器工具栏上的关闭按钮，则显示未经筛选的窗体。
1e .uno%3AConfigureToolboxVisible 4b 打开对话框，在该对话框您可以添加、编辑和删除图标。
16 SVX_HID_FM_IS_NOT_NULL c3 在此区域中，筛选条件可以作为文本直接编辑。如果要检查字段是否包含内容，可以选择筛选条件“空”(SQL："Is Null") 或“非空”(SQL："Is not Null")。
1a .uno%3AFormFilterNavigator 61 要使用逻辑 OR 连接多个筛选条件，请单击筛选器栏上的筛选器导航图标。
19 SVX_HID_FM_FILTER_IS_NULL c3 在此区域中，筛选条件可以作为文本直接编辑。如果要检查字段是否包含内容，可以选择筛选条件“空”(SQL："Is Null") 或“非空”(SQL："Is not Null")。
18 SVX_HID_FILTER_NAVIGATOR 145 已设置的过滤条件显示在筛选器导航中。设置筛选器后，筛选器导航的底部将会立即显示一个空的过滤条目。单击文字“或”就可选择此条目。选择空的过滤条目后，就可在窗体中输入附加过滤条件。这些条件通过布尔 OR 与前面定义的条件相连。
f SVX_HID_FM_EDIT c3 在此区域中，筛选条件可以作为文本直接编辑。如果要检查字段是否包含内容，可以选择筛选条件“空”(SQL："Is Null") 或“非空”(SQL："Is not Null")。
1e SVX_HID_CTL_FONTWORK_FAVORITES c0 选择艺术字样式并单击“确定”，将“艺术字”插入到文档中。双击或按住 Ctrl 的同时双击文档中的艺术字以进入文本编辑模式，然后更改文本。
12 .uno%3ASaveGraphic b3 在 Writer 中导出一个位图图像：右键单击该位图图像，选择“保存图形”。您会看到“导出图形”对话框。输入文件名并选择文件类型。
17 SFX2_HID_SIDEBAR_WINDOW 87 侧边栏提供了常用的工具，将它们分类组合在面板上。在垂直标签栏上点击一个标签以选择一个面板。
26 .HelpId%3AStartCenter%3ATemplateButton 39 模板图标可以打开“模板和文档”对话框。
22 .HelpId%3AStartCenter%3AMathButton 42 每个文档图标都可以打开一个新的指定类型文档。
22 .HelpId%3AStartCenter%3ADrawButton 42 每个文档图标都可以打开一个新的指定类型文档。
24 .HelpId%3AStartCenter%3AWriterButton 42 每个文档图标都可以打开一个新的指定类型文档。
20 .HelpId%3AStartCenter%3ADBButton 42 每个文档图标都可以打开一个新的指定类型文档。
22 .HelpId%3AStartCenter%3AOpenButton 42 “打开一个文档”图标打开“打开文件”对话框。
22 .HelpId%3AStartCenter%3ACalcButton 42 每个文档图标都可以打开一个新的指定类型文档。
25 .HelpId%3AStartCenter%3AImpressButton 42 每个文档图标都可以打开一个新的指定类型文档。
15 FWK_HID_BACKINGWINDOW 45 单击图标以打开一个新的文档，或打开文件对话框。
c .uno%3AAbout 3f 用于显示一般的程序信息，如版本号和版权等。
12 .uno%3AHelpSupport 24 显示如何获得支持的信息。
f .uno%3AHelpMenu 48 “帮助”菜单用于启动和控制 $[officename] 的帮助系统。
13 .uno%3AOnlineUpdate 118 为 %PRODUCTNAME 启用 Internet 连接。如果需要使用“代理服务器”，请检查%PRODUCTNAME - 首选项工具 - 选项 - Internet 中的“%PRODUCTNAME 代理服务器”设置。然后选择“检查更新”，以便检查您的办公套件是否有较新版本。
10 .uno%3AHelpIndex 3c 打开当前应用程序的 $[officename] 帮助主页面。
2c service%3Acom.sun.star.tab.tabreg%3Fpurchase 6f 该菜单命令在 %PRODUCTNAME 的评估版本中可见。请选择以打开“%PRODUCTNAME 购买向导”。
13 .uno%3AExtendedHelp 48 在鼠标指针下启用扩展帮助提示，直到下一次的单击。
e RID_ENVTOOLBOX 6c 标准栏位于 $[officename] 窗口顶部。此工具栏在各 $[officename] 应用程序中均可使用。
18 .HelpId%3Atableobjectbar 7c 表格 工具栏包括处理表格所需的各项功能。将光标移动到表格中，可以显示“表格”工具栏。
19 SVX_HID_OFA_HYPERLINK_DLG a0 使用超链接地址栏在文档中创建和编辑超链接，还可以在地址栏内输入搜索条件，使用可用的 Internet 搜索引擎进行搜索。
17 .uno%3ADSBInsertColumns 54 将所有已标记的记录的字段插入到当前文档中光标所在的位置。
14 .uno%3ADSBFormLetter 36 启动“邮件合并向导”以创建格式信函。
11 .uno%3APrevRecord 18 转到上一个记录。
11 .uno%3ALastRecord 1b 转到最后一个记录。
12 .uno%3AFirstRecord 18 转到第一个记录。
e .uno%3ARecSave 3c 保存新的数据条目。修改被注册到数据库中。
13 .uno%3ADeleteRecord 2d 删除记录。删除前需要确认询问。
10 .uno%3ANewRecord 15 创建新的记录。
11 .uno%3ANextRecord 18 转到下一个记录。
15 .uno%3AAbsoluteRecord 4b 显示当前记录的编号。输入一个编号以转到对应的记录。
e .uno%3ARecUndo 1b 用于撤消数据条目。
e .uno%3AGridUse 2d 指定只能在网格点之间移动对象。
14 .uno%3ABezierConvert 36 将曲线转换为直线或将直线转换为曲线。
12 .uno%3ABezierClose 18 关闭线条或曲线。
13 .uno%3ABezierInsert 39 启动插入模式。在此模式中可以插入接点。
1c .uno%3ABezierEliminatePoints 30 标记要删除的当前接点或选定接点。
11 .uno%3ABezierMove 24 启动可以移动接点的模式。
13 .uno%3ABezierSmooth 2a 将角点或对称点转换成平滑点。
11 .uno%3ABezierEdge 30 将选定的一个或多个点转换成角点。
14 .uno%3ABezierCutLine 81 使用分割曲线图标可以分割曲线。选择一个或多个点，然后单击此图标，曲线将从选定点处分割。
13 .uno%3ABezierDelete 8b 使用删除接点图标可以删除一个或多个选定的点。如果希望选择多个点，请按住 Shift 键并单击相应的点。
12 HID_BEZIER_TOOLBOX 57 当您选择一个多边形对象并单击编辑接点时，会显示编辑接点栏。
16 .uno%3ABezierSymmetric 33 此图标将角点或平滑点转换为对称点。
1a CUI_HID_OFADLG_TREELISTBOX 3f 在这里您能够参阅有关各种选项范围的说明文。
18 .uno%3AOptionsTreeDialog 42 此命令用于打开一个对话框，以自定义程序配置。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_CITY 1e 键入所在城市的名称。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_ZIP 24 在此字段中键入邮政编码。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_MAIL 21 键入您的电子邮件地址。
2e cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_POSITION 30 在此字段中键入您在公司中的职务。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FAX 2a 在此字段中键入您的传真号码。
2b cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_EMAIL 21 键入您的电子邮件地址。
29 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_FAX 2a 在此字段中键入您的传真号码。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_CITY 1e 键入所在城市的名称。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_CITY 1e 键入所在城市的名称。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_COUNTRY 1c 键入国家/地区名称。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COMP 27 在此字段中键入公司的名称。
2c cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_STREET 27 在此字段中键入街道的名称。
30 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_ZIPCODE 24 在此字段中键入邮政编码。
28 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_COUNTRY 1c 键入国家/地区名称。
30 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TELCOMPANY 2d 在此字段中键入办公室电话号码。
2a cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_NAME 15 键入您的姓氏。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_COMPANY 27 在此字段中键入公司的名称。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_FAX 2a 在此字段中键入您的传真号码。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_SHORTNAME 15 键入您的缩写。
17 CUI_HID_OPTIONS_GENERAL 36 此选项卡页面用于输入或编辑用户数据。
29 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_PLZ 24 在此字段中键入邮政编码。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_MAIL 21 键入您的电子邮件地址。
2a sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FIRSTNAME 15 键入您的名字。
29 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COUNTRY 1c 键入国家/地区名称。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_STREET 27 在此字段中键入街道的名称。
2a sw%3AEdit%3ATP_BUSINESS_DATA%3AED_POSITION 30 在此字段中键入您在公司中的职务。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TELPRIVAT 30 在此字段中键入您的私人电话号码。
2b cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TITLE 24 在此字段中键入您的头衔。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_ZIP 24 在此字段中键入邮政编码。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_NAME 15 键入您的姓氏。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_STREET 27 在此字段中键入街道的名称。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_FIRSTNAME 15 键入您的名字。
2a cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_CITY 1e 键入所在城市的名称。
2e cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_STATE 1c 键入国家/地区名称。
29 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_SHORTCUT 15 键入您的缩写。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_TITLE 24 在此字段中键入您的头衔。
2c cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_FILTER 99 指定始终将左侧的选定类型文档保存为此文件类型的方法。也可以在另存为对话框中为当前文档选择其他文件类型。
29 cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_APP 3c 指定您想为其定义默认文件格式的文档类型。
38 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_NOPRETTYPRINTING 5a 保存文档时，%PRODUCTNAME 会写入不含任何缩进和额外换行的 XML 数据。
34 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ACB_LOAD_SETTINGS 33 用文档加载存入文档的用户特定设置。
38 sfx2%3ACheckBox%3ARID_DLG_ALIEN_WARNING%3ACB_WARNING_OFF b1 您可以选择在以非 OpenDocument 格式或以在“选项”对话框的加载/保存 - 常规中设置的默认格式之外的格式保存文档时，收到警告消息。
35 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_RELATIVE_FSYS 37 选中此框可以相对保存文件系统中的 URL。
35 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_RELATIVE_INET 42 选择此框，可以对 Internet 中的 URL 进行相对保存。
37 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_WARNALIENFORMAT b1 您可以选择在以非 OpenDocument 格式或以在“选项”对话框的加载/保存 - 常规中设置的默认格式之外的格式保存文档时，收到警告消息。
33 cui%3ANumericField%3ARID_SFXPAGE_SAVE%3AED_AUTOSAVE 3c 以分钟为单位指定自动恢复选项的时间间隔。
2f cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_DOCINFO 42 指定在每次选择另存为命令时都显示属性对话框。
31 cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_ODF_VERSION dd 一些公司或组织可能会要求 ODF 1.0/1.1 格式的 ODF 文档。您可以在列表框中选择该格式。此旧格式不能存储所有的新功能，因此建议您尽可能使用新格式 ODF 1.2（扩展）。
2e cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_BACKUP b7 保存文档时，将文档的前一版本保存为备份副本。%PRODUCTNAME 会在每次保存时创建新的备份副本以替换前一版本。备份副本的扩展名是 .BAK。
30 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_AUTOSAVE 8f 指定 %PRODUCTNAME 保存所有需要的信息，以便在崩溃发生时可恢复所有打开的文档。您可以指定保存时间间隔。
36 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ACB_LOAD_DOCPRINTER 13e 如果启用，打印机设置将与文档一起装入。如果您没有在“打印”对话框中手动更改打印机，则可以在远程打印机上打印文档。如果禁用，将会使用您的标准打印机打印此文档。无论是否选中此选项，当前打印机设置将会与文档一起被存储。
14 CUI_HID_OPTIONS_SAVE 69 在常规区域中，您可以选择用于保存文档的默认设置，以及选择默认文件格式。
2e cui%3APushButton%3ARID_SFXPAGE_PATH%3ABTN_PATH 36 单击以显示选择路径或编辑路径对话框。
32 cui%3APushButton%3ARID_SFXPAGE_PATH%3ABTN_STANDARD 3f 默认按钮用于重设所有选定条目的预定义路径。
14 CUI_HID_OPTIONS_PATH 60 本节介绍 $[officename] 中重要文件夹的默认路径。用户可以编辑这些路径。
18 CUI_HID_OPTPATH_CTL_PATH 66 要修改此列表中的条目，请单击该条目，然后单击编辑。也可以双击该条目。
33 cui%3AListBox%3ARID_SVXDLG_MULTIPATH%3ALB_MULTIPATH 45 包含已添加的路径的列表。标记新文件的默认路径。
3b cui%3APushButton%3ARID_SVXDLG_MULTIPATH%3ABTN_ADD_MULTIPATH 60 打开选择路径对话框选择其他文件夹，或使用打开对话框选择其他文件。
41 cui%3ANumericField%3ARID_SVXDLG_LNG_ED_NUM_PREBREAK%3AED_PREBREAK 3c 键入在连字符之前或之后出现的最小字符数。
3a cui%3APushButton%3ARID_SFXDLG_EDITDICT%3APB_DELETE_REPLACE 36 从当前的自定义词典中删除标记的单词。
3d cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_EDIT_DIC 66 打开编辑自定义词典对话框，您可以在其中添加自定义词典或编辑现有条目。
18 CUI_HID_OPTIONS_DICT_NEW 75 打开新建词典对话框，您可以在其中命名新的自定义词典或例外词典，也可以指定语言。
1d CUI_HID_CLB_EDIT_MODULES_DICS 1e 列出可用的用户词典。
31 cui%3AListBox%3ARID_SFXDLG_EDITDICT%3ALB_ALLDICTS 1b 指定要编辑的书籍。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_MODULES_EDIT 3c 要编辑一个语言模块，请选中它并单击编辑。
19 CUI_HID_CLB_LINGU_MODULES 21 含有已安装的语言模块。
19 CUI_HID_OPTIONS_DICT_EDIT 66 打开编辑自定义词典对话框，您可以在其中添加自定义词典或编辑现有条目。
15 CUI_HID_OPTIONS_LINGU 33 指定拼写检查、同义词库和断字属性。
1b CUI_HID_LNGDLG_NUM_PREBREAK 3c 键入在连字符之前或之后出现的最小字符数。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_DEL_DIC 51 如果选定词典未处于写保护状态，则在获得确认后将其删除。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_OPTIONS_EDIT 4e 如果需要修改某个值，请选择对应的条目，然后单击编辑。
19 CUI_HID_CLB_LINGU_OPTIONS 2d 定义用于拼写检查和断字的选项。
2a cui%3AEdit%3ARID_SFXDLG_EDITDICT%3AED_WORD 75 您可以键入新的字词以添加到词典中。在下面的列表中将看到当前自定义词典中的内容。
31 cui%3AListBox%3ARID_SFXDLG_EDITDICT%3ALB_DICTLANG 33 为当前自定义词典指定一种新的语言。
37 cui%3APushButton%3ARID_SFXDLG_EDITDICT%3APB_NEW_REPLACE 87 将词汇文本字段中的字词添加到当前的自定义词典中。使用例外词典时，还将添加建议字段中的字词。
30 cui%3ACheckBox%3ARID_SFXDLG_NEWDICT%3ABTN_EXCEPT 36 指定是否希望在文档中避免出现某些词。
2d cui%3AEdit%3ARID_SFXDLG_NEWDICT%3AED_DICTNAME 24 指定新的自定义词典名称。
12 .uno%3ASpellOnline 42 键入时自动进行拼写检查，并用下划线标记错误。
2d cui%3AEdit%3ARID_SFXDLG_EDITDICT%3AED_REPLACE 93 只有在编辑一个例外词典时，此输入字段才可使用。该字段给“字词”文本框中的当前字词提供可选择的建议。
30 cui%3AListBox%3ARID_SFXDLG_NEWDICT%3ALB_DICTLANG 48 通过选择某种语言，可以限制自定义词典的使用范围。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_NEW_DIC 75 打开新建词典对话框，您可以在其中命名新的自定义词典或例外词典，也可以指定语言。
46 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_DOWN 36 将列表框中选定模块的优先级降低一级。
44 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_UP 36 将列表框中选定模块的优先级增加一级。
20 CUI_HID_CLB_EDIT_MODULES_MODULES 51 为选定的模块指定语言、可用拼写、断字和同义词库子模块。
42 cui%3AListBox%3ARID_SVXDLG_EDIT_MODULES%3ALB_EDIT_MODULES_LANGUAGE 18 指定模块的语言。
46 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_BACK 30 单击此处撤消列表框中的当前修改。
30 cui%3AImageButton%3ARID_SVXPAGE_COLOR%3ABTN_SAVE 54 打开另存为对话框，您可以将当前的颜色表另存为指定的名称。
32 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_WORK_ON f6 打开颜色拾取器。首先从颜色表中标出一个颜色。然后点“编辑”以打开颜色拾取器。在颜色拾取器中选择一个颜色，然后点“确定”。选择的颜色就会替代颜色表中被标出的那个颜色。
2e cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_ADD 12 添加新颜色。
31 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_MODIFY 48 修改当前颜色。请注意，覆盖颜色之前不会进行确认。
15 CUI_HID_TPCOLOR_RGB_2 6 绿色
31 cui%3AListBox%3ARID_SVXPAGE_COLOR%3ALB_COLORMODEL 55 要修改，请选择颜色模型：红-绿-蓝 (RGB) 或青-紫红-黄-黑 (CMYK)。
15 CUI_HID_OPTIONS_COLOR 4b 用于从颜色表中选择颜色、编辑现有颜色或定义新颜色。
15 CUI_HID_TPCOLOR_RGB_3 6 蓝色
15 CUI_HID_TPCOLOR_RGB_1 6 红色
2c cui%3AListBox%3ARID_SVXPAGE_COLOR%3ALB_COLOR 4e 含有可用颜色的列表。要选择颜色，请从列表中进行选择。
30 cui%3AImageButton%3ARID_SVXPAGE_COLOR%3ABTN_LOAD 39 访问打开对话框，您可以在其中选择调色板
29 cui%3AEdit%3ARID_SVXPAGE_COLOR%3AEDT_NAME 5d 指定选定颜色的名称。在定义新颜色时，也可以在此字段中键入名称。
2c cui%3AMetricField%3AColorPicker%3ABrightness 27 设定HSB颜色模式中的亮度值。
25 cui%3AMetricField%3AColorPicker%3AHex 5a 在RGB颜色模式中设置红色、绿色、和蓝色值，使用十六进制格式RRGGBB
25 cui%3AMetricField%3AColorPicker%3AHue 24 设定hsb颜色模式中的色调值
25 cui%3ARadioButton%3AColorPicker%3AHue 24 设定hsb颜色模式中的色调值
27 cui%3AMetricField%3AColorPicker%3AGreen 24 设定RGB颜色模式中的绿色值
29 cui%3AControl%3AColorPicker%3AColorSlider 99 在颜色滑块，根据选择的选项按钮，你将看到你可以改变的范围值。在颜色滑块中点击可以改变在大颜色域中的值。
2a cui%3AControl%3AColorPicker%3AColorPreview 60 在预览域的左半边，您可以随时看到您在这个对话框里工作的当前结果。
2c cui%3ARadioButton%3AColorPicker%3ASaturation 24 设置HSB颜色模式中的饱和度
26 cui%3AMetricField%3AColorPicker%3ABlue 24 设定RGB颜色模式中的蓝色值
27 cui%3ARadioButton%3AColorPicker%3AGreen 24 设定RGB颜色模式中的绿色值
26 cui%3ARadioButton%3AColorPicker%3ABlue 24 设定RGB颜色模式中的蓝色值
25 cui%3AMetricField%3AColorPicker%3AKey 2e 设置CMYK颜色模式中的母色（黑色）
29 cui%3AMetricField%3AColorPicker%3AMagenta 25 设置CMYK颜色模式中的紫红色
2c cui%3ARadioButton%3AColorPicker%3ABrightness 27 设定HSB颜色模式中的亮度值。
28 cui%3AMetricField%3AColorPicker%3AYellow 22 设置CMYK颜色模式中的黄色
26 cui%3AMetricField%3AColorPicker%3ACyan 22 设置CMYK颜色模式中的青色
28 cui%3AControl%3AColorPicker%3AColorField 3c 在大颜色域中，你可以点击选择一个新颜色。
2c cui%3AMetricField%3AColorPicker%3ASaturation 24 设置HSB颜色模式中的饱和度
25 cui%3AMetricField%3AColorPicker%3ARed 24 设定RGB颜色模式中的红色值
25 cui%3ARadioButton%3AColorPicker%3ARed 24 设定RGB颜色模式中的红色值
2b cui%3AControl%3AColorPicker%3AColorPrevious 5a 在预览区域的右半边，您可以看到有父标签页来的原始颜色，Colors。
1f cui%3AModalDialog%3AColorPicker 21 允许你定义自己的颜色。
2b cui%3AListBox%3AOFA_TP_MISC%3ALB_HELPFORMAT 27 选择 $[officename] 帮助的样式。
33 cui%3APushButton%3AOFA_TP_MISC%3APB_HELPAGENT_RESET 5a 单击重设帮助助理，可以恢复所显示帮助助理对应的状态默认列表。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_EXTHELP 54 当光标停留在图标、菜单命令或对话框控件上时显示帮助文本。
2f cui%3ANumericField%3AOFA_TP_MISC%3ANF_YEARVALUE 4e 定义日期范围，在此范围内的两位数将被系统认为是年份。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_FILEDLG 6c 指定是否使用 $[officename] 对话框打开和保存文档。否则将使用操作系统的对话框。
2b cui%3ACheckBox%3AOFA_TP_MISC%3ACB_HELPAGENT 8d 指定在选定情形中自动显示帮助助理。单击“帮助助理”窗口以查看具有关于当前上下文信息的帮助页面。
2b cui%3ACheckBox%3AOFA_TP_MISC%3ACB_DOCSTATUS 2a 指定是否将文档打印算作修改。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_TOOLTIP 75 在具有多个章节的文档中滚动时，将显示图标名称和更多气泡帮助信息，例如章节名称。
35 cui%3AComboBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_FONT2 27 输入或选择替换字体的名称。
39 cui%3AListBox%3ARID_SVX_FONT_SUBSTITUTION%3ALB_FONTHEIGHT 3d 选择用于显示 HTML 和 Basic 源代码的字体大小。
18 CUI_HID_OFA_SUBST_DELETE 1e 删除选定的字体替换。
17 CUI_HID_OFA_SUBST_APPLY 1b 应用选定字体替换。
37 cui%3ACheckBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_NONPROP 42 选中此选项，则字体列表框中仅显示非比例字体。
1a CUI_HID_OFA_FONT_SUBST_CLB e1 列出了原字体和将要替代它的字体。选择始终以替换该字体，即使原字体已经安装到了您的系统中。选择只在屏幕上显示只替换屏幕字体，并且始终不会替代打印的字体。
1d CUI_HID_OFA_FONT_SUBSTITUTION c6 使用您选择的字体替代一个字体。该替代只替换屏幕上显示的、或既在屏幕上显示同时也在打印时出现的字体。该替代不更改文档中保存的字体设置。
37 cui%3AListBox%3ARID_SVX_FONT_SUBSTITUTION%3ALB_FONTNAME 37 选择用于显示 HTML 和 Basic 源代码的字体。
35 cui%3AComboBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_FONT1 2a 输入或选择要替换的字体名称。
38 cui%3ACheckBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_USETABLE 27 启用已定义的字体替换设置。
29 cui%3AListBox%3AOFA_TP_VIEW%3ALB_MOUSEPOS 51 指定鼠标指针是否在新打开的对话框中定位，以及如何定位。
30 cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_USE_HARDACCELL 4e 直接访问图形显示适配器的硬件功能可改善屏幕显示效果。
2b svx%3ACheckBox%3AOFA_TP_VIEW%3ACB_SELECTION a1 如果启用，则 Writer 中选定的文本和 Calc 中选定的单元格将使用透明颜色显示。如果未启用，则选定的内容将以反色显示。
19 .uno%3ARestoreEditingView 6f 指定是否恢复上次使用的文档视图。上次保存文档时有效的很多视图属性都将恢复。
24 SVX_LISTBOX_OFA_TP_VIEW_LB_ICONSTYLE 39 为工具栏和对话框中的图标选择图标样式。
23 SVX_LISTBOX_OFA_TP_VIEW_LB_ICONSIZE 27 指定工具栏图标的显示大小。
2c cui%3AListBox%3AOFA_TP_VIEW%3ALB_MOUSEMIDDLE 1e 定义鼠标中键的功能。
32 cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONTANTIALIASING 24 选择平滑文本的屏幕显示。
29 cui%3AListBox%3AOFA_TP_VIEW%3ALB_ICONSIZE 27 指定工具栏图标的显示大小。
33 cui%3ANumericField%3AOFA_TP_VIEW%3ANF_AA_POINTLIMIT 2a 输入最小字体以应用平滑技术。
27 SVX_CHECKBOX_OFA_TP_VIEW_CB_SYSTEM_FONT 5d 指定使用系统字体显示所有菜单和对话框。或者使用其他安装的字体。
30 svx%3ACheckBox%3AOFA_TP_VIEW%3ACB_USE_ANTIALIASE ae 如果支持的话，您可以启用或禁用图形的反失真。启用反失真的话，大部分图形对象看上去会更平滑，并且有较少人为加工的痕迹。
2e cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONT_HISTORY 72 在格式栏的字体名称方框的列表顶部，列出了您在当前文档中最近使用过的五种字体。
2b cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONT_SHOW 66 显示了相应字体（例如，在格式栏的“字体”框中的字体）的可选字体名称。
2e svx%3AMetricField%3AOFA_TP_VIEW%3AMF_SELECTION 5f 为透明选项选择透明度。默认值为 75%。您可以选择 10% 到 90% 之间的值。
12 .uno%3ARefreshView 3b 按 Shift+Ctrl+R 键可恢复或刷新当前文档视图。
2b cui%3AListBox%3AOFA_TP_VIEW%3ALB_MENU_ICONS a2 在相应菜单项旁边显示图标。从“自动”、“隐藏”和“显示”中选择。选择“自动”会根据系统设置和主题来显示图标。
2d cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_SYSTEM_FONT 5d 指定使用系统字体显示所有菜单和对话框。或者使用其他安装的字体。
13 CUI_HID_OFA_TP_VIEW 15 指定视图选项。
2a cui%3AListBox%3AOFA_TP_VIEW%3ALB_ICONSTYLE 39 为工具栏和对话框中的图标选择图标样式。
2f cui%3AMetricField%3AOFA_TP_VIEW%3AMF_WINDOWSIZE 5d 在用户界面元素（如对话框和图标标签）中使用百分比表示字体大小。
40 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_CONVERTTOGREYSCALES 2a 指定所有颜色都仅以灰度打印。
44 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_NORMAL 76 很好的打印质量对应于 300dpi 的打印分辨率。一般的打印质量对应于 200dpi 的打印分辨率。
39 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_TRANSPARENCY 51 如果希望在文档含有透明对象时收到警告，请选中此复选框。
36 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_PAPERSIZE 57 如果需要使用特定的纸张大小来打印当前文档，请选中此复选框。
47 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEBITMAPS_TRANSPARENCY 63 如果标记了此字段，则位图打印质量的降低也同样适用于对象的透明区域。
45 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_OPTIMAL 76 很好的打印质量对应于 300dpi 的打印分辨率。一般的打印质量对应于 200dpi 的打印分辨率。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCETRANSPARENCY_NONE 30 使用此选项后，从不打印透明对象。
3a sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEBITMAPS 51 指定使用较低的质量打印位图。分辨率只能降低，不能升高。
3f sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_PRINTFILEOUTPUT 45 指定打印设置是用于直接打印还是用于打印到文件。
3d sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_PRINTEROUTPUT 45 指定打印设置是用于直接打印还是用于打印到文件。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEGRADIENTS_STRIPES 2d 指定打印时渐变条纹的最大数量。
3f sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCETRANSPARENCY a5 如果标记了此字段，便能将透明对象如同普通的、不透明的对象那样打印，这项功能取决于您对于下面两个选项按钮的选择。
45 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEGRADIENTS_COLOR 2a 指定仅用一种中间色打印渐变。
3d sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_PAPERORIENTATION 54 如果在打印当前文档时需要特定的纸张方向，请选择此复选框。
3c sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEGRADIENTS 42 如果标记了此字段，则以较低的质量打印的渐变。
44 sfx2%3AListBox%3ATP_COMMONPRINTOPTIONS%3ALB_REDUCEBITMAPS_RESOLUTION 4d 指定最佳打印质量的 dpi 值。分辨率只能降低，不能升高。
4a sfx2%3ANumericField%3ATP_COMMONPRINTOPTIONS%3ANF_REDUCEGRADIENTS_STEPCOUNT 2d 指定打印时渐变条纹的最大数量。
48 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_RESOLUTION 4d 指定最佳打印质量的 dpi 值。分辨率只能降低，不能升高。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCETRANSPARENCY_AUTO 6c 指定只有当透明区域所占的面积不超过整个页面的四分之一时，才打印透明区域。
36 cui%3ATimeField%3AOFA_TP_MEMORY%3ATF_GRAPHICOBJECTTIME 54 指定每个图形在缓冲区中的存储时间（以小时和分钟为单位）。
3a cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_GRAPHICOBJECTCACHE 52 指定不在缓冲区中存储大于选定大小（以 MB 为单位）的对象。
2c cui%3ANumericField%3AOFA_TP_MEMORY%3AED_UNDO 48 通过从列表中选择一个数值，指定可以撤消的步骤数。
30 cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_OLECACHE 38 选择缓冲区中最多可以保留的 OLE 对象数。
2f cui%3ACheckBox%3AOFA_TP_MEMORY%3ACB_QUICKLAUNCH 90 如果您希望 $[officename] 启用快速启动，请选中此复选框。如果已经安装了“快速启动”模块，则此选项可用。
34 cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_GRAPHICCACHE 30 指定用于全部图形的总缓冲区大小。
27 CUI_HID_OPTIONS_COLORCONFIG_NAME_SCHEME 1e 输入颜色图案的名称。
3a cui%3APushButton%3ARID_SVXPAGE_COLORCONFIG%3APB_SAVESCHEME 45 将当前的颜色设置保存为颜色图案，以备将来使用。
38 cui%3AListBox%3ARID_SVXPAGE_COLORCONFIG%3ALB_COLORSCHEME 21 选择要使用的颜色图案。
29 CUI_HID_OPTIONS_COLORCONFIG_COLORLIST_WIN 2a 选择用于用户界面元素的颜色。
3c cui%3APushButton%3ARID_SVXPAGE_COLORCONFIG%3APB_DELETESCHEME 4b 删除图案框中显示的颜色图案。无法删除“默认”图案。
43 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_PAGE_PREVIEWS 3c 在打印预览中应用操作系统的高对比度设置。
3d cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_TIPHELP 33 在经过所输入的秒数后隐藏帮助提示。
43 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_TEXTSELECTION 21 在只读文档中显示光标。
48 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ACCESSIBILITY_TOOL b7 允许您使用辅助工具，如外部屏幕阅读器、盲文设备或语音识别输入设备。在启用辅助支持之前，必须先在计算机中安装 Java 运行时环境。
44 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_AUTO_DETECT_HC 53 当系统背景颜色非常深时，将 %PRODUCTNAME 切换到高对比度模式。
4a cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_AUTOMATIC_FONT_COLOR 5c 使用系统颜色设置显示 %PRODUCTNAME 中的字体。此选项仅影响屏幕显示。
41 cui%3ANumericField%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ANF_TIPHELP 24 输入帮助提示显示的秒数。
47 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ANIMATED_GRAPHICS 3a 预览 %PRODUCTNAME 中的动画图形，如 GIF 图像。
44 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ANIMATED_TEXTS 3e 在 %PRODUCTNAME 中预览动画文本，如闪烁和滚动。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTPS_PORT 2a 键入相应代理服务器的端口号。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_NOPROXYFOR 4b 指定不需要任何代理服务器的服务器名称，用分号分隔。
32 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_FTP_PROXY 25 键入FTP 代理服务器的名称。
34 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTPS_PROXY 4f 输入 HTTPS 代理服务器的名称。在右边的字段中输入端口号。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTP_PROXY 27 键入 HTTP 代理服务器的名称。
31 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_FTP_PORT 2a 键入相应代理服务器的端口号。
35 cui%3AListBox%3ARID_SVXPAGE_INET_PROXY%3ALB_PROXYMODE 1e 指定代理定义的类型。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_SOCKS_PORT 2a 键入相应代理服务器的端口号。
32 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTP_PORT 2a 键入相应代理服务器的端口号。
15 CUI_HID_OPTIONS_PROXY 1e 指定代理定义的类型。
33 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_ADD 33 单击此按钮将新的配置添加到此列表。
16 CUI_HID_OPTIONS_SEARCH 40 使用此选项卡页面定义在 Internet 中搜索的设置。
36 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_CHANGE 36 单击此按钮应用对现有配置所作的修改。
2d cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_URL 66 此文字框显示 URL 以及搜索机器的查询命令的第一部分。也可以直接键入 URL。
31 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_POSTFIX 36 后缀提供命令序列，在搜索结束时发出。
34 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_AND 5a 设置高级搜索首选项（和、或、精确）以及定义和显示结果的方式。
33 cui%3AListBox%3ARID_SVXPAGE_INET_SEARCH%3ALB_SEARCH 1e 显示可用的搜索引擎。
36 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_DELETE 78 单击此按钮就会立刻删除一个被选中的搜索引擎的配置，在这之前不会向您提出安全询问。
33 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_SEPARATOR 6c 如果在运行搜索时键入一个以上的单词，则会使用此处指定的字符拆分这些单词。
33 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_OR 5a 设置高级搜索首选项（和、或、精确）以及定义和显示结果的方式。
33 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_NEW 2d 使用此按钮可包括新的搜索引擎。
36 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_EXACT 5a 设置高级搜索首选项（和、或、精确）以及定义和显示结果的方式。
35 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_SEARCH_NAME 24 显示选定搜索引擎的名称。
31 cui%3AListBox%3ARID_SVXPAGE_INET_SEARCH%3AED_CASE 2d 确定运行搜索时是否区分大小写。
31 cui%3AEdit%3ARID_SVXPAGE_INET_MAIL%3AED_MAILERURL 2a 输入电子邮件程序路径和名称。
37 cui%3APushButton%3ARID_SVXPAGE_INET_MAIL%3APB_MAILERURL 30 打开文件对话框选择电子邮件程序。
37 uui%3AEdit%3ADLG_UUI_MASTERPASSWORD%3AED_MASTERPASSWORD 1b 输入主密码以继续。
44 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_MASTERPASSWORD 27 打开“输入主密码对话框”。
41 cui%3ACheckBox%3ARID_SVXPAGE_INET_SECURITY%3ACB_SEC_SAVEPASSWORDS a5 如果启用，%PRODUCTNAME 会安全地存储您访问 Web 服务器文件时使用的所有密码。您可以在输入主密码后从列表中获得这些密码。
41 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_CONNECTIONS 59 需要主密码。如果主密码正确，显示“存储 Web 连接信息”对话框。
3c cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_REMOVEINFO b1 选择始终从文件属性中删除用户数据。如果没有选择此选项，仍然可以使用文件 - 属性 - 常规上的重置按钮删除当前文档的个人信息。
3a cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_SIGNDOCS a8 选择在尝试签署包含记录的修改、版本、字段、其他资源（例如，链接区域或链接图片）引用或批注的文档时显示警告对话框。
3e cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_MACROSEC 1e 打开宏安全性对话框。
3f cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_REMOVEALL 21 从列表中删除所有条目。
3c cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_REMOVE 21 从列表中删除选定条目。
3f cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_CTRLHYPERLINK 81 如果可用，您必须在单击超链接的同时，按下 Ctrl 键以跟踪链接。如果不可用，单击打开超链接。
3b cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_PRINTDOCS 57 选择在尝试打印包含记录的修改或批注的文档时显示警告对话框。
3e cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_SAVESENDDOCS 69 选择在尝试保存或发送包含记录的修改、版本或批注的文档时显示警告对话框。
3c cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_CHANGE 48 打开一个对话框，您可以查看和修改选定条目的密码。
3f uui%3AEdit%3ADLG_UUI_MASTERPASSWORD_CRT%3AED_MASTERPASSWORD_CRT 12 输入主密码。
3b cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_CREATEPDF 88 选择在尝试将文档导出为 PDF 格式时显示警告对话框，此格式可显示在 Writer 中记录的修改或显示批注。
45 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_SECURITYOPTIONS 2d 打开“安全选项和警告”对话框。
42 uui%3AEdit%3ADLG_UUI_MASTERPASSWORD_CRT%3AED_MASTERPASSWORD_REPEAT 18 再次输入主密码。
3e cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_RECOMMENDPWD 84 选择始终启用文件保存对话框中的使用密码保存选项。取消选择此选项将默认不使用密码保存文件。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE2 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
33 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_STARBASIC 57 选中此复选框可以在导出为 HTML 格式时包含 $[officename] Basic 指令。
30 cui%3AListBox%3ARID_OFAPAGE_HTMLOPT%3ALB_CHARSET 24 选择用于导出的相应字符集
2f cui%3AListBox%3ARID_OFAPAGE_HTMLOPT%3ALB_EXPORT 2a 定义用于导出 HTML 文档的设置。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE5 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE3 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
33 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_LOCAL_GRF d3 选中此复选框可以在使用 FTP 进行上载时自动将嵌入图片上载到 Internet 服务器。使用另存为对话框保存文档，并输入一个完整的 FTP URL 作为 Internet 中的文件名。
39 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_PRINT_EXTENSION 4b 如果你标记该字段，则当前文档的打印布局也将被导出。
3b cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_STARBASIC_WARNING 77 如果选中此字段，则在导出为 HTML 格式时将显示一个警告，指出 %PRODUCTNAME Basic 宏将丢失。
36 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_UNKNOWN_TAGS 63 如果希望将 $[officename] 无法识别的标记当作字段导入，请选中这个复选框。
3c cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_NUMBERS_ENGLISH_US c6 如果未选中，请根据“选项”对话框的语言设置 - 语言 - 语言环境设置中的设置来解释数字。如果选中，则以“英语（美国）”语言环境来解释数字。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE1 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE7 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
3a cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_IGNORE_FONTNAMES 7a 选择此复选框，在导入时忽略所有的字体设置。在“HTML 页面样式”中定义的字体将被使用。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE6 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE4 7d 使用调节按钮（大小 1 至 大小 7）可以定义 HTML <font size=1> 至 <font size=7> 标记的各个字体大小。
14 .uno%3ASwEditOptions 81 这些设置确定了如何处理在 $[officename] 中创建的文本文档。还可以定义用于当前文本文档的设置。
2d sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_ANY_RULER 4b 启用标尺。使用下面的两个复选框来选择要显示的标尺。
27 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_GRF 30 指定是否在屏幕上显示图形和对象。
31 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_SMOOTH_SCROLL 21 激活平滑滚动页面功能。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HRULER 45 显示水平标尺。从相应列表中选择所需的度量单位。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HANDLE 5a 将控点（定界矩形上的八个点）显示为没有三维效果的简单正方形。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_POSTIT d5 显示批注。单击批注可编辑文字。使用“导航”中的上下文菜单可定位或删除一条批注。使用批注的上下文菜单可删除该批注或所有批注，或该作者的所有批注。
27 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_TBL 21 显示文档中包含的表格。
30 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VRULER_RIGHT 24 将垂直标尺与右边框对齐。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HSCROLL 18 显示水平滚动条。
29 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_FIELD 39 显示文档中的字段名称而不是字段的内容。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_DRWFAST 2a 显示文档中包含的图形和控件。
16 .uno%3ATableBoundaries 83 为显示表格边界，请右键单击任意表格并选择表格边界，或在 Writer 文档中选择表格 - 隐藏网格线。
2a sw%3AListBox%3ATP_CONTENT_OPT%3ALB_HMETRIC 45 显示水平标尺。从相应列表中选择所需的度量单位。
29 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_CROSS 93 移动框架时在框架周围显示辅助线。可以选择在移动时显示辅助线选项，以便使用线性值显示对象的确切位置。
29 sw%3AListBox%3ATP_CONTENT_OPT%3ALB_METRIC 24 指定用于 HTML 文档的单位。
2a sw%3AListBox%3ATP_CONTENT_OPT%3ALB_VMETRIC 45 显示垂直标尺。从相应列表中选择所需的度量单位。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VRULER 45 显示垂直标尺。从相应列表中选择所需的度量单位。
2d sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_BIGHANDLE 33 显示大控点（定界矩形上的八个点）。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VSCROLL 18 显示垂直滚动条。
26 sw%3AComboBox%3ATP_STD_FONT%3ALB_LABEL 2d 指定用于图像和表格标题的字体。
25 sw%3AComboBox%3ATP_STD_FONT%3ALB_LIST 3c 指定用于列表、编号和所有衍生样式的字体。
26 sw%3AComboBox%3ATP_STD_FONT%3ALB_TITLE 1e 指定用于标题的字体。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_INDEX_SIZE 18 指定字体的大小。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_LABEL_SIZE 18 指定字体的大小。
24 sw%3AComboBox%3ATP_STD_FONT%3ALB_IDX 3c 指定用于索引、字母顺序索引和目录的字体。
29 sw%3AComboBox%3ATP_STD_FONT%3ALB_STANDARD 2a 指定用于默认段落样式的字体。
f SW_HID_STD_FONT 27 指定文档中基本字体的设置。
28 sw%3ACheckBox%3ATP_STD_FONT%3ACB_DOCONLY 27 指定设置仅应用于当前文档。
2b sw%3AMetricBox%3ATP_STD_FONT%3ALB_LIST_SIZE 18 指定字体的大小。
2f sw%3AMetricBox%3ATP_STD_FONT%3ALB_STANDARD_SIZE 18 指定字体的大小。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_TITLE_SIZE 18 指定字体的大小。
2e sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PROSPECT 39 选择小册子选项，以小册子格式打印文档。
2b sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_LEFTP 36 指定是否打印文档的所有左（偶数）页。
38 .HelpID%3Avcl%3APrintDialog%3APrintHiddenText%3ACheckBox 30 启用该选项打印标记为隐藏的文本。
36 .HelpId%3Avcl%3APrintDialog%3APrintControls%3ACheckBox 39 指定是否打印文本文档中的窗体控件字段。
3b .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AListBox 27 指定是否打印文档中的注释。
2d sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_CTRLFLD 39 指定是否打印文本文档中的窗体控件字段。
14 SW_HID_OPTPRINT_PAGE 30 指定文本或 HTML 文档中的打印设置。
2c sw_CheckBox_TP_OPTPRINT_PAGE_CB_PROSPECT_RTL 4e 对一个从右至左的脚本，检查以按正确的顺序打印手册页。
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_BLACK_FONT 2a 指定是否始终以黑色打印文本。
28 SW%3ALISTBOX%3ATP_OPTPRINT_PAGE%3ALB_FAX 81 如果已经在计算机上安装了传真软件，并希望直接从文本文档发送传真，请选择要使用的传真机。
2d sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_REVERSE 45 指定是否逆序打印。将从文档的最后一页开始打印。
3c .HelpId%3Avcl%3APrintDialog%3APrintPageBackground%3ACheckBox 77 指定是否在打印文档中包括插入页面背景（“格式”-“页面”-“背景”）的颜色和对象。
40 .HelpId%3Avcl%3APrintDialog%3APrintPicturesAndObjects%3ACheckBox 2d 指定是否打印文本文档中的图形。
2a sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PGRF 2d 指定是否打印文本文档中的图形。
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_SINGLEJOBS 60 指定每次新的打印工作都从一张新纸开始，即便您使用的是双面打印机。
38 .HelpID%3Avcl%3APrintDialog%3APrintEmptyPages%3ACheckBox 14c 如果此选项被启用，则会打印自动插入的空白页。执行双面打印时此方法为最佳选择。例如：在某本书中，“章节”段落样式被设置为始终以奇数页开始。如果前一章在奇数页结束，则 %PRODUCTNAME 会插入一个偶数空白页。此选项控制是否打印该偶数页。
2b SW%3ARADIOBUTTON%3ATP_OPTPRINT_PAGE%3ARB_NO 27 指定是否打印文档中的注释。
2c sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_RIGHTP 36 指定是否打印文档的所有右（奇数）页。
30 sw_CheckBox_TP_OPTPRINT_PAGE_CB_TEXT_PLACEHOLDER 5d 启用该选项来打印文本占位符。禁用该选项文本占位符将输出为空白。
38 .HelpId%3Avcl%3APrintDialog%3APrintBlackFonts%3ACheckBox 2a 指定是否始终以黑色打印文本。
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_BACKGROUND 77 指定是否在打印文档中包括插入页面背景（“格式”-“页面”-“背景”）的颜色和对象。
34 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PAPERFROMSETUP 93 对于有多个纸盒的打印机，“使用打印机设置纸张来源”选项指定使用的纸盒是否由打印机的系统设置来指定。
2f sw_CheckBox_TP_OPTPRINT_PAGE_CB_PRINTEMPTYPAGES 14c 如果此选项被启用，则会打印自动插入的空白页。执行双面打印时此方法为最佳选择。例如：在某本书中，“章节”段落样式被设置为始终以奇数页开始。如果前一章在奇数页结束，则 %PRODUCTNAME 会插入一个偶数空白页。此选项控制是否打印该偶数页。
3d .HelpId%3Avcl%3APrintDialog%3APrintTextPlaceholder%3ACheckBox 5d 启用该选项来打印文本占位符。禁用该选项文本占位符将输出为空白。
2b sw_CheckBox_TP_OPTPRINT_PAGE_CB_HIDDEN_TEXT 30 启用该选项打印标记为隐藏的文本。
14 SW_HID_OPTTABLE_PAGE 27 指定文本文档中表格的属性。
1d .uno%3ATableNumberRecognition 3f 指定将文本表格中的数字识别和格式化为数字。
30 sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_FIXPROP 2d 指定行列的修改会影响整个表格。
30 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_COLMOVE 24 指定移动列时使用的数值。
32 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMALIGNMENT 36 指定数字在单元格中总是向右下方对齐。
2c sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_BORDER 33 指定默认情况下表格单元格具有边框。
30 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_DONT_SPLIT 3c 指定表格不会被任何类型的换行和分页拆分。
2c sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_HEADER 45 指定表格首行以“表格标题”段落样式进行格式化。
32 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_COLINSERT 27 指定插入列时使用的默认值。
32 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_ROWINSERT 27 指定插入行时使用的默认值。
30 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_ROWMOVE 21 指定移动行时使用的值。
33 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMFORMATTING 3f 指定将文本表格中的数字识别和格式化为数字。
2c sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_FIX 45 指定对一行或一列的修改仅影响其对应的相邻区域。
33 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_REPEAT_HEADER 48 指定是否要在分页之后的新页上继续应用该表格标题。
2c sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_VAR 33 指定对行或列的修改将影响表格大小。
37 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMFMT_FORMATTING 81 如果不选中识别数字格式，则仅接受单元格中设置格式的输入。所有其他输入格式被重设为文字。
38 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLINDENT 7e 使用直接定位光标时，段落的左缩进设置在您单击直接定位光标的水平位置处。段落为左对齐。
2e sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_FLD_HIDDEN 42 显示被有条件的文本或隐藏文字字段隐藏的文本。
31 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_ALLOW_IN_PROT 48 指定可以将光标定位在保护区域，但不能做任何修改。
12 SW_HID_OPTSHDWCRSR 66 在 $[officename] 文本文档和 HTML 文档中，定义特定字符和直接定位光标的显示。
2f sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_CHAR_HIDDEN 61 在选中显示/隐藏非打印字符图标时，显示使用“隐藏”字符格式的文本。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SHYPH 123 指定是否显示自定义的连字符，即通过按命令+连字符 (-)Ctrl+连字符 (-) 在字词内输入的隐藏的连字符。如果字词中带有自定义的连字符，则该字词只在行末插入了自定义的连字符的位置分开，而与是否启动自动断字无关。
33 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_FLD_HIDDEN_PARA 57 如果使用隐藏段落字段插入了文本，请指定是否显示隐藏的段落。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_BREAK 82 显示所有使用 Shift+Enter 快捷键插入的换行符。这些换行符可以创建新的行，但不会开始新的段落。
28 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_PARA 5a 指定是否显示段落分隔符。段落分隔符还含有特定的段落格式信息。
30 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SHDWCRSONOFF 1b 激活直接定位光标。
37 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLSPACE 6f 使用直接定位光标时，将在新段落中单击的位置之前插入相应数量的制表符和空格。
35 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLTAB 60 使用直接定位光标时，在新段落中的单击位置之前添加足够多的制表符。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SPACE 39 指定是否使用点来表示文本中的各个空格。
38 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLMARGIN f3 设置使用直接定位光标时段落的对齐方式。段落将应用左对齐、居中或右对齐的格式，具体的对齐方式取决于单击的位置。单击鼠标之前，光标显示为一个三角形，以指示对齐的方式。
27 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_TAB 27 指定将制表符显示为小箭头。
2a sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_HSPACE ab 指定将不间断空格显示为灰色的框。不间断空格在行末不会被分开，它们是使用快捷键 Command+Shift+空格键 Ctrl+Shift+空格键输入的。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_CHG_ATTR 63 定义在文档中如何显示对文本属性（例如粗体、斜体、下划线等）的修改。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_DEL_ATTR ab 指定在文档中当文本被删除时如何显示修改。如果记录了文本删除，则文本会以选定的属性（例如删除线）显示，而不会被删除。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_MARKPOS 3c 定义是否以及在哪里标记文档中已修改的行。
29 sw%3AListBox%3ATP_REDLINE_OPT%3ALB_LC_COL 3f 指定用于突出显示文本中已经修改的行的颜色。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_DEL_COL d4 您也可以选择一种颜色来显示每种被记录的修改的类型。当在列表中选择“按作者”条件时，$[officename] 会自动确定颜色，然后将颜色与每个修改的作者相匹配。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_INS_COL d4 您也可以选择一种颜色来显示每种被记录的修改的类型。当在列表中选择“按作者”条件时，$[officename] 会自动确定颜色，然后将颜色与每个修改的作者相匹配。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_INS_ATTR 36 指定插入文本时文档中的修改如何显示。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_CHG_COL d4 您也可以选择一种颜色来显示每种被记录的修改的类型。当在列表中选择“按作者”条件时，$[officename] 会自动确定颜色，然后将颜色与每个修改的作者相匹配。
12 SW_HID_REDLINE_OPT 27 定义如何显示文档中的修改。
2d sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_NEVER 2d 在装入文档时，永远不更新链接。
2f sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_NEVER 2d 在装入文档时，永远不更新链接。
2f sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_REQUEST 33 在装入文档时，仅在请求时更新链接。
31 sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_REQUEST 33 在装入文档时，仅在请求时更新链接。
30 sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_ALWAYS 27 在装入文档时始终更新链接。
37 sw%3ACheckBox%3ATP_OPTLOAD_PAGE%3ACB_AUTO_UPDATE_FIELDS a2 每当屏幕显示新的内容，所有字段内容都会自动更新。即使未选中此复选框，每当发生特殊情况时，一些字段也会被更新。
37 sw%3ACheckBox%3ATP_OPTLOAD_PAGE%3ACB_AUTO_UPDATE_CHARTS a1 指定是否自动更新图表。每当 Writer 表格单元格值被修改，且光标离开此单元格时，显示该单元格值的图表将被自动更新。
13 SW_HID_OPTLOAD_PAGE 24 指定文本文档的常规设置。
2b sw%3AMetricField%3ATP_OPTLOAD_PAGE%3AMF_TAB 27 指定各个制表位之间的间距。
2a sw%3AListBox%3ATP_OPTLOAD_PAGE%3ALB_METRIC 24 指定文本文档的度量单位。
2e sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_ALWAYS 27 在装入文档时始终更新链接。
37 sw%3APushButton%3ATP_OPTCOMPATIBILITY_PAGE%3APB_DEFAULT 65 单击此按钮，将此选项卡页面上的设置作为今后 %PRODUCTNAME 会话的默认设置。
1c SW_HID_OPTCOMPATIBILITY_PAGE 74 指定文本文档的兼容性设置。这些选项有助于在导入 Microsoft Word 文档时微调 %PRODUCTNAME。
38 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACB_USE_PRINTER_METRICS 48 指定在打印以及格式化屏幕显示时应用的打印机标准。
30 sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ALB_CHARSTYLE 15 指定字符样式。
2c sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ALB_LEVEL 39 指定开始编号的位置所在的标题或章节级。
29 sw%3AEdit%3ATP_OPTCAPTION_PAGE%3AEDT_TEXT 3c 定义标题或章节级的编号后面要显示的字符。
15 SW_HID_OPTCAPTION_CLB 39 选择对自动插入标题设置有效的对象类型。
2b sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ABOX_POS 27 确定标题相对于对象的位置。
31 sw%3AComboBox%3ATP_OPTCAPTION_PAGE%3ABOX_CATEGORY 1e 指定选定对象的类别。
33 sw%3ACheckBox%3ATP_OPTCAPTION_PAGE%3ACB_APPLYBORDER 30 将对象的边框和阴影应用到标题框。
2d sw%3AEdit%3ATP_OPTCAPTION_PAGE%3AED_SEPARATOR 3c 定义标题或章节级的编号后面要显示的字符。
2e sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ABOX_FORMAT 1e 指定所需的编号类型。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SYNCHRONIZE 33 指定是否对称地修改当前的网格设置。
34 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_GRID_VISIBLE 1b 指定是否显示网格。
12 .uno%3AGridVisible 1b 指定是否显示网格。
14 SVX_HID_OPTIONS_GRID 9f 指定文档页面上可配置网格的设置。此网格有助于确定对象的准确位置。您也可以使此网格与“磁性”坐标网格相一致。
3a svx%3ANumericField%3ARID_SVXPAGE_GRID%3ANUM_FLD_DIVISION_X 37 指定X轴上网格点之间的中间空间的数目。
35 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_DRAW_Y 39 以所需度量单位在 Y 轴上定义网格点间距。
15 .uno%3AHelplinesFront 24 在所有对象前设置坐标线。
10 .uno%3AGridFront 27 在所有对象前设置可见网格。
35 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_DRAW_X 39 定义 X 轴上网格点之间的间隔的度量单位。
3a svx%3ANumericField%3ARID_SVXPAGE_GRID%3ANUM_FLD_DIVISION_Y 37 指定Y轴上网格点之间的中间空间的数目。
34 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_USE_GRIDSNAP 48 指定是否仅在网格点之间移动框架、绘图元素和控件。
13 .uno%3AToolsOptions db 定义电子表格的各种设置、要显示的内容，以及输入单元格条目后光标的移动方向。还可以定义排序列表，确定小数点位数以及进行有关记录和突出显示修改的设置。
14 .uno%3AScEditOptions db 定义电子表格的各种设置、要显示的内容，以及输入单元格条目后光标的移动方向。还可以定义排序列表，确定小数点位数以及进行有关记录和突出显示修改的设置。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_HSCROLL 3c 指定是否要在文档窗口底部显示水平滚动条。
12 .uno%3ANoteVisible 57 要始终显示批注，请在单元格的上下文菜单中选择显示批注命令。
32 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_PAGEBREAKS 39 指定是否在定义的打印区域内显示分页符。
2e sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ANCHOR 4b 指定当选择插入的对象（如图形）时是否显示锁定图标。
2b sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_NIL 1b 指定是否显示零值。
15 SC_HID_SCPAGE_CONTENT 73 指定显示 %PRODUCTNAME Calc 主窗口的哪些元素。还可以指定是否要在表格中突出显示数值。
2c sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_CLIP f0 如果单元格包含的文本的宽度超过单元格的宽度，该文本将显示在同一行的相邻空单元格中。如果不存在空的相邻单元格，则在单元格的边框上显示一个小三角，表示还有更多文字。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ANNOT a9 指定单元格右上角的小矩形表示此单元格含有注释。仅当您在“选项”对话框的 %PRODUCTNAME - 常规下启用了提示时才显示此注释。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_RFIND ab 指定公式中的每个引用都以彩色突出显示。一旦选定含有引用的单元格以进行编辑，对应的单元格区域就会以彩色边框突出显示。
2b sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_DRAW 36 定义文档中的绘图对象是显示还是隐藏。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_VALUE db 选中突出显示数值框可以根据类型以不同的颜色来显示单元格内容。文本单元格、公式和数字单元格分别采用黑色、绿色和蓝色的格式（无论其显示采用何种格式）。
32 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_BIGHANDLES 48 指定显示比普通控点（选择框上的八个点）大的控点。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_HANDLES 63 指定是否将控点（选择框上的八个点）显示为没有三维效果的简单正方形。
30 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_SYNCZOOM 90 如果选中的话，使用同一显示比例显示所有工作表。如果未选中的话，则每一工作表使用其自己的显示比例。
2d sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_OBJGRF 2d 定义对象和图形是显示还是隐藏。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_OUTLINE 72 如果您指定了大纲，分级显示符号选项将指定是否在工作表边框处显示分级显示符号。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_VSCROLL 3c 指定是否要在文档窗口右侧显示垂直滚动条。
34 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ROWCOLHEADER 27 指定是否显示行和列的标题。
2e sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_DIAGRAM 30 定义文档中的图表是显示还是隐藏。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_FORMULA 36 指定单元格中是显示公式还是显示结果。
2c sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_GRID 33 指定是否要在单元格之间显示网格线。
2e sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_TBLREG 42 指定是否要在电子表格文档底部显示工作表标签。
31 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_GUIDELINE 51 指定在移动绘图、框架、图形及其他对象时是否显示辅助线。
2c sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_COLOR 2a 定义当前文档中网格线的颜色。
2b sc%3AListBox%3ARID_SCPAGE_LAYOUT%3ALB_ALIGN 43 确定按 Enter 键之后光标在电子表格中的移动方向。
2f sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_REPLWARN 57 指定在将单元格从剪贴板粘贴到非空白的单元格区域时显示警告。
2d sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_FORMAT 57 指定是否将选定单元格的格式属性自动应用到相邻的空白单元格。
2d sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_EXPREF b1 指定在引用范围的周围插入行或列时是否扩展引用。只有插入行或列的引用范围在所需的方向上包含至少两个单元格，才会扩展引用。
13 .uno%3ASetInputMode 40 允许您在按 Enter 键之后直接编辑选定的单元格。
2a sc%3AListBox%3ARID_SCPAGE_LAYOUT%3ALB_UNIT 27 定义电子表格中的度量单位。
2e sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_TEXTFMT 45 指定在打印以及格式化屏幕显示时应用打印机版式。
2d sc%3AMetricField%3ARID_SCPAGE_LAYOUT%3AMF_TAB 1b 定义制表位的距离。
2e sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_MARKHDR 3f 指定是否在选定列和行中突出显示列和行标题。
2f sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_EDITMODE 40 允许您在按 Enter 键之后直接编辑选定的单元格。
2c sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_ALIGN 43 确定按 Enter 键之后光标在电子表格中的移动方向。
2e sc%3AListBox%3ARID_SCPAGE_USERLISTS%3ALB_LISTS 3f 显示所有可用列表。可选定这些列表进行编辑。
17 SC_HID_SCPAGE_USERLISTS 9c 排序列表对话框中显示了所有自定义的列表。您也可以定义和编辑自己的列表。只有文本可用作排序列表，数字不能。
30 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_ADD 24 在列表框中添加新的列表。
30 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_NEW 2a 在条目框中输入新列表的内容。
36 sc%3AMultiLineEdit%3ARID_SCPAGE_USERLISTS%3AED_ENTRIES 42 显示当前选定列表的内容。可对该内容进行编辑。
31 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_COPY e7 在复制列表从框中复制单元格的内容。如果选择对相关行和列的引用，则单击该按钮后会出现复制列表对话框。可以使用此对话框定义是按行还是按列将引用转换为排序列表。
2e sc%3AEdit%3ARID_SCPAGE_USERLISTS%3AED_COPYFROM 99 定义要复制的电子表格和单元格，以将其包含在列表框中。默认情况下，框中显示的是电子表格中当前选定的区域。
36 sc%3ARadioButton%3ARID_SCDLG_COLORROW%3ABTN_GROUP_COLS 39 选择列选项将选定列的内容编成一个列表。
36 sc%3ARadioButton%3ARID_SCDLG_COLORROW%3ABTN_GROUP_ROWS 39 选择行选项将选定行的内容编成一个列表。
25 sc%3AModalDialog%3ARID_SCDLG_COLORROW 36 允许将标记的单元格复制到排序列表中。
12 SC_HID_SCPAGE_CALC 2a 定义用于电子表格的计算设置。
2a sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_CASE 42 指定在比较单元格内容时是否区分字母的大小写。
31 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATESC10 69 将 1/1/1900 设置为零。对于含有日期条目的 StarCalc 1.0 电子表格，请使用此设置。
30 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATESTD 1e 将 12/30/1899 设置为零。
24 sc%3AEdit%3ARID_SCPAGE_CALC%3AED_EPS 7b 指定两个连续的迭代步数的结果之间的差值。如果迭代结果小于最小偏差值，则迭代将停止。
2c sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_LOOKUP b1 指定您可以使用任意单元格中的文本作为该文本下方的列或其右侧的行的标签。文本至少要包含一个单词，并且不能含有任何运算符。
2a sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_CALC ed 指定是否使用工作表中显示的舍入值进行计算。显示图表时将使用所显示的值。如果未选中显示的精度选项，则显示的数字是舍入后的值，但在内部计算时将使用未经舍入的数字。
31 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATE1904 5e 将 1/1/1904 设置为零。对于从外来格式导入的电子表格，请使用此设置。
32 sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_GENERAL_PREC da 你可以指定具有 General 数字格式的单元格其默认显示的最大小数点位数。如果不启用该选项，General 数字格式的单元格将显示其宽度所允许的尽可能多的小数点位数。
2b sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_MATCH 109 指定为 Calc 数据库函数设置的查找条件必须与整个单元格完全匹配。当选中查找条件 = 和 <> 必须应用于整个单元格框，则 $[officename] Calc 使用数据库函数查找单元格时，其行为方式与 MS Excel 完全相同。
2d sc%3ANumericField%3ARID_SCPAGE_CALC%3AED_PREC da 你可以指定具有 General 数字格式的单元格其默认显示的最大小数点位数。如果不启用该选项，General 数字格式的单元格将显示其宽度所允许的尽可能多的小数点位数。
2b sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_REGEX 45 指定在进行查找和比较字符串时，启用正则表达式。
2e sc%3ANumericField%3ARID_SCPAGE_CALC%3AED_STEPS 1e 设置最大的迭代步数。
2d sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_ITERATE 8a 指定对于带有迭代引用的公式（即不断重复计算直至得出结果的公式）是否要进行一定次数的重复计算。
30 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_INSERT 3c 指定用于突出显示文档中的插入内容的颜色。
31 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_CONTENT 30 指定已经修改的单元格内容的颜色。
17 SC_HID_SCPAGE_OPREDLINE 60 在修改对话框中，可以指定各种选项，以突出显示文档中已记录的修改。
30 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_REMOVE 39 指定用于突出显示文档中删除部分的颜色。
2e sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_MOVE 3c 指定用于突出显示已移动单元格内容的颜色。
35 sc%3ACheckBox%3ARID_SCPAGE_PRINT%3ABTN_SELECTEDSHEETS c6 指定只打印选定工作表中的内容，即使您在文件 - 打印对话框或格式 - 打印范围对话框中指定了更大的打印范围。不会打印未选定的工作表中的内容。
35 sc%3ACheckBox%3ARID_SCPAGE_PRINT%3ABTN_SKIPEMPTYPAGES 3f 指定不打印无单元格内容或绘图对象的空白页。
14 .uno%3ASdEditOptions 99 为新创建的演示文稿文档定义各种设置，例如要显示哪些内容、使用哪种度量单位、是否以及如何采用网格对齐等。
36 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_MOVE_OUTLINE 4f 当移动每个单一对象时，%PRODUCTNAME 将显示该对象的轮廓线。
33 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_HELPLINES 2d 指定移动对象时是否显示辅助线。
16 SD_HID_SD_OPTIONS_VIEW 1e 指定可用的显示模式。
2f sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_RULER 3c 指定是否在工作区域的上边和左边显示标尺。
38 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_HANDLES_BEZIER c0 如果已经事先选择贝塞尔曲线，则显示所有曲线点的控制点。如果不选中贝赛尔曲线编辑器之内的全部控点选项，则只显示选定曲线点的控制点。
1a SD_HID_SD_OPTIONS_CONTENTS 1e 指定可用的显示模式。
14 .uno%3AHelplinesMove 2d 指定移动对象时是否显示辅助线。
38 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_BEZ_ANGLE 1e 定义减少接点的角度。
38 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_SNAP_AREA a9 定义鼠标指针与对象轮廓之间的对齐间隔。如果鼠标指针位于对齐范围控件中选定的间隔内，$[officename] Impress 将对齐到对齐点。
11 .uno%3ASnapBorder 42 指定是否将图形对象的轮廓与最近的页边距对齐。
16 SD_HID_SD_OPTIONS_SNAP 33 定义用于创建和移动对象的网格设置。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_BORDER 42 指定是否将图形对象的轮廓与最近的页边距对齐。
36 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_HELPLINES 4b 释放鼠标时，拖动的对象的边缘将与最近的对齐线对齐。
34 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_ANGLE 5a 指定图形对象只能按照您在旋转时控件中选定的旋转角度进行旋转。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_POINTS 48 指定是否将图形对象的轮廓与最近的图形对象点对齐。
10 .uno%3ASnapFrame 4b 指定是否将图形对象的轮廓与最近的图形对象边框对齐。
2e svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_ROTATE 5a 指定图形对象只能按照您在旋转时控件中选定的旋转角度进行旋转。
13 .uno%3AHelplinesUse 4b 释放鼠标时，拖动的对象的边缘将与最近的对齐线对齐。
2d svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_ORTHO 66 指定在创建或移动图形对象时应用的限制方式：水平、垂直或对角（45 度）。
11 .uno%3ASnapPoints 48 指定是否将图形对象的轮廓与最近的图形对象点对齐。
32 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_FRAME 4b 指定是否将图形对象的轮廓与最近的图形对象边框对齐。
30 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_BIGORTHO 121 指定在释放鼠标键之前按下 Shift 键，则基于矩形较长的边建立一个正方形。此选项对椭圆也同样适用（基于椭圆最长的直径建立一个圆）。如果没有选中较长的图边形复选框，则会基于较短的边或直径建立正方形或圆。
32 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_PAGETILE 84 指定将以平铺格式打印页面。如果页面或幻灯片小于纸张，则将在一张纸上打印多个页面或幻灯片。
2b SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_DATE 21 指定是否打印当前日期。
31 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_DEFAULT 36 指定打印时不需要进一步调整页面比例。
33 SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_HIDDEN_PAGES 36 指定是否打印演示文稿中当前隐藏页面。
17 SD_HID_SD_PRINT_OPTIONS 3f 指定用于绘图文档或演示文稿文档的打印设置。
32 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_PAGESIZE 6f 指定是否按比例缩小那些超出当前打印机边距的对象，使其能够匹配打印机的纸张。
33 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_GRAYSCALE 27 指定是否将彩色打印为灰度。
2b sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_BACK 2a 选择反面以打印小册子的反面。
31 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_BOOKLET 39 选择小册子选项将以小册子格式打印文档。
2f sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_PAPERBIN 4e 确定要使用的纸张来源应用打印机设置中定义的纸张来源。
2c sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_FRONT 2a 选择正面以打印小册子的正面。
34 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_BLACKWHITE 30 指定是否要以黑白的方式打印文档。
2b SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_TIME 21 指定是否打印当前时间。
2f sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_COLOR 24 指定是否以原始颜色打印。
2f SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_PAGENAME 21 指定是否打印页面名称。
34 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACB_MERGE_PARA_DIST 5a 指定 $[officename] Impress 计算段落间距的方法与 Microsoft PowerPoint 相同。
41 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_PRESENTER_SCREEN 5d 指定在多显示器设置时，你希望投影仪屏幕总是和演示文稿一起开始。
39 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_CROOK_NO_CONTORTION 5d 扭曲变形对象时，保持贝塞尔曲线点和平面绘图对象之间的相对对齐。
2a sd%3AComboBox%3ATP_OPTIONS_MISC%3ACB_SCALE 27 确定标尺上的绘图显示比例。
36 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_MASTERPAGE_CACHE 3c 指定当显示母版页上的对象时是否使用缓存。
12 .uno%3APickThrough 33 指定是否通过单击文本来选择文本框。
39 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_TEMPLATE 57 指定以文件 - 新建 - 演示文稿方式打开演示文稿时是否启动向导。
31 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_PICKTHROUGH 33 指定是否通过单击文本来选择文本框。
2f sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_QUICKEDIT 90 如果勾选该选项，你可以单击文本对象后立刻开始编辑文字。如果取消勾选该选项，你必须双击来编辑文字。
2a sd%3AListBox%3ATP_OPTIONS_MISC%3ALB_METRIC 24 确定演示文稿的度量单位。
3d sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_MARKED_HIT_MOVES_ALWAYS 7e 指定使用已启用的旋转工具移动对象。如果未选中对象总是可移动，则旋转工具仅限于旋转体。
16 SD_HID_SD_OPTIONS_MISC 2d 定义绘图或演示文档的常规选项。
3c sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_ACTUAL_PAGE 33 指定演示文稿始终从当前幻灯片开始。
2a sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_COPY 73 若启用，当移动一个对象同时按住 Ctrl 键（Mac：Command键），则创建该对象的一个副本。
10 .uno%3AQuickEdit 90 如果勾选该选项，你可以单击文本对象后立刻开始编辑文字。如果取消勾选该选项，你必须双击来编辑文字。
34 sd%3AMetricField%3ATP_OPTIONS_MISC%3AMTR_FLD_TABSTOP 21 定义制表位之间的间距。
17 .uno%3ASdGraphicOptions 8d 定义用于绘图文档的全局设置，包括要显示的内容、要使用的显示比例、网格对齐以及默认的打印内容等。
14 .uno%3ASmEditOptions 85 定义所有新建的公式文档的打印格式和打印选项。这些选项适用于直接从 %PRODUCTNAME Math 中打印公式。
d tobeannounced 27 仅保存公式中所用到的符号。
36 starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_ZOOM 3f 使用指定的缩放比例来缩小或放大打印的公式。
3f starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_ORIGINAL_SIZE 33 打印公式，但不调整当前的字体大小。
2e STARMATH_CHECKBOX_RID_PRINTOPTIONPAGE_CB_FRAME 33 对打印输出中的公式区域应用细边框。
3c starmath%3ACheckBox%3ARID_PRINTOPTIONPAGE%3ACB_EQUATION_TEXT 42 指定是否在打印输出的底部包括命令窗口的内容。
31 STARMATH_CHECKBOX_RID_PRINTOPTIONPAGE_CB_TITLEROW 36 指定是否在打印输出中包括文档的名称。
3d starmath%3ACheckBox%3ARID_PRINTOPTIONPAGE%3ACB_IGNORE_SPACING 42 指定如果这些空格通配符位于行末，则将其删除。
36 starmath%3AMetricField%3ARID_PRINTOPTIONPAGE%3AMF_ZOOM 3f 使用指定的缩放比例来缩小或放大打印的公式。
3d starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_FIT_TO_PAGE 36 根据打印输出使用的页面格式调整公式。
16 SFX2_HID_PRINT_OPTIONS 30 定义对所有文档均有效的公式设置。
28 starmath%3ATabPage%3ARID_PRINTOPTIONPAGE 30 定义对所有文档均有效的公式设置。
41 cui%3AListBox%3ARID_OPTPAGE_CHART_DEFCOLORS%3ALB_CHART_COLOR_LIST 2d 显示可用于数据序列的所有颜色。
44 cui%3APushButton%3ARID_OPTPAGE_CHART_DEFCOLORS%3APB_RESET_TO_DEFAULT 30 恢复到安装程序时定义的颜色设置。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_PBAS_CODE da 将 Microsoft 文档中的 Basic 代码作为文档的特殊 $[officename] Basic 模块装入并保存。被禁用的 Microsoft Basic 代码在 $[officename] Basic IDE 中是可见的，位于 Sub 和 End Sub 之间。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_WBAS_CODE da 将 Microsoft 文档中的 Basic 代码作为文档的特殊 $[officename] Basic 模块装入并保存。被禁用的 Microsoft Basic 代码在 $[officename] Basic IDE 中是可见的，位于 Sub 和 End Sub 之间。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_PBAS_STG 101 指定在 $[officename] 中装入某一文档的整个过程中，始终将该文档中包含的原始 Microsoft Basic 代码保存在特殊的内存储器中。以 Microsoft 格式保存文档时，Microsoft Basic 也会以不变格式与其一起保存。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_WBAS_STG 101 指定在 $[officename] 中装入某一文档的整个过程中，始终将该文档中包含的原始 Microsoft Basic 代码保存在特殊的内存储器中。以 Microsoft 格式保存文档时，Microsoft Basic 也会以不变格式与其一起保存。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_CODE da 将 Microsoft 文档中的 Basic 代码作为文档的特殊 $[officename] Basic 模块装入并保存。被禁用的 Microsoft Basic 代码在 $[officename] Basic IDE 中是可见的，位于 Sub 和 End Sub 之间。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_STG 101 指定在 $[officename] 中装入某一文档的整个过程中，始终将该文档中包含的原始 Microsoft Basic 代码保存在特殊的内存储器中。以 Microsoft 格式保存文档时，Microsoft Basic 也会以不变格式与其一起保存。
3a svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_EXECTBL bf VBA (Visual Basic for Applications) 代码将要被装入并准备执行。如果没有选中该复选框，则 VBA 代码将会被注释，这样一来它会被检查，但是不会运行。
1b CUI_HID_OFAPAGE_MSFLTR2_CLB a9 列表框 字段中显示了用于成对的 OLE 对象的条目。在装入到 $[officename] (L) 和/或保存为 Microsoft 格式 (S) 时，可以转换这些对象。.
33 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_ASIANSUPPORT 59 激活亚洲语言支持。现在可修改 %PRODUCTNAME 中相应的亚洲语言设置。
32 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_CURRENT_DOC 33 指定默认语言设置仅对当前文档有效。
2e cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_CURRENCY 39 指定用于货币格式和货币字段的默认货币。
37 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_DECIMALSEPARATOR 60 指定在按下数字小键盘上的各个键时使用在系统中设置的数字分隔符键。
31 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_CTLSUPPORT 68 激活复杂文本版式支持。现在就可修改 %PRODUCTNAME 中有关复杂文本版式的设置。
33 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_LOCALESETTING 67 指定国家/地区设置的语言环境设置，这将影响编号、货币和度量单位的设置。
33 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_USERINTERFACE b6 选择用户界面所使用的语言，例如菜单、对话框、帮助文件。您必须已经安装了至少一种其他的语言包或者一个 %PRODUCTNAME 的多语言版本。
32 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_COMPLEX_LANG 36 指定用于复杂文本版式拼写检查的语言。
30 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_ASIAN_LANG 3c 指定对亚洲文字进行拼写检查时使用的语言。
2f cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_WEST_LANG 3c 指定对西文文字进行拼写检查时使用的语言。
41 cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_PUNCT_KANA_COMP 2a 指定压缩标点符号和日文假名。
3e cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_CHAR_KERNING 33 指定字母间隔相等仅应用于西文文本。
30 cui%3AEdit%3ARID_SVXPAGE_ASIAN_LAYOUT%3AED_START 2d 指定不应单独出现在行首的字符。
36 cui%3AListBox%3ARID_SVXPAGE_ASIAN_LAYOUT%3ALB_LANGUAGE 36 指定要定义开头字符和结尾字符的语言。
3c cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_PUNCT_COMP 1e 指定只压缩标点符号。
2e cui%3AEdit%3ARID_SVXPAGE_ASIAN_LAYOUT%3AED_END 2d 指定不应单独出现在行尾的字符。
37 cui%3ACheckBox%3ARID_SVXPAGE_ASIAN_LAYOUT%3ACB_STANDARD 5d 当您选中默认时，以下两个文本框中将填入针对选定语言的默认字符：
39 cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_NO_COMP 18 指定不进行压缩。
3c cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_CHAR_PUNCT 48 指定字母间隔相等适用于西文文本和中日韩标点符号。
41 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_BAVA_HAFA 27 指定查找中视为等同的选项。
46 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_OLD_KANA_FORMS 27 指定查找中视为等同的选项。
43 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_MIDDLE_DOT 1b 指定要忽略的字符。
42 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_SESHE_ZEJE 27 指定查找中视为等同的选项。
47 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_TSITHICHI_DHIZI 27 指定查找中视为等同的选项。
47 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_FULL_HALF_WIDTH 27 指定查找中视为等同的选项。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_WHITESPACES 1b 指定要忽略的字符。
3c cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_KIKU 27 指定查找中视为等同的选项。
3c cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_CASE 27 指定查找中视为等同的选项。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_PUNCTUATION 1b 指定要忽略的字符。
4a cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_VARIANT_FORM_KANJI 27 指定查找中视为等同的选项。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_CONTRACTIONS 27 指定查找中视为等同的选项。
49 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_REPEAT_CHAR_MARKS 27 指定查找中视为等同的选项。
4b cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_PROLONGED_SOUNDMARK 27 指定查找中视为等同的选项。
45 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_HYUFYU_BYUVYU 27 指定查找中视为等同的选项。
41 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_DIZI_DUZU 27 指定查找中视为等同的选项。
48 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_MINUS_DASH_CHOON 27 指定查找中视为等同的选项。
3d cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_IAIYA 27 指定查找中视为等同的选项。
49 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_HIRAGANA_KATAKANA 27 指定查找中视为等同的选项。
41 cui%3ARadioButton%3ARID_SVXPAGE_OPTIONS_CTL%3ARB_MOVEMENT_LOGICAL 78 按右箭头键，将文本光标移到文本末尾位置。按左箭头键，将文本光标移到文本起始位置。
35 cui%3AListBox%3ARID_SVXPAGE_OPTIONS_CTL%3ALB_NUMERALS 9b 选择所有 %PRODUCTNAME 模块中文字、对象中的文字、字段和控件所使用的数字类型，%PRODUCTNAME Calc 中的单元格内容除外。
40 cui%3ARadioButton%3ARID_SVXPAGE_OPTIONS_CTL%3ARB_MOVEMENT_VISUAL 60 按右箭头键，将文本光标向右移动。按左箭头键，将文本光标向左移动。
3e cui%3ACheckBox%3ARID_SVXPAGE_OPTIONS_CTL%3ACB_SEQUENCECHECKING 36 启用对语言（如泰语）的顺序输入检查。
3d cui%3ANumericField%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ANF_TIMEOUT 36 定义保留连接池的时间（以秒为单位），
3f cui%3ACheckBox%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ACB_DRIVERPOOLING 84 从列表中选择某个驱动程序，并选中为这个驱动程序启用连接池复选框，以保留该驱动程序的连接。
3c cui%3ACheckBox%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ACB_POOL_CONNS 24 指定是否保留选定的连接。
24 DBACCESS_HID_DSBROWSER_DISCONNECTING 24 指定是否保留选定的连接。
36 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_DELETE 21 从列表中删除选定条目。
34 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_EDIT 36 打开数据库链接对话框以编辑选定条目。
33 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_NEW 33 打开数据库链接对话框以创建新条目。
17 CUI_HID_DBPATH_CTL_PATH 5a 列出所有注册数据库的注册名称和数据库文件。双击条目进行编辑。
27 cui%3AEdit%3ADLG_DOCUMENTLINK%3AET_NAME 49 输入数据库的名称。%PRODUCTNAME 使用此名称访问数据库。
33 cui%3APushButton%3ADLG_DOCUMENTLINK%3APB_BROWSEFILE 51 打开一个文件对话框，您可以在该对话框中选择数据库文件。
38 cui%3APushButton%3ARID_SVXPAGE_IMPROVEMENT%3APB_SHOWDATA 81 单击该按钮可显示当前收集的数据。下次会发送此数据，以及您结束当前会话前收集的所有数据。
33 cui%3ARadioButton%3ARID_SVXPAGE_IMPROVEMENT%3ARB_NO 33 单击否，我不想参加以禁用自动反馈。
34 cui%3ARadioButton%3ARID_SVXPAGE_IMPROVEMENT%3ARB_YES 34 单击是，我愿意参加 …启用自动反馈。
3a cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_PARAMETER 23 打开Java 启动参数对话框。
19 CUI_HID_OPTIONS_JAVA_LIST b7 选择您想使用的 JRE。在一些系统中，您必须等待一分钟直到列表被填充。在一些系统中，您必须重新启动 %PRODUCTNAME 以使用更改过的设置。
3a cui%3ACheckBox%3ARID_SVXPAGE_OPTIONS_JAVA%3ACB_JAVA_ENABLE 38 允许您在 %PRODUCTNAME 中运行 Java 应用程序。
3a cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_CLASSPATH 1b 打开类路径对话框。
34 cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_ADD 3b 在您的计算机 JRE 根文件夹中添加一个路径。
39 cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_ADDPATH 30 选择文件夹并将其添加到类路径中。
33 cui%3AListBox%3ARID_SVXDLG_JAVA_CLASSPATH%3ALB_PATH 2a 指定 Java 类或 Java 类库的位置。
3c cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_ADDARCHIVE 55 选择 jar 或 zip 格式的存档文件，然后将该文件添加到类路径中。
3d cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_REMOVE_PATH 5d 在列表中选择档案或文件夹，然后单击“删除”从类路径中删除对象。
37 cui%3AListBox%3ARID_SVXDLG_JAVA_PARAMETER%3ALB_ASSIGNED 6b 将会列出指定的 JRE 启动参数。要删除启动参数，请选择该参数，然后单击删除。
35 cui%3AEdit%3ARID_SVXDLG_JAVA_PARAMETER%3AED_PARAMETER 89 如同针对命令行操作一样，输入 JRE 的启动参数。单击“指定”即可将参数添加到可用启动参数的列表。
38 cui%3APushButton%3ARID_SVXDLG_JAVA_PARAMETER%3APB_REMOVE 23 删除选定的 JRE 启动参数。
38 cui%3APushButton%3ARID_SVXDLG_JAVA_PARAMETER%3APB_ASSIGN 2f 将当前 JRE 启动参数添加到列表中。
3a xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_LOW c6 可以将宏设置为自动启动，但是宏可能会执行具有潜在破坏性的操作，例如删除或重命名文件。在打开由其他作者创建的文档时，不建议使用此设置。
3d xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_MEDIUM db 可以在“可靠来源”选项卡页面上设置可靠来源。允许运行来自可靠来源的签名宏。此外，还允许运行任何来自可靠文件位置的宏。所有其他宏都必须经过您的确认。
3f xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_VERYHIGH 7b 可以在“可靠来源”选项卡页面上设置可靠文件位置。允许运行任何来自可靠文件位置的宏。
3b xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_HIGH b1 可以在“可靠来源”选项卡页面上设置可靠来源。只允许运行来自可靠来源的签名宏。此外，还允许运行任何来自可靠文件位置的宏。
4a xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3APB_REMOVE_TRUSTCERT 2d 从可靠证书列表中删除选定证书。
48 xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3APB_VIEW_TRUSTCERT 33 为选定证书打开“查看证书”对话框。
4a xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3AFL_ADD_TRUSTFILELOC 4b 打开文件夹选择对话框。选择允许执行所有宏的文件夹。
43 xmlsecurity%3AListBox%3ARID_XMLSECTP_TRUSTSOURCES%3ALB_TRUSTFILELOC 42 文档宏仅当从下列位置之一打开时才可以被执行。
4d xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3AFL_REMOVE_TRUSTFILELOC 36 从可靠文件位置列表中删除选定文件夹。
28 XMLSECURITY_HID_XMLSEC_CTRL_TRUSTSOURCES 15 列出可靠证书。
2b sw%3ANumericField%3ATP_MAILCONFIG%3ANF_PORT 15 输入 SMTP 端口。
26 sw%3AEdit%3ATP_MAILCONFIG%3AED_REPLYTO 24 输入回复电子邮件的地址。
26 sw%3AEdit%3ATP_MAILCONFIG%3AED_ADDRESS 27 输入您的电子邮件回复地址。
29 sw%3APushButton%3ATP_MAILCONFIG%3APB_TEST 36 打开测试账户设置对话框测试当前设置。
33 sw%3APushButton%3ATP_MAILCONFIG%3APB_AUTHENTICATION 6f 打开服务器验证对话框，您可以在该对话框中指定安全电子邮件的服务器验证设置。
2a sw%3ACheckBox%3ATP_MAILCONFIG%3ACB_REPLYTO 5d 将“回复地址”文字框中输入的电子邮件地址用作回复电子邮件地址。
23 SW_CHECKBOX_TP_MAILCONFIG_CB_SECURE 36 可用时，请使用安全连接发送电子邮件。
25 sw%3AEdit%3ATP_MAILCONFIG%3AED_SERVER 1e 输入 SMTP 服务器名称。
2a sw%3AEdit%3ATP_MAILCONFIG%3AED_DISPLAYNAME 15 输入您的姓名。
3a cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYDAY 1b 每天执行一次检查。
39 cui%3APushButton%3ARID_SVXPAGE_ONLINEUPDATE%3APB_CHECKNOW 15 立即执行检查。
3c cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYMONTH 1b 每月执行一次检查。
38 cui%3ACheckBox%3ARID_SVXPAGE_ONLINEUPDATE%3ACB_AUTOCHECK 62 标记以定期检查联机更新，然后选择 %PRODUCTNAME 检查联机更新的时间间隔。
3b cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYWEEK 30 每周执行一次检查。此为默认设置。
39 sw%3ANumericField%3ADLG_MM_SERVERAUTHENTICATION%3ANF_PORT 2d 输入 POP3 或 IMAP 服务器上的端口。
33 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_SERVER 3a 输入 POP 3 或 IMAP 邮件服务器的服务器名称。
35 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_USERNAME 24 输入 SMTP 服务器的用户名。
37 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_INPASSWORD f 输入密码。
38 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_IMAP 29 指定接收邮件服务器使用 IMAP。
46 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_SEP_AUTHENTICATION 36 选择 SMTP 服务器是否需要用户名和密码。
3f sw%3ACheckBox%3ADLG_MM_SERVERAUTHENTICATION%3ACB_AUTHENTICATION 36 启用通过 SMTP 发送电子邮件所需的验证。
38 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_POP3 2a 指定接收邮件服务器使用 POP 3。
37 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_INUSERNAME 24 输入 IMAP 服务器的用户名。
40 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_SMPTAFTERPOP 42 选择是否必须在发送电子邮件之前阅读电子邮件。
38 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_OUTPASSWORD 1b 输入用户名的密码。
36 sw%3APushButton%3ADLG_MM_TESTACCOUNTSETTINGS%3APB_STOP 30 单击停止按钮可手动停止测试会话。
21 SW_HID_MM_TESTACCOUNTSETTINGS_TLB 3c 在顶部列表框中，可以看到测试会话的结果。
3a sw%3AMultiLineEdit%3ADLG_MM_TESTACCOUNTSETTINGS%3AED_ERROR 54 在“错误”列表框中，可以查阅测试设置时遇到的错误的说明。
2a XMLSECURITY_HID_XMLSEC_CTRL_VIEWSIGNATURES 51 查看证书对话框的“证书路径”页面显示证书的位置和状态。
22 XMLSECURITY_HID_XMLSEC_TP_CERTPATH 51 查看证书对话框的“证书路径”页面显示证书的位置和状态。
43 xmlsecurity%3AMultiLineEdit%3ARID_XMLSECTP_CERTPATH%3AML_CERTSTATUS 51 查看证书对话框的“证书路径”页面显示证书的位置和状态。
24 XMLSECURITY_HID_XMLSEC_CTRL_ELEMENTS 54 查看证书对话框的“详细信息”页面显示有关证书的详细信息。
3f xmlsecurity%3AMultiLineEdit%3ARID_XMLSECTP_DETAILS%3AML_ELEMENT 3f 使用数值列表框查看数值并将其复制到剪贴板。
21 XMLSECURITY_HID_XMLSEC_TP_DETAILS 54 查看证书对话框的“详细信息”页面显示有关证书的详细信息。
21 XMLSECURITY_HID_XMLSEC_TP_GENERAL 4e 查看证书对话框的“常规”页面显示有关证书的基本信息。
