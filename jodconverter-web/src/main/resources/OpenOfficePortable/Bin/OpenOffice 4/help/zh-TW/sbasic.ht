3c basctl%3AListBox%3ARID_DLG_SETDEF_LANGUAGE%3ALB_DEF_LANGUAGE 3f 該預設語言會用做為所有其他語言字串的來源。
3b basctl%3APushButton%3ARID_DLG_MANAGE_LANGUAGE%3APB_DEL_LANG ca 從清單中選取語言，然後按一下 [刪除] 以移除該語言。當您移除所有語言時，也會從目前的程式庫中移除所有對話方塊中可本土化的對話方塊字串資源。
3b basctl%3APushButton%3ARID_DLG_MANAGE_LANGUAGE%3APB_ADD_LANG 39 開啟對話方塊，在此可將語言增加至清單。
38 basctl%3AListBox%3ARID_DLG_MANAGE_LANGUAGE%3ALB_LANGUAGE 80 按一下語言，然後按一下 [預設] 將此語言設定為預設值，或按一下 [刪除] 從清單中移除該語言。
22 BASCTL_HID_BASICIDE_ADDNEW_CONTROL 28 增加對話方塊字串的 UI 語言。
39 BASCTL_PUSHBUTTON_RID_DLG_MANAGE_LANGUAGE_PB_MAKE_DEFAULT 5b 從清單中選取語言，然後按一下 [預設] 以將此語言設定為預設語言。
16 .uno%3ACurrentLanguage 55 選取您要編輯的字串語言。按一下 [管理語言] 圖示以增加語言。
20 BASCTL_HID_BASICIDE_LIBSDLG_TREE 81 此欄位顯示所選檔案的檔案名稱。在上面的清單方塊中，您將看到包含在該檔案中的所有程式庫。
12 .uno%3AHideCurPage 15 隱藏目前模組。
14 .uno%3ARenameCurrent 2d 在原來的位置重新命名目前模組。
10 .uno%3ANewModule 2a 將一個新模組插入目前程式庫。
10 .uno%3ANewDialog 30 將一個新對話方塊插入目前程式庫。
15 .uno%3ABasicIDEAppear 2e 開啟可寫入和編輯巨集的 Basic IDE。
14 .uno%3ADeleteCurrent 18 刪除選取的模組。
24 BASCTL_HID_BASICIDE_WATCHWINDOW_LIST 75 顯示檢視的變數之清單。在某個條目上按兩次 (兩次之間稍作停頓)，以對其值進行編輯。
1f BASCTL_HID_BASICIDE_REMOVEWATCH 36 從檢視的變數之清單中移除選取的變數。
24 BASCTL_HID_BASICIDE_WATCHWINDOW_EDIT 48 在此文字方塊中輸入變數，其值將顯示在清單方塊中。
24 BASCTL_HID_BASICIDE_STACKWINDOW_LIST 36 在程式執行期間顯示程序和函數的序列。
3d basctl%3APushButton%3ARID_BASICIDE_BREAKPOINTDLG%3ARID_PB_NEW 2d 在指定的行號上建立一個中斷點。
40 basctl%3ACheckBox%3ARID_BASICIDE_BREAKPOINTDLG%3ARID_CHKB_ACTIVE 24 啟動或關閉目前的中斷點。
1c BASCTL_HID_BASICIDE_BRKPROPS 1b 指定中斷點的選項。
19 BASCTL_HID_BASICIDE_ACTIV 24 啟動或關閉目前的中斷點。
41 basctl%3AComboBox%3ARID_BASICIDE_BREAKPOINTDLG%3ARID_CB_BRKPOINTS 39 輸入新中斷點的行號，然後按一下 [新增]。
3d basctl%3APushButton%3ARID_BASICIDE_BREAKPOINTDLG%3ARID_PB_DEL 1b 刪除選取的中斷點。
41 basctl%3ANumericField%3ARID_BASICIDE_BREAKPOINTDLG%3ARID_FLD_PASS 36 指定在中斷點生效前要執行迴圈的次數。
1a BASCTL_HID_BASICIDE_BRKDLG 1b 指定中斷點的選項。
26 EXTENSIONS_HID_PROP_SHOWS_ROOT_HANDLES 36 指定是否也應在根層級顯示節點的控點。
18 EXTENSIONS_HID_PROP_STEP 63 指定目前控制項被指定的對話方塊頁面數，或您要編輯的對話方塊頁面數。
1a EXTENSIONS_HID_PROP_HEIGHT 30 指定目前控制項或對話方塊的高度。
20 EXTENSIONS_HID_PROP_REPEAT_DELAY 3c 以毫秒為單位指定捲軸觸發事件之間的延遲。
22 EXTENSIONS_HID_PROP_SELECTION_TYPE 39 指定此樹狀結構控制項所啟用的選取模式。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MAX 24 指定捲軸控制項的最大值。
1f EXTENSIONS_HID_PROP_SCROLLVALUE 4e 指定捲軸控制項的初始值。此值可決定捲軸調整器的位置。
23 EXTENSIONS_HID_PROP_SHOW_SCROLLBARS 2d 增加為文字方塊指定的捲軸類型。
18 EXTENSIONS_HID_PROP_DATE 33 指定日期控制項中會顯示預設的日期。
1a EXTENSIONS_HID_PROP_REPEAT 4e 在微調按鈕等控制項上按住滑鼠按鍵時，會重複觸發事件。
2c EXTENSIONS_HID_PROP_INVOKES_STOP_NOT_EDITING 90 指定當編輯作業因為選取了樹狀結構中的其他節點、變更了樹狀結構的資料或其他特定方式而中斷後的結果。
1c EXTENSIONS_HID_PROP_EDITABLE 36 指定樹狀結構控制項的節點是否可編輯。
21 EXTENSIONS_HID_PROP_SHOWS_HANDLES 27 指定是否應顯示節點的控點。
1b EXTENSIONS_HID_PROP_VSCROLL 2d 增加為文字方塊指定的捲軸類型。
22 EXTENSIONS_HID_PROP_ROOT_DISPLAYED 36 指定是否顯示樹狀結構控制項的根節點。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MIN 24 指定捲軸控制項的最小值。
1b EXTENSIONS_HID_PROP_HSCROLL 2d 增加為文字方塊指定的捲軸類型。
1d EXTENSIONS_HID_PROP_POSITIONY 3f 指定目前控制項與對話方塊上邊緣之間的距離。
1d EXTENSIONS_HID_PROP_POSITIONX 3c 指定目前控制項與對話方塊左側之間的距離。
12 .uno%3ALibSelector 1e 選取要編輯的程式庫。
13 .uno%3ACompileBasic 16 編譯 Basic 巨集。
f .uno%3ARunBasic 27 執行目前模組的第一個巨集。
10 .uno%3ABasicStop 1e 停止執行目前的巨集。
14 .uno%3ABasicStepInto 39 執行巨集，並在下一條指令後停止該巨集。
14 .uno%3ABasicStepOver 39 執行巨集，並在下一個程序後停止該巨集。
17 .uno%3AToggleBreakPoint 21 在程式行中插入中斷點。
f .uno%3AAddWatch 60 按一下此圖示以檢視巨集中的變數。變數的內容將顯示在獨立的視窗中。
1d BASCTL_HID_BASICIDE_OBJECTCAT cd 顯示目前 $[officename] 巨集程式庫、模組與話方塊的階層式檢視。若要顯示視窗中某一項目的內容，請連按兩下其名稱，或選取該名稱並按一下 [顯示] 圖示。
1f BASCTL_HID_BASICIDE_OBJCAT_SHOW 33 顯示所選物件的來源文字或對話方塊。
14 .uno%3AObjectCatalog 44 開啟 [物件] 對話方塊，在此您可以檢視 Basic 物件。
12 .uno%3AChooseMacro 1f 開啟 [巨集] 對話方塊。
13 .uno%3AModuleDialog 37 按一下此處以開啟 [管理巨集] 對話方塊。
11 .uno%3AMatchGroup 81 反白顯示由兩個對應括號括起的文字。將文字游標置於左括號或右括號之前，然後按一下此圖示。
10 .uno%3ALoadBasic 33 在 Basic IDE 視窗中開啟 Basic 來源文字。
12 .uno%3ASaveBasicAs 28 儲存所選 Basic 巨集的原始碼。
13 .uno%3ABasicStepOut 2a 跳回目前巨集中的上一個常式。
18 .uno%3AManageBreakPoints 27 呼叫對話方塊以管理中斷點。
13 .uno%3AImportDialog 41 呼叫 [開啟] 對話方塊，匯入 BASIC 對話方塊檔案。
13 .uno%3AExportDialog 74 在對話方塊編輯器中，此命令會呼叫 [另存新檔] 對話方塊，匯出目前的 BASIC 對話方塊。
16 .uno%3AInsertTimeField 15 加入時間欄位。
17 .uno%3AInsertPushbutton 15 加入指令按鈕。
15 .uno%3AChooseControls 19 開啟 [工具箱] 列。
18 .uno%3AInsertTreeControl 7e 增加可顯示階層式清單的樹狀結構控制項。您可以透過程式使用 API 呼叫 (XtreeControl) 填入清單。
12 .uno%3AProgressBar 27 將進度列加入到對話方塊中。
15 .uno%3AManageLanguage 51 開啟對話方塊，啟用或管理多種語言的多個對話方塊資源集。
16 .uno%3AMacroBarVisible 48 [巨集工具列] 包含用於建立、編輯和執行巨集的指令。
19 SFX2_HID_TABDLG_RESET_BTN 84 當此對話方塊開啟時，將目前標籤的變更重設為可適用設定。當您關閉對話方塊時不會要求您確認。
30 sc%3AImageButton%3ARID_SCDLG_SOLVER%3ARB_VARCELL f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
2f sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_COLCELL f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
34 sc%3APushButton%3ARID_SCDLG_CONSOLIDATE%3ABTN_REMOVE 33 刪除選取的元素或不需要確認的元素。
2f sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_ROWCELL f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_DELDOUBLECAPS 33 刪除選取的元素或不需要確認的元素。
1b DBACCESS_HID_DBWIZ_PREVIOUS 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
1c WIZARDS_HID_QUERYWIZARD_NEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
2e sc%3APushButton%3ARID_SCDLG_NAMES%3ABTN_REMOVE 36 刪除選取的元素或已確認要刪除的元素。
1c SFX2_HID_TABDLG_STANDARD_BTN 3c 將此對話方塊中顯示的值重設為預設安裝值。
11 WIZARDS_HID0_NEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND1_1 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
3b cui%3APushButton%3ARID_SVXDLG_MULTIPATH%3ABTN_DEL_MULTIPATH 33 刪除選取的元素或不需要確認的元素。
30 sc%3APushButton%3ARID_SCDLG_DBNAMES%3ABTN_REMOVE 36 刪除選取的元素或已確認要刪除的元素。
32 sc%3AImageButton%3ARID_SCDLG_FILTER%3ARB_COPY_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
34 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
1e WIZARDS_HID_QUERYWIZARD_CANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_REPEATCOL f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_DELABBREV 33 刪除選取的元素或不需要確認的元素。
3b sc%3AImageButton%3ARID_SCDLG_SPEC_FILTER%3ARB_CRITERIA_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
2d sd%3APushButton%3ADLG_CUSTOMSHOW%3ABTN_REMOVE 33 刪除選取的元素或不需要確認的元素。
2e sc%3AImageButton%3ARID_SCDLG_NAMES%3ARB_ASSIGN f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1e WIZARDS_HID_DLGTABLE_CMDCANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
21 EXTENSIONS_HID_GROUPWIZARD_CANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
20 EXTENSIONS_HID_LISTWIZARD_CANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
1b WIZARDS_HID_DLGFORM_CMDNEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_PRINTAREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
37 sc%3AImageButton%3ARID_SCDLG_CONSOLIDATE%3ARB_DATA_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_DELETE 36 刪除選取的元素或已確認要刪除的元素。
34 sc%3AImageButton%3ARID_SCDLG_SOLVER%3ARB_FORMULACELL f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND1_2 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1e WIZARDS_HID_QUERYWIZARD_CREATE 24 套用所有變更並關閉精靈。
37 sc%3AImageButton%3ARID_SCDLG_COLROWNAMERANGES%3ARB_DATA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1c WIZARDS_HID_DLGTABLE_CMDNEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
37 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
1d WIZARDS_HID_DLGFORM_CMDCANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND2_2 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1c WIZARDS_HID_QUERYWIZARD_BACK 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
1b CUI_HID_MEASURE_CTL_PREVIEW 1e 顯示目前選取的預覽。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND2_1 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
31 cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND3_1 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
2c starmath%3APushButton%3ARID_SYMBOLDIALOG%3A2 4e 在不關閉對話方塊的情況下套用經過修改的值或選取的值。
1c WIZARDS_HID_DLGTABLE_CMDPREV 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
34 sc%3AImageButton%3ARID_SCDLG_TABOP%3ARB_FORMULARANGE f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
15 SW_HID_MD_GLOS_DELETE 36 刪除選取的元素或已確認要刪除的元素。
11 WIZARDS_HID0_PREV 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
2c sd%3APushButton%3ADLG_COPY%3ABTN_SET_DEFAULT 3c 將此對話方塊中顯示的值重設為預設安裝值。
30 sc%3AImageButton%3ARID_SCDLG_DBNAMES%3ARB_DBAREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
13 WIZARDS_HID0_CANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
37 sc%3AImageButton%3ARID_SCDLG_CONSOLIDATE%3ARB_DEST_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
33 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_REMOVE 36 刪除選取的元素或已確認要刪除的元素。
21 WIZARDS_HID_DLGIMPORT_0_CMDCANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
32 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_DEL 36 刪除選取的元素或已確認要刪除的元素。
34 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
37 sc%3AImageButton%3ARID_SCDLG_SPEC_FILTER%3ARB_COPY_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
21 WIZARDS_HID_DLGREPORT_0_CMDCANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
33 sc%3APushButton%3ARID_SCDLG_AUTOFORMAT%3ABTN_REMOVE 36 刪除選取的元素或已確認要刪除的元素。
1e EXTENSIONS_HID_LISTWIZARD_NEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
2a sw%3APushButton%3ADLG_BIB_BASE%3APB_DELETE 33 刪除選取的元素或不需要確認的元素。
31 basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_DELETE 36 刪除選取的元素或已確認要刪除的元素。
37 sc%3AImageButton%3ARID_SCDLG_COLROWNAMERANGES%3ARB_AREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1b WIZARDS_HID_DLGFORM_CMDPREV 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_DELETE 36 刪除選取的元素或已確認要刪除的元素。
22 EXTENSIONS_HID_LISTWIZARD_PREVIOUS 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
31 sc%3AImageButton%3ARID_SCDLG_AREAS%3ARB_REPEATROW f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
17 DBACCESS_HID_DBWIZ_NEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
23 EXTENSIONS_HID_GROUPWIZARD_PREVIOUS 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
36 sc%3AImageButton%3ARID_SCDLG_PIVOT_LAYOUT%3ARB_OUTAREA f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
34 sc%3AImageButton%3ARID_SCDLG_CONDFORMAT%3ARB_COND3_2 f5 按一下 [縮小] 圖示，使對話方塊縮小至輸入欄位的大小。這樣可以更方便地在試算表中標記所需的參照。而圖示自動轉換成 [放大] 圖示。按一下此圖示，對話方塊將復原到原來的大小。
1e WIZARDS_HID_DLGTABLE_CMDFINISH 24 套用所有變更並關閉精靈。
1f EXTENSIONS_HID_GROUPWIZARD_NEXT a3 按 [下一步] 按鈕，精靈就會使用目前的對話方塊設定，並繼續進行下一步操作。若是在最後一步，則此按鈕會變為 [建立]。
17 SVX_HID_GALLERY_PREVIEW 1e 顯示目前選取的預覽。
31 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_DELETE 36 刪除選取的元素或已確認要刪除的元素。
43 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_REPLACE%3APB_DELETE_REPLACE 33 刪除選取的元素或不需要確認的元素。
1c WIZARDS_HID_FAXWIZARD_CANCEL 49 按一下 [取消] 關閉對話方塊，不儲存所做的任何變更。
34 cui%3APushButton%3ARID_OFADLG_OPTIONS_TREE%3APB_BACK 33 將修改的值重設回 $[officename] 預設值。
39 sc%3APushButton%3ARID_SCDLG_COLROWNAMERANGES%3ABTN_REMOVE 36 刪除選取的元素或已確認要刪除的元素。
1f WIZARDS_HID_DLGREPORT_0_CMDPREV 57 在對話方塊中檢視前一個步驟選取的內容。目前的設定維持不變。
2b sw%3APushButton%3ATP_STD_FONT%3APB_STANDARD 3c 將此對話方塊中顯示的值重設為預設安裝值。
1f SID_EXPLORERCONTENT_OPEN_OBJECT 45 透過這個指令可在新程式單元中開啟已選取的物件。
19 SVX_HID_GALLERY_ACTUALIZE 2a 更新視窗或所選物件中的檢視。
1f SID_EXPLORERCONTENT_OPEN_FOLDER 45 透過這個指令可在新程式單元中開啟已選取的物件。
18 SID_EXPLORERCONTENT_OPEN 45 透過這個指令可在新程式單元中開啟已選取的物件。
1b SID_EXPLORERCONTENT_DESTROY 84 刪除目前的選取。在多重選取下，會刪除所有已選取的物件。在刪除之前，通常都會出現確認覆寫。
19 SID_EXPLORERCONTENT_PASTE 30 將移至剪貼簿上的元素插入文件中。
1a SID_EXPLORERCONTENT_RENAME 21 允許重新命名所選物件。
18 SID_EXPLORERCONTENT_COPY 30 將已選取的元素複製至「剪貼簿」。
1d SVX_HID_GALLERY_MN_BACKGROUND 33 藉此可插入已選取圖形當作背景圖片。
1c SFX2_HID_DID_SAVE_PACKED_XML 51 依預設，$[officename] 會以 Open Office 檔案格式載入與儲存檔案。
e .uno%3AHelpTip 45 啟用在滑鼠指標位置顯示圖示名稱與其他說明內容。
13 .uno%3AHelperDialog ae 可讓您啟動自動 [說明代理程式]。您也可以透過 [選項] 對話方塊中的 [$[officename]] - [一般] - [說明代理程式]，啟動 [說明代理程式]。
11 .uno%3AActiveHelp 45 啟用在滑鼠指標位置顯示功能表與圖示的簡短描述。
33 svtools%3ANumericField%3ADLG_EXPORT%3ANF_RESOLUTION 4b Enter the image resolution. Select the measurement units from the list box.
2d svtools%3AMetricField%3ADLG_EXPORT%3AMF_SIZEY 15 Specifies the height.
32 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_LEVEL2 73 Select the Level 2 option if your output device supports colored bitmaps, palette graphics and compressed graphics.
32 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_LEVEL1 8c Compression is not available at this level. Select the Level 1 option if your PostScript printer does not offer the capabilities of Level 2.
2c svtools%3ARadioButton%3ADLG_EXPORT%3ARB_TEXT 57 Exports the file in ASCII text format. The resulting file is larger than a binary file.
31 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_RLE_ENCODING 36 Applies RLE (Run Length Encoding) to the BMP graphics.
2f svtools%3ACheckBox%3ADLG_EXPORT%3ACB_INTERLACED 40 Specifies whether the graphic is to be saved in interlaced mode.
2e svtools%3ARadioButton%3ADLG_EXPORT%3ARB_BINARY 52 Exports the file in binary format. The resulting file is smaller than a text file.
2d svtools%3AMetricField%3ADLG_EXPORT%3AMF_SIZEX 14 Specifies the width.
39 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COLOR_FORMAT1 1a Exports the file in color.
35 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_EPS_PREVIEW_EPSI b6 Specifies whether a monochrome preview graphic in EPSI format is exported together with the PostScript file. This format only contains printable characters from the 7-bit ASCII code.
34 svtools%3ANumericField%3ADLG_EXPORT%3ANF_COMPRESSION da Sets the compression and quality for the export. Choose from a low quality with minimal file size, up to a high quality and big file size. A high compression means a low quality, a low compression means a high quality.
2e svtools%3AListBox%3ADLG_EXPORT%3ALB_RESOLUTION 4b Enter the image resolution. Select the measurement units from the list box.
3b svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COMPRESSION_LZW 66 LZW compression is the compression of a file into a smaller file using a table-based lookup algorithm.
39 svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COLOR_FORMAT2 24 Exports the file in grayscale tones.
36 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_SAVE_TRANSPARENCY ba Specifies whether to save the background of the picture as transparent. Only objects will be visible in the GIF image. Use the Color Replacer to set the transparent color in the picture.
2f svtools%3AListBox%3ADLG_EXPORT%3ALB_COLOR_DEPTH 41 Select the color depth from 8 bit grayscale or 24 bit true color.
29 svtools%3AListBox%3ADLG_EXPORT%3ALB_SIZEX 20 Specifies the measurement units.
35 svtools%3ACheckBox%3ADLG_EXPORT%3ACB_EPS_PREVIEW_TIFF 6a Specifies whether a preview image is exported in the TIFF format together with the actual PostScript file.
3c svtools%3ARadioButton%3ADLG_EXPORT%3ARB_EPS_COMPRESSION_NONE 32 Specifies that you do not wish to use compression.
2c sc%3AListBox%3ARID_SCDLG_IMPORTOPT%3ALB_FONT 37 從選項中選取匯入/匯出要使用的字元集。
32 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_SAVESHOWN a2 預設為啟用，會如其所示儲存資料，包含套用的數字格式。如果未標記此核取方塊，則會如同舊版軟體儲存原始資料內容。
31 sc%3AComboBox%3ARID_SCDLG_IMPORTOPT%3AED_FIELDSEP 36 選擇或輸入分隔資料欄位的欄位分隔符。
33 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_FIXEDWIDTH 2a 以固定寬度匯出所有資料欄位。
30 sc%3AComboBox%3ARID_SCDLG_IMPORTOPT%3AED_TEXTSEP 4e 選擇或輸入文字分隔符，此分隔符會將括起每個資料欄位。
31 sc%3ACheckBox%3ARID_SCDLG_IMPORTOPT%3ACB_QUOTEALL c0 Exports all text cells with leading and trailing quote characters as set in the Text delimiter box. If not checked, only those text cells get quoted that contain the Field delimiter character.
2e sc%3AListBox%3ARID_SCDLG_IMPORTOPT%3ADDLB_FONT 21 指定文字匯出的字元集。
29 sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_TYPE1 5f Choose a column in the preview window and select the data type to be applied the imported data.
3a sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_DETECT_SPECIAL_NUMBER 9e When this option is enabled, Calc will automatically detect all number formats, including special number formats such as dates, time, and scientific notation.
2f sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_SEMICOLON 2a 將用分號分隔的資料分隔成欄。
26 sc%3AEdit%3ARID_SCDLG_ASCII%3AED_OTHER 75 使用指定的自訂分隔符號，將資料分隔成欄。備註：在資料中也必須包含自訂分隔符號。
29 sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_TAB 2d 將用定位鍵分隔的資料分隔為欄。
33 sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_QUOTED_AS_TEXT b2 When this option is enabled, fields or cells whose values are quoted in their entirety (the first and last characters of the value equal the text delimiter) are imported as text.
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_COMMA 2a 將用逗號分隔的資料分隔為欄。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACB_ASONCE 36 合併連續的分隔符並移除空白資料欄位。
2c sc%3AComboBox%3ARID_SCDLG_ASCII%3ACB_TEXTSEP 57 選取要分隔文字資料的字元。您也可以在此文字方塊中輸入字元。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_OTHER 75 使用指定的自訂分隔符號，將資料分隔成欄。備註：在資料中也必須包含自訂分隔符號。
2d sc%3ARadioButton%3ARID_SCDLG_ASCII%3ARB_FIXED 3c 將固定寬度的資料（相等字元數）分隔為欄。
2f sc%3ANumericField%3ARID_SCDLG_ASCII%3ANF_AT_ROW 21 指定從哪一列開始匯入。
2e sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_CUSTOMLANG 2f Determines how the number strings are imported.
31 sc%3ARadioButton%3ARID_SCDLG_ASCII%3ARB_SEPARATED 24 選取資料中使用的分隔符。
2b sc%3AListBox%3ARID_SCDLG_ASCII%3ALB_CHARSET 33 指定匯入的檔案中所要使用的字元集。
22 sc%3AModalDialog%3ARID_SCDLG_ASCII 24 設定分隔資料的匯入選項。
2b sc%3ACheckBox%3ARID_SCDLG_ASCII%3ACKB_SPACE 2a 將空格所分隔的資料分隔成欄。
31 sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_SCALE 27 指定是否跨頁分佈列印輸出。
2f sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_CUT 57 指定剪下所有超過最大列印範圍的部分，且這些部分將無法列印。
32 sd%3ARadioButton%3ADLG_PRINT_WARNINGS%3ARBT_POSTER 27 指定是否跨頁分佈列印輸出。
25 sd%3AModalDialog%3ADLG_PRINT_WARNINGS 6d 當頁面設定與定義的列印範圍不相符時，螢幕上會顯示 [列印選項警告] 對話方塊。
2d sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_LANGUAGE 63 如果尚未定義文字的語言，則請指定語言。只有在匯入時才可使用此設定。
2b sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_CR 2d 產生作為段落換行的「換行符」。
2d sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_CRLF 42 產生「換行符」和「換行」。此選項為預設選項。
2c sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_CHARSET 2d 指定所匯出或匯入檔案的字元集。
2b sw%3ARadioButton%3ADLG_ASCII_FILTER%3ARB_LF 2a 產生作為段落換行的「換行」。
29 sw%3AListBox%3ADLG_ASCII_FILTER%3ALB_FONT 6f 您可以設定預設字型，指定文字以特定字型顯示。預設字型僅可在匯入時進行選取。
1b SVX_HID_GALLERY_MN_ADD_LINK 21 將所選圖形插入為連結。
15 SVX_HID_GALLERY_TITLE 2a 為所選畫廊物件指定一個標題。
1a SVX_HID_GALLERY_MN_ADDMENU 33 定義如何將所選圖形物件插入文件中。
16 SVX_HID_GALLERY_MN_ADD 36 將所選圖形物件的副本直接插入文件中。
19 SVX_HID_GALLERY_MN_DELETE 1e 確認後刪除所選圖形。
1a SVX_HID_GALLERY_MN_PREVIEW 27 [預覽] 指令會顯示所選圖形。
21 SW_HID_MN_READONLY_SAVEBACKGROUND 21 允許您儲存網頁的背景。
1e SW_HID_MN_READONLY_LOADGRAPHIC 55 如果已經關閉圖形顯示，請選擇 [載入圖形] 指令，以顯示圖形。
1e SW_HID_MN_READONLY_COPYGRAPHIC 27 將所選圖形複製到剪貼簿中。
1d SW_HID_MN_READONLY_GRAPHICOFF 2a 將文件中的所有圖形設為隱藏。
1c SW_HID_MN_READONLY_PLUGINOFF 1e 停用插入的外掛程式。
1b SW_HID_MN_READONLY_COPYLINK 3c 將滑鼠指標所在位置的連結複製到剪貼簿中。
1e SW_HID_MN_READONLY_SAVEGRAPHIC 42 開啟一個對話方塊，您可以在其中儲存所選圖形。
22 CUI_HID_GALLERY_PROPERTIES_GENERAL 18 顯示主題的名稱。
2c private%3Afactory%2Fswriter%2FGlobalDocument 1b 建立新的主控文件。
28 private%3Afactory%2Fswriter%3Fslot=21053 1d 建立新的 XForms 文件。
1b private%3Afactory%2Fswriter 32 建立新的文字文件 ($[officename] Writer)。
19 private%3Afactory%2Fscalc 33 建立新的試算表文件 ($[officename] Calc)。
d .uno%3ANewDoc 50 若要使用範本建立文件，請選擇 [開啟新檔] - [範本與文件]。
28 private%3Afactory%2Fswriter%3Fslot=21052 81 開啟 [名片] 對話方塊，您可以在其中設定名片的選項，然後建立新的文字文件 ($[officename] Writer)。
21 private%3Afactory%2Fswriter%2Fweb 1b 建立新的 HTML 文件。
2f service%3Acom.sun.star.sdb.DatabaseWizardDialog 36 開啟「資料庫精靈」以建立資料庫檔案。
19 private%3Afactory%2Fsmath 30 建立新的公式文件 ($[officename] Math)。
19 private%3Afactory%2Fsdraw 30 建立新的繪圖文件 ($[officename] Draw)。
1b SFX2_HID_TBXCONTROL_FILENEW 4b 建立新的 $[officename] 文件。按一下箭頭以選取文件類型。
10 .uno%3AAddDirect 24 建立新的 $[officename] 文件。
28 private%3Afactory%2Fsimpress%3Fslot=6686 64 建立新的簡報文件 ($[officename] Impress)。螢幕上會顯示 [簡報精靈] 對話方塊。
28 private%3Afactory%2Fswriter%3Fslot=21051 90 開啟 [標籤] 對話方塊，您可以在其中設定標籤的選項，然後為這些標籤建立新的文字文件 ($[officename] Writer)。
b slot%3A5500 42 使用現有的範本或開啟範例文件來建立新的文件。
2b private%3Afactory%2Fsdatabase%3FInteractive 36 開啟「資料庫精靈」以建立資料庫檔案。
1e SVT_HID_TEMPLATEDLG_TB_PREVIEW 27 允許您預覽所選範本或文件。
1c SVT_HID_TEMPLATEDLG_TB_PRINT 1e 列印所選範本或文件。
1c SVT_HID_TEMPLATEDLG_FILEVIEW af 列出所選分類的可用範本或文件。選取範本或文件，然後按一下 [開啟舊檔]。若要預覽文件，請按一下右側方塊上方的 [預覽] 按鈕。
3d svtools%3APushButton%3ADLG_DOCTEMPLATE%3ABTN_DOCTEMPLATE_EDIT 27 開啟選取的範本以進行編輯。
3f svtools%3APushButton%3ADLG_DOCTEMPLATE%3ABTN_DOCTEMPLATE_MANAGE 36 新增、移除或重新排序範本或範例文件。
1b SVT_HID_TEMPLATEDLG_TB_PREV 2a 如果可以，上移一個資料夾級。
1b SVT_HID_TEMPLATEDLG_TB_BACK 2a 移回對話方塊中的上一個視窗。
1c SVT_HID_TEMPLATEDLG_ICONCTRL 9a [範本和文件] 對話方塊左側的方塊中會顯示分類。按一下某個分類，即可在 [標題] 方塊中，顯示與之相關聯的檔案。
1e SVT_HID_TEMPLATEDLG_TB_DOCINFO 27 顯示所選範本或文件的特性。
13 .uno%3AInsertLabels 3c 可讓您建立標籤。標籤會建立在文字文件中。
13 SW_HID_LABEL_INSERT 1e 建立新文件進行編輯。
24 sw%3AListBox%3ATP_LAB_LAB%3ABOX_MAKE 21 選取要使用的紙張商標。
29 sw%3ARadioButton%3ATP_LAB_LAB%3ABTN_SHEET 1e 在單張紙上列印標籤。
27 sw%3AListBox%3ATP_LAB_LAB%3ALB_DATABASE 39 選取要作為標籤的資料來源使用的資料庫。
25 sw%3ACheckBox%3ATP_LAB_LAB%3ABOX_ADDR 64 建立一個包含回傳地址的標籤。目前在 [標籤] 文字方塊中的文字會被覆寫。
e SW_HID_LAB_LAB 36 指定標籤文字，並選擇標籤的紙張大小。
2a sw%3AImageButton%3ATP_LAB_LAB%3ABTN_INSERT 79 選取需要的資料庫欄位，然後按一下此方塊左側的箭頭，將該欄位插入 [標籤] 文字方塊中。
24 sw%3AListBox%3ATP_LAB_LAB%3ALB_TABLE 3c 選取包含標籤中要使用的欄位的資料庫表格。
2d sw%3AMultiLineEdit%3ATP_LAB_LAB%3AEDT_WRITING 48 輸入要在標籤上顯示的文字。也可以插入資料庫欄位。
26 sw%3AListBox%3ATP_LAB_LAB%3ALB_DBFIELD 79 選取需要的資料庫欄位，然後按一下此方塊左側的箭頭，將該欄位插入 [標籤] 文字方塊中。
24 sw%3AListBox%3ATP_LAB_LAB%3ABOX_TYPE d4 選取要使用的大小格式。可用的格式取決於您在 [商標] 清單中所選的商標。如果要使用自訂標籤格式，請選取 [使用者]，然後按一下 [格式] 標籤以定義該格式。
28 sw%3ARadioButton%3ATP_LAB_LAB%3ABTN_CONT 24 在連續的紙張上列印標籤。
2a sw%3AMetricField%3ATP_LAB_FMT%3AFLD_HEIGHT 63 顯示標籤或名片的高度。如果您定義的是自訂格式，請在此處輸入一個值。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_VDIST 7b 顯示上下相鄰的標籤或名片的上邊緣之間的間隔。如果要定義自訂格式，請在此處輸入數值。
28 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_LEFT 81 顯示從頁面左邊緣到第一張標籤或名片左邊緣的間隔。如果要定義自訂格式，請在此處輸入數值。
28 sw%3AComboBox%3ADLG_SAVE_LABEL%3ACB_MAKE 21 輸入或選取所需的商標。
29 sw%3ANumericField%3ATP_LAB_FMT%3AFLD_ROWS 39 輸入要佈滿整個頁面高度的標籤或名片數。
2c sw%3ANumericField%3ATP_LAB_FMT%3AFLD_COLUMNS 39 輸入要佈滿整個頁面寬度的標籤或名片數。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_HDIST 72 顯示相鄰標籤或名片的左邊緣之間的間隔。如果要定義自訂格式，請在此處輸入數值。
24 sw%3AEdit%3ADLG_SAVE_LABEL%3AED_TYPE 1e 輸入或選取標籤類型。
26 sw%3APushButton%3ATP_LAB_FMT%3APB_SAVE 27 儲存目前的標籤或名片格式。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_UPPER 81 顯示從頁面上邊緣到第一張標籤或名片上邊緣的間隔。如果要定義自訂格式，請在此處輸入數值。
29 sw%3AMetricField%3ATP_LAB_FMT%3AFLD_WIDTH 63 顯示標籤或名片的寬度。如果您定義的是自訂格式，請在此處輸入一個值。
e SW_HID_LAB_FMT 1b 設定紙張格式選項。
28 sw%3ANumericField%3ATP_LAB_PRT%3AFLD_COL 3f 輸入您要在頁面的一列中放置的標籤或名片數。
29 sw%3APushButton%3ADLG_SYNC_BTN%3ABTN_SYNC 54 將左上方的標籤或名片的內容複製到頁面上其餘標籤或名片上。
2b sw%3APushButton%3ATP_LAB_PRT%3ABTN_PRTSETUP 26 開啟[設置印表機]對話方塊。
28 sw%3ACheckBox%3ATP_LAB_PRT%3ACB_SYNCHRON 83 允許您編輯單一標籤或名片，並在按一下[標籤同步化]按鈕後，更新頁面上其餘標籤或名片的內容。
2a sw%3ARadioButton%3ATP_LAB_PRT%3ABTN_SINGLE 2a 在一頁上列印一個貼標或名片。
28 sw%3ANumericField%3ATP_LAB_PRT%3AFLD_ROW 3c 輸入您要在頁面上放置的標籤或名片的列數。
28 sw%3ARadioButton%3ATP_LAB_PRT%3ABTN_PAGE 24 建立一整頁的貼標或名片。
e SW_HID_LAB_PRT 5a 設定用於標籤或名片的其他選項，其中包括文字同步和印表機設定。
19 .uno%3AInsertBusinessCard 24 設計並建立您自己的名片。
1e SW_HID_BUSINESS_FMT_PAGE_SHEET 21 在個別紙張上列印名片。
1d SW_HID_BUSINESS_FMT_PAGE_TYPE d1 選取要使用的大小格式。可用的格式取決於您在 [廠牌] 清單中所做的選取。如果要使用自訂大小格式，請選取 [使用者]，然後按一下 [格式] 標籤以定義格式。
1e SW_HID_BUSINESS_FMT_PAGE_BRAND 24 選取所要使用紙張的廠牌。
1d SW_HID_BUSINESS_FMT_PAGE_CONT 21 在連續紙張上列印名片。
35 sw%3AListBox%3ATP_VISITING_CARDS%3ALB_AUTO_TEXT_GROUP 41 選取名片分類，然後在[內容]清單中按一下版式。
1c SW_HID_BUSINESS_CARD_CONTENT 6f 在[自動圖文集] - [區域]方塊中選取一個名片分類，然後在[內容]清單中按一下版式。
2c sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FIRSTNAME_2 2a 輸入要作為第二連絡人的名字。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_STATE 2b 輸入所居住的國家/地區的名稱。
2b sw%3AEdit%3ATP_PRIVATE_DATA%3AED_SHORTCUT_2 2d 輸入作為第二連絡人的姓名縮寫。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_NAME_2 27 輸入作為第二連絡人的姓氏。
2b sw%3AEdit%3ATP_PRIVATE_DATA%3AED_PROFESSION 15 輸入您的職稱。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_WWW 27 輸入您的網際網路首頁位址。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_PHONE 1b 輸入住家電話號碼。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_MOBILE 21 輸入您的行動電話號碼。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_WWW 30 輸入貴公司之網際網路首頁的位址。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_MOBILE 1b 輸入行動電話號碼。
2a sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COMP_EXT 24 輸入公司的其他詳細資訊。
27 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_PHONE 1e 輸入公司的電話號碼。
27 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_STATE 2b 輸入公司所在的國家/地區名稱。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_SLOGAN 1b 輸入貴公司的標語。
1b SFX2_HID_FILEDLG_INSERT_BTN 3c 將選取的檔案插入目前文件中的游標所在處。
33 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_NO 45 按一下以取消刪除在此對話方塊中顯示名稱的檔案。
1c SVT_HID_FILEVIEW_MENU_DELETE 57 若要刪除檔案，請在該檔案上按一下滑鼠右鍵，然後選擇 [刪除]。
47 fpicker%3AMenuButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_STANDARD 2a 顯示預設使用者目錄中的檔案。
49 fpicker%3AImageButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_NEWFOLDER 18 建立一個新目錄。
18 SVT_HID_EXPLORERDLG_FILE 18 開啟或匯入檔案。
43 fpicker%3AListBox%3ADLG_SVT_EXPLORERFILE%3ALB_EXPLORERFILE_FILETYPE 68 選取要開啟的檔案類型，或選取 [全部檔案 (*)] 以顯示目錄中全部檔案的清單。
34 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_YES 3f 按一下以刪除在此對話方塊中顯示名稱的檔案。
19 SFX2_HID_FILEDLG_STANDARD 30 顯示您的個人目錄中的檔案和目錄。
b .uno%3AOpen 18 開啟或匯入檔案。
18 SVT_HID_FILEOPEN_VERSION 4b 如果選取的檔案有多個版本，請選取您想要開啟的版本。
19 SVT_HID_FILEOPEN_READONLY 21 以唯讀模式開啟此檔案。
34 svtools%3APushButton%3ADLG_SVT_QUERYDELETE%3ABTN_ALL 2a 按一下以刪除所有選取的檔案。
1c SVT_HID_FILEVIEW_MENU_RENAME 63 若要重新命名檔案，請在該檔案上按一下滑鼠右鍵，然後選擇 [重新命名]。
41 fpicker%3AMenuButton%3ADLG_SVT_EXPLORERFILE%3ABTN_EXPLORERFILE_UP 5a 在目錄階層中上移一個目錄。長時間按下，以查看較高層級的目錄。
23 FPICKER_HID_FILEDLG_AUTOCOMPLETEBOX 68 輸入檔案的檔名或路徑。您也可以輸入以協定名稱 ftp、http 或 https 開頭的 URL。
20 SFX2_HID_FILEDLG_FOLDER_FILENAME 68 輸入檔案的檔名或路徑。您也可以輸入以協定名稱 ftp、http 或 https 開頭的 URL。
19 SFX2_HID_FILEDLG_PATH_BTN 18 選取指示的路徑。
1e SFX2_HID_FILEDLG_PATH_FILENAME 2a 輸入路徑或從清單中選取路徑。
2e uui%3AListBox%3ADLG_FILTER_SELECT%3ALB_FILTERS 2a 為要開啟的檔案選取匯入篩選。
f .uno%3ACloseDoc 2a 關閉目前文件，但不結束程式。
b .uno%3ASave 15 儲存目前文件。
21 SVT_HID_FILESAVE_SAVEWITHPASSWORD 4e 採用密碼保護的檔案，使用者必須在開啟檔案前輸入密碼。
19 SVT_HID_FILESAVE_FILETYPE 2a 選取儲存文件所需的檔案格式。
20 SVT_HID_FILESAVE_CREATEDIRECTORY 18 建立一個新目錄。
18 SVT_HID_FILESAVE_FILEURL 40 輸入此檔案的檔案名稱或路徑。您也可以輸入 URL
d .uno%3ASaveAs 60 將目前文件儲存在不同的位置，或者以不同的檔案名稱或檔案類型儲存。
20 SVT_HID_FILESAVE_CUSTOMIZEFILTER 42 允許您為某些類型的資料檔設定試算表儲存選項。
18 SVT_HID_FILESAVE_LEVELUP 5a 在目錄階層中上移一個目錄。長時間按下，以查看較高層級的目錄。
21 SVT_HID_FILESAVE_DEFAULTDIRECTORY 2a 顯示預設使用者目錄中的檔案。
d SID_SAVEASDOC 60 將目前文件儲存在不同的位置，或者以不同的檔案名稱或檔案類型儲存。
19 SVT_HID_FILESAVE_FILEVIEW 30 顯示您的個人目錄中的檔案和目錄。
1a SVT_HID_FILESAVE_SELECTION 8e 僅將 %PRODUCTNAME Draw 和 Impress 中的所選圖形物件匯出成另一種格式。如果未核取此方塊，則將匯出整個文件。
17 SVT_HID_FILESAVE_DOSAVE f 儲存檔案。
17 SVT_HID_FILESAVE_DIALOG 60 將目前文件儲存在不同的位置，或者以不同的檔案名稱或檔案類型儲存。
f .uno%3AExportTo 45 以不同的名稱和格式將目前文件儲存至指定的位置。
1c .uno%3ASetDocumentProperties 54 顯示目前檔案的特性，包括字數統計和檔案建立日期等統計值。
14 SFX2_HID_DOCINFODESC 27 包含有關文件的描述性資訊。
27 sfx2%3AEdit%3ATP_DOCINFODESC%3AED_TITLE 3c 輸入文件的標題。標題可以不同於檔案名稱。
32 sfx2%3AMultiLineEdit%3ATP_DOCINFODESC%3AED_COMMENT 27 輸入註解，以便於識別文件。
27 sfx2%3AEdit%3ATP_DOCINFODESC%3AED_THEMA 4b 輸入文件的主題。可以使用主題來分組內容相似的文件。
2a sfx2%3AEdit%3ATP_DOCINFODESC%3AED_KEYWORDS 7e 輸入要用以編排文件內容索引的文字。關鍵字必須以逗點分隔。關鍵字可包含空格字元或分號。
2e sfx2%3APushButton%3ATP_DOCINFODOC%3ABTN_DELETE 80 重設編輯時間為零、建立日期為目前的日期和時間、版本號碼為 1，並刪除修改日期和列印日期。
31 sfx2%3ACheckBox%3ATP_DOCINFODOC%3ACB_USE_USERDATA 9e 以檔案儲存使用者全名。您可以選擇 [%PRODUCTNAME] - [喜好設定][工具] - [選項] - [$[officename]] - [使用者資料]，以編輯名稱。
2a sfx2%3AEdit%3ATP_DOCINFODOC%3AED_FILE_NAME 15 顯示檔案名稱。
13 SFX2_HID_DOCINFODOC 2a 包含有關目前檔案的基本資訊。
1e SFX2_HID_CTRL_CUSTOMPROPERTIES a2 輸入您的自訂內容。您可變更名稱、類型以及每一列的內容。可以新增或移除列。項目將匯出為其他檔案格式的中介資料。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO2 a2 輸入您的自訂內容。您可變更名稱、類型以及每一列的內容。可以新增或移除列。項目將匯出為其他檔案格式的中介資料。
14 SFX2_HID_DOCINFOUSER 33 可讓您將自訂的資訊欄位指定給文件。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO1 a2 輸入您的自訂內容。您可變更名稱、類型以及每一列的內容。可以新增或移除列。項目將匯出為其他檔案格式的中介資料。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO3 a2 輸入您的自訂內容。您可變更名稱、類型以及每一列的內容。可以新增或移除列。項目將匯出為其他檔案格式的中介資料。
31 sfx2%3APushButton%3ATP_CUSTOMPROPERTIES%3ABTN_ADD 31 按一下可將新列增加至 [特性] 清單。
27 sfx2%3AEdit%3ATP_DOCINFOUSER%3AED_INFO4 a2 輸入您的自訂內容。您可變更名稱、類型以及每一列的內容。可以新增或移除列。項目將匯出為其他檔案格式的中介資料。
f SW_HID_DOC_STAT 24 顯示目前檔案的統計資料。
1e sc%3ATabPage%3ARID_SCPAGE_STAT 24 顯示目前檔案的統計資料。
27 sfx2%3AEdit%3ATP_DOCINFORELOAD%3AED_URL 29 輸入所要開啟檔案的 URL 地址。
33 sfx2%3ANumericField%3ATP_DOCINFORELOAD%3AED_FORWARD 48 輸入將瀏覽器重新導向不同檔案之前，要等待的秒數。
37 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_RELOADUPDATE 86 經過您於[秒]方塊中輸入的秒數之後，重新載入 HTML 頁面。若要觀察結果，請在瀏覽器中開啟此頁面。
2d sfx2%3AComboBox%3ATP_DOCINFORELOAD%3ACB_FRAME 5d 如果目前的 HTML 頁面使用訊框，選取您要載入檔案的目標訊框的名稱。
33 sfx2%3APushButton%3ATP_DOCINFORELOAD%3APB_BROWSEURL 3b 尋找要開啟的檔案，然後按一下[開啟舊檔]。
38 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_FORWARDUPDATE 4a 經過您於[秒]方塊中輸入的秒數之後，載入指定的頁面。
32 sfx2%3ANumericField%3ATP_DOCINFORELOAD%3AED_RELOAD 33 輸入重新載入頁面之前要等待的秒數。
37 sfx2%3ARadioButton%3ATP_DOCINFORELOAD%3ARB_NOAUTOUPDATE 27 使用者必須手動更新此頁面。
16 SFX2_HID_DOCINFORELOAD 39 設定用於 HTML 頁面的更新和重新導向選項。
27 SFX2_HID_SECURITYTAB_OPEN_FILE_READONLY 39 選取此選項僅允許以唯讀模式開啟此文件。
2d HID_DLG_PASSWORD_TO_OPEN_MODIFY_FILE_READONLY 39 選取此選項僅允許以唯讀模式開啟此文件。
30 HID_DLG_PASSWORD_TO_OPEN_MODIFY_PASSWORD_TO_OPEN 2a 鍵入密碼。密碼會區分大小寫。
2a HID_SECURITYTAB_CONFIRM_PASSWORD_TO_MODIFY 15 重新輸入密碼。
32 HID_DLG_PASSWORD_TO_OPEN_MODIFY_PASSWORD_TO_MODIFY 27 鍵入密碼。密碼區分大小寫。
38 HID_DLG_PASSWORD_TO_OPEN_MODIFY_CONFIRM_PASSWORD_TO_OPEN 15 重新輸入密碼。
23 SFX2_HID_SECURITYTAB_RECORD_CHANGES 62 選取此選項可啟用記錄變更功能。這與 [編輯] - [變更] - [記錄] 效果相同。
1f SFX2_HID_SECURITYTAB_PROTECTION c0 以密碼保護變更記錄狀態。若目前文件的變更記錄受到保護，則按鈕名稱為 [取消保護]。按一下 [取消保護]，並輸入正確的密碼，即可停用保護。
22 HID_SECURITYTAB_PASSWORD_TO_MODIFY 27 鍵入密碼。密碼區分大小寫。
28 HID_SECURITYTAB_CONFIRM_PASSWORD_TO_OPEN 15 重新輸入密碼。
3a HID_DLG_PASSWORD_TO_OPEN_MODIFY_CONFIRM_PASSWORD_TO_MODIFY 15 重新輸入密碼。
20 HID_SECURITYTAB_PASSWORD_TO_OPEN 2a 鍵入密碼。密碼會區分大小寫。
13 .uno%3ATemplateMenu 4b 可讓您管理與編輯範本，以及將目前的檔案儲存為範本。
16 SFX2_HID_ORGANIZE_EDIT 27 開啟選取的範本以進行編輯。
18 SFX2_HID_ORGANIZE_DELETE 18 刪除目前的選擇。
1f SFX2_HID_ORGANIZE_PRINTER_SETUP 2d 為所選文件變更印表機及其設定。
1c SFX2_HID_CTL_ORGANIZER_RIGHT 94 顯示可用的範本分類或開啟的 $[officename] 檔案。若要變更清單中的內容，請選取下方方塊中的 [範本] 或 [文件]。
1b SFX2_HID_CTL_ORGANIZER_LEFT 94 顯示可用的範本分類或開啟的 $[officename] 檔案。若要變更清單中的內容，請選取下方方塊中的 [範本] 或 [文件]。
10 .uno%3AOrganizer 59 開啟[管理範本]對話方塊，您可以在其中管理範本和定義預設範本。
36 sfx2%3APushButton%3ADLG_ORGANIZE%3ABTN_ADDRESSTEMPLATE 48 找到您要新增到文件清單的檔案，然後按一下 [開啟]。
18 SFX2_HID_ORGANIZE_RESCAN 1b 更新清單中的內容。
19 SFX2_HID_ORGANIZE_COPY_TO 18 匯出選取的範本。
15 SFX2_HID_ORGANIZE_NEW 1e 建立一個新的範本類。
21 SFX2_HID_ORGANIZE_STDTEMPLATE_DEL 51 選取 $[officename] 文件類型，以將預設範本重設為原來的範本。
2b sfx2%3AMenuButton%3ADLG_ORGANIZE%3ABTN_EDIT 36 包含管理、編輯範本和文件所需的指令。
17 SFX2_HID_ORGANIZE_PRINT 30 列印此檔案所用樣式的名稱和特性。
2c sfx2%3AListBox%3ADLG_ORGANIZE%3ALB_RIGHT_TYP 49 選取 [範本] 或 [文件]，以變更上方清單中顯示的內容。
21 SFX2_HID_ORGANIZE_STDTEMPLATE_ADD 5a 建立同一類型的新 $[officename] 文件時，使用所選範本作為預設範本。
2c sfx2%3APushButton%3ADLG_ORGANIZE%3ABTN_FILES 48 找到您要新增到文件清單的檔案，然後按一下 [開啟]。
1b SFX2_HID_ORGANIZE_COPY_FROM 8b 匯入其他範本。若要匯入範本，請選取清單中的範本資料夾，按一下 [指令] 按鈕，然後選取 [匯入範本]。
2b sfx2%3AListBox%3ADLG_ORGANIZE%3ALB_LEFT_TYP 49 選取 [範本] 或 [文件]，以變更上方清單中顯示的內容。
3a svtools%3AComboBox%3ADLG_ADDRESSBOOKSOURCE%3ACB_DATASOURCE 21 選取通訊錄的資料來源。
22 SVT_HID_ADDRTEMPL_FIELD_ASSIGNMENT 39 選取資料表格中對應於通訊錄條目的欄位。
35 svtools%3AComboBox%3ADLG_ADDRESSBOOKSOURCE%3ACB_TABLE 21 選取通訊錄的資料表格。
49 svtools%3APushButton%3ADLG_ADDRESSBOOKSOURCE%3APB_ADMINISTATE_DATASOURCES 3a 將新的資料來源加入 [通訊錄來源] 清單中。
18 .uno%3AAddressBookSource 30 編輯通訊錄的欄位指定和資料來源。
28 sfx2%3AEdit%3ADLG_DOC_TEMPLATE%3AED_NAME 18 輸入範本的名稱。
32 sfx2%3AListBox%3ADLG_DOC_TEMPLATE%3ALB_STYLESHEETS 1e 列出可用的範本分類。
2e sfx2%3AListBox%3ADLG_DOC_TEMPLATE%3ALB_SECTION 2d 選取一個分類，以儲存新的範本。
15 .uno%3ASaveAsTemplate 21 將目前文件儲存為範本。
32 sfx2%3APushButton%3ADLG_DOC_TEMPLATE%3ABT_ORGANIZE 55 開啟 [範本管理] 對話方塊，您可以在其中管理或建立新的範本。
2e sfx2%3APushButton%3ADLG_DOC_TEMPLATE%3ABT_EDIT 27 開啟選取的範本以進行編輯。
13 .uno%3AOpenTemplate 45 開啟對話方塊，您可以在其中選取範本以進行編輯。
20 .HelpID%3Avcl%3APrintDialog%3AOK 66 列印目前文件、選取內容或指定的頁面。您還可以為目前文件設定列印選項。
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A2 4a Reduces or enlarges the size of the printed formula by a specified factor.
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A1 44 Specifies that you do not want to further scale pages when printing.
2f .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ARowsBox 16 Select number of rows.
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A0 26 Specifies to print in original colors.
c HID_PRINTDLG 66 列印目前文件、選取內容或指定的頁面。您還可以為目前文件設定列印選項。
37 .HelpID%3Avcl%3APrintDialog%3APageContentType%3AListBox 35 Select which parts of the document should be printed.
2e .HelpID%3Avcl%3APrintDialog%3APageRange%3AEdit c3 To print a range of pages, use a format like 3-6. To print single pages, use a format like 7;9;11. You can print a combination of page ranges and single pages, by using a format like 3-6;8;10;12.
35 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APagesPerSheet 28 Print multiple pages per sheet of paper.
3c .HelpID%3Avcl%3APrintDialog%3APrintPaperFromSetup%3ACheckBox 86 For printers with multiple trays this option specifies whether the paper tray used is specified by the system settings of the printer.
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A0 3b Prints the formula without adjusting the current font size.
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A2 33 僅列印目前文件中的選定區域或物件。
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A0 2c Specify how to scale slides in the printout.
3b .HelpID%3Avcl%3APrintDialog%3APrintFormat%3ARadioButton%3A1 3c Adjusts the formula to the page format used in the printout.
38 .HelpID%3Avcl%3APrintDialog%3AIsPrintDateTime%3ACheckBox 35 Specifies whether to print the current date and time.
2f .HelpID%3Avcl%3APrintDialog%3ABorder%3ACheckBox 3a Applies a thin border to the formula area in the printout.
36 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ASheetMarginBox 37 Select margin between the printed pages and paper edge.
2e .HelpID%3Avcl%3APrintDialog%3AJobPage%3ACopies 1e 輸入您要列印的份數。
36 .HelpID%3Avcl%3APrintDialog%3APrintProspect%3ACheckBox 44 Select the Brochure option to print the document in brochure format.
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A0 15 列印整份文件。
35 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APageMarginBox 3e Select margin between individual pages on each sheet of paper.
38 .HelpID%3Avcl%3APrintDialog%3APrintEmptyPages%3ACheckBox 182 If this option is enabled automatically inserted blank pages are printed. This is best if you are printing double-sided. For example, in a book, a "chapter" paragraph style has been set to always start with an odd numbered page. If the previous chapter ends on an odd page, %PRODUCTNAME inserts an even numbered blank page. This option controls whether to print that even numbered page.
31 .HelpID%3Avcl%3APrintDialog%3AOptPage%3AToReverse 26 Check to print pages in reverse order.
30 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AOrderBox 2e Select order in which pages are to be printed.
31 .HelpID%3Avcl%3APrintDialog%3ATitleRow%3ACheckBox 53 Specifies whether you want the name of the document to be included in the printout.
38 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3APagesPerSheetBox 32 Select how many pages to print per sheet of paper.
38 .HelpID%3Avcl%3APrintDialog%3APrintHiddenText%3ACheckBox 3a Enable this option to print text that is marked as hidden.
3d .HelpID%3Avcl%3APrintDialog%3APrintTextPlaceholder%3ACheckBox 78 Enable this option to print text placeholders. Disable this option to leave the text placeholders blank in the printout.
3b .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AListBox 2b "Specify where to print comments (if any)."
37 .HelpID%3Avcl%3APrintDialog%3APrintScale%3ANumericField 4a Reduces or enlarges the size of the printed formula by a specified factor.
3d .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AFixedText 29 Specify where to print comments (if any).
32 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AColumnsBox 19 Select number of columns.
3c .HelpID%3Avcl%3APrintDialog%3APrintPageBackground%3ACheckBox 7b Specifies whether to print colors and objects that are inserted to the background of the page (Format - Page - Background).
38 .HelpID%3Avcl%3APrintDialog%3APrintProspectRTL%3AListBox 54 針對小手冊列印，您可以選取由左至右或由右至左的頁面順序。
3b .HelpID%3Avcl%3APrintDialog%3APrintLeftRightPages%3AListBox 2d Specify which pages to include in the output.
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A2 2d Specifies to print colors as black and white.
2e .HelpID%3Avcl%3APrintDialog%3AOptPage%3AToFile 6f 列印至檔案而不列印至印表機。啟用此選項時，[列印] 按鈕會更名為 [列印成檔案...]
3a .HelpID%3Avcl%3APrintDialog%3ASlidesPerPageOrder%3AListBox 32 Specify how to arrange slides on the printed page.
32 .HelpID%3Avcl%3APrintDialog%3AJobPage%3AProperties 5a 開啟印表機特性對話方塊。印表機特性會依您選取的印表機而不同。
31 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3ABorderBox 28 Check to draw a border around each page.
32 .HelpID%3Avcl%3APrintDialog%3AJobPage%3ADetailsBtn 37 Show/Hide detailed information of the selected printer.
35 .HelpID%3Avcl%3APrintDialog%3ASlidesPerPage%3AListBox 29 Select how many slides to print per page.
32 .HelpID%3Avcl%3APrintDialog%3AOptPage%3ASingleJobs 68 Check to not rely on the printer to create collated copies but create a print job for each copy instead.
3c .HelpID%3Avcl%3APrintDialog%3APrintProspectInclude%3AListBox 2a Select which pages of a brochure to print.
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A2 83 Specifies whether to scale down objects that are beyond the margins of the current printer so they fit on the paper in the printer.
36 .HelpID%3Avcl%3APrintDialog%3APrintControls%3ACheckBox 4b Specifies whether the form control fields of the text document are printed.
36 .HelpID%3Avcl%3APrintDialog%3ANUpPage%3AOrientationBox 24 Select the orientation of the paper.
3b .HelpID%3Avcl%3APrintDialog%3APageOptions%3ARadioButton%3A3 a8 Specifies that pages are to be printed in tiled format. If the pages or slides are smaller than the paper, several pages or slides will be printed on one page of paper.
c .uno%3APrint 66 列印目前文件、選取內容或指定的頁面。您還可以為目前文件設定列印選項。
34 .HelpID%3Avcl%3APrintDialog%3AIsPrintName%3ACheckBox 37 Specifies whether to print the page name of a document.
40 .HelpID%3Avcl%3APrintDialog%3APrintPicturesAndObjects%3ACheckBox 5d Specifies whether the graphics and drawings or OLE objects of your text document are printed.
34 .HelpID%3Avcl%3APrintDialog%3AFormulaText%3ACheckBox 5f Specifies whether to include the contents of the Commands window at the bottom of the printout.
33 .HelpID%3Avcl%3APrintDialog%3AJobPage%3APrinterList f9 The list box shows the installed printers. Click the printer to use for the current print job. Click the Printer details button to see some information about the selected printer. Click the Properties button to change some of the printer properties.
38 .HelpID%3Avcl%3APrintDialog%3APrintBlackFonts%3ACheckBox 30 Specifies whether to always print text in black.
2f .HelpID%3Avcl%3APrintDialog%3AJobPage%3ACollate 1e 保留原始文件的頁序。
37 .HelpID%3Avcl%3APrintDialog%3AQuality%3ARadioButton%3A1 27 Specifies to print colors as grayscale.
36 .HelpID%3Avcl%3APrintDialog%3AIsPrintHidden%3ACheckBox 3f Specifies whether to print the pages that are currently hidden.
3c .HelpID%3Avcl%3APrintDialog%3AIsIncludeEmptyPages%3ACheckBox 52 If checked empty pages that have no cell contents or draw objects are not printed.
3c .HelpID%3Avcl%3APrintDialog%3APrintContent%3ARadioButton%3A1 31 僅列印您在 [頁] 方塊中指定的頁面。
42 svtools%3APushButton%3ADLG_SVT_PRNDLG_PRNSETUPDLG%3ABTN_PROPERTIES 45 依目前文件的類型，變更作業系統中印表機的設定。
39 svtools%3AListBox%3ADLG_SVT_PRNDLG_PRNSETUPDLG%3ALB_NAMES 72 列出作業系統上已安裝的印表機。若要變更預設印表機，請從清單中選取印表機名稱。
32 svtools%3AModalDialog%3ADLG_SVT_PRNDLG_PRNSETUPDLG 2a 為目前的文件選取預設印表機。
13 .uno%3APrinterSetup 2a 為目前的文件選取預設印表機。
17 .uno%3ASendMailDocAsOOo 77 以預設電子郵件程式開啟新的視窗，並以目前的文件當作附件。使用 OpenDocument 檔案格式。
11 .uno%3ASendToMenu 39 將目前文件的副本傳送至不同的應用程式。
16 .uno%3ASendMailDocAsMS 74 以預設電子郵件程式開啟新的視窗，並以目前的文件當作附件。使用 Microsoft 檔案格式。
f .uno%3ASendMail 84 會在您的預設電子郵件程式中開啟新視窗，並包含目前的文件作為附件。將會使用目前的檔案格式。
24 SW_HID_SEND_MASTER_CTRL_EDIT_FILEURL 51 選取要用來將來源文件分隔為子文件的段落樣式或大綱層次。
19 SVT_HID_FILESAVE_TEMPLATE 51 選取要用來將來源文件分隔為子文件的段落樣式或大綱層次。
28 SW_HID_SEND_MASTER_CTRL_CONTROL_FILEVIEW 51 選取要用來將來源文件分隔為子文件的段落樣式或大綱層次。
28 SW_HID_SEND_MASTER_CTRL_LISTBOX_TEMPLATE 51 選取要用來將來源文件分隔為子文件的段落樣式或大綱層次。
13 .uno%3ANewGlobalDoc 92 從目前的 Writer 文件建立主控文件。來源文件中每個採用段落樣式或大綱層次之處，都會建立一個新的子文件。
b .uno%3AQuit 47 Closes all $[officename] programs and prompts you to save your changes.
e .uno%3ASaveAll 30 儲存所有修改過的 $[officename] 文件。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_OPEN 2a 在唯讀視窗中開啟選取的版本。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_VIEW 24 顯示所選版本的全部註解。
31 sfx2%3AMultiLineEdit%3ADLG_COMMENTS%3AME_VERSIONS 7f 儲存新版本時在此處輸入註解。如果您是按一下 [顯示] 來開啟此對話方塊的，則無法編輯註釋。
2a sfx2%3APushButton%3ADLG_VERSIONS%3APB_SAVE 9a 將文件的目前狀態儲存為新版本。如果需要，您還可以在儲存新版本之前，於 [插入版本註解] 對話方塊中輸入註解。
2f sfx2%3ACheckBox%3ADLG_VERSIONS%3ACB_SAVEONCLOSE 62 如果您已對文件進行了變更，關閉文件時，$[officename] 會自動儲存新版本。
14 .uno%3AVersionDialog 6f 在同一檔案中儲存並管理目前文件的多個版本。您還可以開啟、刪除和比較舊版本。
2d sfx2%3APushButton%3ADLG_VERSIONS%3APB_COMPARE 2a 比較在每個版本中所做的變更。
21 sfx2%3AModalDialog%3ADLG_VERSIONS 66 列出目前文件的現有版本、這些版本的建立日期和時間、作者以及相關註解。
2c sfx2%3APushButton%3ADLG_VERSIONS%3APB_DELETE 18 刪除選取的版本。
15 .uno%3ARecentFileList 5a 列出最近開啟的檔案。若要開啟該清單中的檔案，請按一下其名稱。
b .uno%3AUndo a0 復原您所鍵入的最後一個指令或最後一個項目。若要選取要復原的指令，請按一下「標準」列上的 [復原] 圖示旁的箭頭。
14 SVX_HID_IMAPDLG_UNDO a0 復原您所鍵入的最後一個指令或最後一個項目。若要選取要復原的指令，請按一下「標準」列上的 [復原] 圖示旁的箭頭。
b .uno%3ARedo a9 撤消最後一個 [還原] 指令的動作。若要選取您要撤消的 [還原] 步驟，請在 [標準] 列上按一下 [恢復撤消指令] 圖示旁邊的箭頭。
14 SVX_HID_IMAPDLG_REDO a9 撤消最後一個 [還原] 指令的動作。若要選取您要撤消的 [還原] 步驟，請在 [標準] 列上按一下 [恢復撤消指令] 圖示旁邊的箭頭。
13 .uno%3ARepeatAction 44 重複上一個指令。在 Writer 與 Calc 中可使用此指令。
d .uno%3ARepeat 44 重複上一個指令。在 Writer 與 Calc 中可使用此指令。
a .uno%3ACut 30 移除選擇內容並將其複製到剪貼簿。
b .uno%3ACopy 27 將所選內容複製到剪貼簿上。
c .uno%3APaste 54 在游標位置插入剪貼簿的內容，並取代任何選定的文字或物件。
17 SC_HID_SC_REPLCELLSWARN 54 在游標位置插入剪貼簿的內容，並取代任何選定的文字或物件。
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_ADD 0 
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_NOOP 0 
30 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSATTRS 0 
30 cui%3AListBox%3AMD_PASTE_OBJECT%3ALB_INSERT_LIST 36 選取貼上剪貼簿內容時所要使用的格式。
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_SKIP_EMPTY 0 
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_SUB 0 
33 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSFORMULAS 0 
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_DOWN 0 
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_MUL 0 
30 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSNOTES 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSSTRINGS 0 
2e sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSALL 0 
31 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_TRANSPOSE 0 
11 CUI_HID_PASTE_DLG 21 顯示剪貼簿內容的來源。
31 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_OP_DIV 0 
2c sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_LINK 0 
33 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSDATETIME 0 
13 .uno%3APasteSpecial 51 將剪貼簿上的內容以您可以指定的格式插入到目前的檔案中。
32 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_NONE 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSNUMBERS 0 
33 sc%3ARadioButton%3ARID_SCDLG_INSCONT%3ABTN_MV_RIGHT 0 
32 sc%3ACheckBox%3ARID_SCDLG_INSCONT%3ABTN_INSOBJECTS 0 
15 .uno%3AInsertContents 0 
15 .uno%3APasteClipboard 51 將剪貼簿上的內容以您可以指定的格式插入到目前的檔案中。
10 .uno%3ASelectAll 3c 選取目前檔案、框架或文字物件的整個內容。
d .uno%3ASelect 3c 選取目前檔案、框架或文字物件的整個內容。
34 svx%3AListBox%3ARID_SVXDLG_SEARCH%3ALB_CALC_SEARCHIN 7c 搜尋您在公式與固定 (非計算的) 數值中所指定的字元。例如，您可以搜尋包含「SUM」的公式。
36 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_REPLACE_ALL 3c 取代您想要取代的所有文字或格式之出現處。
11 .uno%3ADownSearch 2d 按一下以向下搜尋下一個出現處。
34 svx%3ARadioButton%3ARID_SVXDLG_SEARCH%3ARB_CALC_ROWS 1e 從左到右搜尋所有列。
2f svx%3AMoreButton%3ARID_SVXDLG_SEARCH%3ABTN_MORE 5d 顯示較多或較少的搜尋選項。再按一下此按鈕可隱藏擴充的搜尋選項。
33 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_NOFORMAT 7a 按一下 [搜尋內容] 或 [取代成] 方塊，然後按一下此按鈕即可移除以格式為基礎的搜尋條件。
41 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_JAP_MATCH_FULL_HALF_WIDTH 24 區分半形與全形字元形式。
2e svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_REGEXP 2a 可讓您在搜尋中使用萬用字元。
f .uno%3AFindText 52 輸入要在目前文件中搜尋的文字。按 Enter 鍵開始搜尋該文字。
40 cui%3APushButton%3ARID_SVXDLG_SEARCHFORM%3APB_SOUNDSLIKESETTINGS 3c 為類似日文中所使用的表示法設定搜尋選項。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_SELECTIONS 27 僅搜尋選取的文字或儲存格。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_MATCH_CASE 18 區分大小寫字元。
33 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_WHOLE_WORDS 3c 搜尋完全相同於搜尋文字的整個字或儲存格。
2e svx%3AListBox%3ARID_SVXDLG_SEARCH%3ALB_REPLACE 5a 輸入替代文字，或者從清單中選取一個最近輸入的替代文字或樣式。
17 .uno%3ASearchProperties 33 搜尋或代替目前文件中的文字或格式。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_ALL_SHEETS 36 在目前的試算表檔案中搜尋所有試算表。
39 cui%3ACheckBox%3ARID_SVXDLG_SEARCHFORM%3ACB_HALFFULLFORMS 24 區分半形與全形字元形式。
2f svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_LAYOUTS ce 搜尋使用您指定之樣式格式化的文字。選取此核取方塊，然後從 [搜尋內容] 清單中選取一個樣式。若要指定取代樣式，請從 [取代成] 清單中選取一個樣式。
32 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_REPLACE 48 代替搜尋到的所選文字或格式，然後搜尋下一個實例。
13 .uno%3ASearchDialog 33 搜尋或代替目前文件中的文字或格式。
37 svx%3ARadioButton%3ARID_SVXDLG_SEARCH%3ARB_CALC_COLUMNS 1e 從上到下搜尋所有欄。
28 svx_ComboBox_RID_SVXDLG_SEARCH_ED_SEARCH 54 輸入要搜尋的文字，或者從清單中選取一個先前搜尋過的項目。
2d SVX%3ALISTBOX%3ARID_SVXDLG_SEARCH%3ALB_SEARCH 54 輸入要搜尋的文字，或者從清單中選取一個先前搜尋過的項目。
31 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_SEARCH 4b 搜尋並選取您在文件中所搜尋文字或格式的下一個實例。
35 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_SEARCH_ALL 89 Finds and selects all instances of the text or the format that you are searching for in the document (only in Writer and Calc documents).
37 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_JAP_SOUNDS_LIKE 8f 可讓您為類似日文中所使用的表示法指定搜尋選項。選取此核取方塊，然後按一下 ... 按鈕來指定搜尋選項。
2f svx%3AComboBox%3ARID_SVXDLG_SEARCH%3AED_REPLACE 5a 輸入替代文字，或者從清單中選取一個最近輸入的替代文字或樣式。
2e svx%3AComboBox%3ARID_SVXDLG_SEARCH%3AED_SEARCH 54 輸入要搜尋的文字，或者從清單中選取一個先前搜尋過的項目。
f .uno%3AUpSearch 2d 按一下以向上搜尋下一個出現處。
35 svx%3APushButton%3ARID_SVXDLG_SEARCH%3APB_JAP_OPTIONS 3c 為類似日文中所使用的表示法設定搜尋選項。
39 cui%3ACheckBox%3ARID_SVXDLG_SEARCHFORM%3ACB_SOUNDSLIKECJK 8f 可讓您為類似日文中所使用的表示法指定搜尋選項。選取此核取方塊，然後按一下 ... 按鈕來指定搜尋選項。
31 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_BACKWARDS 57 從目前游標所在位置開始搜尋，搜尋完畢後返回檔案的起始位置。
1d CUI_HID_SEARCH_APPROXSETTINGS 83 尋找與 [搜尋內容] 文字相似的字詞。選取此核取方塊，然後按一下 [...] 按鈕，以定義相似字選項。
3b cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_OTHER 30 輸入搜尋字詞中可以交換的字元數。
37 cui%3ACheckBox%3ARID_SVXDLG_SEARCHSIMILARITY%3ACB_RELAX 3c 搜尋任何與相似字搜尋設定組合相符的字詞。
32 svx%3ACheckBox%3ARID_SVXDLG_SEARCH%3ACB_SIMILARITY 83 尋找與 [搜尋內容] 文字相似的字詞。選取此核取方塊，然後按一下 [...] 按鈕，以定義相似字選項。
3d cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_SHORTER 63 輸入單一字詞在搜尋字詞中的字元數下限；該字詞的字元數不可小於此值。
3c cui%3ANumericField%3ARID_SVXDLG_SEARCHSIMILARITY%3ANF_LONGER 63 輸入單一字詞在搜尋字詞中的字元數上限；該字詞的字元數不可大於此值。
34 svx%3APushButton%3ARID_SVXDLG_SEARCH%3APB_SIMILARITY 21 設定相似字搜尋的選項。
15 CUI_HID_SEARCH_APPROX 83 尋找與 [搜尋內容] 文字相似的字詞。選取此核取方塊，然後按一下 [...] 按鈕，以定義相似字選項。
1b CUI_HID_SEARCHATTR_CTL_ATTR 1b 選取要搜尋的特性。
12 CUI_HID_SEARCHATTR 1b 選取要搜尋的特性。
34 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_ATTRIBUTE df 選擇要搜尋的文字特性。例如，如果搜尋 [字型] 特性，則會找到未使用預設字型的文字實例。會找到擁有直接編碼字型特性的所有文字，以及切換字型特性的所有文字。
31 svx%3APushButton%3ARID_SVXDLG_SEARCH%3ABTN_FORMAT 5d 尋找特定文字格式功能，例如字型類型、字型效果及換行和分頁特性。
11 SW_HID_NAVI_TBX23 39 在瀏覽清單中，將所選條目下移一個位置。
11 SW_HID_NAVI_TBX20 36 在主控文件中插入檔案、索引或新文件。
1c SW_HID_GLBLTREE_INS_NEW_FILE 21 建立並插入新的子文件。
18 SW_HID_GLBLTREE_UPD_LINK 15 更新所有連結。
11 SW_HID_NAVI_TBX18 a5 編輯在瀏覽清單中選取的元件之內容。如果選取了檔案，將開啟此檔案以進行編輯。如果選取了索引，將開啟索引對話方塊。
11 SW_HID_NAVI_TBX21 8d 在主控文件中儲存連結的檔案之內容的副本。這樣可確保在無法存取連結的檔案時，仍可使用目前的內容。
16 SW_HID_GLBLTREE_INSERT 36 在主控文件中插入檔案、索引或新文件。
17 SW_HID_GLBLTREE_UPD_SEL 18 更新選擇的內容。
16 SW_HID_GLBLTREE_UPDATE 27 按一下並選擇要更新的內容。
1e SW_HID_NAVIGATOR_GLOB_TREELIST 90 瀏覽將列出主控文件的主程式元件。如果將滑鼠指標置於清單中子文件的名稱上，會顯示子文件的完整路徑。
13 SW_HID_GLBLTREE_DEL 27 從瀏覽清單中刪除所選條目。
11 SW_HID_NAVI_TBX22 39 在瀏覽清單中，將所選條目上移一個位置。
19 SW_HID_GLBLTREE_EDIT_LINK 24 變更所選檔案的連結屬性。
16 SW_HID_GLBLTREEUPD_ALL 15 更新所有內容。
18 SW_HID_GLBLTREE_INS_FILE 36 在主控文件中插入一個或多個現有檔案。
11 SW_HID_NAVI_TBX19 27 按一下並選擇要更新的內容。
18 SW_HID_GLBLTREE_INS_TEXT 8b 在主控文件中插入一個新段落，您可以在其中輸入文字。您無法在 [助手] 中現有的文字項目旁插入文字。
17 SW_HID_GLBLTREE_UPD_IDX 15 更新所有索引。
17 SW_HID_GLBLTREE_INS_IDX 30 在主控文件中插入索引或內容目錄。
14 SW_HID_GLBLTREE_EDIT a5 編輯在瀏覽清單中選取的元件之內容。如果選取了檔案，將開啟此檔案以進行編輯。如果選取了索引，將開啟索引對話方塊。
33 cui%3ARadioButton%3AMD_UPDATE_BASELINKS%3ARB_MANUAL 47 只有在您按一下 「更新」 按鈕時，才會更新此連結。
36 cui%3ARadioButton%3AMD_UPDATE_BASELINKS%3ARB_AUTOMATIC a5 開啟檔案時自動更新連結的內容。來源檔案中所做的所有變更還會顯示在包含連結的檔案中。僅可手動更新連結的圖形檔案，
15 CUI_HID_LINKDLG_TABLB 7b 連按兩下清單中的連結以開啟檔案對話方塊，您可以在此對話方塊中選取此連結的其他物件。
11 .uno%3ALinkDialog ab 您可以編輯目前文件中每個連結的屬性，其中包括來源檔案的路徑。如果目前文件中不包含其他檔案的連結，則無法使用此指令。
36 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_BREAK_LINK 72 中斷來源檔案和目前文件之間的連結。來源檔案最近更新的內容會保留在目前文件中。
36 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_UPDATE_NOW 51 更新選取的連結，以在目前文件中顯示連結檔案的最新版本。
12 .uno%3AManageLinks ab 您可以編輯目前文件中每個連結的屬性，其中包括來源檔案的路徑。如果目前文件中不包含其他檔案的連結，則無法使用此指令。
39 cui%3APushButton%3AMD_UPDATE_BASELINKS%3APB_CHANGE_SOURCE 24 變更所選連結的來源檔案。
10 .uno%3AEditLinks ab 您可以編輯目前文件中每個連結的屬性，其中包括來源檔案的路徑。如果目前文件中不包含其他檔案的連結，則無法使用此指令。
14 .uno%3APlugInsActive 11a 允許您編輯檔案中的外掛程式。選擇此指令以啟用或停用此功能。啟用時，此指令旁會出現核取標記，而您會找到指令以在其右鍵功能表中編輯外掛程式。停用時，您會找到指令以在其右鍵功能表中控制外掛程式。
13 .uno%3AOriginalSize 27 將物件大小調整為原始大小。
12 .uno%3AObjectMenue 54 可讓您編輯透過 [插入] - [物件] 指令插入到檔案中的所選物件。
38 cui%3APushButton%3AMD_INSERT_OBJECT_IFRAME%3ABT_FILEOPEN 57 找到要在選取的浮動訊框中顯示的檔案，然後按一下 [開啟舊檔]。
2d cui%3AEdit%3AMD_INSERT_OBJECT_IFRAME%3AED_URL 82 輸入您要在浮動訊框中顯示的檔案路徑與名稱。您也可以按一下 [...] 按鈕，尋找您要顯示的檔案。
41 cui%3ACheckBox%3AMD_INSERT_OBJECT_IFRAME%3ACB_MARGINHEIGHTDEFAULT 15 套用預設間隔。
3e cui%3ANumericField%3AMD_INSERT_OBJECT_IFRAME%3ANM_MARGINHEIGHT 8a 輸入浮動訊框上、下邊緣與訊框內容之間要留出的垂直間隔。浮動訊框內外兩個文件都必須是 HTML 文件。
3d cui%3ANumericField%3AMD_INSERT_OBJECT_IFRAME%3ANM_MARGINWIDTH 8a 輸入浮動訊框左、右邊緣與訊框內容之間要留出的水平間隔。浮動訊框內外兩個文件都必須是 HTML 文件。
33 cui%3AEdit%3AMD_INSERT_OBJECT_IFRAME%3AED_FRAMENAME 70 輸入此浮動訊框的名稱。該名稱不可包含空格、特殊字元，且不能以底線 ( _ ) 開頭。
3d cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_FRMBORDER_ON 1e 顯示浮動訊框的邊框。
3c cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGON 21 顯示浮動訊框的捲動軸。
3e cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_FRMBORDER_OFF 1e 隱藏浮動訊框的邊框。
3d cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGOFF 21 隱藏浮動訊框的捲動軸。
3e cui%3ARadioButton%3AMD_INSERT_OBJECT_IFRAME%3ARB_SCROLLINGAUTO 5a 如果目前使用中的浮動訊框在需要時可以有捲動軸，請標記此選項。
15 SVX_HID_IMAPDLG_MACRO 45 可讓您指定按一下瀏覽器中所選熱點時執行的巨集。
1a SVX_HID_IMAPDLG_POLYINSERT 42 於您在熱點輪廓上所按的位置處加入一個標號點。
16 SVX_HID_IMAPDLG_ACTIVE 48 停用或啟用所選熱點的超連結。停用的熱點是透明的。
18 SVX_HID_IMAPDLG_POLYMOVE 30 可讓您移動所選熱點的個別標號點。
18 SVX_HID_IMAPDLG_FREEPOLY 115 繪製一個基於自由形多邊形的熱點。按一下此圖示並移至要繪製熱點的位置。繪製一條自由形線條，然後放開滑鼠按鍵以封閉該形狀。之後，您可以輸入熱點的位址和文字，然後選取要在其中開啟 URL 的[訊框]。
16 SVX_HID_IMAPDLG_CIRCLE 94 在圖形中，於拖曳處繪製橢圓熱點。之後，您可以輸入熱點的位址和文字，然後選取要在其中開啟 URL 的[訊框]。
15 SVX_HID_IMAPDLG_APPLY 2a 套用您對影像映射所做的變更。
2a svx%3AComboBox%3ARID_SVXDLG_IMAP%3ACBB_URL 3a 輸入按一下所選熱點時要開啟的檔案之 URL。
1a SVX_HID_IMAPDLG_POLYDELETE 1b 刪除選取的標號點。
14 SVX_HID_IMAPDLG_OPEN 5c 以 MAP-CERN、MAP-NCSA 或 SIP StarView ImageMap 檔案格式載入現有的影像映射。
18 SVX_HID_IMAPDLG_PROPERTY 27 允許您定義所選熱點的屬性。
14 SVX_HID_IMAPDLG_POLY 19a 在圖形中繪製多邊形熱點。按一下此圖示，在圖形中拖曳，然後按一下以定義多邊形的一條邊。移至要放置下一條邊的端點的位置，然後按一下滑鼠。重複此操作，直到多邊形的所有邊都繪製完畢。完成後，按兩下滑鼠以封閉多邊形。之後，您可以輸入熱點的位址和文字，然後選取要在其中開啟 URL 的 [訊框]。
16 SVX_HID_IMAPDLG_SELECT 2a 選取影像映射中的熱點來編輯。
15 .uno%3AImageMapDialog 80 讓您將 URL 連接到圖形或圖形群組上稱為作用區的特定區域。影像映射是一或多個作用區的群組。
27 svx%3AEdit%3ARID_SVXDLG_IMAP%3AEDT_TEXT 42 輸入滑鼠停留在瀏覽器中熱點上時要顯示的文字。
18 SVX_HID_IMAPDLG_POLYEDIT 3f 可讓您透過編輯標號點，變更所選熱點的形狀。
14 SVX_HID_IMAPDLG_RECT 95 在圖形中，於拖曳處繪製矩形熱點。之後，您可以輸入熱點的位址和文字，然後選取要在其中開啟 URL 的 [訊框]。
18 SVX_HID_IMAPDLG_GRAPHWND 0 
16 SVX_HID_IMAPDLG_SAVEAS 53 以 MAP-CERN、MAP-NCSA 或 SIP StarView ImageMap 檔案格式儲存影像映射。
26 SVX%3AMODALDIALOG%3ARID_SVXDLG_IMAPURL 1e 列出所選熱點的特性。
34 SVX_MULTILINEEDIT_RID_SVXDLG_IMAPURL_EDT_DESCRIPTION 18 輸入熱點的描述。
2a SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_NAME 18 輸入影像的名稱。
34 SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_URLDESCRIPTION 42 輸入滑鼠停留在瀏覽器中熱點上時要顯示的文字。
29 SVX%3AEDIT%3ARID_SVXDLG_IMAPURL%3AEDT_URL 3a 輸入按一下所選熱點時要開啟之檔案的 URL。
31 SVX%3ACOMBOBOX%3ARID_SVXDLG_IMAPURL%3ACBB_TARGETS 83 輸入要在其中開啟 URL 的目標訊框名稱。也可以從清單中選取所有瀏覽器皆可辨識的標準訊框名稱。
12 .uno%3AChangesMenu 33 列出可用於在檔案中追蹤變更的指令。
16 .uno%3ATraceChangeMode 3c 依作者和日期追蹤目前文件中的每一個變更。
13 .uno%3ATrackChanges 3c 依作者和日期追蹤目前文件中的每一個變更。
12 SC_HID_CHG_PROTECT 69 防止使用者關閉記錄變更功能；或使用者必須輸入密碼，才會接受或拒絕變更。
1d .uno%3AProtectTraceChangeMode 69 防止使用者關閉記錄變更功能；或使用者必須輸入密碼，才會接受或拒絕變更。
12 .uno%3AShowChanges 21 顯示或隱藏記錄的變更。
19 .uno%3AShowTrackedChanges 21 顯示或隱藏記錄的變更。
16 CUI_HID_REDLINING_EDIT 21 輸入已記錄變更的注釋。
14 .uno%3ACommentChange 21 輸入已記錄變更的注釋。
1c .uno%3ACommentChangeTracking 21 輸入已記錄變更的注釋。
1b .uno%3AAcceptTrackedChanges 24 接受或拒絕已記錄的變更。
14 .uno%3AAcceptChanges 24 接受或拒絕已記錄的變更。
19 SC_HID_SC_CHANGES_COMMENT 21 編輯對所選變更的註解。
13 SW_HID_SORT_COMMENT 2a 依附加到變更的註解排序清單。
20 SVX_HID_REDLINING_VIEW_PB_ACCEPT 48 接受選取的變更，並在文件中移除該變更的突出顯示。
15 SC_HID_SC_SORT_AUTHOR 21 列出進行變更的使用者。
1e SVX_HID_REDLINING_VIEW_DG_VIEW 9c 列出文件中已記錄的變更。當您選取清單中的項目時，變更在文件中會呈反白顯示。若要排序清單，請按一下欄標題。
14 SC_HID_SORT_POSITION 5a 依文件中所做變更的位置以向下順序排序清單。這是預設排序方法。
10 SW_HID_SORT_DATE 21 依日期和時間排序清單。
1e SVX_HID_REDLINING_VIEW_PB_UNDO 21 編輯對所選變更的註解。
12 SW_HID_SORT_ACTION 1e 依變更類型排序清單。
13 SW_HID_EDIT_COMMENT 21 編輯對所選變更的註解。
23 SVX_HID_REDLINING_VIEW_PB_ACCEPTALL 4b 接受所有的變更，並在文件中移除這些變更的突出顯示。
13 SC_HID_SC_SORT_DATE 27 列出進行變更的日期和時間。
1b SVX_HID_REDLINING_VIEW_PAGE 1e 接受或拒絕個別變更。
19 .uno%3AAcceptTracedChange 48 接受選取的變更，並在文件中移除該變更的突出顯示。
15 SC_HID_SC_SORT_ACTION 24 列出在文件中所做的變更。
12 SW_HID_SORT_AUTHOR 18 依作者排序清單。
23 SVX_HID_REDLINING_VIEW_PB_REJECTALL 4b 拒絕所有的變更，並在文件中移除這些變更的突出顯示。
19 .uno%3ARejectTracedChange 48 拒絕選取的變更，並在文件中移除該變更的突出顯示。
20 SVX_HID_REDLINING_VIEW_PB_REJECT 48 拒絕選取的變更，並在文件中移除該變更的突出顯示。
16 SC_HID_SC_SORT_COMMENT 21 列出附加到變更的註解。
21 SVX_HID_REDLINING_FILTER_IB_CLOCK 36 在相應的方塊中輸入目前的日期和時間。
32 sc%3AImageButton%3ARID_SCDLG_SIMPLEREF%3ARB_ASSIGN 33 依輸入的關鍵字來篩選對變更的註解。
21 SVX_HID_REDLINING_FILTER_LB_AUTOR 3c 依照從清單中選取的作者名稱篩選變更清單。
22 SVX_HID_REDLINING_FILTER_IB_CLOCK2 36 在相應的方塊中輸入目前的日期和時間。
21 SVX_HID_REDLINING_FILTER_DF_DATE2 30 依指定的日期和時間篩選變更清單。
23 SVX_HID_REDLINING_FILTER_CB_COMMENT 33 依輸入的關鍵字來篩選對變更的註解。
21 SVX_HID_REDLINING_FILTER_ED_RANGE 30 選取要做為篩選之用的儲存格範圍。
21 SVX_HID_REDLINING_FILTER_TF_DATE2 30 依指定的日期和時間篩選變更清單。
21 SVX_HID_REDLINING_FILTER_CB_RANGE 30 選取要做為篩選之用的儲存格範圍。
20 SVX_HID_REDLINING_FILTER_DF_DATE 30 依指定的日期和時間篩選變更清單。
20 SVX_HID_REDLINING_FILTER_CB_DATE 30 依指定的日期和時間篩選變更清單。
22 SVX_HID_REDLINING_FILTER_CB_ACTION 33 依輸入的關鍵字來篩選對變更的註解。
20 SVX_HID_REDLINING_FILTER_BTN_REF 30 選取要做為篩選之用的儲存格範圍。
23 SVX_HID_REDLINING_FILTER_ED_COMMENT 33 依輸入的關鍵字來篩選對變更的註解。
3a sc%3AImageButton%3ARID_SCDLG_HIGHLIGHT_CHANGES%3ARB_ASSIGN 33 依輸入的關鍵字來篩選對變更的註解。
22 SVX_HID_REDLINING_FILTER_LB_ACTION 33 依輸入的關鍵字來篩選對變更的註解。
20 SVX_HID_REDLINING_FILTER_LB_DATE 30 依指定的日期和時間篩選變更清單。
21 SVX_HID_REDLINING_FILTER_CB_AUTOR 3c 依照從清單中選取的作者名稱篩選變更清單。
20 SVX_HID_REDLINING_FILTER_TF_DATE 30 依指定的日期和時間篩選變更清單。
15 .uno%3AMergeDocuments 8d 將在同一文件的多個副本中所做的變更匯入到原來的文件中。忽略對註腳、頁首、框架和欄位所做的變更。
17 .uno%3ACompareDocuments 27 比較目前文件和選取的文件。
1b .uno%3ABib%2FstandardFilter 49 使用 [標準篩選]，可微調及合併 [自動篩選] 搜尋選項。
20 EXTENSIONS_HID_BIB_BOOKTITLE_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
18 SVX_HID_GRID_TRAVEL_PREV 24 移至表格中的上一個條目。
1b EXTENSIONS_HID_BIB_ISBN_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
19 .uno%3ABib%2FInsertRecord 2a 將新的條目插入目前的表格中。
19 .uno%3ABib%2FDeleteRecord 18 刪除目前的條目。
1a EXTENSIONS_HID_BIB_URL_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1e EXTENSIONS_HID_BIB_EDITION_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1c EXTENSIONS_HID_BIB_TITLE_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
18 SVX_HID_GRID_TRAVEL_NEXT 24 移至表格中的下一個條目。
1c SVX_HID_GRID_TRAVEL_ABSOLUTE 3a 鍵入您要顯示的條目編號，然後按 Enter 鍵。
1d EXTENSIONS_HID_BIB_NUMBER_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1e EXTENSIONS_HID_BIB_JOURNAL_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1d EXTENSIONS_HID_BIB_VOLUME_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
12 .uno%3ABib%2Fquery 16b 鍵入要搜尋的資訊，然後按 Enter 鍵。若要變更搜尋的篩選選項，請長時間按下 [自動篩選] 圖示，然後選取不同的資料欄位。搜尋時，可以使用萬用字元如 % 或 * 來取代若干個字元，或使用 _ 或 ? 來取代單個字元。若要顯示表格中的所有記錄，請清除此方塊，然後按 Enter 鍵。
15 SVX_HID_FM_DELETEROWS 1e 刪除所選的資料條目。
1b EXTENSIONS_HID_BIB_YEAR_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1d EXTENSIONS_HID_BIB_EDITOR_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1c EXTENSIONS_HID_BIB_MONTH_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
23 EXTENSIONS_HID_BIB_HOWPUBLISHED_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
18 SVX_HID_GRID_TRAVEL_LAST 27 移至表格中的最後一個條目。
1c EXTENSIONS_HID_BIB_PAGES_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
16 .uno%3ABib%2Fsdbsource 33 選取參考文獻目錄資料庫的資料來源。
21 EXTENSIONS_HID_BIB_REPORTTYPE_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
14 .uno%3ABib%2FMapping bf 可讓您建立欄標題與不同資料來源中資料欄位的對映。若要為參考文獻目錄定義不同的資料來源，請按一下記錄 [物件列] 上的 [資料來源] 按鈕。
1c .uno%3ABibliographyComponent 4b 在參考文獻目錄資料庫中插入、刪除、編輯和管理記錄。
24 EXTENSIONS_HID_BIB_ORGANIZATIONS_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
17 .uno%3ABib%2FautoFilter 94 長時間按下以選取要使用在 [搜尋關鍵字] 方塊中所輸入的術語來搜尋的資料欄位。您僅可以搜尋一個資料欄位。
21 EXTENSIONS_HID_BIB_IDENTIFIER_POS 58 輸入條目的短名稱。此短名稱會顯示在條目清單的 [識別碼] 欄中。
19 .uno%3ABib%2FremoveFilter 4c 若要顯示表格中的所有條目，請按一下 [移除篩選] 圖示。
1e EXTENSIONS_HID_BIB_CHAPTER_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1d EXTENSIONS_HID_BIB_ANNOTE_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
19 SVX_HID_GRID_TRAVEL_FIRST 24 移至表格中的第一個條目。
1e EXTENSIONS_HID_BIB_ADDRESS_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1d EXTENSIONS_HID_BIB_SCHOOL_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
20 EXTENSIONS_HID_BIB_PUBLISHER_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
19 EXTENSIONS_HID_BIB_DB_TBX 4b 在參考文獻目錄資料庫中插入、刪除、編輯和管理記錄。
24 EXTENSIONS_HID_BIB_AUTHORITYTYPE_POS 96 選取所要建立條目的類型。$[officename] 在條目的 [類型] 欄中插入一個編號，此編號與您在此處選取的類型相對應。
1d EXTENSIONS_HID_BIB_SERIES_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
1d EXTENSIONS_HID_BIB_MAPPINGDLG bf 可讓您建立欄標題與不同資料來源中資料欄位的對映。若要為參考文獻目錄定義不同的資料來源，請按一下記錄 [物件列] 上的 [資料來源] 按鈕。
1b EXTENSIONS_HID_BIB_NOTE_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
17 SVX_HID_GRID_TRAVEL_NEW 30 將新的資料條目插入目前的表格中。
22 EXTENSIONS_HID_BIB_INSTITUTION_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
13 .uno%3ABib%2Fsource 66 列出目前資料庫中可用的表格。按一下清單中的名稱，以顯示該表格的條目。
1d EXTENSIONS_HID_BIB_AUTHOR_POS 63 輸入選取條目的其他資訊。您也可以視需要在表格的對應欄位中輸入資訊。
27 SVX_METRICFIELD_RID_SVXDLG_ZOOM_ED_USER 45 輸入文件要採用的顯示比例。在方塊中輸入百分比。
2d cui%3AMetricField%3ARID_SVXDLG_ZOOM%3AED_USER 45 輸入文件要採用的顯示比例。在方塊中輸入百分比。
13 SVX_HID_MNU_ZOOM_75 26 以實際大小的 75% 顯示文件。
e .uno%3AView100 24 以文件實際大小顯示文件。
b SID_VIEW200 27 以實際大小的兩倍顯示文件。
31 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_OPTIMAL 3f 重新調整顯示大小，以符合文件中文字的寬度。
b SID_VIEW050 27 以實際大小的一半顯示文件。
13 SVX_HID_MNU_ZOOM_50 27 以實際大小的一半顯示文件。
2d cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_100 24 以文件實際大小顯示文件。
1b SVX_HID_MNU_ZOOM_PAGE_WIDTH 4e 顯示文件頁面的完整寬度。可能看不到頁面的上、下邊緣。
1b SVX_HID_MNU_ZOOM_WHOLE_PAGE 21 螢幕上會顯示整個頁面。
14 SVX_HID_MNU_ZOOM_100 24 以文件實際大小顯示文件。
2a SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_SINGLE 48 單頁檢視版面配置會逐頁顯示頁面，但一律不會並排。
14 SVX_HID_MNU_ZOOM_200 27 以實際大小的兩倍顯示文件。
34 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_PAGE_WIDTH 4e 顯示文件頁面的完整寬度。可能看不到頁面的上、下邊緣。
34 cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_WHOLE_PAGE 21 螢幕上會顯示整個頁面。
b .uno%3AZoom 2f 縮小或增大 %PRODUCTNAME 的螢幕顯示。
25 SVX_CHECKBOX_RID_SVXDLG_ZOOM_CHK_BOOK 7b 在書籍模式檢視版面配置中，您會看到兩頁如同打開的書本般並排。第一頁是奇數頁的右頁。
2d SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_AUTOMATIC 5a 自動檢視版面配置會依顯示比例允許的最多頁數進行並排顯示頁面。
14 SVX_HID_MNU_ZOOM_150 29 以實際大小的 1.5 倍顯示文件。
18 SVX_HID_MNU_ZOOM_OPTIMAL 3f 重新調整顯示大小，以符合文件中文字的寬度。
2a SVX_METRICFIELD_RID_SVXDLG_ZOOM_ED_COLUMNS 60 在欄位檢視版面配置中，可檢視並排顯示指定欄數的頁面。請輸入欄數。
28 SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_USER 45 輸入文件要採用的顯示比例。在方塊中輸入百分比。
2b SVX_RADIOBUTTON_RID_SVXDLG_ZOOM_BTN_COLUMNS 60 在欄位檢視版面配置中，可檢視並排顯示指定欄數的頁面。請輸入欄數。
2e cui%3ARadioButton%3ARID_SVXDLG_ZOOM%3ABTN_USER 45 輸入文件要採用的顯示比例。在方塊中輸入百分比。
19 .uno%3AFunctionBarVisible 23 顯示或隱藏[標準工具列]。
1a .uno%3AShowImeStatusWindow 34 顯示或隱藏輸入法引擎 (IME) 狀態視窗。
15 .uno%3AToolBarVisible 1e 顯示或隱藏 [工具列]。
17 .uno%3AStatusBarVisible 30 顯示或隱藏視窗下邊緣的 [狀態列]。
15 .uno%3ATaskBarVisible 30 顯示或隱藏視窗下邊緣的 [狀態列]。
11 .uno%3AFullScreen 93 顯示或隱藏 Writer 或 Calc 中的功能表與工具列。若要結束全螢幕模式，請按一下 [啟動或關閉全螢幕模式] 按鈕。
1a SFX2_HID_FULLSCREENTOOLBOX 73 在 Writer 與 Calc 中，還可以使用捷徑鍵 CommandCtrl+Shift+J，在標準與全螢幕模式之間切換。
13 .uno%3AColorControl 8a 顯示或隱藏 [顏色列]。若要修改或變更顯示的顏色表，請選擇 [格式] - [區域]，然後按一下 [顏色] 標籤。
18 SVX_HID_COLOR_CTL_COLORS 156 按一下要使用的顏色。若要變更目前檔案中物件的充填顏色，請選取此物件，然後按一下某種顏色。若要變更選取的物件之線條顏色，請在某種顏色上按一下滑鼠右鍵。若要變更文字物件中文字的顏色，請按兩下該文字物件，選取文字，然後按一下某種顏色。
18 .uno%3AAvailableToolbars 30 開啟子功能表以顯示或隱藏工具列。
18 .cmd%3ARestoreVisibility a0 選擇 [檢視] - [工具列] - [重設]，將工具列重設為預設與上下文相關的運作方式。此時會根據上下文自動顯示部分工具列。
22 .uno%3ADeleteAllAnnotationByAuthor 30 刪除目前文件中此作者的所有備註。
17 .uno%3ADeleteAnnotation 18 刪除目前的備註。
11 .uno%3ADeleteNote 18 刪除目前的備註。
17 .uno%3AInsertAnnotation f 插入備註。
16 .uno%3AShowAnnotations 56 使用 [檢視] - [備註] 即可顯示或隱藏所有備註 (Calc 中無法使用)。
1a .uno%3ADeleteAllAnnotation 24 刪除目前文件的所有備註。
15 .uno%3ADeleteAllNotes 24 刪除目前文件的所有備註。
13 .uno%3ADeleteAuthor 30 刪除目前文件中此作者的所有備註。
b .uno%3AScan 21 將掃描影像插入文件中。
12 .uno%3ATwainSelect 1e 選取要使用的掃描器。
14 .uno%3ATwainTransfer 66 掃描影像，然後將掃描結果插入文件中。掃描對話方塊由掃描器製造商提供。
13 .uno%3AInsertSymbol 2a 插入所安裝字型中的特殊字元。
d .uno%3ABullet 2a 插入所安裝字型中的特殊字元。
2e cui%3AListBox%3ARID_SVXDLG_CHARMAP%3ALB_SUBSET 27 選取目前字型的 Unicode 分類。
32 cui%3APushButton%3ARID_SVXDLG_CHARMAP%3ABTN_DELETE 33 清除您目前選擇的要插入的特殊字元。
1b CUI_HID_CHARMAP_CTL_SHOWSET 3f 按一下要插入的特殊字元，然後按一下 [確定]。
2c cui%3AListBox%3ARID_SVXDLG_CHARMAP%3ALB_FONT 3f 選取一種字型，以顯示與其相關聯的特殊字元。
16 .uno%3AInsertHyperlink 86 顯示或隱藏 [超連結位址欄]，您可以在此欄中插入和編輯 URL，或者使用關鍵字在 Internet 內進行搜尋。
1a SVT_HID_FILEDLG_PREVIEW_CB 27 顯示選取的圖形檔案的預覽。
14 .uno%3AInsertGraphic 21 在目前文件中插入圖片。
17 SVT_HID_FILEDLG_LINK_CB 2d 將選取的圖形檔案作為連結插入。
15 HID_IMPGRF_CB_PREVIEW 27 顯示選取的圖形檔案的預覽。
1f SVT_HID_FILEOPEN_IMAGE_TEMPLATE 1e 選取圖形的框架樣式。
1d SVX_HID_OFA_HYPERLINK_URL_TXT 2d 將選取的圖形檔案作為連結插入。
11 .uno%3AObjectMenu 68 將物件插入您的文件中。至於電影與音效方面，請改用 [插入] - [電影與音效]。
13 .uno%3AInsertObject 5a 將 OLE 物件插入目前的文件中。OLE 物件已插入為連結或內嵌式物件。
13 .uno%3AInsertPlugin 27 在目前文件中插入外掛程式。
12 .uno%3AInsertSound 2a 將聲音檔案插入目前的文件中。
12 .uno%3AInsertVideo 27 將視訊檔插入目前的文件中。
1b .uno%3AInsertObjectStarMath 21 在目前文件中插入公式。
11 .uno%3AInsertMath 21 在目前文件中插入公式。
20 .uno%3AInsertObjectFloatingFrame 87 Inserts a floating frame into the current document. Floating frames are used in HTML documents to display the contents of another file.
1c .uno%3AViewDataSourceBrowser 5c 列出在 %PRODUCTNAME 中登記的資料庫，並可讓您管理這些資料庫的內容。
11 .uno%3ADataImport 5c 列出在 %PRODUCTNAME 中登記的資料庫，並可讓您管理這些資料庫的內容。
12 .uno%3AGraphicMenu 24 選取要插入之圖片的來源。
1d .uno%3AStandardTextAttributes 3f 從選取範圍移除直接格式及依字元樣式的格式。
11 .uno%3ASetDefault 3f 從選取範圍移除直接格式及依字元樣式的格式。
16 .uno%3AResetAttributes 3f 從選取範圍移除直接格式及依字元樣式的格式。
11 .uno%3AFontDialog 2d 變更所選字元的字型和字型格式。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_LANG 7b 設定拼字檢查程式用於所選文字或所輸入文字的語言。可用的語言模組前方會顯示核取標記。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_STYLE 1b 選取要套用的格式。
36 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_STYLE 1b 選取要套用的格式。
36 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_STYLE 1b 選取要套用的格式。
33 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_LANG 7b 設定拼字檢查程式用於所選文字或所輸入文字的語言。可用的語言模組前方會顯示核取標記。
36 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_SIZE 63 輸入或選取要套用的字型大小。對於可調整大小的字型，還可以輸入小數。
3c cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_STYLE_NOCJK 1b 選取要套用的格式。
3b cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_NAME_NOCJK 4e 輸入要使用的已安裝字型的名稱，或者從清單中選取字型。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_NAME 4e 輸入要使用的已安裝字型的名稱，或者從清單中選取字型。
3a cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_LANG_NOCJK 7b 設定拼字檢查程式用於所選文字或所輸入文字的語言。可用的語言模組前方會顯示核取標記。
3c cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_SIZE_NOCJK 63 輸入或選取要套用的字型大小。對於可調整大小的字型，還可以輸入小數。
19 CUI_HID_SVXPAGE_CHAR_NAME 24 指定要套用的格式和字型。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_LANG 7b 設定拼字檢查程式用於所選文字或所輸入文字的語言。可用的語言模組前方會顯示核取標記。
36 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_WEST_SIZE 63 輸入或選取要套用的字型大小。對於可調整大小的字型，還可以輸入小數。
34 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_NAME 4e 輸入要使用的已安裝字型的名稱，或者從清單中選取字型。
35 cui%3AComboBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_EAST_NAME 4e 輸入要使用的已安裝字型的名稱，或者從清單中選取字型。
35 cui%3AMetricBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_CTL_SIZE 63 輸入或選取要套用的字型大小。對於可調整大小的字型，還可以輸入小數。
31 cui%3AListBox%3ARID_SVXPAGE_CHAR_NAME%3ALB_COLOR2 a5 設定用於所選文字的顏色。如果您選取 [自動]，則在淺色背景下設定文字顏色為黑色，而在深色背景下設定文字顏色為白色。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_EMPHASIS 42 選取要在整個所選文字的上方或下方顯示的字元。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_STRIKEOUT 27 選取所選文字的刪除線樣式。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_POSITION 24 指定顯示強調標記的位置。
3d cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_UNDERLINE_COLOR 18 選取底線的顏色。
39 cui%3ATriStateBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_OUTLINE 4b 顯示所選字元的輪廓。此效果並不是對所有字型都有效。
10 .uno%3AFontColor d3 按一下即可將目前的字型顏色套用至所選的字元。您也可以按一下此處，然後透過拖曳選擇以變更文字顏色。按一下圖示旁的箭號即可開啟 [字型顏色] 工具列。
38 cui%3ATriStateBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_SHADOW 36 加入投射在所選字元下方和右側的陰影。
c .uno%3AColor d3 按一下即可將目前的字型顏色套用至所選的字元。您也可以按一下此處，然後透過拖曳選擇以變更文字顏色。按一下圖示旁的箭號即可開啟 [字型顏色] 工具列。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_OVERLINE 6d 選取您要套用的下劃線樣式。若只要將下劃線套用至單字，請選取 [個別字] 方塊。
36 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_EFFECTS2 b1 選取要套用至所選文字的浮雕效果。浮凸的浮雕效果使字元看起來好像是凸出頁面。而雕刻的浮雕效果使字元看起來好像是凹入頁面。
1c CUI_HID_SVXPAGE_CHAR_EFFECTS 21 指定要使用的字型效果。
3e cui%3ACheckBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ACB_INDIVIDUALWORDS 36 僅對字詞套用選取的效果，並忽略空格。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_FONTCOLOR a5 設定用於所選文字的顏色。如果您選取 [自動]，則在淺色背景下設定文字顏色為黑色，而在深色背景下設定文字顏色為白色。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_UNDERLINE 61 選取您要套用的底線樣式。若只要為單字加底線，請選取 [個別字] 方塊。
f .uno%3AOverline 72 將所選文字加上頂線或移除頂線。若游標不位於字組，則所輸入的新文字會加上頂線。
3c cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_OVERLINE_COLOR 18 選取頂線的顏色。
34 cui%3AListBox%3ARID_SVXPAGE_CHAR_EFFECTS%3ALB_RELIEF b1 選取要套用至所選文字的浮雕效果。浮凸的浮雕效果使字元看起來好像是凸出頁面。而雕刻的浮雕效果使字元看起來好像是凹入頁面。
31 cui%3AEdit%3ARID_SVXPAGE_NUMBERFORMAT%3AED_FORMAT 4b 顯示所選格式的數字格式碼，也可以輸入一個自訂格式。
3b cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ACB_SOURCEFORMAT 57 使用與包含圖表資料的儲存格所使用的數字格式相同的數字格式。
3d cui%3ANumericField%3ARID_SVXPAGE_NUMBERFORMAT%3AED_LEADZEROES 30 輸入小數點之前最多要顯示幾個零。
38 cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ABTN_THOUSAND 4e 插入千位分隔符號。所用的分隔符號類型取決於語言設定。
1e CUI_HID_NUMBERFORMAT_LB_FORMAT 2d 選取所選儲存格內容的顯示方式。
14 CUI_HID_NUMBERFORMAT 2a 指定所選儲存格的格式化選項。
32 cui%3AEdit%3ARID_SVXPAGE_NUMBERFORMAT%3AED_COMMENT 48 輸入對所選數字格式的註解，然後在此方塊外按一下。
1d CUI_HID_NUMBERFORMAT_TBI_INFO 2d 將註解增加到選取的數字格式中。
36 cui%3ACheckBox%3ARID_SVXPAGE_NUMBERFORMAT%3ABTN_NEGRED 27 將負數的字型顏色變為紅色。
3b cui%3ANumericField%3ARID_SVXPAGE_NUMBERFORMAT%3AED_DECIMALS 21 輸入要顯示的小數位數。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_LANGUAGE d2 指定選取的  欄位的語言設定。如果將語言設定為 [自動]，$[officename] 會自動套用與系統預設語言相關聯的數字格式。選取任一語言即可確定所選  欄位的設定。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_CATEGORY 52 從清單中選取一個類別，然後在 [格式] 方塊中選取格式樣式。
1f CUI_HID_NUMBERFORMAT_TBI_REMOVE 1e 刪除選取的數字格式。
1c CUI_HID_NUMBERFORMAT_TBI_ADD 42 將輸入的數字格式碼增加到使用者定義的類別中。
36 cui%3AListBox%3ARID_SVXPAGE_NUMBERFORMAT%3ALB_CURRENCY 6a 選取一種貨幣，然後捲動至 [格式] 清單的頂端，以檢視該貨幣對應的格式選項。
1e .uno%3ATableNumberFormatDialog 2a 指定所選儲存格的格式化選項。
19 .uno%3AInsertHyperlinkDlg 33 指定新的超連結或編輯選取的超連結。
f SW_HID_CHAR_URL 33 指定新的超連結或編輯選取的超連結。
27 sw%3AComboBox%3ATP_CHAR_URL%3ALB_TARGET 69 輸入要在其中開啟連結檔案的訊框之名稱，或者從清單中選取預先定義的訊框。
28 sw%3APushButton%3ATP_CHAR_URL%3APB_EVENT 30 指定按一下超連結時所觸發的事件。
21 sw%3AEdit%3ATP_CHAR_URL%3AED_TEXT 27 輸入超連結中要顯示的文字。
2b sw%3AListBox%3ATP_CHAR_URL%3ALB_NOT_VISITED c4 從清單中選取用於未造訪的連結的格式樣式。若要在此清單中增加或修改樣式，請關閉此對話方塊，並按一下 [格式] 工具列上的樣式和格式化圖示。
20 sw%3AEdit%3ATP_CHAR_URL%3AED_URL 37 輸入按一下超連結時要開啟的檔案之 URL。
21 sw%3AEdit%3ATP_CHAR_URL%3AED_NAME 1b 輸入超連結的名稱。
26 sw%3APushButton%3ATP_CHAR_URL%3APB_URL 3c 找到要連結的檔案，然後按一下 [開啟舊檔]。
27 sw%3AListBox%3ATP_CHAR_URL%3ALB_VISITED c4 從清單中選取用於已造訪的連結的格式樣式。若要在此清單中增加或修改樣式，請關閉此對話方塊，並按一下 [格式] 工具列上的樣式和格式化圖示。
3e cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AMF_SCALE_WIDTH 4b 輸入沿水平方向拉伸或壓縮所選文字的字型寬度百分比。
3b cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_FONTSIZE 33 輸入所選文字的字型大小要縮小的量。
3b cui%3ACheckBox%3ARID_SVXPAGE_CHAR_POSITION%3ACB_PAIRKERNING 30 自動調整特定字母組合的字元間隔。
3a cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_HIGHLOW 66 輸入所選文字相對於基線提昇或降低的量。百分之一百表示等於字型的高度。
37 cui%3AListBox%3ARID_SVXPAGE_CHAR_POSITION%3ALB_KERNING2 85 指定所選文字之字元的間隔。若要擴充或壓縮間隔，請在 [間隔] 方塊中輸入要擴充或壓縮文字的量。
37 cui%3ACheckBox%3ARID_SVXPAGE_CHAR_POSITION%3ACB_HIGHLOW 3f 自動設定所選文字相對於基線上升或下降的量。
39 cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_LOWPOS 48 縮小所選文字的字型大小，並將文字降低到基線之下。
3a cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_HIGHPOS 48 縮小所選文字的字型大小，並將文字提昇到基線之上。
12 .uno%3ASuperScript 48 縮小所選文字的字型大小，並將文字提昇到基線之上。
3c cui%3ARadioButton%3ARID_SVXPAGE_CHAR_POSITION%3ARB_NORMALPOS 2a 移除向上格式化或向下格式化。
1d CUI_HID_SVXPAGE_CHAR_POSITION 39 指定字元的位置、顯示比例、旋轉和間隔。
3b cui%3AMetricField%3ARID_SVXPAGE_CHAR_POSITION%3AED_KERNING2 3c 輸入要擴充或壓縮所選文字之字元間隔的量。
10 .uno%3ASubScript 48 縮小所選文字的字型大小，並將文字降低到基線之下。
39 cui%3AListBox%3ARID_SVXPAGE_CHAR_TWOLINES%3AED_ENDBRACKET 66 選取定義雙行區域結尾的字元。如果您要選擇自訂字元，請選取 [其他字元]。
3b cui%3AListBox%3ARID_SVXPAGE_CHAR_TWOLINES%3AED_STARTBRACKET 66 選取定義雙行區域起始的字元。如果您要選擇自訂字元，請選取 [其他字元]。
38 cui%3ACheckBox%3ARID_SVXPAGE_CHAR_TWOLINES%3ACB_TWOLINES 3c 允許您在目前文件內的所選區域中雙行寫入。
3c cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_FORBIDDEN 69 不要清單中的字元作為字行的開頭或結尾。這些字元會重置於上一行或下一行。
3f cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_SCRIPT_SPACE 48 在亞洲字元、拉丁字元和複雜字元之間插入一個空格。
3c cui%3ATriStateBox%3ARID_SVXPAGE_PARA_ASIAN%3ACB_AS_HANG_PUNC 66 防止以逗號和句點中斷行。而應將這些字元加到行尾，甚至放在頁面邊距中。
10 .uno%3AEditStyle 3c 修改目前段落的格式，例如縮排和對齊方式。
16 .uno%3AParagraphDialog 3c 修改目前段落的格式，例如縮排和對齊方式。
37 cui%3AListBox%3ARID_SVXPAGE_STD_PARAGRAPH%3ALB_LINEDIST 36 指定段落中各行文字之間要留出的間隔。
42 cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LINEDISTPERCENT 1b 輸入用作行距的值。
34 cui%3ACheckBox%3ARID_SVXPAGE_STD_PARAGRAPH%3ACB_AUTO 33 輸入您要在所選段落之上留出的間隔。
3d cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LEFTINDENT 10e 輸入段落要從頁面邊距縮排的空格量。如果您要段落延伸至頁面邊距內，請輸入負數。在由左至右的語言中，段落的左邊距會向左頁面邊距縮排。在由右至左的語言中，段落的右邊距會向右頁面邊距縮排。
3d cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_BOTTOMDIST 33 輸入您要在所選段落之下留出的間隔。
41 cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_LINEDISTMETRIC 1b 輸入用作行距的值。
3e cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_RIGHTINDENT 117 輸入段落要從頁面邊距縮排的間隔。如果您要段落延伸至頁面邊距內，請輸入負數。在由左至右的語言中，段落的右邊緣會根據右頁面邊距來縮排。在由右至左的語言中，段落的左邊緣會根據左頁面邊距來縮排。
1c CUI_HID_FORMAT_PARAGRAPH_STD 2d 設定段落的縮排選項和間隔選項。
3a cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_TOPDIST 33 輸入您要在所選段落之上留出的間隔。
3e cui%3AMetricField%3ARID_SVXPAGE_STD_PARAGRAPH%3AED_FLINEINDENT 108 依您輸入的量縮排段落的第一行。若要建立首行凸排，請為 [文字之前] 輸入正值，並為 [第一行] 輸入負值。若要縮排使用編號或項目符號之段落的第一行，請選擇 [格式] - [項目符號與編號] - [位置]。
3e cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_OTHER 3c 您可以指定用於充填定位鍵左側空白的字元。
3c cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_LEFT 45 將文字的左邊緣與定位鍵對齊，並將文字向右延伸。
11 CUI_HID_TABULATOR 27 設定定位鍵在段落中的位置。
3f cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_POINTS 27 用點充填定位鍵左側的空白。
43 cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_UNDERSCORE 30 繪製線條以充填定位鍵左側的空白。
3d cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_RIGHT 51 將文字的右邊緣與定位鍵對齊，並將文字向定位鍵左側延伸。
3e cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_CENTER 27 將文字的中心與定位鍵對齊。
41 cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_DASHLINE 2d 用破折號充填定位鍵左側的空白。
32 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_NEW 30 在目前段落中加入您定義的定位鍵。
3f cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_TABTYPE_DECIMAL 57 將數字的小數點與定位鍵的中心對齊，而文字與定位鍵左側對齊。
35 cui%3APushButton%3ARID_SVXPAGE_TABULATOR%3ABTN_DELALL 91 移除所有在 [位置] 下定義的定位點停止位置。將固定間隔的左定位點停止位置設定為預設的定位點停止位置。
36 cui%3AEdit%3ARID_SVXPAGE_TABULATOR%3AED_FILLCHAR_OTHER 3c 您可以指定用於充填定位鍵左側空白的字元。
3b cui%3ARadioButton%3ARID_SVXPAGE_TABULATOR%3ABTN_FILLCHAR_NO 4b 不插入充填字元，或者移除定位鍵左側現有的充填字元。
33 cui%3AMetricBox%3ARID_SVXPAGE_TABULATOR%3AED_TABPOS f9 選取定位鍵類型，輸入新的定量單位，然後按一下 [新增]。如果需要，您還可以指定用於定位鍵的定量單位 (cm 表示公分，或 " 表示英吋)。您設定的第一個定位鍵左側的現有定位鍵會移除。
37 cui%3AEdit%3ARID_SVXPAGE_TABULATOR%3AED_TABTYPE_DECCHAR 3f 輸入您想要用作小數分隔符的小數定位點字元。
31 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_LINESTYLE 51 按一下要套用的邊框樣式。預覽中選取的邊框會套用此樣式。
1a CUI_HID_BORDER_CTL_PRESETS 30 選取要套用的預先定義之邊框樣式。
36 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AED_SHADOWSIZE 18 輸入陰影的寬度。
1a CUI_HID_BORDER_CTL_SHADOWS 30 按一下所選邊框要套用的陰影樣式。
2d cui%3ACheckBox%3ARID_SVXPAGE_BORDER%3ACB_SYNC 61 輸入新的間隔時，所有的四個邊框都會套用相同的 [至內容的間隔] 設定。
33 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_SHADOWCOLOR 18 選取陰影的顏色。
2f cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_TOP 39 輸入上邊框與選取內容之間要留出的間隔。
30 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_LEFT 39 輸入左邊框與選取內容之間要留出的間隔。
31 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_RIGHT 39 輸入右邊框與選取內容之間要留出的間隔。
32 cui%3AMetricField%3ARID_SVXPAGE_BORDER%3AMF_BOTTOM 39 輸入下邊框與選取內容之間要留出的間隔。
31 cui%3AListBox%3ARID_SVXPAGE_BORDER%3ALB_LINECOLOR 30 選取您要用於所選邊框的線條顏色。
e CUI_HID_BORDER 3b 在 Writer 或 Calc 中設定所選物件的邊框選項。
36 cui%3APushButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_BROWSE 48 找到要用作背景的圖形檔案，然後按一下 [開啟舊檔]。
35 cui%3ACheckBox%3ARID_SVXPAGE_BACKGROUND%3ABTN_PREVIEW 27 顯示或隱藏所選圖形的預覽。
39 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_POSITION 42 選取此選項，然後按一下位置網格中的一個位置。
22 CUI_HID_BACKGROUND_CTL_BGDCOLORSET 5a 按一下要用作背景的顏色。若要移除背景顏色，請按一下 [不填充]。
35 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_TILE 39 重複圖形，使其覆蓋所選物件的整個背景。
1f CUI_HID_BACKGROUND_CTL_POSITION 42 選取此選項，然後按一下位置網格中的一個位置。
32 cui%3ACheckBox%3ARID_SVXPAGE_BACKGROUND%3ABTN_LINK 4e 連結至目前檔案中的圖形檔，或內嵌目前檔案中的圖形檔。
33 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_TBL_BOX 4e 連結至目前檔案中的圖形檔，或內嵌目前檔案中的圖形檔。
34 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_PARA_BOX 4e 連結至目前檔案中的圖形檔，或內嵌目前檔案中的圖形檔。
35 cui%3ARadioButton%3ARID_SVXPAGE_BACKGROUND%3ABTN_AREA 33 拉伸圖形以充填所選物件的整個背景。
39 cui%3AMetricField%3ARID_SVXPAGE_BACKGROUND%3AMF_COL_TRANS 4e 連結至目前檔案中的圖形檔，或內嵌目前檔案中的圖形檔。
18 SVX_HID_POPUP_COLOR_CTRL 84 按一下某個顏色。按一下 [不充填]，可移除背景或反白顯示色彩。按一下 [自動]，可重設字型色彩。
34 cui%3AListBox%3ARID_SVXPAGE_BACKGROUND%3ALB_SELECTOR 21 選取要套用的背景類型。
12 CUI_HID_BACKGROUND 24 設定背景顏色或背景圖形。
3f cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_LEFTALIGN 24 將段落與頁面左邊距對齊。
42 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_JUSTIFYALIGN 27 將段落與左右頁面邊距對齊。
1e CUI_HID_FORMAT_PARAGRAPH_ALIGN 2d 相對於頁面邊距設定段落的對齊。
38 cui%3ACheckBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ACB_EXPAND 8b 指定使用複合文字版面配置 (CTL) 段落的文字方向。除非已啟用複合文字版面配置，否則無法使用此功能。
41 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_CENTERALIGN 24 在頁面上置中段落的內容。
3e cui%3AListBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ALB_TEXTDIRECTION 8b 指定使用複合文字版面配置 (CTL) 段落的文字方向。除非已啟用複合文字版面配置，否則無法使用此功能。
40 cui%3ARadioButton%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ABTN_RIGHTALIGN 24 將段落與頁面右邊距對齊。
2b sw%3AListBox%3ATP_COLUMN%3ALB_TEXTDIRECTION 8b 指定使用複合文字版面配置 (CTL) 段落的文字方向。除非已啟用複合文字版面配置，否則無法使用此功能。
39 cui%3AListBox%3ARID_SVXPAGE_ALIGN_PARAGRAPH%3ALB_LASTLINE 8b 指定使用複合文字版面配置 (CTL) 段落的文字方向。除非已啟用複合文字版面配置，否則無法使用此功能。
31 sw%3AListBox%3ATP_FORMAT_TABLE%3ALB_TEXTDIRECTION 8b 指定使用複合文字版面配置 (CTL) 段落的文字方向。除非已啟用複合文字版面配置，否則無法使用此功能。
32 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_RIGHT 139 如果選取了[保持顯示比例]選項，則輸入正值來修剪圖形的右邊緣，或者輸入負值在圖形的右側加入空白區域。如果選取了[保持畫面大小]選項，則輸入正值來增大圖形水平方向的顯示比例，或者輸入負值來縮小圖形水平方向的顯示比例。
36 cui%3ARadioButton%3ARID_SVXPAGE_GRFCROP%3ARB_SIZECONST f3 裁剪時保持圖形原來的大小，從而僅變更圖形的顯示比例。若要縮小圖形的顯示比例，請選取此選項，並在裁剪方塊中輸入負值。若要增大圖形的顯示比例，請在裁剪方塊中輸入正值。
36 cui%3ARadioButton%3ARID_SVXPAGE_GRFCROP%3ARB_ZOOMCONST 4e 裁剪時保持圖形原來的顯示比例，從而僅變更圖形的大小。
33 cui%3APushButton%3ARID_SVXPAGE_GRFCROP%3APB_ORGSIZE 2a 將所選圖形恢復成原來的大小。
37 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_HEIGHTZOOM 33 輸入所選圖形的高度，以百分比表示。
36 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_WIDTHZOOM 33 輸入所選圖形的寬度，以百分比表示。
31 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_LEFT 13b 如果選取了 [保持顯示比例] 選項，則輸入正值來修剪圖形的左邊緣，或者輸入負值在圖形的左側加入空白區域。如果選取了[保持畫面大小]選項，則輸入正值來增大圖形水平方向的顯示比例，或者輸入負值來縮小圖形水平方向的顯示比例。
33 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_BOTTOM 137 如果選取了 [保持顯示比例] 選項，則輸入正值來修剪圖形的底端，或者輸入負值在圖形下方增加空白區域。如果選取了 [保持畫面大小] 選項，則輸入正值來增大圖形垂直方向的顯示比例，或者輸入負值來縮小圖形垂直方向的顯示比例。
33 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_HEIGHT 1e 輸入所選圖形的高度。
32 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_WIDTH 1e 輸入所選圖形的寬度。
30 cui%3AMetricField%3ARID_SVXPAGE_GRFCROP%3AMF_TOP 137 如果選取了 [保持顯示比例] 選項，則輸入正值來修剪圖形的頂端，或者輸入負值在圖形上方加入空白區域。如果選取了 [保持畫面大小] 選項，則輸入正值來增大圖形垂直方向的顯示比例，或者輸入負值來縮小圖形垂直方向的顯示比例。
2b sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_BASE 64 選取要作為新樣式建立基礎的現有樣式，或者選取 [無] 以定義自己的樣式。
2c sfx2%3ACheckBox%3ATP_MANAGE_STYLES%3ACB_AUTO 87 在文件中使用此樣式直接對段落套用格式時，會更新樣式。所有使用此樣式之段落的格式會自動更新。
31 sfx2%3AMultiLineEdit%3ATP_MANAGE_STYLES%3AED_DESC 30 說明目前樣式中所使用的有關格式。
16 SFX2_HID_MANAGE_STYLES 1e 設定所選樣式的選項。
2b sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_NEXT e8 選取一個現有樣式，將其作為文件中目前樣式的後續樣式。對於段落樣式，當您按 Enter 鍵後所建立的段落將套用下一個樣式。對於頁面樣式，建立新頁面時將套用下一個樣式。
28 sfx2%3AEdit%3ATP_MANAGE_STYLES%3AED_NAME 96 顯示所選樣式的名稱。如果要建立或修改自訂樣式，請為該樣式輸入一個名稱。您無法變更預先定義的樣式名稱。
2d sfx2%3AListBox%3ATP_MANAGE_STYLES%3ALB_REGION 6b 顯示目前樣式的分類。如果您要建立或修改新樣式，請從清單中選取[自訂樣式]。
2b cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_VERT 2a 在列印頁面上垂直置中儲存格。
2e cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_REGISTER ab 選取要在所選頁面樣式上對齊文字時，想用作參照的段落樣式。在參照樣式集中指定的字型高度，會設定頁面垂直網格線的間距。
2f cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_REGISTER 3c 將所選頁面樣式上的文字對齊頁面垂直網格。
34 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_TOP_MARGIN 3f 輸入頁面上邊緣與文件文字之間要留出的間隔。
32 cui%3ARadioButton%3ARID_SVXPAGE_PAGE%3ARB_PORTRAIT 39 以垂直方向的紙張顯示並列印目前的文件。
2c cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_ADAPT 6c 調整繪圖物件的大小，使其符合您選取的紙張格式。繪圖物件的編排將予以保留。
36 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_RIGHT_MARGIN b8 輸入頁面右邊緣與文件文字之間要留出的間隔。如果使用的是 [翻轉的] 頁面版面配置，請輸入文字外邊距與頁面外邊緣之間要留出的間隔。
35 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_LEFT_MARGIN b2 輸入頁面左邊緣與文件文字之間要留出的間隔。如果使用的是 [翻轉的] 頁面版式，請輸入文字內邊距與頁面內邊緣之間要留出的間隔。
36 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_PAPER_HEIGHT 5a 顯示所選紙張格式的高度。若要定義自訂格式，請在此處輸入高度。
2c cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_LAYOUT 5d 指定目前的樣式應顯示奇數頁、偶數頁，或同時顯示奇數頁與偶數頁。
2b cui%3ACheckBox%3ARID_SVXPAGE_PAGE%3ACB_HORZ 2a 在列印頁面上水平置中儲存格。
2f cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_TEXT_FLOW 2d 選擇要在文件中使用的文字方向。
33 cui%3ARadioButton%3ARID_SVXPAGE_PAGE%3ARB_LANDSCAPE 39 以水平方向的紙張顯示並列印目前的文件。
13 CUI_HID_FORMAT_PAGE 4e 允許您定義單頁或多頁文件的頁面版式、編號和紙張格式。
37 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_BOTTOM_MARGIN 3f 輸入頁面下邊緣與文件文字之間要留出的間隔。
35 cui%3AMetricField%3ARID_SVXPAGE_PAGE%3AED_PAPER_WIDTH 5d 顯示選取紙張格式的寬度。如果要定義自訂格式，請在此處輸入寬度。
33 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_NUMBER_FORMAT 3c 選取目前頁面樣式所要採用的頁面編號格式。
30 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_PAPER_TRAY e2 選取印表機的紙張來源。如果需要，可以為不同的頁面樣式指定不同的送紙匣。例如，指定 [首頁] 樣式使用不同的送紙匣，並將附有您公司的箋頭的紙張裝入該送紙匣中。
30 cui%3AListBox%3ARID_SVXPAGE_PAGE%3ALB_PAPER_SIZE 79 選取預先定義的紙張大小，或者在[高度]和[寬度]方塊中輸入紙張的尺寸，以建立自訂格式。
32 svx%3APushButton%3ARID_SVXPAGE_HEADER%3ABTN_EXTRAS 36 定義頁首的邊框、背景顏色或背景圖案。
33 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_RMARGIN 42 輸入頁面右邊緣和頁尾右邊緣之間要留出的間隔。
2f svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_SHARED 2d 偶數頁和奇數頁共用相同的內容。
15 SVX_HID_FORMAT_HEADER 78 將頁首加入目前的頁面樣式中。頁首是頁面上邊距中的區域，在此您可以加入文字或圖形。
33 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_LMARGIN 42 輸入頁面左邊緣和頁首左邊緣之間要留出的間隔。
33 svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_DYNSPACING 5b 置換 [間隔] 設定，並允許頁首延伸至頁首和文件文字之間的區域內。
15 SC_HID_SC_HEADER_EDIT 1e 新增或編輯頁首文字。
32 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_HEIGHT 21 輸入您需要的頁首高度。
30 svx%3AMetricField%3ARID_SVXPAGE_HEADER%3AED_DIST 48 輸入頁首下邊緣和文件文字上邊緣之間要保持的間隔。
2f svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_TURNON 2a 將頁首加入目前的頁面樣式中。
33 svx%3ACheckBox%3ARID_SVXPAGE_HEADER%3ACB_HEIGHT_DYN 39 自動調整頁首高度，以適合您輸入的內容。
32 svx%3APushButton%3ARID_SVXPAGE_FOOTER%3ABTN_EXTRAS 36 定義頁尾的邊框、背景顏色或背景圖案。
32 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_HEIGHT 21 輸入您需要的頁尾高度。
2f svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_SHARED 2d 偶數頁和奇數頁共用相同的內容。
2f svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_TURNON 2a 將頁尾加入目前的頁面樣式中。
15 SVX_HID_FORMAT_FOOTER 78 將頁尾加入目前的頁面樣式中。頁尾是頁面下邊距中的區域，在此您可以加入文字或圖形。
33 svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_DYNSPACING 5b 置換 [間隔] 設定，並允許頁尾延伸至頁尾和文件文字之間的區域內。
33 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_RMARGIN 42 輸入頁面右邊緣和頁尾右邊緣之間要留出的間隔。
15 SC_HID_SC_FOOTER_EDIT 1e 新增或編輯頁尾文字。
33 svx%3ACheckBox%3ARID_SVXPAGE_FOOTER%3ACB_HEIGHT_DYN 39 自動調整頁尾高度，以適合您輸入的內容。
30 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_DIST 48 輸入文件文字下邊緣與頁尾上邊緣之間要保留的間隔。
33 svx%3AMetricField%3ARID_SVXPAGE_FOOTER%3AED_LMARGIN 42 輸入頁面左邊緣和頁尾左邊緣之間要留出的間隔。
1b .uno%3AChangeCaseToKatakana 39 將選取的亞洲語言字元變更為片假名字元。
1c .uno%3AChangeCaseToHalfWidth 33 將所選亞洲語言字元變更為半形字元。
1a SID_TRANSLITERATE_SENTENCE 3f 將選定的西文字元第一個字母變更為大寫字元。
18 .uno%3ATransliterateMenu 6f 變更選項中的字元大小寫。若游標位於字詞中，且未選取文字，則表示選取該字詞。
18 .uno%3AChangeCaseToUpper 33 將所選取的西文字元變更為大寫字元。
18 .uno%3AChangeCaseToLower 33 將所選取的西文字元變更為小寫字元。
1f .uno%3AChangeCaseToSentenceCase 3f 將選定的西文字元第一個字母變更為大寫字元。
1b .uno%3AChangeCaseToHiragana 36 將所選亞洲語言字元變更為平假名字元。
1c .uno%3AChangeCaseToFullWidth 39 將所選亞洲語言字元變更為標準寬度字元。
1c .uno%3AChangeCaseToTitleCase 51 將所選取的西文字元每個字詞的第一個字元變更為大寫字元。
1d .uno%3AChangeCaseToToggleCase 30 切換所有所選之西文字元的大小寫。
18 SID_TRANSLITERATE_TOGGLE 30 切換所有所選之西文字元的大小寫。
1c SID_TRANSLITERATE_CAPITALIZE 51 將所選取的西文字元每個字詞的第一個字元變更為大寫字元。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_3 87 顯示您在目前的檔案中選取的基本文字。如果需要，您可以透過在此處輸入新的文字來修改基本文字。
2f svx%3APushButton%3ARID_SVXDLG_RUBY%3APB_STYLIST 58 開啟 [樣式和格式] 視窗，您可以在其中選取注音文字的字元樣式。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_4 33 輸入要用作基本文字發音指導的文字。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_2 33 輸入要用作基本文字發音指導的文字。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_2 87 顯示您在目前的檔案中選取的基本文字。如果需要，您可以透過在此處輸入新的文字來修改基本文字。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_1 87 顯示您在目前的檔案中選取的基本文字。如果需要，您可以透過在此處輸入新的文字來修改基本文字。
2d svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_POSITION 27 選取要放置注音文字的位置。
2b svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_ADJUST 24 為注音文字選取水平對齊。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_1 33 輸入要用作基本文字發音指導的文字。
11 .uno%3ARubyDialog 48 允許您在亞洲語言字元上方加入作為發音指導的註解。
2f svx%3AListBox%3ARID_SVXDLG_RUBY%3ALB_CHAR_STYLE 24 選取注音文字的字元樣式。
29 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_RIGHT_3 33 輸入要用作基本文字發音指導的文字。
28 svx%3AEdit%3ARID_SVXDLG_RUBY%3AED_LEFT_4 87 顯示您在目前的檔案中選取的基本文字。如果需要，您可以透過在此處輸入新的文字來修改基本文字。
15 .uno%3AAlignFrameMenu 1b 所選物件互相對齊。
16 .uno%3AObjectAlignLeft 90 將所選物件的左邊緣對齊。如果僅選取了一個 Draw 或 Impress 物件，則該物件的左邊緣會與頁面的左邊距對齊。
10 .uno%3AAlignLeft 90 將所選物件的左邊緣對齊。如果僅選取了一個 Draw 或 Impress 物件，則該物件的左邊緣會與頁面的左邊距對齊。
12 .uno%3AAlignCenter 8d 將選取的物件水平置中。如果僅選取了一個 Draw 或 Impress 物件，則該物件的中心會與頁面的水平中心對齊。
1c .uno%3AAlignHorizontalCenter 8d 將選取的物件水平置中。如果僅選取了一個 Draw 或 Impress 物件，則該物件的中心會與頁面的水平中心對齊。
11 .uno%3AAlignRight 90 所選物件的右邊緣對齊。在 Draw 或 Impress 中如果只選取一個物件，則該物件的右邊緣會與頁面的右邊距對齊。
17 .uno%3AObjectAlignRight 90 所選物件的右邊緣對齊。在 Draw 或 Impress 中如果只選取一個物件，則該物件的右邊緣會與頁面的右邊距對齊。
f .uno%3AAlignTop 96 所選物件的上邊緣垂直對齊。在 Draw 或 Impress 中如果只選取一個物件，則該物件的上邊緣會與頁面的上邊距對齊。
e .uno%3AAlignUp 96 所選物件的上邊緣垂直對齊。在 Draw 或 Impress 中如果只選取一個物件，則該物件的上邊緣會與頁面的上邊距對齊。
1a .uno%3AAlignVerticalCenter 8d 將選取的物件垂直置中。如果僅選取了一個 Draw 或 Impress 物件，則該物件的中心會與頁面的垂直中心對齊。
12 .uno%3AAlignMiddle 8d 將選取的物件垂直置中。如果僅選取了一個 Draw 或 Impress 物件，則該物件的中心會與頁面的垂直中心對齊。
12 .uno%3AAlignBottom 96 垂直對齊所選物件的下邊緣。在 Draw 或 Impress 中如果只選取一個物件，則該物件的下邊緣將與頁面的下邊距對齊。
10 .uno%3AAlignDown 96 垂直對齊所選物件的下邊緣。在 Draw 或 Impress 中如果只選取一個物件，則該物件的下邊緣將與頁面的下邊距對齊。
f .uno%3ALeftPara 2a 將所選段落與頁面左邊距對齊。
10 .uno%3ARightPara 2a 將所選段落與頁面右邊距對齊。
11 .uno%3ACenterPara 21 在頁面上置中所選段落。
12 .uno%3AJustifyPara 9a 將選取的段落與左右頁面邊距對齊。您也可以視需要選擇 [格式] - [段落] - [對齊]，為段落的最後一行指定對齊選項。
11 .uno%3AMergeCells 3c 將選取的表格儲存格內容合併至單一儲存格。
10 .uno%3ASplitCell 60 依據所指定的儲存格數目，以水平或垂直方式分割儲存格或儲存格群組。
12 .uno%3ACellVertTop 2a 儲存格內容對齊儲存格上邊緣。
15 .uno%3ACellVertCenter 44 Centers the contents of the cell between top and bottom of the cell.
15 .uno%3ACellVertBottom 2a 儲存格內容對齊儲存格下邊緣。
b .uno%3ABold b7 將選取的文字設為粗體。如果游標位於字詞中，則整個字詞會變為粗體。如果所選文字或游標所在的字詞已經是粗體，則取消此粗體格式。
d .uno%3AItalic b4 將所選文字設為斜體。如果游標位於字詞中，則整個字詞會變為斜體。如果所選文字或游標所在的字詞已經是斜體，則取消此斜體格式。
16 .uno%3AUnderlineDouble 27 為所選文字標出雙線條底線。
10 .uno%3AUnderline 2d 為所選文字標出底線或移除底線。
10 .uno%3AStrikeout 63 為所選文字劃上刪除線，如果游標位於字詞中，則為整個字詞劃上刪除線。
f .uno%3AShadowed 5d 為所選文字加入陰影，如果游標位於字詞中，則為整個字詞加入陰影。
15 .uno%3ADistributeRows 45 將所選多列的高度調整為選擇範圍中最高列的高度。
11 .uno%3ASpacePara1 36 對目前段落套用單行行距。這是預設值。
12 .uno%3ASpacePara15 2d 將目前段落的行距設定為一行半。
11 .uno%3ASpacePara2 2a 將目前段落的行距設定為雙行。
18 .uno%3ADistributeColumns 45 將所選多欄的寬度調整為選擇範圍中最寬欄的寬度。
33 sfx2%3AComboBox%3ADLG_NEW_STYLE_BY_EXAMPLE%3ALB_COL 1b 輸入新樣式的名稱。
18 .uno%3AStyleNewByExample 1b 輸入新樣式的名稱。
29 sw%3AEdit%3ADLG_SWDLG_STRINPUT%3AED_INPUT 3c 輸入新自動格式的名稱，然後按一下 [確定]。
1b SD_HID_SD_NAMEDIALOG_OBJECT 42 輸入所選物件的名稱。[助手] 中即會顯示該名稱。
13 .uno%3ARenameObject 4c 為所選物件指定名稱，以便可迅速在 [助手] 中找出物件。
10 .uno%3ANameGroup 4c 為所選物件指定名稱，以便可迅速在 [助手] 中找出物件。
1e SW_HID_FORMAT_NAME_OBJECT_NAME 42 輸入所選物件的名稱。[助手] 中即會顯示該名稱。
32 cui%3AEdit%3ARID_SVXDLG_OBJECT_NAME%3ANTD_EDT_NAME 42 輸入所選物件的名稱。[助手] 中即會顯示該名稱。
1d .uno%3AObjectTitleDescription 75 為選取的物件指定標題及描述。當您匯出文件時，其可供協助工具存取並當成替代標籤。
39 cui%3AEdit%3ARID_SVXDLG_OBJECT_TITLE_DESC%3ANTD_EDT_TITLE 6f 輸入標題文字。此短名稱在 HTML 格式中會顯示為替代標籤。協助工具可讀取此文字。
41 cui%3AMultiLineEdit%3ARID_SVXDLG_OBJECT_TITLE_DESC%3ANTD_EDT_DESC bd 輸入描述文字。您可輸入長描述文字，向使用螢幕讀取器軟體的使用者描述複雜的物件或物件群組。此描述會顯示為適合協助工具的替代標籤。
11 .uno%3AFormatLine 24 設定所選線條的格式選項。
37 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMF_SYMBOL_HEIGHT 18 輸入圖示的高度。
39 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_LINE_WIDTH 78 選取線條的寬度。您可以附加定量單位。線寬度為零會產生寬度為輸出紙張一像素的細線。
10 .uno%3ALineWidth 78 選取線條的寬度。您可以附加定量單位。線寬度為零會產生寬度為輸出紙張一像素的細線。
30 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_EDGE_STYLE 91 Select the shape to be used at the corners of the line. In case of a small angle between lines, a mitered shape is replaced with a beveled shape.
36 cui%3AMenuButton%3ARID_SVXPAGE_LINE%3AMB_SYMBOL_BITMAP 2d 選取要在圖表中使用的圖示樣式。
11 CUI_HID_LINE_LINE 75 為選取的線條或要繪製的線條設定格式選項。還可以為線條加上箭頭或者變更圖表圖示。
37 cui%3ATriStateBox%3ARID_SVXPAGE_LINE%3ATSB_CENTER_START 33 將箭頭的中心置於所選線條的端點上。
38 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_END_WIDTH 18 輸入箭頭的寬度。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_LINE_TRANSPARENT 5b 輸入線條的透明度，其中 100% 表示完全透明，而 0% 表示完全不透明。
11 .uno%3AXLineStyle 21 選取要使用的線條樣式。
3a cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMTR_FLD_START_WIDTH 18 輸入箭頭的寬度。
2f cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_END_STYLE 2a 選取要套用至所選線條的箭頭。
33 cui%3ACheckBox%3ARID_SVXPAGE_LINE%3ACBX_SYNCHRONIZE 75 當您輸入不同的寬度、選取不同的箭頭樣式或置中箭頭時，系統自動更新兩個箭頭設定。
35 cui%3ATriStateBox%3ARID_SVXPAGE_LINE%3ATSB_CENTER_END 33 將箭頭的中心置於所選線條的端點上。
31 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_START_STYLE 2a 選取要套用至所選線條的箭頭。
11 .uno%3AXLineColor 18 選取線條的顏色。
2f cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_CAP_STYLE 52 Select the style of the line end caps. The caps are added to inner dashes as well.
33 cui%3ACheckBox%3ARID_SVXPAGE_LINE%3ACB_SYMBOL_RATIO 45 輸入新的高度值或寬度值時，保持圖示的比例不變。
36 cui%3AMetricField%3ARID_SVXPAGE_LINE%3AMF_SYMBOL_WIDTH 18 輸入圖示的寬度。
2b cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_COLOR 18 選取線條的顏色。
30 cui%3AListBox%3ARID_SVXPAGE_LINE%3ALB_LINE_STYLE 21 選取要使用的線條樣式。
34 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_MODIFY 7e 使用目前設定更新所選線條樣式。若要變更所選線條樣式的名稱，請在系統提示時輸入新名稱。
35 cui%3ANumericField%3ARID_SVXPAGE_LINE_DEF%3ANUM_FLD_2 3f 輸入您希望在序列中出現的點或破折號的次數。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_LENGTH_1 1b 輸入破折號的長度。
30 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_TYPE_2 2a 選取所需的破折號和點的組合。
30 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_TYPE_1 2a 選取所需的破折號和點的組合。
33 cui%3AImageButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_SAVE 3f 儲存目前的線條樣式清單，以備日後再次載入。
34 cui%3AListBox%3ARID_SVXPAGE_LINE_DEF%3ALB_LINESTYLES 24 選擇所要建立的線條樣式。
29 cui%3AEdit%3ARID_SVXDLG_NAME%3AEDT_STRING f 輸入名稱。
37 cui%3ACheckBox%3ARID_SVXPAGE_LINE_DEF%3ACBX_SYNCHRONIZE 2d 自動調整與線條長度有關的條目。
35 cui%3ANumericField%3ARID_SVXPAGE_LINE_DEF%3ANUM_FLD_1 3f 輸入您希望在序列中出現的點或破折號的次數。
33 cui%3AImageButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_LOAD 1e 匯入線條樣式的清單。
31 cui%3APushButton%3ARID_SVXPAGE_LINE_DEF%3ABTN_ADD 2d 使用目前設定建立新的線條樣式。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_DISTANCE 36 輸入點之間或破折號之間要留出的間隔。
3b cui%3AMetricField%3ARID_SVXPAGE_LINE_DEF%3AMTR_FLD_LENGTH_2 1b 輸入破折號的長度。
10 CUI_HID_LINE_DEF 30 編輯或建立虛線或點線的線條樣式。
35 cui%3AListBox%3ARID_SVXPAGE_LINEEND_DEF%3ALB_LINEENDS 3c 從清單方塊中選擇預先定義的箭頭樣式符號。
36 cui%3AImageButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_LOAD 1e 匯入箭頭樣式的清單。
34 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_ADD 66 若要定義一個自訂的箭頭樣式，請選取文件中的繪圖物件，然後按一下此處。
13 CUI_HID_LINE_ENDDEF 1e 編輯或建立箭頭樣式。
37 cui%3APushButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_MODIFY 24 變更所選箭頭樣式的名稱。
2f cui%3AEdit%3ARID_SVXPAGE_LINEEND_DEF%3AEDT_NAME 24 顯示所選箭頭樣式的名稱。
36 cui%3AImageButton%3ARID_SVXPAGE_LINEEND_DEF%3ABTN_SAVE 39 儲存目前的箭頭樣式清單，以備日後載入。
11 .uno%3AFormatArea 2a 設定所選繪圖物件的充填屬性。
37 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_Y_OFFSET 30 輸入平鋪點陣圖時採用的垂直偏移。
2c cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_BITMAP 2d 按一下要套用至所選物件的充填。
31 cui%3ARadioButton%3ARID_SVXPAGE_AREA%3ARBT_COLUMN 54 依輸入的偏移量，相對於點陣圖平鋪來垂直偏移原來的點陣圖。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_Y_SIZE 1b 輸入點陣圖的高度。
2b cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_COLOR 2d 按一下要套用至所選物件的充填。
32 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_STRETCH 59 拉伸點陣圖以充填所選物件。若要使用此功能，請清除[平鋪]方塊。
30 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_SCALE f8 依您在[寬度]和[高度]方塊中輸入的百分比值，相對於所選物件的大小重新調整點陣圖的顯示比例。清除此核取方塊，依照您在[寬度]和[高度]方塊中輸入的定量單位，變更所選物件的大小。
34 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_STEPCOUNT 45 自動確定調合漸層色圖案中兩端顏色時所需的步數。
10 .uno%3AFillStyle 36 選取要套用至所選繪圖物件的充填類型。
2e cui%3ARadioButton%3ARID_SVXPAGE_AREA%3ARBT_ROW 54 依輸入的偏移量，相對於點陣圖平鋪來水平偏移原來的點陣圖。
32 cui%3ACheckBox%3ARID_SVXPAGE_AREA%3ACB_HATCHBCKGRD 6c 將背景顏色套用至陰影線圖案。選取此核取方塊，然後按一下清單中的某種顏色。
2f cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_TILE 27 平鋪點陣圖以充填所選物件。
37 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_X_OFFSET 30 輸入平鋪點陣圖時採用的水平偏移。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_OFFSET 24 輸入列或欄的偏移百分比。
35 cui%3AMetricField%3ARID_SVXPAGE_AREA%3AMTR_FLD_X_SIZE 1b 輸入點陣圖的寬度。
33 cui%3ATriStateBox%3ARID_SVXPAGE_AREA%3ATSB_ORIGINAL 8d 充填所選物件時保留點陣圖的原來大小。若要變更點陣圖的大小，請清除此核取方塊，然後按一下 [相對]。
2e cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_HATCHING 2d 按一下要套用至所選物件的充填。
11 CUI_HID_AREA_AREA 2a 設定所選繪圖物件的充填選項。
36 cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_HATCHBCKGRDCOLOR 39 按一下要用作所選陰影線圖案背景的顏色。
39 cui%3ANumericField%3ARID_SVXPAGE_AREA%3ANUM_FLD_STEPCOUNT 3f 輸入調合漸層色圖案中兩端顏色時所需的步數。
2e cui%3AListBox%3ARID_SVXPAGE_AREA%3ALB_GRADIENT 2d 按一下要套用至所選物件的充填。
39 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_COLOR_FROM 5d 在 [從] 方塊中輸入顏色強度，其中，0% 對應黑色，100% 對應所選顏色。
33 cui%3AImageButton%3ARID_SVXPAGE_GRADIENT%3ABTN_LOAD 24 載入其他漸層色圖案清單。
33 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_GRADIENTS 39 選取所要套用或建立的漸層色圖案之類型。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_COLOR_TO 5d 在 [至] 方塊中輸入顏色強度，其中，0% 對應黑色，100% 對應所選顏色。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_CENTER_X a1 輸入漸層色圖案的水平偏移，其中 0% 對應漸層色圖案中端點顏色目前的水平位置。端點顏色是在 [至] 方塊中選取的顏色。
31 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_ADD 6f 將自訂漸層色圖案加入目前的清單中。指定漸層色圖案的屬性，然後按一下此按鈕。
32 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_COLOR_TO 27 選取漸層色圖案的最終顏色。
34 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_ANGLE 2d 輸入所選漸層色圖案的旋轉角度。
34 cui%3APushButton%3ARID_SVXPAGE_GRADIENT%3ABTN_MODIFY 87 將目前的漸層色圖案屬性套用至所選漸層色圖案中。如果需要，可以使用其他名稱儲存此漸層色圖案。
34 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_COLOR_FROM 27 選取漸層色圖案的起始顏色。
35 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_BORDER 70 輸入漸層色圖案的端點顏色區域要調整的量。端點顏色是在 [至] 方塊中選取的顏色。
33 cui%3AImageButton%3ARID_SVXPAGE_GRADIENT%3ABTN_SAVE 3c 儲存目前的漸層色圖案清單，以備日後載入。
37 cui%3AMetricField%3ARID_SVXPAGE_GRADIENT%3AMTR_CENTER_Y a1 輸入漸層色圖案的垂直偏移，其中 0% 對應漸層色圖案中端點顏色目前的垂直位置。端點顏色是在 [至] 方塊中選取的顏色。
38 cui%3AListBox%3ARID_SVXPAGE_GRADIENT%3ALB_GRADIENT_TYPES 27 選取所要套用的漸層色圖案。
15 CUI_HID_AREA_GRADIENT 4e 設定漸層色圖案的特性，或者儲存和載入漸層色圖案清單。
30 cui%3AImageButton%3ARID_SVXPAGE_HATCH%3ABTN_SAVE 3c 儲存目前陰影線圖案的清單，以備日後載入。
30 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_HATCHINGS 63 列出可用的陰影線圖案。按一下要採用的陰影線圖案，然後按一下 [確定]。
12 CUI_HID_AREA_HATCH 48 設定陰影線圖案的屬性，或者儲存和載入陰影線清單。
31 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_LINE_COLOR 21 選取陰影線線條的顏色。
14 CUI_HID_TPHATCH_CTRL 51 按一下該網格中的某個位置，可定義陰影線線條的旋轉角度。
31 cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_MODIFY 78 將目前的陰影線屬性採用至所選陰影線圖案中。如果需要，可以使用其他名稱儲存此圖案。
30 cui%3AImageButton%3ARID_SVXPAGE_HATCH%3ABTN_LOAD 27 載入其他的陰影線圖案清單。
35 cui%3AMetricField%3ARID_SVXPAGE_HATCH%3AMTR_FLD_ANGLE 57 輸入陰影線線條的旋轉角度，或者按一下角度網格中的一個位置。
2e cui%3APushButton%3ARID_SVXPAGE_HATCH%3ABTN_ADD 6f 將自訂陰影線圖案加入目前的清單中。指定陰影線圖案的屬性，然後按一下此按鈕。
30 cui%3AListBox%3ARID_SVXPAGE_HATCH%3ALB_LINE_TYPE 2a 選取要使用的陰影線線條類型。
38 cui%3AMetricField%3ARID_SVXPAGE_HATCH%3AMTR_FLD_DISTANCE 30 輸入陰影線線條之間要留出的間隔。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_IMPORT 78 找到要匯入的點陣圖，然後按一下 [開啟舊檔]。該點陣圖會加入至可用點陣圖清單的尾端。
38 cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_BACKGROUND_COLOR 27 選取點陣圖圖案的背景顏色。
2d cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_COLOR 54 選取一種前景顏色，然後在網格內按一下以將像素加入圖案中。
31 cui%3AImageButton%3ARID_SVXPAGE_BITMAP%3ABTN_SAVE 36 儲存目前的點陣圖清單，以備日後載入。
31 cui%3AImageButton%3ARID_SVXPAGE_BITMAP%3ABTN_LOAD 1e 載入其他點陣圖清單。
32 cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_MODIFY 8b 用目前的點陣圖圖案代替您在 [圖案編輯器] 中建立的點陣圖。如果需要，可以使用其他名稱儲存此圖案。
2f cui%3APushButton%3ARID_SVXPAGE_BITMAP%3ABTN_ADD 44 將您在[編輯圖案]中建立的點陣圖加入目前清單中。
13 CUI_HID_AREA_BITMAP 93 選取要作為充填圖案使用的點陣圖，或者建立自己的像素圖案。您也可以匯入點陣圖，儲存或載入點陣圖清單。
2f cui%3AListBox%3ARID_SVXPAGE_BITMAP%3ALB_BITMAPS 63 從清單中選取一個點陣圖，然後按一下 [確定]，將此圖案套用至所選物件。
38 cui%3ATriStateBox%3ARID_SVXPAGE_SHADOW%3ATSB_SHOW_SHADOW 24 為所選繪圖物件加入陰影。
34 cui%3AListBox%3ARID_SVXPAGE_SHADOW%3ALB_SHADOW_COLOR 18 選取陰影的顏色。
15 CUI_HID_TPSHADOW_CTRL 24 按一下要投射陰影的位置。
11 .uno%3AFillShadow c3 為所選物件加入陰影。如果物件已經具有陰影，則會移除此陰影。如果在未選取物件的情況下按一下此圖示，則會為您繪製的下一個物件加入陰影。
39 cui%3AMetricField%3ARID_SVXPAGE_SHADOW%3AMTR_FLD_DISTANCE 2a 輸入陰影偏移所選物件的間隔。
3f cui%3AMetricField%3ARID_SVXPAGE_SHADOW%3AMTR_SHADOW_TRANSPARENT 61 輸入 0% (不透明) 到 100% (透明) 之間的一個百分比，以指定陰影的透明度。
13 CUI_HID_AREA_SHADOW 45 在所選繪圖物件中加入陰影，並定義此陰影的屬性。
3d cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_ANGLE 27 輸入漸層色圖案的旋轉角度。
41 cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_GRADIENT 6f 將透明漸層色圖案套用至目前的充填顏色。選取此選項，然後設定漸層色圖案屬性。
43 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_START_VALUE 60 輸入漸層色圖案的起始透明值，其中 0% 為完全不透明，100% 為完全透明。
3f cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_LINEAR 82 開啟顏色透明度。選取此選項，然後在方塊中輸入數字，其中 0% 是完全不透明而 100% 是完全透明。
3c cui%3ARadioButton%3ARID_SVXPAGE_TRANSPARENCE%3ARBT_TRANS_OFF 18 關閉顏色透明度。
41 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_END_VALUE 60 輸入漸層色圖案的最終透明值，其中 0% 為完全不透明，100% 為完全透明。
40 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_CENTER_X 27 輸入漸層色圖案的水平偏移。
3e cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_BORDER 45 輸入漸層色圖案透明區域要調整的量。預設值為 0%。
3e cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRANSPARENT 61 調整目前充填顏色的透明度。輸入 0% (不透明) 和 100% (透明) 之間的數字。
40 cui%3AMetricField%3ARID_SVXPAGE_TRANSPARENCE%3AMTR_TRGR_CENTER_Y 27 輸入漸層色圖案的垂直偏移。
41 cui%3AListBox%3ARID_SVXPAGE_TRANSPARENCE%3ALB_TRGR_GRADIENT_TYPES 30 選取要套用的透明漸層色圖案類型。
39 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_FULL_WIDTH 48 Anchors the text to the full width of the drawing object or text object.
3c cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_WORDWRAP_TEXT 51 使連按兩下自訂形狀之後增加的文字換行以符合該自訂形狀。
3e cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_HEIGHT 4b 如果物件小於文字，則將物件的高度擴展為文字的高度。
3d cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_WIDTH 42 如果物件小於文字，擴充物件寬度到文字的寬度。
15 .uno%3ATextAttributes 4e 為所選繪圖物件或文字物件中的文字設定版式和鎖定特性。
39 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_BOTTOM 5d 輸入繪圖物件或文字物件的下邊緣與文字的下邊框之間要留出的間隔。
1d CUI_HID_TEXTATTR_CTL_POSITION 2a 按一下要放置文字標號的位置。
3c cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_AUTOGROW_SIZE 5d 重設自訂形狀的大小，使其符合您在連按兩下該形狀之後輸入的文字。
3a cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_FIT_TO_SIZE 4e 變更文字的大小，以適合繪圖物件或文字物件的整個區域。
15 CUI_HID_PAGE_TEXTATTR 4e 為所選繪圖物件或文字物件中的文字設定版式和鎖定特性。
36 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_TOP 5d 輸入繪圖物件或文字物件的上邊緣與文字的上邊框之間要留出的間隔。
38 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_RIGHT 5d 輸入繪圖物件或文字物件的右邊緣與文字的右邊框之間要留出的間隔。
37 cui%3AMetricField%3ARID_SVXPAGE_TEXTATTR%3AMTR_FLD_LEFT 5d 輸入繪圖物件或文字物件的左邊緣與文字的左邊框之間要留出的間隔。
36 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTATTR%3ATSB_CONTOUR 3f 調整換行和分頁，以符合所選繪圖物件的輪廓。
16 .uno%3ATransformDialog 45 變更所選物件大小，移動、旋轉或者傾斜所選物件。
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_WIDTH 1e 輸入所選物件的寬度。
35 cui%3AListBox%3ARID_SVXPAGE_POSITION_SIZE%3ALB_ORIENT 0 
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_POS_X 45 輸入物件相對於網格中所選基點要移動的水平距離。
3d cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_POS_Y 45 輸入物件相對於網格中所選基點要移動的垂直距離。
13 CUI_HID_TPSIZE_CTRL 74 按一下網格中的基點，然後在 [寬度] 方塊和 [高度] 方塊中輸入所選物件的新尺寸大小。
17 CUI_HID_TPPOSITION_CTRL b1 按一下網格中的基點，然後在 [位置 Y] 方塊和 [位置 X] 方塊中輸入物件相對於所選基點的移動量。這些基點相當於物件上的選擇控點。
3f cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_SIZEPROTECT 21 防止您變更物件的大小。
42 cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_AUTOGROW_WIDTH 39 變更文字的大小，以適合所選物件的寬度。
3e cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_POSPROTECT 2d 防止變更所選物件的位置或大小。
3e cui%3AMetricField%3ARID_SVXPAGE_POSITION_SIZE%3AMTR_FLD_HEIGHT 1e 輸入所選物件的高度。
43 cui%3ATriStateBox%3ARID_SVXPAGE_POSITION_SIZE%3ATSB_AUTOGROW_HEIGHT 39 變更文字的大小，以適合所選物件的高度。
35 cui%3AListBox%3ARID_SVXPAGE_POSITION_SIZE%3ALB_ANCHOR 0 
36 cui%3ACheckBox%3ARID_SVXPAGE_POSITION_SIZE%3ACBX_SCALE 36 變更所選物件的大小時，保持比例不變。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_ANGLE 2a 輸入所選物件需要旋轉的角度。
18 CUI_HID_TPROTATION_CTRL2 4c 按一下滑鼠按鈕，以指定與 45 度成倍數關係的旋轉角度。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_POS_Y 36 輸入從頁面上邊緣到中樞點的垂直距離。
35 cui%3AMetricField%3ARID_SVXPAGE_ANGLE%3AMTR_FLD_POS_X 36 輸入從頁面左邊緣到中樞點的水平距離。
13 CUI_HID_TRANS_ANGLE 18 旋轉選取的物件。
18 CUI_HID_TPROTATION_CTRL1 27 按一下要放置中樞點的位置。
36 cui%3AMetricField%3ARID_SVXPAGE_SLANT%3AMTR_FLD_RADIUS 36 輸入圓角化角點時所要使用的圓之半徑。
13 CUI_HID_TRANS_SLANT 3f 傾斜所選物件，或者將矩形物件的角點圓角化。
35 cui%3AMetricField%3ARID_SVXPAGE_SLANT%3AMTR_FLD_ANGLE 1b 輸入傾斜軸的角度。
30 cui%3ACheckBox%3ARID_SVXPAGE_CAPTION%3ACB_LAENGE 51 如果您要讓直接彎曲的角形線作最合適顯示，則按一下此處。
33 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_ANSATZ 4b 選取相對於圖說文字方塊延伸圖說文字線條的起始位置。
2f cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_ANSATZ 4b 選取相對於圖說文字方塊延伸圖說文字線條的起始位置。
18 CUI_HID_CAPTION_CTL_TYPE 3c 按一下所選圖說文字要套用的圖說文字樣式。
2f cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_WINKEL 3c 按一下所選圖說文字要套用的圖說文字樣式。
34 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_ABSTAND 4e 輸入圖說文字線條尾端與圖說文字方塊之間要留出的間隔。
33 cui%3AMetricField%3ARID_SVXPAGE_CAPTION%3AMF_LAENGE 57 輸入從圖說文字方塊延伸到線條彎曲點的圖說文字線條段之長度。
33 cui%3AListBox%3ARID_SVXPAGE_CAPTION%3ALB_ANSATZ_REL 4b 選取相對於圖說文字方塊延伸圖說文字線條的起始位置。
f .uno%3AFlipMenu 33 沿水平方向或垂直方向翻轉所選物件。
11 .uno%3AMirrorVert 30 沿垂直方向從上向下翻轉所選物件。
1b .uno%3AObjectMirrorVertical 30 沿垂直方向從上向下翻轉所選物件。
1d .uno%3AObjectMirrorHorizontal 30 沿水平方向從左向右翻轉所選物件。
11 .uno%3AMirrorHorz 30 沿水平方向從左向右翻轉所選物件。
17 .uno%3AArrangeFrameMenu 24 變更所選物件的堆疊順序。
12 .uno%3AArrangeMenu 24 變更所選物件的堆疊順序。
15 .uno%3AObjectPosition 24 變更所選物件的堆疊順序。
13 .uno%3ABringToFront 54 將選取的物件移至堆疊順序的頂部，使之位於其他物件的前面。
17 .uno%3AObjectForwardOne 45 將選取的物件上移一級，以更接近堆疊順序的頂層。
e .uno%3AForward 45 將選取的物件上移一級，以更接近堆疊順序的頂層。
f .uno%3ABackward 45 將選取的物件下移一級，以更接近堆疊順序的底層。
14 .uno%3AObjectBackOne 45 將選取的物件下移一級，以更接近堆疊順序的底層。
11 .uno%3ASendToBack 54 將選取的物件移至堆疊順序的底部，使之位於其他物件的背後。
1c .uno%3ASetObjectToForeground 27 將選取的物件移至文字之前。
1c .uno%3ASetObjectToBackground 27 將選取的物件移至文字背後。
11 .uno%3AAnchorMenu 24 設定所選物件的錨點選項。
16 .uno%3ASetAnchorToPage 2d 將選取的項目鎖定至目前的頁面。
16 .uno%3ASetAnchorToPara 2d 將選取的項目鎖定至目前的段落。
16 .uno%3ASetAnchorToCell 24 將所選項目鎖定至儲存格。
17 .uno%3ASetAnchorToFrame 2d 將選取的項目鎖定至周圍的框架。
16 .uno%3ASetAnchorToChar a5 在目前的文字中，將選取的項目作為字元鎖定。如果所選項目的高度大於目前的字型大小，則會增加包含此項目的行的高度。
16 .uno%3ASetAnchorAsChar a5 在目前的文字中，將選取的項目作為字元鎖定。如果所選項目的高度大於目前的字型大小，則會增加包含此項目的行的高度。
1d .uno%3AToggleObjectBezierMode 2d 可讓您變更所選繪圖物件的形狀。
21 SVX_HID_FONTWORK_TBI_SHADOW_SLANT 8a 為所選物件中的文字加入傾斜陰影。按一下此按鈕，然後在 [間隔 X] 和 [間隔 Y] 方塊中輸入陰影的尺寸。
1d SVX_HID_FONTWORK_TBI_SHOWFORM 36 顯示或隱藏文字基線或所選物件的邊緣。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_DISTANCE 42 輸入文字基線與各個字元底部之間要留出的間隔。
21 SVX_HID_FONTWORK_TBI_STYLE_ROTATE 3f 使用所選物件的上邊緣或下邊緣作為文字基線。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_SHADOW_X 39 輸入文字字元與陰影邊緣之間的水平間隔。
22 SVX_HID_FONTWORK_TBI_SHADOW_NORMAL 84 為所選物件中的文字加入陰影。按一下此按鈕，然後在 [間隔 X] 和 [間隔 Y] 方塊中輸入陰影的尺寸。
1c SVX_HID_FONTWORK_TBI_OUTLINE 30 顯示或隱藏文字中個別字元的邊框。
3b svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_TEXTSTART 54 輸入文字基線的起始位置與文字的起始位置之間要留出的間隔。
24 SVX_HID_FONTWORK_TBI_ADJUST_AUTOSIZE 39 變更文字的大小，以適合文字基線的長度。
22 SVX_HID_FONTWORK_TBI_ADJUST_CENTER 24 將文字在文字基線上置中。
1a SVX_HID_FONTWORK_CTL_FORMS 2a 按一下要用作文字基線的形狀。
36 svx%3AListBox%3ARID_SVXDLG_FONTWORK%3ACLB_SHADOW_COLOR 1e 選取文字陰影的顏色。
21 SVX_HID_FONTWORK_TBI_ADJUST_RIGHT 2a 將文字與文字基線的右端對齊。
1e SVX_HID_FONTWORK_TBI_STYLE_OFF 15 移除基線格式。
1f SVX_HID_FONTWORK_TBI_SHADOW_OFF 27 移除對文字套用的陰影效果。
21 SVX_HID_FONTWORK_TBI_STYLE_SLANTX 2d 水平方向傾斜文字物件中的字元。
20 SVX_HID_FONTWORK_TBI_ADJUST_LEFT 2a 將文字與文字基線的左端對齊。
3a svx%3AMetricField%3ARID_SVXDLG_FONTWORK%3AMTR_FLD_SHADOW_Y 39 輸入文字字元與陰影邊緣之間的垂直間隔。
22 SVX_HID_FONTWORK_TBI_ADJUST_MIRROR 93 反向換行和分頁方向，並水平方向或垂直方向翻轉文字。若要使用此指令，您必須先將不同的基線套用至文字。
21 SVX_HID_FONTWORK_TBI_STYLE_SLANTY 2d 垂直方向傾斜文字物件中的字元。
22 SVX_HID_FONTWORK_TBI_STYLE_UPRIGHT 66 將所選物件的上邊緣或下邊緣用作文字基線，保留個別字元原來的垂直對齊。
f .uno%3AFontWork 61 編輯已經使用先前 [美術字型] 對話方塊建立之選取物件的美術字型效果。
10 .uno%3AGroupMenu 5a 群組會與所選物件合在一起，以便將其作為單一物件移動或格式化。
12 .uno%3AFormatGroup 3c 分組所選物件，以便將其作為一個物件移動。
14 .uno%3AFormatUngroup 24 將所選群組分成個別物件。
11 .uno%3AEnterGroup 8d 開啟選取的群組，以編輯其中的各個物件。如果選取的群組中包含嵌套的群組，可以對子群組重複此指令。
11 .uno%3ALeaveGroup 42 退出群組，如此您無法再編輯群組中的個別物件。
38 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_LEFT 1b 從右向左捲動文字。
29 cui%3ATabPage%3ARID_SVXPAGE_TEXTANIMATION 33 為所選繪圖物件的文字加入動畫效果。
38 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_AUTO b2 $[officename] 自動確定重複此效果之前需要等待的時間。若要手動指定延遲時間，請清除此核取方塊，然後在[自動]方塊中輸入一個數值。
39 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_RIGHT 1b 從左向右捲動文字。
3f cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_STOP_INSIDE 2d 套用此效果後，會一直顯示文字。
3e cui%3AMetricField%3ARID_SVXPAGE_TEXTANIMATION%3AMTR_FLD_AMOUNT 2d 輸入捲動文字時要依據的遞增值。
35 cui%3AListBox%3ARID_SVXPAGE_TEXTANIMATION%3ALB_EFFECT 75 選取要對所選繪圖物件中的文字套用的動畫效果。若要移除動畫效果，請選取 [無效果]。
3b cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_ENDLESS 8c 連續播放動畫效果。若要指定播放此效果的次數，請清除此核取方塊，然後在[連續]方塊中輸入一個數值。
40 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_START_INSIDE 3c 套用此效果時，可以看到繪圖物件內的文字。
38 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_DOWN 1b 從上向下捲動文字。
36 cui%3AImageButton%3ARID_SVXPAGE_TEXTANIMATION%3ABTN_UP 1b 從下向上捲動文字。
3d cui%3AMetricField%3ARID_SVXPAGE_TEXTANIMATION%3AMTR_FLD_DELAY 33 輸入重複此效果之前需要等待的時間。
39 cui%3ATriStateBox%3ARID_SVXPAGE_TEXTANIMATION%3ATSB_PIXEL 24 以像素為單位定量遞增值。
3e cui%3ANumericField%3ARID_SVXPAGE_TEXTANIMATION%3ANUM_FLD_COUNT 2a 輸入重複播放動畫效果的次數。
1e DBACCESS_HID_BROWSER_ROWHEIGHT 27 變更目前列或所選列的高度。
10 .uno%3ARowHeight 27 變更目前列或所選列的高度。
2e sc%3ACheckBox%3ARID_SCDLG_ROW_MAN%3ABTN_DEFVAL a5 將列高調整為以預設範本為基礎的大小。現有的內容可能會以垂直剪裁顯示。當您輸入更大的內容時，高度不會再自動增加。
31 dbaccess%3AMetricField%3ADLG_ROWHEIGHT%3AMF_VALUE 1b 輸入要使用的列高。
2f sc%3AMetricField%3ARID_SCDLG_ROW_MAN%3AED_VALUE 1b 輸入要使用的列高。
31 dbaccess%3ACheckBox%3ADLG_ROWHEIGHT%3ACB_STANDARD a5 將列高調整為以預設範本為基礎的大小。現有的內容可能會以垂直剪裁顯示。當您輸入更大的內容時，高度不會再自動增加。
2f sc%3AMetricField%3ARID_SCDLG_COL_MAN%3AED_VALUE 1b 輸入要使用的欄寬。
2e sc%3ACheckBox%3ARID_SCDLG_COL_MAN%3ABTN_DEFVAL 2d 基於目前的字型，自動調整欄寬。
12 .uno%3AColumnWidth 27 變更目前欄或所選欄的寬度。
30 dbaccess%3ACheckBox%3ADLG_COLWIDTH%3ACB_STANDARD 2d 基於目前的字型，自動調整欄寬。
20 DBACCESS_HID_BROWSER_COLUMNWIDTH 27 變更目前欄或所選欄的寬度。
30 dbaccess%3AMetricField%3ADLG_COLWIDTH%3AMF_VALUE 1b 輸入要使用的欄寬。
3a cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_TXTSTACKED 15 垂直對齊文字。
11 CUI_HID_ALIGNMENT 3f 設定目前儲存格或所選儲存格內容的對齊選項。
36 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_SHRINK 8a 縮小字型的外觀大小，讓儲存格內容符合目前的儲存格寬度。您無法將此指令套用至包含換行的儲存格。
34 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_HYPH 30 對換行至下一行的字詞啟用連字符。
1a CUI_HID_ALIGNMENT_CTR_DIAL 30 在刻度盤中按一下以設定文字方向。
3e cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_ASIAN_VERTICAL f7 在選取的儲存格中將亞洲語言字元彼此向下對齊。如果此儲存格包含多行文字，這些行會轉換成從右向左排序的文字欄。轉換後文字中的西文字元會向右旋轉 90 度。亞洲語言字元不旋轉。
21 CUI_HID_ALIGNMENT_CTR_BORDER_LOCK 36 指定從儲存格哪一邊開始寫入旋轉文字。
33 cui%3AListBox%3ARID_SVXPAGE_ALIGNMENT%3ALB_VERALIGN 36 選取儲存格內容要採用的垂直對齊選項。
35 cui%3AMetricField%3ARID_SVXPAGE_ALIGNMENT%3AED_INDENT 33 依您輸入的量自儲存格的左邊緣縮排。
33 cui%3AListBox%3ARID_SVXPAGE_ALIGNMENT%3ALB_HORALIGN 36 選取儲存格內容要採用的水平對齊選項。
34 cui%3ATriStateBox%3ARID_SVXPAGE_ALIGNMENT%3ABTN_WRAP 4b 文字到達儲存格邊框時換行。行數取決於儲存格的寬度。
37 cui%3ANumericField%3ARID_SVXPAGE_ALIGNMENT%3ANF_DEGREES 66 輸入所選儲存格中文字的旋轉角度。正值向左旋轉文字，負值向右旋轉文字。
1c SVX_HID_GRID_NUMBEROFRECORDS a0 顯示記錄數目。例如，「記錄 7 / 9(2)」表示在含有 9 筆記錄的表格中選取了二筆記錄 (2)，而游標位於記錄編號 7 的位置。
18 SVX_HID_GRID_TRAVEL_PREV 24 移至表格中的上一筆記錄。
1c SVX_HID_GRID_TRAVEL_ABSOLUTE 3a 輸入您要顯示的記錄編號，然後按 Enter 鍵。
17 SVX_HID_GRID_TRAVEL_NEW 2a 將新的記錄插入目前的表格中。
1b DBACCESS_HID_CTL_TABBROWSER 6f 選取資料庫記錄。將列或儲存格拖放至文件，以插入內容。拖放欄首，以插入欄位。
18 SVX_HID_GRID_TRAVEL_NEXT 24 移至表格中的下一筆記錄。
19 SVX_HID_GRID_TRAVEL_FIRST 24 移至表格中的第一筆記錄。
18 SVX_HID_GRID_TRAVEL_LAST 27 移至表格中的最後一筆記錄。
20 DBACCESS_HID_BROWSER_TABLEFORMAT 18 格式化選取的列。
21 DBACCESS_HID_BROWSER_COLUMNFORMAT 18 格式化選取的欄。
f .uno%3AWindow3D 2b 指定目前文件中 3D 物件的特性。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_INVERT f 反轉光源。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_DOUBLE_SIDED 5d 關閉透過擠壓自由形線條 ([轉換] - [變成 3D]) 而建立的 3D 物件之形狀。
3a svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_PERCENT_DIAGONAL 3a 輸入圓角化所選 3D 物件各角點要採用的值。
2d svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_GEO 10e 調整所選 3D 物件的形狀。您僅能修改由平面物件轉換而成的 3D 物件之形狀。若要將某個平面物件轉換成 3D 物件，請選取該物件，按一下滑鼠右鍵，然後選擇 [轉換] - [變成 3D] 或 [轉換] - [變成 3D 旋轉體]。
33 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_END_ANGLE 2b 輸入所選 3D 旋轉體的旋轉角度。
33 svx%3ANumericField%3ARID_SVXFLOAT_3D%3ANUM_VERTICAL 3a 輸入在所選 3D 旋轉體中使用的垂直區段數。
2f svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_DEPTH 4a 輸入所選 3D 物件的突出深度。此選項對 3D 旋轉體無效。
3c svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TWO_SIDED_LIGHTING 85 從物件的外部和內部照明物件。若要使用環境光源，請按一下此按鈕，然後按一下 [反置法線] 按鈕。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_FLAT 1f 描繪多邊形的 3D 表面。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_OBJ 4f 依物件的形狀描繪 3D 表面。例如，將圓形描繪成球形表面。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_NORMALS_SPHERE 1c 描繪平滑的 3D 表面。
35 svx%3ANumericField%3ARID_SVXFLOAT_3D%3ANUM_HORIZONTAL 3a 輸入在所選 3D 旋轉體中使用的水平區段數。
33 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_BACKSCALE 34 輸入所選 3D 物件正面區域要增減的量。
2f svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_SLANT 2d 輸入陰影的投射角度 (0 到 90 度)。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_SHADOW_3D 2e 為選取的 3D 物件加入或移除陰影。
32 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_DISTANCE 3f 輸入照相機與所選物件中心之間要留出的距離。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_REPRESENTATION 3a 設定所選 3D 物件的加陰影選項和陰影選項。
36 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_FOCAL_LENGTH 63 輸入照相機的焦距。小焦距值對應「魚眼」鏡頭，大焦距值對應遠距鏡頭。
2e svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_SHADEMODE 117 選取要使用的陰影方法。平面陰影為物件表面上的單個多邊形指定單一顏色。郭氏陰影可調合各個多邊形中的顏色。芬氏陰影依每個像素週圍的像素來平均設定像素的顏色，這種陰影模式對處理能力的要求最高。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_8 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_5 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_2 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_AMBIENTLIGHT 1e 為環境光線選取顏色。
2c svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_LIGHT_1 21 為目前的光源選取顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_4 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_COLOR 1e 為環境光線選取顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_6 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
2f svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT 22 定義所選 3D 物件的光源。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_3 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_1 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_LIGHT_7 a3 按兩次以開啟光源，然後從清單中選取光的顏色。如果需要，您也可以從 [環境光線] 方塊中選取顏色來設定環境光的顏色。
34 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_FILTER 42 使紋理產生輕微的模糊效果，以移除多餘的斑點。
37 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_LUMINANCE 24 將紋理轉換成黑色和白色。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_PARALLEL_Y 2d 依平行於垂直軸的方向套用紋理。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_OBJECT_X 33 依據物件的形狀和大小自動調整紋理。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_COLOR 1b 將紋理轉換成彩色。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_CIRCLE_Y 30 垂直軸方向的紋理圖案呈圓形環繞。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_CIRCLE_X 30 水平軸方向的紋理圖案呈圓形環繞。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_MODULATE 73 套用有陰影的紋理。若要定義紋理的陰影選項，請按一下此對話方塊中的 [陰影] 按鈕。
36 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_OBJECT_Y 33 依據物件的形狀和大小自動調整紋理。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_PARALLEL_X 2d 依平行於水平軸的方向套用紋理。
31 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEXTURE f9 設定所選 3D 物件的表面紋理特性。只有對所選物件套用表面紋理後，才可使用此功能。若要快速套用表面紋理，請開啟 [畫廊]，按住 Shift+Ctrl (Mac：Shift+Command) 並將影像拖到所選 3D 物件上。
35 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_TEX_REPLACE 1e 套用沒有陰影的紋理。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_EMISSION_COLOR 21 選取物件要反射的顏色。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_EMISSION 27 選取裝飾該物件所需的顏色。
33 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_MAT_COLOR 27 選取裝飾該物件所需的顏色。
2e svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_COLOR 27 選取要套用至該物件的顏色。
40 svx%3AMetricField%3ARID_SVXFLOAT_3D%3AMTR_MAT_SPECULAR_INTENSITY 21 輸入發光點效果的強度。
38 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_SPECULAR_COLOR 21 輸入發光點效果的強度。
31 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_SPECULAR 21 選取物件要反射的顏色。
32 svx%3AImageButton%3ARID_SVXFLOAT_3D%3ABTN_MATERIAL 22 變更所選 3D 物件的顏色。
32 svx%3AListBox%3ARID_SVXFLOAT_3D%3ALB_MAT_FAVORITES 5f 選取預先定義的顏色圖案，或者選取[使用者自訂]，定義自訂顏色圖案。
38 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_TOP 42 分佈所選物件，使各物件的上邊緣之間間隔相等。
1a .uno%3ADistributeSelection 75 沿水平軸或垂直軸平均分佈三個或更多個所選物件。您也可以平均分佈物件之間的間隔。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_LEFT 42 分佈所選物件，使各物件的左邊緣之間間隔相等。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_NONE 18 未垂直分佈物件。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_BOTTOM 42 分佈所選物件，使各物件的下邊緣之間間隔相等。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_CENTER 45 分佈所選物件，使各物件的垂直中心之間間隔相等。
3b cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_CENTER 45 分佈所選物件，使各物件的水平中心之間間隔相等。
3d cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_DISTANCE 3c 水平分佈所選物件，使各物件之間間隔相等。
39 cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_NONE 18 未水平分佈物件。
3d cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_VER_DISTANCE 3c 垂直分佈所選物件，使各物件之間間隔相等。
3a cui%3ARadioButton%3ARID_SVXPAGE_DISTRIBUTE%3ABTN_HOR_RIGHT 42 分佈所選物件，使各物件的右邊緣之間間隔相等。
1c CUI_HID_SPLDLG_BUTTON_CHANGE 6d 將未知的字詞以目前的建議字詞替代，或者以您在 [字詞] 方塊中輸入的文字取代。
38 cui%3AMultiLineEdit%3ARID_SVXDLG_SPELLCHECK%3AED_NEWWORD 7c 顯示拼錯字詞反白顯示的句子。編輯字詞或者句子,或者在下面的文字方塊中按一下一個建議。
f .uno%3ASpelling 87 檢查文件或目前所選內容有無拼字錯誤。若已安裝文法檢查擴充軟體，則對話方塊還會檢查文法錯誤。
1f CUI_HID_SPLDLG_BUTTON_CHANGEALL 79 將所有出現的未知字詞以目前建議的字詞取代，或是以您在 [字詞] 方塊中輸入的文字取代。
1f CUI_HID_SPLDLG_BUTTON_IGNOREALL 4e 跳過整份文件中所有出現的未知字詞，然後繼續拼字檢查。
1f .uno%3ASpellingAndGrammarDialog 87 檢查文件或目前所選內容有無拼字錯誤。若已安裝文法檢查擴充軟體，則對話方塊還會檢查文法錯誤。
32 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_UNDO 6f 按一下即可還原目前句子中最後一個變更。再按一下可還原相同句子中前次的變更。
39 cui%3ACheckBox%3ARID_SVXDLG_SPELLCHECK%3ACB_CHECK_GRAMMAR 5b 啟用 [檢查文法] 可先處理所有拼字錯誤，然後再處理所有文法錯誤。
1c CUI_HID_SPLDLG_BUTTON_IGNORE 33 跳過未知的字詞，然後繼續拼字檢查。
35 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_OPTIONS 6f 開啟對話方塊，您可以在其中選取使用者定義的字典，還可以設定拼寫檢查的規則。
37 cui%3AMenuButton%3ARID_SVXDLG_SPELLCHECK%3AMB_ADDTODICT 45 將[字詞] 方塊中的文字加入所選使用者自訂字典中。
36 cui%3APushButton%3ARID_SVXDLG_SPELLCHECK%3APB_AUTOCORR 6f 開啟對話方塊，您可以在其中選取使用者定義的字典，還可以設定拼寫檢查的規則。
35 cui%3AListBox%3ARID_SVXDLG_SPELLCHECK%3ALB_SUGGESTION 79 列出建議的字詞，以取代 [字] 方塊中拼錯的字詞。選取要使用的字詞，然後按一下 [取代]。
20 CUI_HID_SPLDLG_BUTTON_IGNORERULE 61 執行文法檢查時，按一下 [忽略規則] 可忽略目前標記為文法錯誤的規則。
33 cui%3AListBox%3ARID_SVXDLG_SPELLCHECK%3ALB_LANGUAGE 78 指定要用於拼字檢查的語言。若要安裝或移除其他語言模組，請執行 $[officename] 安裝程式。
17 .uno%3AMoreDictionaries 36 在字典擴充軟體頁面開啟預設的瀏覽器。
13 .uno%3ALanguageMenu 39 開啟您可以選擇語言特定指令的子功能表。
18 .uno%3AChineseConversion 8a 將選取的中文從一個中文寫入系統轉換成另一個中文寫入系統。如果沒有選取文字，則會轉換整份文件。
2b svx%3AEdit%3ARID_SVXDLG_THESAURUS%3AED_REPL ac 當您按一下 [取代] 按鈕，[取代成] 文字方塊中的字詞或多個字將會取代為文件中的原始字詞。您也可以直接在此方塊中輸入文字。
33 svx%3AImageButton%3ARID_SVXDLG_THESAURUS%3ABTN_LEFT 34 叫出 [目前單字] 文字方塊先前的內容。
10 .uno%3AThesaurus 48 開啟對話方塊，以同義詞或相關字詞取代目前的字詞。
1c CUI_HID_CT_THES_ALTERNATIVES c0 按一下 [替代] 清單中的一個項目，將相關字詞複製到 [取代成] 文字方塊。按兩下項目，將相關字詞複製到 [目前單字] 文字方塊，並查閱該字詞。
16 .uno%3AThesaurusDialog 48 開啟對話方塊，以同義詞或相關字詞取代目前的字詞。
2f svx%3AComboBox%3ARID_SVXDLG_THESAURUS%3ACB_WORD a6 顯示目前的字詞，或是您在 [替代] 清單中按兩下點選之字行的相關字詞。您也可以直接在此方塊中輸入文字，查閱您的文字。
35 svx%3AMenuButton%3ARID_SVXDLG_THESAURUS%3AMB_LANGUAGE 21 選取同義詞詞典的語言。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_1 64 選取此核取方塊，以使用在 [替代] 方塊中指定的顏色代替目前的 [源顏色]。
e .uno%3ABmpMask 5f Opens the Color Replacer dialog, where you can replace colors in bitmap and meta file graphics.
2d svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_TRANS 3c 選取代替目前影像中透明區域要使用的顏色。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_3 99 列出可用的替代顏色。若要修改目前的顏色清單，請取消選取影像，選擇 [格式] - [區域]，然後按一下 [顏色] 標籤。
1a SVX_HID_BMPMASK_CTL_QCOL_1 af Displays the color in the selected image that you want to replace. To set the source color, click here, click the Color Replacer, and then click a color in the selected image.
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_4 64 選取此核取方塊，以使用在 [替代] 方塊中指定的顏色代替目前的 [源顏色]。
1b SVX_HID_BMPMASK_TBI_PIPETTE 7e 選取四個源顏色方塊中的其中一種。將滑鼠指標移至選取的影像上，然後按一下要替代的顏色。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_2 99 列出可用的替代顏色。若要修改目前的顏色清單，請取消選取影像，選擇 [格式] - [區域]，然後按一下 [顏色] 標籤。
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_1 99 列出可用的替代顏色。若要修改目前的顏色清單，請取消選取影像，選擇 [格式] - [區域]，然後按一下 [顏色] 標籤。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_3 c9 設定代替來源影像中的源顏色所使用的偏差。若要代替與所選顏色類似的顏色，請輸入較低的偏差值。若要代替較大範圍的顏色，請輸入較高的偏差值。
1b SVX_HID_BMPMASK_CTL_PIPETTE a5 Displays the color in the selected image that directly underlies the current mouse pointer position. This features only works if the Color Replacer tool is selected.
29 svx%3AListBox%3ARID_SVXDLG_BMPMASK%3ALB_4 99 列出可用的替代顏色。若要修改目前的顏色清單，請取消選取影像，選擇 [格式] - [區域]，然後按一下 [顏色] 標籤。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_1 c9 設定代替來源影像中的源顏色所使用的偏差。若要代替與所選顏色類似的顏色，請輸入較低的偏差值。若要代替較大範圍的顏色，請輸入較高的偏差值。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_4 c9 設定代替來源影像中的源顏色所使用的偏差。若要代替與所選顏色類似的顏色，請輸入較低的偏差值。若要代替較大範圍的顏色，請輸入較高的偏差值。
2f svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_TRANS 39 使用所選顏色代替目前影像中的透明區域。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_3 64 選取此核取方塊，以使用在 [替代] 方塊中指定的顏色代替目前的 [源顏色]。
2b svx%3ACheckBox%3ARID_SVXDLG_BMPMASK%3ACBX_2 64 選取此核取方塊，以使用在 [替代] 方塊中指定的顏色代替目前的 [源顏色]。
30 svx%3APushButton%3ARID_SVXDLG_BMPMASK%3ABTN_EXEC 55 用您在 [替代] 方塊中指定的顏色代替目前影像中選取的源顏色。
2d svx%3AMetricField%3ARID_SVXDLG_BMPMASK%3ASP_2 c9 設定代替來源影像中的源顏色所使用的偏差。若要代替與所選顏色類似的顏色，請輸入較低的偏差值。若要代替較大範圍的顏色，請輸入較高的偏差值。
15 .uno%3AAutoCorrectDlg 33 設定用於鍵入時自動代替文字的選項。
1c CUI_HID_OFAPAGE_AUTOCORR_CLB 4e 選取用於鍵入時自動校正錯誤的選項，然後按一下 [確定]。
20 CUI_HID_OFAPAGE_AUTOCORR_OPTIONS 4e 選取用於鍵入時自動校正錯誤的選項，然後按一下 [確定]。
1f CUI_HID_OFAPAGE_AUTOFMT_OPTIONS 4e 選取用於鍵入時自動校正錯誤的選項，然後按一下 [確定]。
36 cui%3APushButton%3ARID_OFAPAGE_AUTOFMT_APPLY%3APB_EDIT 2b 修改所選取的 [自動校正] 選項。
1e CUI_HID_OFAPAGE_AUTOFORMAT_CLB 4e 選取用於鍵入時自動校正錯誤的選項，然後按一下 [確定]。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_REPLACE%3ACB_TEXT_ONLY 85 儲存 [更替成] 方塊中的條目，但不儲存其格式。進行替代時，文字使用與文件中的文字相同的格式。
1f CUI_HID_OFACTL_AUTOCORR_REPLACE 152 列出在鍵入時自動代替字詞或縮寫的條目。若要加入條目，請在 [縮寫] 和 [更替成] 方塊中輸入文字，然後按一下 [新增]。若要編輯條目，請選取此條目，變更 [更替成] 方塊中的文字，然後按一下 [替代]。若要刪除條目，請選取此條目，然後按一下[刪除]。
40 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_REPLACE%3APB_NEW_REPLACE 2a 在替代表格中新增或代替條目。
36 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_REPLACE%3AED_REPLACE c2 輸入用來代替 [縮寫] 方塊中文字的替代文字、圖形、框架或 OLE 物件。如果在文件中已選取文字、圖形、框架或 OLE 物件，則此處已輸入相關資訊。
20 CUI_HID_OFAPAGE_AUTOCORR_REPLACE 4e 編輯用於自動校正或代替文件中的字詞或縮寫的替代表格。
34 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_REPLACE%3AED_SHORT 2d 輸入鍵入時要代替的字詞或縮寫。
39 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3AED_DOUBLE_CAPS c2 鍵入以兩個大寫字母開頭，且您不希望 $[officename] 將其變更為以一個大寫字母開頭的字詞或縮寫。例如，輸入 PC 以防止 $[officename] 將 PC 變更為 Pc。
19 CUI_HID_AUTOCORR_LANGUAGE 30 選取要建立或編輯替代規則的語言。
3a cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ACB_AUTOCAPS 137 將以兩個大寫字母開頭的縮寫或字詞自動加入到相應的例外清單中。只有在 [T] 欄中選取了 [校正字首的兩個大寫字母] 選項或 [設定每個句首由大寫字母開始] 選項，而這些選項都位於此對話方塊的 [選項] 標籤中，這個功能才能起作用。
1f CUI_HID_OFAPAGE_AUTOCORR_EXCEPT 45 指定不需要 $[officename] 自動校正的縮寫或字母組合。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_NEWABBREV 27 將目前條目加入例外清單中。
34 cui%3AEdit%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3AED_ABBREV aa 鍵入一個縮寫，後面加一個句點，然後按一下[新增]。這樣可防止 $[officename] 自動大寫縮寫結尾處句點之後出現的字詞之首字母。
37 cui%3AListBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ALB_ABBREV 24 列出沒有自動校正的縮寫。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ACB_AUTOABBREV 137 將以兩個大寫字母開頭的縮寫或字詞自動加入到相應的例外清單中。只有在 [T] 欄中選取了 [校正字首的兩個大寫字母] 選項或 [設定每個句首由大寫字母開始] 選項，而這些選項都位於此對話方塊的 [選項] 標籤中，這個功能才能起作用。
3c cui%3AListBox%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3ALB_DOUBLE_CAPS 8d 列出以兩個大寫字母開頭，且沒有自動校正的字詞或縮寫。此欄位中列出以兩個大寫字母開頭的所有字詞。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_EXCEPT%3APB_NEWDOUBLECAPS 27 將目前條目加入例外清單中。
3b cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_ENDQUOTE 87 選取 特殊字元，即會在選擇 [格式] - [自動校正] - [套用] 時，自動用該字元取代文件中目前的右引號。
1c CUI_HID_OFAPAGE_QUOTE_SW_CLB 4f 選擇在您輸入 [T] 或修改現有的文字 [M] 時，套用替代項目。
3a cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_STD 21 將引號重設為預設符號。
41 cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_STARTQUOTE 87 選取 特殊字元，即會在選擇 [格式] - [自動校正] - [套用] 時，自動用該字元取代文件中目前的左引號。
39 cui%3ACheckbox%3ARID_OFAPAGE_AUTOCORR_QUOTE%3ACB_SGL_TYPO 4b 使用您指定的特殊字元自動代替單引號的預設系統符號。
3a cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_DBL_STD 21 將引號重設為預設符號。
35 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCORR_QUOTE%3ACB_TYPO 4b 使用您指定的特殊字元自動代替單引號的預設系統符號。
3f cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_SGL_ENDQUOTE 87 選取 特殊字元，即會在選擇 [格式] - [自動校正] - [套用] 時，自動用該字元取代文件中目前的右引號。
3d cui%3APushButton%3ARID_OFAPAGE_AUTOCORR_QUOTE%3APB_STARTQUOTE 87 選取 特殊字元，即會在選擇 [格式] - [自動校正] - [套用] 時，自動用該字元取代文件中目前的左引號。
18 SW_HID_LINGU_IGNORE_WORD 3c 忽略目前文件中突出顯示的字詞之所有實例。
21 EDITENG_HID_EDITENG_SPELLER_START 22 開啟拼字檢查 對話方塊。
19 SW_HID_LINGU_SPELLING_DLG 22 開啟拼字檢查 對話方塊。
1a SW_HID_LINGU_WORD_LANGUAGE 60 如果在其他字典中找到了突出顯示的字詞，可以變更該字詞的語言設定。
27 EDITENG_HID_EDITENG_SPELLER_AUTOCORRECT a4 若要一律取代反白顯示的字詞，請按一下清單中的字詞。字詞組儲存在 [工具] - [自動校正選項] - [取代] 之下的取代清單中。
22 EDITENG_HID_EDITENG_SPELLER_IGNORE 3c 忽略目前文件中突出顯示的字詞之所有實例。
15 SW_HID_LINGU_ADD_WORD 39 將突出顯示的字詞加入使用者自訂字典中。
14 SW_HID_LINGU_REPLACE 64 按一下字詞可取代反白顯示的字。使用 [自動校正] 子功能表可以永久取代。
15 SW_HID_LINGU_AUTOCORR a4 若要一律取代反白顯示的字詞，請按一下清單中的字詞。字詞組儲存在 [工具] - [自動校正選項] - [取代] 之下的取代清單中。
1a SW_HID_LINGU_PARA_LANGUAGE 6c 如果在其他字典中找到了突出顯示的字詞，可以變更該字詞所在段落的語言設定。
23 EDITENG_HID_EDITENG_SPELLER_ADDWORD 39 將突出顯示的字詞加入使用者自訂字典中。
42 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_REMOVE_LIST ec 啟用時，若關閉目前的文件，則會清除該清單。若未啟用，可讓目前的「自動完成字詞」清單於關閉目前檔案之後，可供其他文件使用。清單在您結束 %PRODUCTNAME 之前可一直使用。
46 cui%3ANumericField%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ANF_MAX_ENTRIES 46 輸入要在 [自動完成字詞] 清單中儲存的最大字詞數。
41 cui%3AListBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ADCB_EXPAND_KEY 33 選取要用於接受自動完成字詞的按鍵。
42 cui%3AMultiListBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ALB_ENTRIES d8 列出收集的字詞。在您關閉目前的文件之前，此清單皆有效。若要讓目前階段作業中的其他文件可以使用此清單，請停用 [在關閉文件時，從清單移除收集的字詞]。
3e cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_COLLECT 94 將常用字詞加入清單中。若要從 [自動完成字詞] 清單中移除一個字詞，請選取該字詞，然後按一下 [刪除項目]。
3d cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_AS_TIP 2a 將完成的字詞顯示成提示說明。
3c cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_ACTIV 72 儲存常用字詞，並在鍵入符合所儲存字詞的前三個字母的三個字母之後自動完成字詞。
40 cui%3APushButton%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3APB_ENTRIES 40 從 [自動完成字詞] 清單中移除所選單字或字詞。
43 cui%3ACheckBox%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ACB_APPEND_SPACE 47 如果您在字詞後未加入標點，$[officename] 會加入空格。
46 cui%3ANumericField%3ARID_OFAPAGE_AUTOCOMPLETE_OPTIONS%3ANF_MIN_WORDLEN 3c 輸入執行自動完成字詞功能的最小字詞長度。
36 SVX_CHECKBOX_RID_OFAPAGE_SMARTTAG_OPTIONS_CB_SMARTTAGS 3f 啟用智慧標籤在文字文件中的計算與顯示功能。
38 SVX_PUSHBUTTON_RID_OFAPAGE_SMARTTAG_OPTIONS_PB_SMARTTAGS 7e 若要配置智慧標籤，請選取智慧標籤的名稱，然後按一下 [特性]。並非所有智慧標籤皆可配置。
14 .uno%3AOutlineBullet 60 為目前段落加入編號或項目符號，並允許您編輯編號或項目符號的格式。
20 .uno%3ABulletsAndNumberingDialog 60 為目前段落加入編號或項目符號，並允許您編輯編號或項目符號的格式。
17 CUI_HID_VALUESET_BULLET 2a 按一下要使用的項目符號樣式。
1a CUI_HID_VALUESET_SINGLENUM 24 按一下要使用的編號樣式。
14 CUI_HID_VALUESET_NUM 24 按一下要使用的大綱樣式。
17 CUI_HID_VALUESET_NUMBMP 2a 按一下要用為項目符號的圖形。
31 cui%3ACheckBox%3ARID_SVXPAGE_PICK_BMP%3ACB_LINKED 5d 若加以啟用，則圖形會插入為連結。若未啟用，則圖形會內嵌至文件。
3b cui%3ANumericField%3ARID_SVXPAGE_NUM_OPTIONS%3ANF_ALL_LEVEL 24 輸入目前級的新起始編號。
30 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_FMT 21 選取所選級的編號樣式。
36 cui%3APushButton%3ARID_SVXPAGE_NUM_OPTIONS%3APB_BULLET 4a 開啟 [特殊字元]  對話方塊，您可以選取項目符號圖示。
33 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_ORIENT 1e 選取圖形的對齊選項。
33 cui%3ACheckBox%3ARID_SVXPAGE_NUM_OPTIONS%3ACB_RATIO 1e 保持圖形的大小比例。
36 cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_WIDTH 18 輸入圖形的寬度。
36 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_BUL_COLOR 24 選取目前編號樣式的顏色。
3d cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_BUL_REL_SIZE 51 輸入相對於目前段落的字型高度變更項目符號字元大小的量。
37 cui%3AMultiListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_LEVEL 24 選取要定義格式選項的級。
37 cui%3AMetricField%3ARID_SVXPAGE_NUM_OPTIONS%3AMF_HEIGHT 18 輸入圖形的高度。
34 cui%3AListBox%3ARID_SVXPAGE_NUM_OPTIONS%3ALB_CHARFMT 24 輸入目前級的新起始編號。
36 cui%3AMenuButton%3ARID_SVXPAGE_NUM_OPTIONS%3AMB_BITMAP 42 選取圖形，否則尋找要用作項目符號的圖形檔案。
37 cui%3ANumericField%3ARID_SVXPAGE_NUM_OPTIONS%3AED_START 24 輸入目前級的新起始編號。
30 cui%3AEdit%3ARID_SVXPAGE_NUM_OPTIONS%3AED_SUFFIX 92 輸入要在清單中號碼之後顯示的字元或文字。如果要建立使用樣式「1.)」的編號清單，請在方塊中輸入「.)」。
30 cui%3AEdit%3ARID_SVXPAGE_NUM_OPTIONS%3AED_PREFIX 3c 輸入要在清單中號碼之前顯示的字元或文字。
2e sw%3AMetricField%3ATP_NUM_POSITION%3AMF_INDENT 48 輸入編號符號左邊緣與文字左邊緣之間要留出的間隔。
29 sw%3AListBox%3ATP_NUM_POSITION%3ALB_ALIGN e4 設定編號符號的對齊方式。選取 [左] 可在「對齊」位置處對齊編號符號行首。選取 [右] 可在「對齊」位置之前對齊符號行尾。選取 [置中] 則可沿著「對齊」位置置中符號。
33 cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_ALIGN e4 設定編號符號的對齊方式。選取 [左] 可在「對齊」位置處對齊編號符號行首。選取 [右] 可在「對齊」位置之前對齊符號行尾。選取 [置中] 則可沿著「對齊」位置置中符號。
2e sw%3AMultiListBox%3ATP_NUM_POSITION%3ALB_LEVEL 1b 選取要修改的層級。
38 cui%3AMultiListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_LEVEL 1b 選取要修改的層級。
39 cui%3APushButton%3ARID_SVXPAGE_NUM_POSITION%3APB_STANDARD 2d 將縮排值和間隔值重設為預設值。
35 cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_ALIGN_2 e4 設定編號符號的對齊方式。選取 [左] 可在「對齊」位置處對齊編號符號行首。選取 [右] 可在「對齊」位置之前對齊符號行尾。選取 [置中] 則可沿著「對齊」位置置中符號。
3c cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_BORDERDIST c1 輸入左頁面邊距 (或文字物件左邊緣) 與編號符號左邊緣之間的間隔量。若目前的段落樣式使用縮排，便會將您在此輸入的數量累加到縮排設定中。
3c cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_ALIGNED_AT 3c 輸入從左頁邊距到編號符號對齊位置的間隔。
39 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_LISTTAB 63 若您選取在編號之後緊接著定位鍵，即可輸入非負值，做為定位鍵的位置。
3f cui%3AListBox%3ARID_SVXPAGE_NUM_POSITION%3ALB_LABEL_FOLLOWED_BY 54 選取將緊接在編號之後的元素：定位鍵、空白或沒有任何元素。
37 cui%3ACheckBox%3ARID_SVXPAGE_NUM_POSITION%3ACB_RELATIVE 39 相對於清單階層中的上一級來縮排目前級。
2f sw%3APushButton%3ATP_NUM_POSITION%3APB_STANDARD 2d 將縮排值和間隔值重設為預設值。
2f sw%3AMetricField%3ATP_NUM_POSITION%3AMF_NUMDIST 2d 將縮排值和間隔值重設為預設值。
3b cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_INDENT_AT 51 輸入從左頁邊距到已編號段落第一行之後的所有行首之間隔。
38 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_INDENT 48 輸入編號符號左邊緣與文字左邊緣之間要留出的間隔。
39 cui%3AMetricField%3ARID_SVXPAGE_NUM_POSITION%3AMF_NUMDIST 2d 將縮排值和間隔值重設為預設值。
2d sw%3ACheckBox%3ATP_NUM_POSITION%3ACB_RELATIVE 39 相對於清單階層中的上一級來縮排目前級。
32 sw%3AMetricField%3ATP_NUM_POSITION%3AMF_BORDERDIST c1 輸入左頁面邊距 (或文字物件左邊緣) 與編號符號左邊緣之間的間隔量。若目前的段落樣式使用縮排，便會將您在此輸入的數量累加到縮排設定中。
39 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_RENAME 45 開啟對話方塊，在此您可以變更所選程式檔的名稱。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_APPEND 66 尋找要加入到目前清單中的 $[officename] Basic 程式庫，然後按一下 [開啟舊檔]。
2f basctl%3AEdit%3ARID_DLG_NEWLIB%3ARID_ED_LIBNAME 27 輸入新程式庫或模組的名稱。
31 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_NEWLIB 18 建立新的程式庫。
31 basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_NEWDLG 2d 開啟編輯器並建立新的對話方塊。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_NEWLIB 30 將記錄的巨集儲存在新的程式庫中。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_DEL 2d 建立新巨集，或刪除選取的巨集。
37 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_EDIT 33 開啟您作業系統的預設程式檔編輯器。
36 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_RUN 54 若要執行程式檔，請在清單中選擇程式檔，然後按一下 [執行]。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_ORG 82 開啟 [管理巨集] 對話方塊，您可以在其中新增、編輯或刪除現有的巨集模組、對話方塊和程式庫。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_NEWMOD 2d 將記錄的巨集儲存在新的模組中。
33 basctl%3AEdit%3ARID_MACROCHOOSER%3ARID_ED_MACRONAME 60 顯示所選巨集的名稱。若要建立或變更巨集的名稱，請在此處輸入名稱。
33 basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_PASSWORD 2a 指定或編輯所選程式庫的密碼。
1d BASCTL_HID_BASICIDE_LIBS_TREE 51 列出用於目前應用程式和所有已開啟文件的現有巨集程式庫。
31 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_EDIT 33 開啟所選巨集或對話方塊以進行編輯。
20 BASCTL_HID_BASICIDE_MODULES_TREE 27 列出現存的巨集和對話方塊。
36 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_ASSIGN 73 開啟 [自訂] 對話方塊，您可以在其中將選取的巨集指定到功能表指令、工具列或事件。
18 BASCTL_HID_BASICIDE_LIBS a5 列出可以在其中開啟或儲存巨集的程式庫和模組。若要將巨集與特定的文件一起儲存，請開啟此文件，然後開啟此對話方塊。
12 CUI_HID_SCRIPTSBOX 90 從「使用者」、「共用」或開啟的文件中選取巨集或程序檔。若要檢視可用的巨集或程序檔，請按兩下項目。
2e basctl%3AListBox%3ARID_TP_LIBS%3ARID_LB_BASICS 42 選取要管理的巨集程式庫所在的應用程式或文件。
39 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_CREATE 18 建立新的程式檔。
1a BASCTL_HID_BASICIDE_MACROS 46 列出 [巨集的來源] 清單中已選取模組所包含的巨集。
2f basctl%3APushButton%3ARID_TP_LIBS%3ARID_PB_EDIT 48 開啟 $[officename] Basic 編輯器，以便您修改所選程式庫。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_NEWDLG 2d 開啟編輯器並建立新的對話方塊。
2f basctl%3APushButton%3ARID_TP_DLGS%3ARID_PB_EDIT 5d 啟動 $[officename] Basic 編輯器，並開啟所選巨集或對話方塊以進行編輯。
12 .uno%3AMacroDialog 24 開啟對話方塊以管理巨集。
36 cui%3APushButton%3ARID_DLG_SCRIPTORGANIZER%3ASF_PB_DEL 24 提示您刪除所選的程式檔。
33 basctl%3APushButton%3ARID_TP_MODULS%3ARID_PB_NEWMOD 27 開啟編輯器並建立新的模組。
33 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_RUN 21 執行或儲存目前的巨集。
34 basctl%3APushButton%3ARID_MACROCHOOSER%3ARID_PB_EDIT 5d 啟動 $[officename] Basic 編輯器，並開啟所選巨集或對話方塊以進行編輯。
15 .uno%3AMacroSignature 5a 在巨集中增加或移除數位簽名。您也可以使用對話方塊來檢視憑證。
27 .uno%3AMacroOrganizer%3FTabId%3Ashort=1 38 開啟 [巨集編組器] 的 [對話方塊] 標籤頁。
f .uno%3ARunMacro 2a 開啟可以啟動巨集的對話方塊。
14 .uno%3AStopRecording 15 停止錄製巨集。
14 .uno%3AMacroRecorder 12 錄製新巨集。
10 SVX_HID_PASSWORD 27 以密碼保護所選取的程式庫。
30 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_NEW_PASSWD 24 輸入所選程式庫的新密碼。
30 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_OLD_PASSWD 27 輸入所選程式庫目前的密碼。
33 svx%3AEdit%3ARID_SVXDLG_PASSWORD%3AED_REPEAT_PASSWD 2a 重新輸入所選程式庫的新密碼。
44 .uno%3AScriptOrganizer%3FScriptOrganizer.Language%3Astring=BeanShell 33 開啟對話方塊，您可在此組織程式檔。
16 .uno%3AScriptOrganizer 57 開啟包含對話方塊連結的子功能表，您可在此組織巨集和程式檔。
45 .uno%3AScriptOrganizer%3FScriptOrganizer.Language%3Astring=JavaScript 33 開啟對話方塊，您可在此組織程式檔。
2e basctl%3ACheckBox%3ARID_DLG_LIBS%3ARID_CB_REPL 39 替代與目前程式庫具有相同名稱的程式庫。
2d basctl%3ACheckBox%3ARID_DLG_LIBS%3ARID_CB_REF 71 將所選取程式庫作為唯讀檔案增加。每次啟動 %PRODUCTNAME 時，都會重新載入該程式庫。
20 BASCTL_HID_BASICIDE_LIBSDLG_TREE 5a 輸入想要附加之程式庫的名稱或路徑。您也可由清單中選取程式庫。
16 .uno%3AConfigureDialog 54 自訂 $[officename] 功能表、組合鍵、工具列和為事件指定的巨集。
12 .uno%3ALoadToolBox 54 自訂 $[officename] 功能表、組合鍵、工具列和為事件指定的巨集。
2d cui%3AListBox%3ARID_SVXPAGE_MENUS%3ALB_SAVEIN 39 選取應用程式或開啟文件以便增加功能表。
19 CUI_HID_SELECTOR_COMMANDS 5e 選取任何指令，然後按一下 [增加]，或將指令拖放至 [自訂] 對話方塊。
35 cui%3AImageButton%3AMD_MENU_ORGANISER%3ABTN_MENU_DOWN 45 在 [功能表項目]清單中，將所選項目下移一個位置。
37 cui%3APushButton%3ARID_SVXPAGE_MENUS%3ABTN_ADD_COMMANDS 83 開啟 [增加指令] 對話方塊。選取任何指令，然後按一下 [增加]，或將指令拖放至 [自訂] 對話方塊。
31 cui%3AMenuButton%3ARID_SVXPAGE_MENUS%3ABTN_CHANGE 2a 開啟具有其他指令的子功能表。
1b CUI_HID_SELECTOR_CATEGORIES 5e 選取任何指令，然後按一下 [增加]，或將指令拖放至 [自訂] 對話方塊。
2c cui%3AListBox%3ARID_SVXPAGE_MENUS%3ALB_MENUS 2d 選取要編輯的功能表和子功能表。
37 cui%3AMenuButton%3ARID_SVXPAGE_MENUS%3ABTN_CHANGE_ENTRY 27 開啟包含其他指令的功能表。
33 cui%3AImageButton%3AMD_MENU_ORGANISER%3ABTN_MENU_UP 24 將所選項目上移一個位置。
2e cui%3APushButton%3ARID_SVXPAGE_MENUS%3ABTN_NEW 6d 開啟 [新功能表] 對話方塊，您可在此輸入新功能表的名稱，以及選取功能表位置。
2f cui%3AEdit%3AMD_MENU_ORGANISER%3AEDIT_MENU_NAME 79 輸入功能表的名稱。若要指定名稱中的字母作為加速鍵，請在該字母前面輸入波浪符號 (~)。
1f CUI_HID_SVX_CONFIG_MENU_LISTBOX 6c 按一下箭頭按鈕，可將所選取的功能表項目在功能表中向上或向下移動一個位置。
34 cui%3ARadioButton%3ARID_SVXPAGE_KEYBOARD%3ARB_OFFICE 3f 顯示所有 $[officename] 應用程式所共用的組合鍵。
33 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_RESET 27 將修改的數值重設回預設值。
38 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_ACC_REMOVE 36 刪除一或多個選取的元素時不需要確認。
1f CUI_HID_CONFIGGROUP_ACC_LISTBOX 58 列出可用的功能分類。若要指定樣式的捷徑。請開啟 [樣式] 分類。
1b CUI_HID_ACCELCONFIG_LISTBOX a6 列出組合鍵及相關指令。若要指定或修改 [功能] 清單中所選指令的組合鍵，請按一下此清單中的組合鍵，然後按一下 [修改]。
38 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_ACC_CHANGE 59 將 [組合鍵] 清單中選取的組合鍵指定給 [功能] 清單中選取的指令。
22 CUI_HID_CONFIGFUNCTION_ACC_LISTBOX d1 選取您要為其指定組合鍵的功能，在 [組合鍵] 清單中按一下按鍵組合，然後按一下 [修改]。如果所選的功能已具有組合鍵，則該組合鍵會顯示在 [鍵] 清單中。
14 CUI_HID_CONFIG_ACCEL 57 指定或編輯用於 $[officename] 指令或 $[officename] Basic 巨集的組合鍵。
32 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_LOAD 3c 用先前儲存的組合鍵配置代替該組合鍵配置。
32 cui%3APushButton%3ARID_SVXPAGE_KEYBOARD%3ABTN_SAVE 36 儲存目前的組合鍵配置，以備日後載入。
34 cui%3ARadioButton%3ARID_SVXPAGE_KEYBOARD%3ARB_MODULE 3c 顯示用於目前 $[officename] 應用程式的組合鍵。
17 CUI_HID_SVX_NEW_TOOLBAR 6a 開啟 [名稱] 對話方塊，您可於此輸入新工具列的名稱，並選取新工具列的位置。
1a CUI_HID_SVX_MODIFY_TOOLBAR 36 [工具列] 按鈕會以下列指令開啟子功能表
13 CUI_HID_SVX_SAVE_IN 2d 選取載入配置與儲存配置的位置。
1f CUI_HID_SVX_MODIFY_TOOLBAR_ITEM 33 [修改] 按鈕會以下列指令開啟子功能表
1c CUI_HID_SVX_NEW_TOOLBAR_ITEM 83 開啟 [增加指令] 對話方塊。選取任何指令，然後按一下 [增加]，或將指令拖曳至 [自訂] 對話方塊。
2e cui%3AImageButton%3ARID_SVXPAGE_MENUS%3ABTN_UP 27 將選取的項目在清單中上移。
30 cui%3AImageButton%3ARID_SVXPAGE_MENUS%3ABTN_DOWN 27 將選取的項目在清單中下移。
2f cui%3APushButton%3AMD_ICONSELECTOR%3ABTN_IMPORT 9b 將新圖示加入圖示清單。隨即顯示檔案開啟對話方塊，您可將選取的一或多個圖示匯入 %PRODUCTNAME 的內部圖示目錄中。
2f cui%3APushButton%3AMD_ICONSELECTOR%3ABTN_DELETE 57 按一下可從清單中移除選取的圖示。僅可移除使用者定義的圖示。
31 cui%3APushButton%3ARID_SVXPAGE_EVENTS%3APB_ASSIGN 3a 開啟 [巨集選擇器] 指定巨集給選取的事件。
2e cui%3AListBox%3ARID_SVXPAGE_EVENTS%3ALB_SAVEIN 50 首先選取儲存事件連結的位置，在目前文件或 %PRODUCTNAME 中。
36 cui%3APushButton%3ARID_SVXPAGE_MACROASSIGN%3APB_DELETE 24 刪除所選事件的巨集指定。
31 cui%3APushButton%3ARID_SVXPAGE_EVENTS%3APB_DELETE 24 刪除所選事件的巨集指定。
12 .uno%3ATableEvents 6d Assigns macros to program events. The assigned macro runs automatically every time the selected event occurs.
1a CUI_HID_SVX_MACRO_LB_EVENT b2 大清單方塊將列出事件與指定的巨集。您在 [儲存位置] 清單方塊中選取位置之後，請在大清單方塊選取事件。然後按一下 [指定巨集]。
36 cui%3APushButton%3ARID_SVXPAGE_MACROASSIGN%3APB_ASSIGN 3a 開啟 [巨集選擇器] 指定巨集給選取的事件。
1a FILTER_HID_XML_FILTER_TEST 30 開啟具有所選檔案名稱的對話方塊。
1c FILTER_HID_XML_FILTER_DELETE 51 在您對隨後出現的對話方塊進行確認後，即可刪除所選檔案。
1c .uno%3AOpenXMLFilterSettings 88 開啟 [XML 篩選設定] 對話方塊，您可以在此處建立、編輯、刪除和測試用於匯入和匯出 XML 檔案的篩選。
25 FILTER_HID_XML_FILTER_SETTINGS_DIALOG 24 顯示此對話方塊的說明頁。
1a FILTER_HID_XML_FILTER_OPEN 59 顯示[開啟舊檔]對話方塊，從 XSLT 篩選套裝軟體 (*.jar) 中開啟篩選。
1a FILTER_HID_XML_FILTER_SAVE 60 顯示 [另存新檔] 對話方塊，將所選檔案儲存為 XSLT 篩選套裝軟體 (*.jar)。
19 FILTER_HID_XML_FILTER_NEW 2d 開啟具有新篩選名稱的對話方塊。
1b FILTER_HID_XML_FILTER_CLOSE 15 關閉對話方塊。
1a FILTER_HID_XML_FILTER_LIST 42 選取一個或多個篩選，然後按一下其中一個按鈕。
1a FILTER_HID_XML_FILTER_EDIT 30 開啟具有所選檔案名稱的對話方塊。
1f FILTER_HID_XML_FILTER_EXTENSION 83 輸入您在未指定篩選而開啟檔案時，所使用的副檔名。$[officename] 會以副檔名決定所要使用的篩選。
24 FILTER_HID_XML_FILTER_INTERFACE_NAME 4f 輸入檔案對話方塊中的 [檔案類型] 方塊內所要顯示的名稱。
1a FILTER_HID_XML_FILTER_NAME 50 輸入 [XML 篩選設定] 對話方塊的清單方塊內所要顯示的名稱。
21 FILTER_HID_XML_FILTER_DESCRIPTION 27 輸入註解 (可選擇使用與否)。
21 FILTER_HID_XML_FILTER_APPLICATION 30 選取要與篩選配合使用的應用程式。
23 FILTER_HID_XML_FILTER_TABPAGE_BASIC 2c 輸入或編輯 XML 篩選的一般資訊。
25 FILTER_HID_XML_FILTER_IMPORT_TEMPLATE 5f 輸入您要用於匯入的範本名稱。在範本中，定義了顯示 XML 標記的樣式。
28 FILTER_HID_XML_FILTER_EXPORT_XSLT_BROWSE 27 開啟一個檔案選擇對話方塊。
19 FILTER_HID_XML_FILTER_DTD 5f 如果需要，則輸入您要使用的 DTD (文件類型定義) 公用標誌或系統標誌。
21 FILTER_HID_XML_FILTER_EXPORT_XSLT 63 如果這是一個匯出篩選，則請輸入您要用於匯出的 XSLT 樣式表之檔案名稱。
2c FILTER_HID_XML_FILTER_IMPORT_TEMPLATE_BROWSE 27 開啟一個檔案選擇對話方塊。
20 FILTER_HID_XML_FILTER_DTD_BROWSE 27 開啟一個檔案選擇對話方塊。
28 FILTER_HID_XML_FILTER_IMPORT_XSLT_BROWSE 27 開啟一個檔案選擇對話方塊。
1d FILTER_HID_XML_FILTER_DOCTYPE 1f 輸入 XML 檔案的 DOCTYPE。
21 FILTER_HID_XML_FILTER_IMPORT_XSLT 63 如果這是一個匯入篩選，則請輸入您要用於匯入的 XSLT 樣式表之檔案名稱。
28 FILTER_HID_XML_FILTER_TEST_IMPORT_BROWSE 5c 開啟一個檔案選擇對話方塊。使用目前的 XML 匯入篩選開啟所選檔案。
2f FILTER_HID_XML_FILTER_TEST_IMPORT_TEMPLATE_FILE 41 顯示您在[變換]標籤頁中輸入的範本的檔案名稱。
28 FILTER_HID_XML_FILTER_TEST_EXPORT_BROWSE 87 尋找您要在其中套用 XML 匯出篩選器的檔案。變換的檔案中的 XML 程式碼會顯示於XML 篩選器匯出 視窗。
28 FILTER_HID_XML_FILTER_TEST_IMPORT_RECENT 39 使用此對話方塊重新開啟上次開啟的文件。
2b FILTER_HID_XML_FILTER_TEST_IMPORT_XSLT_FILE 4f 顯示您在 [變換] 標籤頁中所輸入的 XSLT 篩選器的檔案名稱。
2d FILTER_HID_XML_FILTER_TEST_IMPORT_RECENT_FILE 39 使用此對話方塊重新開啟上次開啟的文件。
2b FILTER_HID_XML_FILTER_TEST_EXPORT_XSLT_FILE 4f 顯示您在 [變換] 標籤頁中所輸入的 XSLT 篩選器的檔案名稱。
29 FILTER_HID_XML_FILTER_TEST_EXPORT_CURRENT c8 將使用符合 XML 篩選器條件的最前台的開啟檔案來測試篩選器。目前的 XML 匯出篩選器會變換檔案，並且在XML 篩選器匯出 視窗中顯示產生的 XML 程式碼。
30 FILTER_HID_XML_FILTER_TEST_IMPORT_DISPLAY_SOURCE 8e Opens the XML 篩選匯出 window, where the XML source of the selected document is displayed. The document is used to test the import filter.
29 FILTER_HID_XML_FILTER_TEST_VALIDATE_OUPUT 23 列出 XML 篩選的測試結果。
23 FILTER_HID_XML_FILTER_OUTPUT_WINDOW 23 列出 XML 篩選的測試結果。
23 FILTER_HID_XML_SOURCE_FILE_VALIDATE 2d 驗證[XML 篩選匯出]視窗中的內容。
20 CUI_HID_HANGULDLG_BUTTON_OPTIONS 2c 開啟 [韓文/漢字選項] 對話方塊。
43 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_SIMPLE_CONVERSION 2d 使用建議的字元代替原來的字元。
1d CUI_HID_HANGULDLG_SUGGESTIONS 2d 顯示字典中全部可用的替代字詞。
18 CUI_HID_SPELLDLG_SETWORD 18 顯示目前的選擇。
48 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_BRACKETED 39 在韓文部份之後的括號中將顯示漢字部份。
48 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_BRACKETED 39 在漢字部份之後的括號中將顯示韓文部份。
1e CUI_HID_HANGULDLG_EDIT_NEWWORD 2a 顯示字典中的第一條替代建議。
3a cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_HANGUL_ONLY 3c 核取此選項後，僅轉換韓文，而不轉換漢字。
22 CUI_HID_HANGULDLG_SUGGESTIONS_LIST 2d 顯示字典中全部可用的替代字詞。
35 cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA%3APB_FIND 45 在字典中搜尋韓文輸入，並將其替代為對應的漢字。
39 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_HANJA_ONLY 3c 核取此選項後，僅轉換漢字，而不轉換韓文。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_BELOW 42 漢字部份將作為注音文字顯示在韓文部份的下方。
43 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA%3ACB_REPLACE_BY_CHARACTER 72 核取此選項後，可在所選文字上逐字元移過。如果未核取此選項，則會替代整個字詞。
1f CUI_HID_HANGULDLG_BUTTON_IGNORE 4b 不變更目前的選擇。將選取下一個字詞或字元進行轉換。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_BELOW 42 韓文部份將作為注音文字顯示在漢字部份的下方。
22 CUI_HID_HANGULDLG_SUGGESTIONS_GRID 2d 顯示字典中全部可用的替代字詞。
1f CUI_HID_HANGULDLG_BUTTON_CHANGE 45 依據格式選項，將此選擇替代為建議的字元或字詞。
22 CUI_HID_HANGULDLG_BUTTON_IGNOREALL 57 不變更目前的選擇，且每次偵測到相同的選擇時，將會自動跳過。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANGUL_HANJA_ABOVE 42 漢字部份將作為注音文字顯示在韓文部份的上方。
1e CUI_HID_HANGULDLG_BUTTON_CLOSE 15 關閉對話方塊。
44 cui%3ARadioButton%3ARID_SVX_MDLG_HANGULHANJA%3ARB_HANJA_HANGUL_ABOVE 42 韓文部份將作為注音文字顯示在漢字部份的上方。
1c .uno%3AHangulHanjaConversion 6c 將所選取的韓文文字從「韓文」轉換成「漢字」，或從「漢字」轉換成「韓文」。
22 CUI_HID_HANGULDLG_BUTTON_CHANGEALL 84 依據格式選項，將此選擇替代為建議的字元或字詞。每次偵測到相同的選擇時，都會將其自動替代。
3c cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_NEW 4c 開啟 [新增字典] 對話方塊，在此處您可以建立新的字典。
3d cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_IGNOREPOST 3c 在搜尋字典時，忽略韓文最後的位置性字元。
3d cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_EDIT 5e 開啟 [編輯自訂字典] 對話方塊，在此您可以編輯所有使用者自訂字典。
3f cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_OPT%3APB_HHO_DELETE 27 刪除選取的使用者自訂字典。
20 CUI_HID_HANGULHANJA_OPT_DICTS_LB 90 列出所有使用者自訂字典。選取在您要使用的字典旁的核取方塊。清除在您不想要使用的字典旁的核取方塊。
3b cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_NEWDICT%3AED_DICTNAME 18 輸入字典的名稱。
44 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_AUTOREPLACEUNIQUE 36 自動替代僅有一個建議替代字詞的字詞。
44 cui%3ACheckBox%3ARID_SVX_MDLG_HANGULHANJA_OPT%3ACB_SHOWRECENTLYFIRST 45 顯示您在上次選取為清單中第一個條目的替代建議。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_4 67 鍵入 [原件] 文字方塊中選取的建議替代項目。替代字詞最多可包含八個字元。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_2 67 鍵入 [原件] 文字方塊中選取的建議替代項目。替代字詞最多可包含八個字元。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_3 67 鍵入 [原件] 文字方塊中選取的建議替代項目。替代字詞最多可包含八個字元。
40 cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3APB_HHE_DELETE 18 刪除選取的條目。
37 cui%3AListBox%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3ALB_BOOK 2d 選取您要編輯的使用者自訂字典。
3d cui%3APushButton%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3APB_HHE_NEW 2a 將目前替代定義新增至字典中。
3c cui%3AComboBox%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3ALB_ORIGINAL 6c 選取您要編輯的目前字典中的條目。若您希望，您也可以在此方塊中輸入新條目。
31 cui%3AEdit%3ARID_SVX_MDLG_HANGULHANJA_EDIT%3AED_1 67 鍵入 [原件] 文字方塊中選取的建議替代項目。替代字詞最多可包含八個字元。
12 .uno%3ASpellDialog 15 手動檢查拼寫。
10 .uno%3ANewWindow 2d 開啟新視窗顯示目前視窗的內容。
3e xmlsecurity%3APushButton%3ARID_XMLSECDLG_DIGSIG%3ABTN_VIEWCERT 25 開啟 [檢視證書] 對話方塊。
33 padmin%3APushButton%3ARID_PADIALOG%3ARID_PA_BTN_DEL 24 從清單中移除選取的來源。
31 sfx2%3APushButton%3ATP_DOCINFODOC%3ABTN_SIGNATURE 6c 此對話方塊會新增與移除文件中的數位簽名。您也可以使用對話方塊來檢視證書。
33 padmin%3APushButton%3ARID_PADIALOG%3ARID_PA_BTN_STD 25 開啟 [選取證書] 對話方塊。
10 .uno%3ASignature 6c 此對話方塊會新增與移除文件中的數位簽名。您也可以使用對話方塊來檢視證書。
29 XMLSECURITY_HID_XMLSEC_CTRL_SIGNATURESDLG 27 為目前的文件列出數位簽名。
38 desktop%3ACheckBox%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_ALL 8d By default, only the down-loadable extensions are shown in the dialog. Mark Show all Updates to see also other extensions and error messages.
46 desktop%3AMultiLineEdit%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_DESCRIPTIONS 7f 檢查更新時，您可以看到進度指示。請等候對話方塊顯示訊息，或按一下 [取消] 中斷更新檢查。
21 DESKTOP_HID_DEPLOYMENT_GUI_UPDATE 119 按一下 [擴充軟體管理程式] 的 [檢查是否有更新] 按鈕，檢查是否有適用於所有已安裝擴充軟體的線上更新。若只檢查是否有適用於所選擴充軟體的線上更新，請按一下滑鼠右鍵開啟右鍵功能表，然後選擇 [更新]。
28 DESKTOP_HID_DEPLOYMENT_GUI_UPDATEINSTALL 119 按一下 [擴充軟體管理程式] 的 [檢查是否有更新] 按鈕，檢查是否有適用於所有已安裝擴充軟體的線上更新。若只檢查是否有適用於所選擴充軟體的線上更新，請按一下滑鼠右鍵開啟右鍵功能表，然後選擇 [更新]。
39 desktop%3APushButton%3ARID_DLG_UPDATE%3ARID_DLG_UPDATE_OK 53 當您按一下 [安裝] 按鈕時，即會顯示 [下載與安裝] 對話方塊。
11 .uno%3AInsertZWSP 97 插入在字詞中看不見，卻會在成為行中最後一個字元時插入換行符的空格。啟用複合文字版面配置 (CTL) 時可啟用。
10 .uno%3AInsertRLM 76 插入可影響尾隨其後之文字方向的文字方向標記。啟用複合文字版面配置 (CTL) 時可啟用。
17 .uno%3AInsertHardHyphen 3c 插入在換行處可讓靠邊字元不中斷的連字符。
10 .uno%3AInsertLRM 76 插入可影響尾隨其後之文字方向的文字方向標記。啟用複合文字版面配置 (CTL) 時可啟用。
17 .uno%3AInsertSoftHyphen 72 插入在字詞中看不見，卻會在成為行中最後一個字元時會出現並建立換行符的連字符。
1d .uno%3AInsertNonBreakingSpace 39 插入在換行處可讓靠邊字元不中斷的空格。
19 .uno%3AFormattingMarkMenu 56 開啟子功能表可插入特殊格式化標記。啟用 CTL 可取得更多指令。
13 .uno%3AInsertZWNBSP 7f 插入在字詞中看不見，卻可在行末保持字詞完整的空格。啟用複合文字版面配置 (CTL) 時可啟用。
16 SVX_HID_GALLERY_WINDOW 53 若要插入[畫廊]物件，請選取該物件，然後將其拖曳到文件中。
e .uno%3AGallery 57 開啟 [畫廊]，您可以在其中選取圖形和聲音，以插入您的文件中。
19 SVX_HID_GALLERY_THEMELIST 36 按一下主題以檢視與該主題相關的物件。
18 SVX_HID_GALLERY_NEWTHEME 4d 將新主題加入[畫廊]中，且讓您選擇主題中要包含的檔案。
18 SVX_HID_GALLERY_ICONVIEW 26 將[畫廊]的內容顯示為圖示。
18 SVX_HID_GALLERY_LISTVIEW 49 將 [畫廊] 的內容顯示為帶有標題和路徑資訊的小圖示。
3f cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_TAKE 2a 將所選檔案加入目前的主題中。
42 cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_TAKEALL 36 將清單中的所有檔案加入目前的主題中。
40 cui%3ACheckBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ACBX_PREVIEW 27 顯示或隱藏所選檔案的預覽。
41 cui%3APushButton%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ABTN_SEARCH 42 找到包含要加入檔案的目錄，然後按一下 [確定]。
41 cui%3AComboBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ACBB_FILETYPE 21 選取要加入的檔案類型。
42 cui%3AMultiListBox%3ARID_SVXTABPAGE_GALLERYTHEME_FILES%3ALBX_FOUND 93 列出可用的檔案。選取要加入的檔案，然後按一下 [加入]。若要加入清單中的所有檔案，請按一下 [全部加入]。
f .uno%3AGridMenu 1e 設定網格的顯示屬性。
14 .uno%3ASnapLinesMenu 21 指定輔助線的顯示選項。
14 .uno%3AAVMediaPlayer 81 開啟「媒體播放程式」視窗，您可於此處預覽電影與聲音檔，並可將這些檔案插入目前的文件中。
14 .uno%3AInsertAVMedia 30 將視訊檔案或聲音檔案插入文件中。
3b cui%3APushButton%3ARID_SVXPAGE_ONLINEUPDATE%3APB_CHANGEPATH 2d 按一下可選取資料夾以下載檔案。
3b cui%3ACheckBox%3ARID_SVXPAGE_ONLINEUPDATE%3ACB_AUTODOWNLOAD 90 標示此選項可啟用自動檢查更新。選擇 [選項] 對話方塊中的 [%PRODUCTNAME] - [線上更新]，可停用或啟用此功能。
36 desktop%3ACheckBox%3ATP_UPDATE_CHECK%3ACB_UPDATE_CHECK 30 您可以手動或自動檢查是否有更新。
22 EXTENSIONS_HID_CHECK_FOR_UPD_PAUSE 3a 暫停下載。稍後按一下 [繼續] 可繼續下載。
25 EXTENSIONS_HID_CHECK_FOR_UPD_DOWNLOAD 94 將更新檔案下載並儲存至桌面或您選擇的資料夾。在 [選項] 對話方塊的 [%PRODUCTNAME] - [線上更新] 中選取資料夾。
24 EXTENSIONS_HID_CHECK_FOR_UPD_INSTALL 18 安裝下載的更新。
26 EXTENSIONS_HID_CHECK_FOR_UPD_DOWNLOAD2 94 將更新檔案下載並儲存至桌面或您選擇的資料夾。在 [選項] 對話方塊的 [%PRODUCTNAME] - [線上更新] 中選取資料夾。
20 EXTENSIONS_HID_CHECK_FOR_UPD_DLG b3 檢查您的 %PRODUCTNAME 版本是否有更新。如果有新版本，您可以選擇下載更新。下載後，如果您有安裝目錄的寫入權限，便可以安裝更新。
23 EXTENSIONS_HID_CHECK_FOR_UPD_CANCEL 30 中止下載，並刪除部分下載的檔案。
21 SFX2_HID_DLG_CHECKFORONLINEUPDATE b3 檢查您的 %PRODUCTNAME 版本是否有更新。如果有新版本，您可以選擇下載更新。下載後，如果您有安裝目錄的寫入權限，便可以安裝更新。
23 EXTENSIONS_HID_CHECK_FOR_UPD_RESUME 18 繼續暫停的下載。
2d DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_DISABLE 1e 啟用或停用擴充軟體。
36 desktop%3AMultiLineEdit%3ARID_DLG_LICENSE%3AML_LICENSE 80 請閱讀授權。視需要按一下 [向下捲動] 按鈕，以向下捲動。按一下 [接受] 以繼續安裝擴充軟體。
39 service%3Acom.sun.star.deployment.ui.PackageManagerDialog 65 [擴充軟體管理程式] 可增加、移除、停用、啟用與更新 %PRODUCTNAME 擴充軟體。
27 DESKTOP_HID_PACKAGE_MANAGER_TREELISTBOX 76 選取要移除、啟用或停用的擴充軟體。對於部分擴充軟體，您還可開啟 [選項] 對話方塊。
2c DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_REMOVE 3c 選取要移除的擴充軟體，然後按一下 [移除]。
1b DESKTOP_HID_PACKAGE_MANAGER 65 [擴充軟體管理程式] 可增加、移除、停用、啟用與更新 %PRODUCTNAME 擴充軟體。
2d DESKTOP_HID_EXTENSION_MANAGER_LISTBOX_OPTIONS 6a 選取一個已安裝的擴充軟體，然後按一下以開啟該擴充軟體的 [選項] 對話方塊。
25 DESKTOP_HID_PACKAGE_MANAGER_MENU_ITEM 65 [擴充軟體管理程式] 可增加、移除、停用、啟用與更新 %PRODUCTNAME 擴充軟體。
38 desktop%3APushButton%3ARID_DLG_LICENSE%3APB_LICENSE_DOWN 80 請閱讀授權。視需要按一下 [向下捲動] 按鈕，以向下捲動。按一下 [接受] 以繼續安裝擴充軟體。
13 SC_HID_PASSWD_TABLE 27 鍵入密碼。密碼區分大小寫。
10 sfx.Edit.2316.26 27 鍵入密碼。密碼區分大小寫。
36 uui%3AEdit%3ADLG_UUI_PASSWORD_CRT%3AED_PASSWORD_REPEAT 15 重新輸入密碼。
33 uui%3AEdit%3ADLG_UUI_PASSWORD_CRT%3AED_PASSWORD_CRT 27 鍵入密碼。密碼區分大小寫。
2b uui%3AEdit%3ADLG_UUI_PASSWORD%3AED_PASSWORD 27 鍵入密碼。密碼區分大小寫。
24 HID_DLG_PASSWORD_TO_OPEN_MODIFY_MORE 38 Click to show or hide the file sharing password options.
2c sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_CONFIRM 15 重新輸入密碼。
2d sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_PASSWORD 27 鍵入密碼。密碼區分大小寫。
11 SC_HID_PASSWD_DOC 15 重新輸入密碼。
10 sfx.Edit.2316.28 15 重新輸入密碼。
3e filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_HIGHRES 24 該文件可以低解析度列印。
3e filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_BROWSER 7e 交叉文件連結使用網際網路瀏覽器開啟。網際網路瀏覽器必須能處理超連結內的指定檔案類型。
3c filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_VISIBLE 77 選取此選項可產生所顯示的頁面文字與圖形會進行縮放而符合讀取器視窗寬度的 PDF 檔案。
12 .uno%3AExportToPDF 3e 將目前檔案儲存為可攜文件格式 (PDF) 版本 1.4。
31 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_ALL 5d 匯出所有定義的列印範圍。如果沒有定義列印範圍，請匯出整份文件。
3b filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_DEFAULT 65 選取此選項可產生會根據讀取器軟體之版面配置設定而顯示頁面的 PDF 檔案。
3b filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_NONE 18 不允許列印文件。
2c filter%3AEdit%3ARID_PDF_TAB_GENER%3AED_PAGES 21 匯出在方塊中輸入的頁。
3a filter%3ACheckBox%3ARID_PDF_TAB_OPNFTR%3ACB_PGLY_FIRSTLEFT ed 選取此選項可產生以連續欄並排顯示頁面的 PDF 檔案。若有兩頁以上，會將第一頁顯示在左側。您必須在 [選項] 對話方塊中的 [語言設定] - [語言] 中啟用複合文字版面配置的支援。
3f FILTER_RADIOBUTTON_RID_PDF_TAB_VPREFER_RB_VISIBLEBOOKMARKLEVELS 5c 選取此項可於讀取器開啟 PDF 檔案時，顯示內文標籤等級至所選等級。
37 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_SELECTION 18 匯出目前的選擇。
3f filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_INSDEL 2a 僅允許插入、刪除及旋轉頁面。
43 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_ALLOWDUPLICATEFIELDNAMES a1 允許在產生的 PDF 檔案中，為多個欄位使用相同的欄位名稱。若停用此功能，將會使用所產生的唯一名稱來匯出欄位名稱。
3d FILTER_NUMERICFIELD_RID_PDF_TAB_OPNFTR_NUM_MAGNF_INITIAL_PAGE 47 選取此項可於讀取器開啟 PDF 檔案時，顯示指定頁面。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_EXP_BMRK_TO_DEST 10a PDF 檔案中的書籤 (參照的目標) 可定義為矩形區域。此外，已命名物件的書籤可依其名稱定義。啟用此核取方塊可將文件中的物件名稱匯出為有效的書籤目標。如此可從其他文件依名稱連結至這些物件。
35 filter%3AListBox%3ARID_PDF_TAB_GENER%3ALB_FORMSFORMAT 2f 選取從 PDF 檔案內傳送表單的格式。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_CNV_OOO_DOCTOPDF c1 啟用此核取方塊可將參照其他 ODF 檔案的 URL 轉換為相同名稱的 PDF 檔案。在參照的 URL 中，副檔名 .odt、.odp、.ods、.odg 與 .odm 會轉換為副檔名 .pdf。
35 filter%3AMetricField%3ARID_PDF_TAB_GENER%3ANF_QUALITY 24 輸入 JPEG 壓縮的品質等級。
36 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTNOTES 43 選擇要將 Writer 和 Calc 文件的備註匯出為 PDF 備註。
40 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_REDUCEIMAGERESOLUTION 48 選擇要重新取樣或縮小影像以達到較少的每英吋像素。
3c filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_DEFAULT ad 選取此選項可產生會顯示原始大小頁面內容的 PDF 檔案。若讀取器軟體配置為預設會使用顯示比例，此頁面即會以該顯示比例顯示。
3b filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTEMPTYPAGES 159 若切換到開啟，自動插入的空白頁面即會匯出為 pdf 檔案。雙面列印 pdf 檔案時最好使用此選項。範例：在書籍中，章節段落樣式設定為一律由奇數頁開始。若前一章節在奇數頁結束，則 %PRODUCTNAME 會插入一個偶數頁的空白頁面。此選項控制是否要匯出該偶數頁。
41 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_FILLFORM 1e 僅允許充填表單欄位。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_OPNFULL 6b 選取此選項可產生以位於所有其他視窗前方的全螢幕讀取器視窗顯示的 PDF 檔案。
3d filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EMBEDSTANDARDFONTS fd Normally the 14 standard Postscript fonts are not embedded in a PDF file, because every PDF reader software already contains these fonts. Enable this option to embed the standard fonts that are installed on your system and that are used in the document.
34 filter%3APushButton%3ARID_PDF_TAB_SECURITY%3ABTN_PWD 30 按一下可開啟輸入密碼之對話方塊。
3a filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTBOOKMARKS cb 選取以將 Writer 文件的書籤匯出為 PDF 書籤。如此會針對您確實在來源文件中指定超連結的所有大綱段落 ([工具] - [大綱編號])，及所有目錄項目建立書籤。
3b FILTER_RADIOBUTTON_RID_PDF_TAB_VPREFER_RB_ALLBOOKMARKLEVELS 53 選取此項可於讀取器開啟 PDF 檔案時，顯示全部內文標籤等級。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_CNTRWIN 56 選取此選項可產生以位於螢幕中央的讀取器視窗顯示的 PDF 檔案。
34 FILTER_METRICFIELD_RID_PDF_TAB_OPNFTR_NUM_MAGNF_ZOOM 3e 當讀取器開啟 PDF 檔案時選取指定的顯示比例。
3e filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_OUTLINE 4d 選取此選項可產生會顯示書籤面板與頁面內容的 PDF 檔案。
3b filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_CONTFAC 86 選取此選項可產生以連續欄並排顯示頁面的 PDF 檔案。若有兩個以上的頁面，會將第一頁顯示在右側。
3b filter%3ACheckBox%3ARID_PDF_TAB_LINKS%3ACB_ENAB_RELLINKFSYS 8c 啟用此核取方塊可將 URL 匯出至其他文件，作為檔案系統中的相對 URL。請參閱「說明」中的"相對超連結"。
33 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_RANGE 21 匯出在方塊中輸入的頁。
39 filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_WIND 68 選取此選項可產生所顯示的頁面會進行縮放而完全符合讀取器視窗的 PDF 檔案。
39 filter%3ACheckBox%3ARID_PDF_TAB_SECURITY%3ACB_ENAB_ACCESS 33 選取此項可啟用協助工具的文字存取。
34 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_TAGGEDPDF 3b 選取編寫 PDF 標籤。這會大量增加檔案大小。
3d filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_NONE 1e 不允許變更任何內容。
38 filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_CONT 47 選取此選項可產生以連續垂直欄顯示頁面的 PDF 檔案。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVWINCTRL 48 選取此選項可在文件為使用中時隱藏讀取器的控制項。
3a FILTER_NUMERICFIELD_RID_PDF_TAB_VPREFER_NUM_BOOKMARKLEVELS 5c 選取此項可於讀取器開啟 PDF 檔案時，顯示內文標籤等級至所選等級。
3d filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_PRINT_LOWRES 67 該文件僅能以低解析度 (150 dpi) 列印。並非所有的 PDF 讀取器皆能執行此設定。
3a filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_MAGNF_WIDTH 68 選取此選項可產生所顯示的頁面會進行縮放而符合讀取器視窗寬度的 PDF 檔案。
39 filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_DISPDOCTITLE 62 選取此選項可產生在顯示時會在讀取器標題列中附上文件標題的 PDF 檔案。
42 filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_APPLICATION 99 交叉文件連結可使用目前顯示文件的 PDF 讀取應用程式開啟。PDF 讀取應用程式必須能處理超連結內的指定檔案類型。
3e filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_TRANSITIONEFFECTS 47 選擇將 Impress 投影片切換效果匯出到各自的 PDF 效果。
40 filter%3AComboBox%3ARID_PDF_TAB_GENER%3ACO_REDUCEIMAGERESOLUTION 21 選取影像的目標解析度。
43 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_ANY_NOCOPY 2d 允許全部變更，唯擷取頁面除外。
41 filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_LOSSLESSCOMPRESSION 33 選擇無損壓縮影像。將保留所有像素。
33 FILTER_RADIOBUTTON_RID_PDF_TAB_OPNFTR_RB_MAGNF_ZOOM 3e 當讀取器開啟 PDF 檔案時選取指定的顯示比例。
3e filter%3ARadioButton%3ARID_PDF_TAB_LINKS%3ACB_VIEW_PDF_DEFAULT 5b PDF 文件與其他文件之間的連結會依其在作業系統中指定的方式處理。
3b filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_EXPORTFORMFIELDS 4c 選擇建立 PDF 表單。這可由 PDF 文件的使用者填寫及列印。
34 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_ADDSTREAM 41 The document being exported will be embedded inside the PDF file.
40 filter%3ARadioButton%3ARID_PDF_TAB_SECURITY%3ARB_CHANGES_COMMENT 2d 僅允許加入註解與充填表單欄位。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVTOOLBAR 48 選取此選項可在文件為使用中時隱藏讀取器的工具列。
39 filter%3ACheckBox%3ARID_PDF_TAB_GENER%3ACB_PDFA_1B_SELECT bf 轉換為 PDF/A-1a 格式。此格式定義為可長期保留的電子文件檔案格式。會將來源文件中使用的所有字型內嵌至產生的 PDF 檔案中，並寫入 PDF 標籤。
3a filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_PGLY_SINGPG 3b 選取此選項可產生逐一顯示頁面的 PDF 檔案。
38 filter%3ACheckBox%3ARID_PDF_TAB_SECURITY%3ACB_ENDAB_COPY 33 選取此項可啟用將內容複製到剪貼簿。
3f filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_PAGEONLY 3e 選取此選項可產生僅顯示頁面內容的 PDF 檔案。
3d filter%3ARadioButton%3ARID_PDF_TAB_OPNFTR%3ARB_OPNMODE_THUMBS 4d 選取此選項可產生會顯示縮圖面板與頁面內容的 PDF 檔案。
3d filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_UOP_HIDEVMENUBAR 4b 選取此選項可在文件為使用中時隱藏讀取器的功能表列。
3b filter%3ACheckBox%3ARID_PDF_TAB_VPREFER%3ACB_WNDOPT_RESINIT 44 選取此可在顯示整個初始頁的視窗中，產生 PDF 檔。
3d filter%3ARadioButton%3ARID_PDF_TAB_GENER%3ARB_JPEGCOMPRESSION ab 選取 JPEG 壓縮影像。若選取高品質等級，幾乎可保留所有像素。若選取低品質等級，將損失一些像素並減損畫質，但可縮小檔案。
17 .uno%3ASendMailDocAsPDF 9a 顯示 [匯出成 PDF] 對話方塊，將目前文件匯出至可攜文件格式 (PDF)，然後再開啟電子郵件傳送視窗並以 PDF 做為附件。
1b UUI_HID_XMLSECDLG_MACROWARN 6c 啟用或停用巨集。在 [選項] 對話方塊中選擇 [%PRODUCTNAME] - [安全性]，以設定選項。
23 SFX2_HID_WARNING_SECURITY_HYPERLINK 6c 啟用或停用巨集。在 [選項] 對話方塊中選擇 [%PRODUCTNAME] - [安全性]，以設定選項。
43 xmlsecurity%3APushButton%3ARID_XMLSECDLG_CERTCHOOSER%3ABTN_VIEWCERT 4f 開啟 [檢視證書] 對話方塊，您可以在其中檢查選取的證書。
3f xmlsecurity%3APushButton%3ARID_XMLSECTP_CERTPATH%3ABTN_VIEWCERT 4f 開啟 [檢視證書] 對話方塊，您可以在其中檢查選取的證書。
2c XMLSECURITY_HID_XMLSEC_CTRL_CHOOSESIGNATURES 48 選取您想要在目前文件中加上數位簽名所使用的證書。
e .uno%3AWebHtml 88 以 HTML 格式建立目前文件的暫存副本，開啟系統預設 Web 瀏覽器，然後在 Web 瀏覽器中顯示此 HTML 檔案。
30 svx%3AEdit%3ARID_SVXDLG_ADD_MODEL%3AED_INST_NAME f 輸入名稱。
16 SVX_HID_XFORMS_TOOLBOX 2c 指定目前 XForms 文件的資料結構。
18 .uno%3AShowDataNavigator 2c 指定目前 XForms 文件的資料結構。
22 SVX_HID_XFORMS_TOOLBOX_ITEM_REMOVE 3f 刪除所選取的項目 (元素、屬性、提交或連結)。
1d SVX_HID_XFORMS_MODELS_MENUBTN 2f 增加、重新命名及移除 XForms 模型。
1a SVX_HID_XFORMS_TAB_CONTROL 2c 指定目前 XForms 文件的資料結構。
20 SVX_HID_XFORMS_TOOLBOX_ITEM_EDIT 54 開啟對話方塊以編輯所選取的項目 (元素、屬性、提交或連結)。
27 SVX_HID_XFORMS_TOOLBOX_ITEM_ADD_ELEMENT 70 開啟對話方塊，以增加新的項目 (元素、屬性、提交或連結) 作為目前項目的子項目。
22 SVX_HID_MN_XFORMS_INSTANCES_REMOVE 39 刪除目前實例。您無法刪除最後一個實例。
3c svx%3ACheckBox%3ARID_SVXDLG_ADD_MODEL%3ACB_MODIFIES_DOCUMENT d5 設為啟用時，當您變更任何連結至模型中任意資料的表單控制項時，文件狀態即會設為「已經修改」；若未啟用，則此類變更不會將文件狀態設為「已經修改」。
1d SVX_HID_MN_XFORMS_MODELS_EDIT 25 重新命名選取的 Xform 模型。
1c SVX_HID_MN_XFORMS_MODELS_ADD 3e 開啟 [增加模型] 對話方塊，可增加 XForm 模型。
1a SVX_HID_XFORMS_MODELS_LIST 23 選取要使用的 XForms 模型。
20 SVX_HID_XFORMS_INSTANCES_MENUBTN 42 此按鈕具有可增加、編輯或移除實例的子功能表。
1f SVX_HID_MN_XFORMS_MODELS_REMOVE 43 刪除選取的 XForm 模型。您無法刪除最後一個模型。
29 SVX_HID_XFORMS_TOOLBOX_ITEM_ADD_ATTRIBUTE 70 開啟對話方塊，以增加新的項目 (元素、屬性、提交或連結) 作為目前項目的子項目。
1f SVX_HID_MN_XFORMS_INSTANCES_ADD 3c 開啟對話方塊，在此處您可以增加新的實例。
1e SVX_HID_MN_XFORMS_SHOW_DETAILS 27 切換顯示以顯示或隱藏細節。
20 SVX_HID_MN_XFORMS_INSTANCES_EDIT 3f 開啟對話方塊，在此處您可以修改目前的實例。
1f SVX_HID_XFORMS_TOOLBOX_ITEM_ADD 70 開啟對話方塊，以增加新的項目 (元素、屬性、提交或連結) 作為目前項目的子項目。
19 SVX_HID_XFORMS_ITEMS_LIST 24 列出屬於目前實例的項目。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_READONLY 18 宣告已計算項目。
20 EXTENSIONS_HID_PROP_XSD_REQUIRED 2b 指定 XForm 是否必須包含此項目。
2e svx%3AEdit%3ARID_SVXDLG_ADD_DATAITEM%3AED_NAME 15 輸入項目名稱。
37 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_CALCULATE 18 宣告已計算項目。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_READONLY 18 宣告項目為唯讀。
20 EXTENSIONS_HID_PROP_XSD_RELEVANT 18 宣告項目為相關。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_RELEVANT 18 宣告項目為相關。
20 EXTENSIONS_HID_PROP_XSD_READONLY 18 宣告項目為唯讀。
38 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_CONSTRAINT 18 宣告項目為限制。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_REQUIRED 18 宣告項目為相關。
35 svx%3AListBox%3ARID_SVXDLG_ADD_DATAITEM%3ALB_DATATYPE 24 選取所選項目的資料類型。
3a svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_CONSTRAINT 18 宣告項目為唯讀。
31 svx%3AEdit%3ARID_SVXDLG_ADD_DATAITEM%3AED_DEFAULT 21 輸入所選項目的預設值。
23 EXTENSIONS_HID_PROP_XSD_CALCULATION 18 宣告已計算項目。
22 EXTENSIONS_HID_PROP_XSD_CONSTRAINT 18 宣告項目為限制。
36 svx%3ACheckBox%3ARID_SVXDLG_ADD_DATAITEM%3ACB_REQUIRED 2b 指定 XForm 是否必須包含此項目。
38 svx%3APushButton%3ARID_SVXDLG_ADD_DATAITEM%3APB_RELEVANT 18 宣告項目為限制。
3d svx%3AMultiLineEdit%3ARID_SVXDLG_ADD_CONDITION%3AED_CONDITION f 輸入條件。
40 svx%3APushButton%3ARID_SVXDLG_ADD_CONDITION%3APB_EDIT_NAMESPACES 5e 開啟 [表單名稱空間] 對話方塊，您可在此增加、編輯或刪除名稱空間。
40 svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_EDIT_NAMESPACE 1e 編輯選取的名稱空間。
3f svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_ADD_NAMESPACE 2a 將新的名稱空間增加到清單中。
42 svx%3APushButton%3ARID_SVXDLG_NAMESPACE_ITEM%3APB_DELETE_NAMESPACE 1e 刪除選取的名稱空間。
21 SVX_HID_XFORMS_NAMESPACEITEM_LIST 2a 列出表單目前定義的名稱空間。
23 EXTENSIONS_HID_PROP_XSD_WHITESPACES d0 指定在目前資料類型的字串處理期間將如何處理空格。可能的值包括 [保留]、[取代] 與 [摺疊]。其語意遵循 http://www.w3.org/TR/xmlschema-2/#rf-whiteSpace 的定義確定。
21 EXTENSIONS_UID_PROP_ADD_DATA_TYPE 9c 按一下按鈕開啟對話方塊，以輸入新的使用者定義資料類型名稱。新的資料類型會繼承目前所選資料類型的所有平面。
20 EXTENSIONS_HID_PROP_XSD_REQUIRED 2b 指定 XForm 是否必須包含此項目。
25 EXTENSIONS_HID_PROP_XSD_MIN_EXCLUSIVE 24 指定值的下限 (不含該值)。
27 EXTENSIONS_HID_PROP_XSD_FRACTION_DIGITS 48 指定十進位資料類型值所能擁有的小數位數總數上限。
24 EXTENSIONS_HID_PROP_XSD_TOTAL_DIGITS 3f 指定十進位資料類型值所能擁有的總位數上限。
24 EXTENSIONS_UID_PROP_REMOVE_DATA_TYPE 4b 選取使用者定義的資料類型，然後按一下按鈕加以刪除。
20 EXTENSIONS_HID_PROP_XSD_RELEVANT 18 宣告項目為相關。
22 EXTENSIONS_HID_PROP_XSD_MAX_LENGTH 21 指定字串的字元數上限。
1e EXTENSIONS_HID_PROP_XSD_LENGTH 1b 指定字串的字元數。
1f EXTENSIONS_HID_PROP_XSD_PATTERN f5 指定常規表示式樣式。對資料類型進行驗證的字串必須符合此樣式才算有效。常規表示式的 XSD 資料類型語法，與用於 %PRODUCTNAME 其他工具 (例如 [尋找與替代]) 中的常規表示式語法不同。
21 EXTENSIONS_HID_PROP_XSD_DATA_TYPE 33 選取控制項據以進行驗證的資料類型。
20 EXTENSIONS_HID_PROP_XSD_READONLY 18 宣告項目為唯讀。
25 EXTENSIONS_HID_PROP_XSD_MAX_INCLUSIVE 24 指定值的上限 (包含該值)。
20 EXTENSIONS_HID_PROP_BINDING_NAME bd 選取或輸入連結名稱。選取現存的連結名稱後，連結即會與表單控制項產生關聯。輸入新名稱可建立新的連結，並使其與表單控制項產生關聯。
22 EXTENSIONS_HID_PROP_XML_DATA_MODEL 36 從目前文件中所有模型清單中選取模型。
25 EXTENSIONS_HID_PROP_XSD_MIN_INCLUSIVE 24 指定值的下限 (包含該值)。
23 EXTENSIONS_HID_PROP_XSD_CALCULATION 18 宣告已計算項目。
22 EXTENSIONS_HID_PROP_XSD_CONSTRAINT 18 宣告項目為限制。
25 EXTENSIONS_HID_PROP_XSD_MAX_EXCLUSIVE 24 指定值的上限 (不含該值)。
23 EXTENSIONS_HID_PROP_BIND_EXPRESSION 82 輸入要與控制項模型連結的 DOM 節點。按一下 [...] 按鈕，即會出現可輸入 XPath 表示式的對話方塊。
13 .uno%3APrintDefault 63 按一下直接列印檔案圖示，以目前預設的印表機設定來列印使用中的文件。
13 .uno%3AText_Marquee c4 將水平文字方向的動畫文字插入目前的文件中。拖曳文字方塊，然後輸入或貼上文字。若要指定動畫文字效果，請選擇 [格式] - [文字] - [文字動畫]。
a .uno%3APie 160 在目前的文件中，繪製由橢圓弧和兩條半徑線定義的充填形狀。若要繪製橢圓缺，請將橢圓拖曳至想要的大小，然後按一下來定義第一條半徑線。將指標移至要放置第二條半徑線的位置，然後按一下。您無需在橢圓上按一下。若要繪製扇形，請在拖曳時按住 Shift 鍵。
17 .uno%3APolygon_Unfilled e7 繪製由一系列線段組成的線條。拖曳繪製線段，按一下來定義該線段的端點，然後拖曳繪製新的線段。按兩下，完成線條的繪製。若要建立封閉形狀，請按兩下此線條的起點。
f .uno%3ADrawText de 在目前文件中，於拖曳滑鼠處繪製水平文字方向的文字方塊。在文件的任意位置，將文字方塊拖曳至所需的大小，然後鍵入或貼上文字。旋轉文字方塊以取得旋轉文字。
10 .uno%3ACircleCut 14e 在目前的文件中，繪製由圓弧和直徑線定義的充填形狀。若要繪製圓缺，請將圓拖曳至想要的大小，然後按一下來定義直徑線的起點。將指標移至要放置直徑線端點的位置，然後按一下。您無需在圓上按一下。若要繪製橢圓缺，請在拖曳時按住 Shift 鍵。
e .uno%3AEllipse a9 在目前的文件中，於拖曳處繪製橢圓。在要繪製橢圓處按一下，然後拖曳至想要的大小。若要繪製圓，請在拖曳時按住 Shift 鍵。
16 .uno%3AVerticalCaption 1b7 在目前的文件中，從拖曳處開始繪製一條在矩形圖說文字 (其文字方向為垂直) 中結束的線條。拖曳圖說文字的控點來調整圖說文字的大小。若要加入文字，請按一下圖說文字的邊緣，然後鍵入或貼上文字。若要將矩形圖說文字變更為圓角圖說文字，請在指標變為手形時，拖曳最大的角控點。只有在啟用亞洲語言支援時，才可使用。
12 .uno%3ADrawCaption 17e 在目前的文件中，從拖曳處開始繪製一條在矩形圖說文字 (其文字方向為水平) 中結束的線條。拖曳圖說文字的控點來變更圖例的大小。若要加入文字，請按一下圖說文字的邊緣，然後鍵入或貼上文字。若要將矩形圖說文字變更為圓角圖說文字，請在指標變更為手形時，拖曳最大的角控點。
a .uno%3AArc 124 在目前的文件中繪製圓弧。若要繪製弧，請將橢圓拖曳至想要的大小，然後按一下來定義弧的起點。將指標移至要放置端點的位置，然後按一下。您無需在橢圓上按一下。若要以圓為基礎繪製弧，請在拖曳時按住 Shift 鍵。
18 .uno%3AFreeline_Unfilled bd 在目前的文件中，於拖曳處繪製自由形曲線。若要結束線條，請放掉滑鼠按鈕。若要繪製封閉形狀，請在接近此線條起點的附近放開滑鼠按鈕。
b .uno%3ARect bb 在目前的文件中，於拖曳處繪製矩形。若要繪製正方形，請在拖曳時按住 Shift 鍵。在要放置矩形角點的位置按一下，然後拖曳至想要的大小。
16 .uno%3ABezier_Unfilled 138 繪製一條平滑的貝茲曲線。在想要曲線開始的位置按一下，拖曳，釋放，然後將指標移至想要曲線結束的位置並按一下。移動指標，再按一下，將線段加入曲線中。按兩下，完成曲線的繪製。若要建立封閉形狀，請按兩下此曲線的起點。
11 .uno%3AInsertDraw 82 按一下以開啟或關閉 [繪圖] 列，在此您可以將形狀、線條、文字和圖說文字增加到目前的文件中。
13 .uno%3AVerticalText 141 在目前文件中，於按一下或拖曳滑鼠處繪製垂直文字方向的文字方塊。在文件的任意位置按一下滑鼠，然後鍵入或貼上文字。也可以移動游標至要增加文字的位置，拖曳文字方塊，然後鍵入或貼上文字。只有在啟用亞洲語言支援時，才可使用。
b .uno%3ALine 77 在目前的文件中，於拖曳處繪製直線。若要限制線條為 45 度角，請在拖曳時按住 Shift 鍵。
12 .uno%3AFileControl 24 建立用於選擇檔案的按鈕。
10 .uno%3AScrollBar f 建立捲軸。
11 .uno%3ASpinbutton 15 建立微調按鈕。
16 .uno%3AFormDesignTools 22 開啟 [表單設計] 工具列。
13 .uno%3AMoreControls 25 開啟 [更多控制項] 工具列。
f .uno%3AComboBox 15 建立組合方塊。
e .uno%3AListBox 15 建立清單方塊。
17 .uno%3AAutoControlFocus 100 如果已啟動 [自動控制焦點]，則當您開啟文件時，第一個表單控制項將被選取。如果未啟動此按鈕，則在開啟文件後，文字將被選取。您已經指定的 [定位順序] 決定哪一個表單控制項是第一個。
f .uno%3AGroupBox 33 建立可直觀地分組數個控制項的訊框。
13 .uno%3ANumericField 15 建立數字欄位。
13 .uno%3AImageControl 42 建立影像控制項。這只能用於從資料庫新增影像。
c .uno%3ALabel 24 建立用於顯示文字的欄位。
f .uno%3ACombobox 15 建立組合方塊。
14 .uno%3ANavigationBar 18 建立瀏覽位址列。
b .uno%3AGrid 30 建立表格控制項以顯示資料庫表格。
12 .uno%3ARadioButton 15 建立選項按鈕。
11 .uno%3APushbutton f 建立按鈕。
15 .uno%3AFormattedField 1b 建立格式化的欄位。
b .uno%3AEdit 15 建立文字方塊。
f .uno%3ACheckbox 15 建立核取方塊。
f .uno%3AGroupbox 33 建立可直觀地分組數個控制項的訊框。
13 .uno%3APatternField 15 建立圖案欄位。
10 .uno%3ADateField 15 建立日期欄位。
11 .uno%3ASpinButton 15 建立微調按鈕。
14 .uno%3ACurrencyField 15 建立貨幣欄位。
10 .uno%3ATimeField 15 建立時間欄位。
12 .uno%3ARadiobutton 15 建立選項按鈕。
f .uno%3ACheckBox 15 建立核取方塊。
d .uno%3AConfig 48 [表單控制項] 工具列含有建立互動式表單所需的工具。
12 .uno%3AImagebutton 21 建立顯示為影像的按鈕。
18 .uno%3AConvertToCurrency 2a 將所選控制項變換成貨幣欄位。
14 .uno%3AConvertToDate 2a 將所選控制項變換成日期欄位。
18 .uno%3AConvertToCheckBox 2a 將所選控制項變換成核取方塊。
19 .uno%3AConvertToFormatted 30 將所選控制項變換成格式化的欄位。
16 .uno%3AConvertToButton 24 將所選控制項變換成按鈕。
1c .uno%3AConvertToImageControl 2d 將所選控制項變換成影像控制項。
1b .uno%3AConvertToFileControl 2a 將所選控制項變換成檔案選擇。
15 .uno%3AConvertToFixed 24 將所選控制項變換成標籤。
17 .uno%3AConvertToNumeric 2a 將所選控制項變換成數字欄位。
17 .uno%3AConvertToPattern 2a 將所選控制項變換成圖案欄位。
14 .uno%3AConvertToTime 2a 將所選控制項變換成時間欄位。
15 .uno%3AConvertToCombo 2a 將所選控制項變換成組合方塊。
15 .uno%3AConvertToRadio 2a 將所選控制項變換成選項按鈕。
14 .uno%3AConvertToEdit 2a 將所選控制項變換成文字方塊。
18 .uno%3AConvertToImageBtn 2a 將所選控制項變換成影像按鈕。
18 .uno%3AChangeControlType 57 呼叫您可選取控制項類型的子功能表以替代文件中選取的控制項。
14 .uno%3AConvertToList 2a 將所選控制項變換成清單方塊。
14 SVX_HID_FM_CHANGECOL 60 開啟子功能表，選取一個資料欄位代替此表格控制項中所選的資料欄位。
12 SVX_HID_FM_HIDECOL 15 隱藏選取的欄。
14 SVX_HID_FM_DELETECOL 1b 刪除目前選取的欄。
16 SVX_HID_FM_SHOWALLCOLS 33 如果您要顯示所有欄，按一下 [全部]。
18 SVX_HID_FM_SHOWCOLS_MORE 22 呼叫 [顯示欄] 對話方塊。
2f cui%3AListBox%3ARID_SVX_DLG_SHOWGRIDCOLUMNS%3A1 83 在 [顯示欄] 對話方塊中，可選取要顯示的欄。按住 Shift 或 Ctrl 鍵 (Mac：Command 鍵) 可選取多個項目。
14 SVX_HID_FM_INSERTCOL 4e 呼叫子功能表，選取要在此表格控制項中採用的資料欄位。
13 SVX_HID_FM_SHOWCOLS 42 呼叫子功能表，您可在此處選取要再次顯示的欄。
20 EXTENSIONS_HID_FM_PROPDLG_TABCTR 3c 開啟用於編輯所選控制項之特性的對話方塊。
18 .uno%3AControlProperties 3c 開啟用於編輯所選控制項之特性的對話方塊。
1e EXTENSIONS_HID_PROP_MAXTEXTLEN 30 定義使用者可以輸入的最多字元數。
23 EXTENSIONS_HID_PROP_SHOW_SCROLLBARS 30 新增您為文字方塊指定的捲軸類型。
1b EXTENSIONS_HID_PROP_ENABLED 72 如果某個控制項欄位的「已啟用」特性為 [是]，則表單使用者可以使用該控制項欄位。
21 EXTENSIONS_HID_PROP_LINEINCREMENT 51 指定當使用者點選捲軸上的箭頭圖示時，要增加或減少的值。
1b EXTENSIONS_HID_PROP_TIMEMIN 2d 確定使用者可以輸入的最小時間。
1b EXTENSIONS_HID_PROP_HELPURL 7a 在 URL 拼字檢查中指定批次標籤，該批次標籤涉及說明文件，並可借助控制項欄位進行呼叫。
1a EXTENSIONS_HID_PROP_BORDER 4e 確定欄位的邊框類型 (「無框」、「3D 顯示」、「平面」)。
20 EXTENSIONS_HID_PROP_EFFECTIVEMIN 5a 您可以在此確定控制項欄位的數值，以避免使用者輸入較小的數值。
1e EXTENSIONS_HID_PROP_TIMEFORMAT 2a 可以定義所需的時間顯示格式。
20 EXTENSIONS_HID_PROP_HIDDEN_VALUE 36 您可以輸入由隱入的控制項繼承的資料。
19 EXTENSIONS_HID_PROP_WIDTH 1b 定義控制項的寬度。
1a EXTENSIONS_HID_PROP_HEIGHT 1b 定義控制項的高度。
20 EXTENSIONS_HID_PROP_DEFAULT_DATE 15 設定預設日期。
1e EXTENSIONS_HID_PROP_BUTTONTYPE 39 動作特性決定當您啟動按鈕時發生的動作。
20 EXTENSIONS_HID_PROP_DEFAULT_TIME 15 設定預設時間。
1f EXTENSIONS_HID_PROP_BORDERCOLOR 48 指定擁有設定為「平面」邊框特性的控制項邊框顏色。
21 EXTENSIONS_HID_PROP_SHOW_POSITION 4b 指定顯示或隱藏所選「瀏覽位址列」控制項的定位項目。
1d EXTENSIONS_HID_PROP_ECHO_CHAR 88 如果文字方塊用於輸入密碼，請輸入顯示字元的 ASCII 碼。顯示時此字元將代替使用者鍵入的密碼字元。
26 EXTENSIONS_HID_PROP_DEFAULT_SELECT_SEQ 36 指定要標記為預設條目的清單方塊條目。
1e EXTENSIONS_HID_PROP_DATEFORMAT 33 您可以在此決定日期顯示設定的格式。
1f EXTENSIONS_HID_PROP_ORIENTATION 39 指定捲動軸或微調按鈕的水平或垂直方向。
22 EXTENSIONS_HID_PROP_DEFAULT_BUTTON 47 [預設按鈕] 特性指定按 Return 鍵時將引發的相應按鈕。
1c EXTENSIONS_HID_PROP_READONLY 3a 決定此控制項為唯讀 (是) 還是可編輯 (否)。
19 EXTENSIONS_HID_PROP_ALIGN 3c 指定控制項上所用之文字或圖形的對齊選項。
1c EXTENSIONS_HID_PROP_HELPTEXT 5d 提供用於輸入說明文字的選項，這些文字將作為提示顯示在控制項上。
1c EXTENSIONS_HID_PROP_DROPDOWN 40 指定組合方塊為可擴展 (是) 還是不可擴展 (否)。
20 EXTENSIONS_HID_PROP_RECORDMARKER 57 指定是否在第一欄中顯示列標籤，其中目前的記錄使用箭頭標記。
1b EXTENSIONS_HID_PROP_VSCROLL 30 新增您為文字方塊指定的捲軸類型。
1f EXTENSIONS_HID_PROP_LITERALMASK 57 定義字元遮罩。字元遮罩包含初始值，且在下載表單後一律可見。
21 EXTENSIONS_HID_PROP_SPININCREMENT 48 決定每次啟動微調按鈕控制項時要增加或減少的間隔。
20 EXTENSIONS_HID_PROP_TARGET_FRAME 55 指定目標訊框，以顯示由「開啟文件/網頁」動作所開啟的文件。
1b EXTENSIONS_HID_PROP_DATEMAX 4e 確定一個日期，以限定使用者輸入的數值不得超過此日期。
1d EXTENSIONS_HID_PROP_WORDBREAK 18 以多行顯示文字。
1f EXTENSIONS_HID_PROP_ANCHOR_TYPE 21 定義固定控制項的位置。
23 EXTENSIONS_HID_PROP_SHOW_NAVIGATION 4b 指定顯示或隱藏所選「瀏覽位址列」控制項的導覽項目。
19 EXTENSIONS_HID_PROP_LABEL 4e 貼標屬性設定控制項欄位的貼標，該貼標會在表單中顯示。
1b EXTENSIONS_HID_PROP_TABSTOP 42 Tabstop AYcE￥i?Tcw?O§_¯a使用 Tab 鍵選取控制項欄位。
22 EXTENSIONS_HID_PROP_MULTISELECTION 30 可讓您在清單方塊中選取多個項目。
24 EXTENSIONS_HID_PROP_DECIMAL_ACCURACY 2d 確定小數點右側顯示的數字位數。
1c EXTENSIONS_HID_PROP_ICONSIZE 4e 指定所選「瀏覽位址列」中的圖示是否為小型或大型圖示。
20 EXTENSIONS_HID_PROP_VISUALEFFECT 5c 指定是否要在 3D 外觀 (預設) 或平面外觀中顯示核取方塊或選項按鈕。
1f EXTENSIONS_HID_PROP_VISIBLESIZE a0 在「值單位」中指定捲軸方塊的大小。(「最大捲動值」減去「最小捲動值」) / 2 的數值就產生佔據半個背景區域的方塊。
23 EXTENSIONS_HID_PROP_SHOW_FILTERSORT 4e 指定顯示或隱藏所選瀏覽位址列控制項的篩選與排序項目。
22 EXTENSIONS_HID_PROP_CURRENCYSYMBOL 30 可以輸入字元或字串作為貨幣符號。
25 EXTENSIONS_HID_PROP_DEFAULT_SPINVALUE 24 設定控制項欄位的預設值。
1d EXTENSIONS_HID_PROP_POSITIONY 33 定義控制項相對於固定標記的 Y 位置。
20 EXTENSIONS_HID_PROP_DEFAULTVALUE 24 設定控制項欄位的預設值。
21 EXTENSIONS_HID_PROP_NAVIGATIONBAR 45 指定是否在表格控制項的下邊框上顯示瀏覽位址列。
1e EXTENSIONS_HID_PROP_SCALEIMAGE 30 變更影像大小以符合控制項的大小。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MAX 24 指定捲軸控制項的最大值。
1d EXTENSIONS_HID_PROP_PRINTABLE 3f 指定是否在文件的列印輸出中顯示控制項欄位。
24 EXTENSIONS_HID_PROP_CURRSYM_POSITION 54 確定在使用貨幣欄位時，數字之前或之後是否要顯示貨幣符號。
23 EXTENSIONS_HID_PROP_SHOWTHOUSANDSEP 1b 插入千位分隔符號。
27 EXTENSIONS_HID_PROP_DEFAULT_SCROLLVALUE 1b 設定捲軸的預設值。
21 EXTENSIONS_HID_PROP_IMAGEPOSITION 3c 指定控制項上所用之文字或圖形的對齊選項。
1d EXTENSIONS_HID_PROP_ROWHEIGHT 27 指定表格控制項欄位的列高。
20 EXTENSIONS_HID_PROP_REPEAT_DELAY 39 在重複事件之間，以毫秒為單位指定延遲。
18 EXTENSIONS_HID_PROP_SPIN 60 若是選取「是」，則可以從控制項中取得一個具有箭頭按鈕的微調按鈕。
24 EXTENSIONS_HID_PROP_EFFECTIVEDEFAULT 24 設定控制項欄位的預設值。
26 EXTENSIONS_HID_PROP_SHOW_RECORDACTIONS 4b 指定顯示或隱藏所選「瀏覽位址列」控制項的動作項目。
1c EXTENSIONS_HID_PROP_VALUEMIN 5a 您可以在此確定控制項欄位的數值，以避免使用者輸入較小的數值。
1a EXTENSIONS_HID_PROP_REPEAT 69 指定在按一下控制項並按住滑鼠按鈕時，是否會重複微調按鈕等控制項的動作。
1f EXTENSIONS_HID_PROP_SYMBOLCOLOR 3c 指定控制項的符號色彩，例如捲軸上的箭頭。
1d EXTENSIONS_HID_PROP_MULTILINE c2 可以讓您在控制項欄位 (例如文字方塊或標籤) 中使用換行與格式化。若要手動輸入一個換行，請按 Enter 鍵。選取「多行格式」以輸入格式化文字。
20 EXTENSIONS_HID_PROP_AUTOCOMPLETE 33 將「自動填入」功能指定給組合方塊。
18 EXTENSIONS_HID_PROP_NAME 8f 在 [特性] 標籤頁上，此選項可指定控制項欄位的名稱。在 [表單特性] 標籤頁上，此選項可指定表單的名稱。
21 EXTENSIONS_HID_PROP_SPINVALUE_MIN 5a 您可以在此確定控制項欄位的數值，以避免使用者輸入較小的數值。
1d EXTENSIONS_HID_PROP_POSITIONX 33 定義控制項相對於固定標記的 X 位置。
20 EXTENSIONS_HID_PROP_CONTROLLABEL 21 指定控制項貼標的來源。
17 EXTENSIONS_HID_PROP_TAG 39 指定控制項欄位的其他資訊或描述性文字。
22 EXTENSIONS_HID_PROP_ENABLE_VISIBLE 69 定義在實況模式中是否可以看見控制項。在設計模式中，一律可以看見控制項。
22 EXTENSIONS_HID_PROP_LINEEND_FORMAT 5a 針對文字欄位，選取將文字寫入資料庫欄時，要使用的行尾程式碼。
20 EXTENSIONS_HID_PROP_EFFECTIVEMAX 57 為控制項欄位定義一個值，以限定使用者輸入的值不得超過此值。
20 EXTENSIONS_HID_PROP_STRICTFORMAT 4b 如果嚴謹的格式功能已啟動 (是)，則僅接受允許的字元。
18 EXTENSIONS_HID_PROP_TEXT 33 設定文字方塊或組合方塊的預設文字。
23 EXTENSIONS_HID_PROP_SCROLLVALUE_MIN 24 指定捲軸控制項的最小值。
23 EXTENSIONS_HID_PROP_BACKGROUNDCOLOR 27 設定控制項欄位的背景顏色。
1b EXTENSIONS_HID_PROP_HSCROLL 30 新增您為文字方塊指定的捲軸類型。
22 EXTENSIONS_HID_PROP_STRINGITEMLIST f2 定義在文件中顯示的清單項目。開啟此清單並鍵入文字。使用 Shift+Enter 組合鍵新增一行。透過清單和組合方塊，可定義將在文件中顯示的清單項目。開啟 [清單項目] 欄位並鍵入文字。
1c EXTENSIONS_HID_PROP_TRISTATE 70 指定核取方塊除了表示所連結資料庫的 TRUE 和 FALSE 值以外，是否也可以表示 ZERO 值。
1c EXTENSIONS_HID_PROP_VALUEMAX 57 為控制項欄位定義一個值，以限定使用者輸入的值不得超過此值。
1d EXTENSIONS_HID_PROP_IMAGE_URL 54 [圖形] 特性指定了您希望顯示在按鈕上的圖形路徑與檔案名稱。
20 EXTENSIONS_HID_PROP_FOCUSONCLICK 5d 若您將此選項設定為「是」，則在按一下按鈕時，[按鈕] 會收到焦點。
1a EXTENSIONS_HID_PROP_TOGGLE 117 指定「按鈕」是否作為「切換按鈕」。當控制項擁有焦點且按一下按鈕或按空白鍵時，若您將 [切換] 設定為「是」，則您可以在 [選取] 與 [不選取] 之間切換控制項狀態。[選取] 狀態中的按鈕會顯示為 [已按下]。
1b EXTENSIONS_HID_PROP_TIMEMAX 4b 確定一個時間，以限定使用者輸入的值不得超過此時間。
1d EXTENSIONS_HID_PROP_FORMATKEY 52 指定用於此控制項的格式碼。按一下 [...] 按鈕，選取格式碼。
21 EXTENSIONS_HID_PROP_SPINVALUE_MAX 57 為控制項欄位定義一個值，以限定使用者輸入的值不得超過此值。
1d EXTENSIONS_HID_PROP_LINECOUNT 7d 指定下拉式清單中應顯示的行數。只有您在 [可擴展] 選項中選擇 [是] 之後，才會啟用此設定。
1c EXTENSIONS_HID_PROP_EDITMASK 72 定義編輯遮罩。透過指定字元碼，您可以決定使用者在控制項欄位中可以輸入的內容。
1e EXTENSIONS_HID_PROP_TARGET_URL 49 指定按一下 [開啟文件/網頁] 按鈕時要開啟的 URL 位址。
1c EXTENSIONS_HID_PROP_TABINDEX 47 [定位順序] 特性決定按下 Tab 鍵聚焦各控制項的順序。
29 EXTENSIONS_HID_PROP_HIDEINACTIVESELECTION 60 指定當焦點不再位於控制項時，控制項上的文字選取是否仍保持為選取。
1d EXTENSIONS_HID_PROP_VALUESTEP 1e 決定微調按鈕的間隔。
22 EXTENSIONS_HID_PROP_BLOCKINCREMENT 57 指定當使用者按一下捲動軸上的滑動軸時，要增加或減少的數值。
22 EXTENSIONS_HID_PROP_WHEEL_BEHAVIOR 123 設定當使用者捲動滑鼠滾輪時，是否變更值。一律不：不變更值。集中時：(預設值) 當聚焦於控制項上且滾輪指向控制項而且捲動時，即變更值。自動：當滾輪指向控制項且捲動時，不論是否聚焦於何控制項，均變更值。
1b EXTENSIONS_HID_PROP_DATEMIN 2d 確定使用者可以輸入的最早日期。
1c EXTENSIONS_HID_PROP_TEXTTYPE c2 可以讓您在控制項欄位 (例如文字方塊或標籤) 中使用換行與格式化。若要手動輸入一個換行，請按 Enter 鍵。選取「多行格式」以輸入格式化文字。
18 EXTENSIONS_HID_PROP_FONT 2a 選取控制項欄位中的文字字型。
26 EXTENSIONS_HID_PROP_CELL_EXCHANGE_TYPE 4b 選取在工作表上使用已連結儲存格連結清單方塊的模式。
1e EXTENSIONS_HID_PROP_BOUND_CELL 6c 指定工作表上連結的儲存格的參照。即時狀態或控制項內容會連結到儲存格內容。
1c EXTENSIONS_HID_PROP_REFVALUE b6 可以輸入 Web 表單的參照值。傳送表單時，該值將傳送至伺服器。在資料庫表單中，輸入值會寫入指定給相應控制項欄位的資料庫欄位中。
1f EXTENSIONS_HID_PROP_BOUNDCOLUMN 99 使用索引，指定與此欄位 (在 [資料欄位] 下提供) 連結的表格欄位或表格 SQL 查詢。此屬性的有效值為 1、2、3 等等。
25 EXTENSIONS_HID_PROP_UNCHECKEDREFVALUE 10a 試算表內的核取方塊和單選按鈕可與目前文件中的儲存格連結。若啟用此控制項，則您在 [參照值 (開啟)] 中輸入的值會複製到儲存格中。若停用此控制項，則 [參照值 (關閉)] 中的值會複製到儲存格中。
1e EXTENSIONS_HID_PROP_LISTSOURCE 99 使用資料庫表單，指定用於該表單元素的清單內容的資料來源。此欄位可用於定義無資料庫連結的文件的數值清單。
23 EXTENSIONS_HID_PROP_LIST_CELL_RANGE 4b 輸入包含清單方塊條目或工作表組合方塊的儲存格範圍。
21 EXTENSIONS_HID_PROP_CONTROLSOURCE 36 指定該控制項所參照的資料源表格欄位。
21 EXTENSIONS_HID_PROP_EMPTY_IS_NULL bd 定義如何處理輸入的空字串。若設為 [是]，長度為零的輸入字串將會視為 NULL 值。若設為 [否]，則所有輸入都會以原狀接受而不進行任何轉換。
22 EXTENSIONS_HID_PROP_LISTSOURCETYPE 3f 決定要充填清單方塊和組合方塊中清單的資料。
22 EXTENSIONS_HID_PROP_FILTERPROPOSAL f2 設計表單時，您可以在相應 [屬性] 對話方塊的 [資料] 標籤中，為每個文字方塊設定「篩選建議」屬性。在篩選模式中的隨後搜尋中，您可以從這些欄位所包含的全部資訊中進行選取。
1c EXTENSIONS_HID_EVT_FOCUSLOST 42 當控制項欄位失去焦點時，失去焦點時事件發生。
22 EXTENSIONS_HID_EVT_ACTIONPERFORMED 3a 啟動某個動作時，會發生 [執行動作] 事件。
1b EXTENSIONS_HID_EVT_KEYTYPED 5a 如果使用者在控制項具有焦點時按任意鍵，則已按下按鍵事件發生。
1e EXTENSIONS_HID_EVT_TEXTCHANGED 4e 在您對輸入欄位輸入或修改文字時，文字已修改事件發生。
29 EXTENSIONS_HID_EVT_APPROVEACTIONPERFORMED 3f 此事件會在按一下控制項而觸發動作之前發生。
1e EXTENSIONS_HID_EVT_MOUSEEXITED 45 當滑鼠位於控制項欄位之外時，滑鼠移出事件發生。
1d EXTENSIONS_HID_EVT_MOUSEMOVED 42 當滑鼠移到控制項上時，[滑鼠已移動] 事件發生。
1f EXTENSIONS_HID_EVT_MOUSEPRESSED 60 當滑鼠指標位於控制項上並按住滑鼠鍵時，[已按下滑鼠按鍵] 事件發生。
1f EXTENSIONS_HID_EVT_MOUSEDRAGGED 55 按住某個鍵並拖曳滑鼠時，會發生 [按下按鍵時移動滑鼠] 事件。
18 EXTENSIONS_HID_EVT_KEYUP 67 在控制項對準控制單元時，若使用者放開任一鍵，則會發生 [放開按鍵] 事件。
1a EXTENSIONS_HID_EVT_CHANGED 5a 在控制項失去焦點，致使控制項的內容變更時，已經變更事件發生。
20 EXTENSIONS_HID_EVT_MOUSERELEASED 5d 當滑鼠指標位於控制項上並按住滑鼠鍵時，已放開滑鼠按鍵事件發生。
1f EXTENSIONS_HID_EVT_MOUSEENTERED 42 當滑鼠位於控制項欄位中時，滑鼠在內事件發生。
1e EXTENSIONS_HID_EVT_FOCUSGAINED 42 當控制項欄位獲得焦點時，接收焦點時事件發生。
23 EXTENSIONS_HID_EVT_ITEMSTATECHANGED 4b 當控制項欄位的狀態改變時，項目狀態已變更事件發生。
15 .uno%3AFormProperties 4b 在此對話方塊中您可以指定整個表單的資料來源和事件。
23 EXTENSIONS_HID_PROP_SUBMIT_ENCODING 24 指定資料傳輸的編碼類型。
21 EXTENSIONS_HID_PROP_SUBMIT_METHOD 36 指定傳送已完成的表單資訊所用的方法。
1e EXTENSIONS_HID_EVT_POSITIONING 45 在目前記錄指標變更之前，[記錄變更前] 事件發生。
23 EXTENSIONS_HID_EVT_APPROVEPARAMETER 57 當要載入的表單中含有必須填寫的參數時，[充填參數] 事件發生。
1b EXTENSIONS_HID_EVT_UNLOADED 5f 在卸載表單之後，即表單與其資料源分離之後，[卸載時]事件立即發生。
1c EXTENSIONS_HID_EVT_ROWCHANGE 48 在目前的記錄變更之後，[記錄動作後] 事件立即發生。
1b EXTENSIONS_HID_EVT_RELOADED 45 在重新載入表單之後，[重新載入時] 事件立即發生。
1b EXTENSIONS_HID_EVT_RESETTED 39 在重設表單後，[重新設定之後] 事件發生。
1d EXTENSIONS_HID_EVT_POSITIONED 4e 在目前的記錄指標變更之後，[記錄變更後] 事件立即發生。
23 EXTENSIONS_HID_EVT_APPROVEROWCHANGE 42 在目前的記錄變更之前，[記錄動作前] 事件發生。
19 EXTENSIONS_HID_EVT_LOADED 39 在載入表單之後，[載入時] 事件立即發生。
22 EXTENSIONS_HID_EVT_APPROVERESETTED 38 在重設表單之前，[重新設定前]事件發生。
1f EXTENSIONS_HID_EVT_ERROROCCURED 4e 當存取資料源的程序中出現錯誤時，[發生錯誤] 事件發生。
1e EXTENSIONS_HID_EVT_AFTERUPDATE 5d 在使用者變更的控制項內容寫入到資料源之後，[更新之後] 事件發生。
20 EXTENSIONS_HID_EVT_CONFIRMDELETE 42 一從表單中刪除資料，[確認刪除] 事件就會發生。
1c EXTENSIONS_HID_EVT_RELOADING 3f 在重新載入表單之前，[重新載入前] 事件發生。
1c EXTENSIONS_HID_EVT_SUBMITTED 39 在傳送表單資料之前，[送出前] 事件發生。
1c EXTENSIONS_HID_EVT_UNLOADING 59 在卸載表單之前，即表單與其資料源分離之前，[卸載前]事件發生。
1f EXTENSIONS_HID_PROP_ALLOW_EDITS 21 確定是否可以修改資料。
25 EXTENSIONS_HID_PROP_ESCAPE_PROCESSING 34 指定 %PRODUCTNAME 是否會分析 SQL 陳述式。
23 EXTENSIONS_HID_PROP_ALLOW_ADDITIONS 21 確定是否可以新增資料。
24 EXTENSIONS_HID_PROP_CURSORSOURCETYPE 7a 定義是否使用現有的資料庫表格或查詢作為資料來源，或者是否基於 SQL 陳述式來產成表單。
20 EXTENSIONS_HID_PROP_MASTERFIELDS 60 如果您建立子表單，請輸入父表單中負責父子表單之間同步的資料欄位。
23 EXTENSIONS_HID_PROP_ALLOW_DELETIONS 21 確定是否可以刪除資料。
1d EXTENSIONS_HID_PROP_DATAENTRY 58 決定表單是否只允許新增加資料 (是) 或也允許加入其他特性 (否)。
20 EXTENSIONS_HID_PROP_CURSORSOURCE e1 確定表單要使用的內容。內容可以是現有的表格或查詢 (已在資料庫中建立)，也可以透過 SQL 陳述式來定義。輸入資料來源之前，您需要在 [內容類型] 中定義明確的類型。
1e EXTENSIONS_HID_PROP_DATASOURCE 27 定義表單要參照的資料來源。
1f EXTENSIONS_HID_PROP_SLAVEFIELDS 57 如果要建立子表單，請輸入用於儲存父表單欄位之可能值的變量。
19 EXTENSIONS_HID_PROP_CYCLE 29 確定如何使用 Tab 鍵進行瀏覽。
1e EXTENSIONS_HID_PROP_NAVIGATION 3c 指定是否可以使用底部表單列中的瀏覽功能。
21 EXTENSIONS_HID_PROP_SORT_CRITERIA 6f 指定表單中資料的排序條件。排序條件的規格遵循 SQL 規則，但不使用 ORDER BY 子句。
10 .uno%3ATabDialog 6c 在 [定位順序] 對話方塊中，可以修改使用者按 Tab 鍵切換聚焦控制項欄位的順序。
15 HID_TABORDER_CONTROLS 71 列出表單中的所有控制項。可以使用 Tab 鍵依照給予的順序從上向下選取這些控制項。
f .uno%3AAddField 5a 開啟一個視窗，您可以在此選取要增加至表單或報告的資料庫欄位。
11 SVX_HID_FIELD_SEL 7c 欄位選擇視窗會列出在 [表單特性] 中指定為資料來源的表格或查詢所含有的全部資料庫欄位。
1e .uno%3ASwitchControlDesignMode b7 開啟或關閉設計模式。此功能用於在設計模式與使用者模式之間快速切換。啟動此功能以編輯表單控制項，關閉此功能以使用表單控制項。
1d .uno%3ASwitchXFormsDesignMode b7 開啟或關閉設計模式。此功能用於在設計模式與使用者模式之間快速切換。啟動此功能以編輯表單控制項，關閉此功能以使用表單控制項。
11 SVX_HID_FM_DELETE 18 刪除選取的條目。
13 SVX_HID_FM_NEW_FORM 1e 在文件中建立新表單。
1a .uno%3AShowPropertyBrowser 31 啟動已選取項目的 [特性] 對話方塊。
18 SVX_HID_FM_RENAME_OBJECT 1e 重新命名選取的物件。
15 SVX_HID_FM_NEW_HIDDEN 93 在所選表單中建立隱藏的控制項，該控制項不會顯示在螢幕上。隱藏的控制項用於存放與表單一同傳送的資料。
15 .uno%3AShowFmExplorer 78 開啟 [表單助手]。[表單助手] 顯示了目前文件的所有表單和子表單以及各自包含的控制項。
e SVX_HID_FM_NEW 74 將新元素加入表單中。只有在 [表單助手] 中選取了某個表單後，才可呼叫 [新增] 功能。
16 SVX_HID_FORM_NAVIGATOR 76 [表單助手] 包含一個清單，其中列出了所有已建立的 (邏輯) 表單以及相應的控制項欄位。
13 .uno%3AOpenReadOnly 3d 在 [設計模式] 中開啟表單，以便編輯該表單。
11 .uno%3AUseWizards 3c 指定在插入新控制項時，是否自動啟動精靈。
15 .uno%3ADesignerDialog 64 指定是否要顯示或隱藏 [樣式和格式] 視窗，在此處您可以指定與管理樣式。
15 SVX_HID_STYLE_LISTBOX 84 允許您為目前段落、所選段落或所選物件指定樣式。在 [格式] - [樣式和格式] 中可以找到其他樣式。
16 .uno%3AStyleApplyState 84 允許您為目前段落、所選段落或所選物件指定樣式。在 [格式] - [樣式和格式] 中可以找到其他樣式。
11 .uno%3AStyleApply 84 允許您為目前段落、所選段落或所選物件指定樣式。在 [格式] - [樣式和格式] 中可以找到其他樣式。
13 .uno%3ACharFontName 48 允許您從清單中選取字型名稱，或直接輸入字型名稱。
11 .uno%3AFontHeight 4e 允許您從清單中選擇各種字型大小，或手動輸入所需大小。
1f .uno%3ATextdirectionLeftToRight 1e 指定文字的水平方向。
1f .uno%3ATextdirectionTopToBottom 1e 指定文字的垂直方向。
16 .uno%3ADecrementIndent 88 按一下 [減少縮排] 圖示，以縮減目前段落或儲存格內容的左縮排，並將其設至前一個預設定位點位置。
16 .uno%3AIncrementIndent 7f 按一下 [擴大縮排] 圖示，增加目前段落或儲存格內容的左縮排，並將其設至下一個定位點位置。
10 .uno%3ABackColor 141 對所選文字的背景套用目前的反白顯示顏色。如果未選取文字，按一下 [反白顯示] 圖示，選取您要反白顯示的文字，然後再按一下 [反白顯示] 圖示。若要變更反白顯示的顏色，請按一下 [反白顯示] 圖示旁邊的箭頭，然後按一下要使用的顏色。
16 .uno%3ABackgroundColor ab Click to open a toolbar where you can click a background color for a paragraph. The color is applied to the background of the current paragraph or the selected paragraphs.
18 .uno%3AParaspaceIncrease 53 按一下 擴大段落間隔 圖示，以增大所選段落上方的段落間隔。
18 .uno%3AParaspaceDecrease 52 按一下 [縮小段落間隔] 圖示可縮小選取段落上方的段落間隔。
15 .uno%3ASetBorderStyle 69 按一下邊框圖示以開啟邊框工具列，您可以在其中修改工作表區域或物件的邊框
10 .uno%3ALineStyle 6a 按一下此圖示，以開啟 [線條樣式] 工具列，您可以在其中修改邊框的線條樣式。
15 .uno%3AFrameLineColor 70 按一下框線顏色圖示，開啟 [邊框顏色] 工具列，透過它您可以變更物件邊框的顏色。
17 .uno%3AToggleAnchorType 27 您可以切換不同的標號選項。
14 .uno%3AOptimizeTable 51 開啟一個工具列，其中包含的功能可最佳化表格中的列與欄。
13 .uno%3ALineEndStyle 58 開啟 [箭頭] 工具列。使用顯示的符號來定義所選線條的端點樣式。
1d .uno%3AToggleObjectRotateMode 18 旋轉選取的物件。
12 .uno%3AObjectAlign 1e 修改選取物件的對齊。
15 .uno%3ADecrementLevel 4b 在編號階層或項目符號階層中，將選取的段落下移一級。
13 .uno%3AOutlineRight 4b 在編號階層或項目符號階層中，將選取的段落下移一級。
12 .uno%3AOutlineLeft 4b 在編號階層或項目符號階層中，將選取的段落上移一級。
15 .uno%3AIncrementLevel 4b 在編號階層或項目符號階層中，將選取的段落上移一級。
d .uno%3AMoveUp 30 將所選段落放到其上一個段落之前。
10 .uno%3AOutlineUp 30 將所選段落放到其上一個段落之前。
12 .uno%3AOutlineDown 30 將所選段落放到其下一個段落之後。
f .uno%3AMoveDown 30 將所選段落放到其下一個段落之後。
14 .uno%3ADefaultBullet 51 為所選段落指定項目符號點，或將其從項目編號段落中移除。
e .uno%3AOpenUrl 9f Loads a document specified by an entered URL. You can type a new URL, edit an URL, or select one from the list. Displays the full path of the current document.
a SID_RELOAD 2d 用上次儲存的版本代替目前文件。
d .uno%3AReload 2d 用上次儲存的版本代替目前文件。
e .uno%3AEditDoc 36 使得您可以編輯唯讀文件或資料庫表格。
21 SFX2_HID_HELP_TEXT_SELECTION_MODE c0 您可以在唯讀文字文件中或說明中啟用選擇游標。選擇[編輯] - [選取文字]；或開啟唯讀文件的右鍵功能表，並選擇[選取文字]。選擇游標不閃動。
15 .uno%3ASelectTextMode c0 您可以在唯讀文字文件中或說明中啟用選擇游標。選擇[編輯] - [選取文字]；或開啟唯讀文件的右鍵功能表，並選擇[選取文字]。選擇游標不閃動。
11 .uno%3ADSBEditDoc 36 開啟或關閉目前資料庫表格的編輯模式。
f SID_BROWSE_STOP 7a 按一下可中斷載入程序。在按下滑鼠的同時按住 Ctrl (Mac：按住 Command) 可中斷所有載入程序。
b .uno%3AStop 7a 按一下可中斷載入程序。在按下滑鼠的同時按住 Ctrl (Mac：按住 Command) 可中斷所有載入程序。
18 .uno%3AExportDirectToPDF 4a 將目前文件直接匯出為 PDF 文件。不顯示設定對話方塊。
15 .uno%3AStatusGetTitle 3b 顯示有關使用中 %PRODUCTNAME Basic 文件的資訊。
18 .uno%3AStatusGetPosition 68 顯示 %PRODUCTNAME Basic 文件中游標目前的位置。先指定列編號，然後指定欄編號。
1a SVX_HID_OFA_HYPERLINK_NAME 29 為 Internet URL 或檔案指定名稱。
19 SVX_HID_OFA_HYPERLINK_URL 56 您可以鍵入一個 URL，或者透過拖曳的方式從文件中插入一個 URL。
13 .uno%3ASetHyperlink 2c 將目前 URL 的超連結插入文件中。
1c SVX_HID_OFA_HYPERLINK_SEARCH 77 從子功能表按一下以選擇其中一個網際網路搜尋引擎。搜尋字詞輸入在 [URL 名稱] 方塊中。
1c SVX_HID_OFA_HYPERLINK_TARGET 29 指定所選 URL 的目標框架類型。
1c CUI_HID_ICCDIALOG_CANCEL_BTN 27 關閉對話方塊而不進行儲存。
1c .uno%3AOpenHyperlinkOnCursor 2f 在預設的 Web 瀏覽器中開啟超連結。
1b CUI_HID_ICCDIALOG_RESET_BTN 36 將對話方塊中的條目重設為原來的狀態。
1c .uno%3ACopyHyperlinkLocation 1d 將 URL 複製到剪貼簿。
1c CUI_HID_ICCDIALOG_CHOICECTRL 24 選取要插入的超連結類型。
14 .uno%3AEditHyperlink 22 開啟 [超連結] 對話方塊。
16 .uno%3AHyperlinkDialog 3f 開啟一個允許您建立和編輯超連結的對話方塊。
16 .uno%3ARemoveHyperlink 27 移除超連結，維持一般文字。
18 CUI_HID_ICCDIALOG_OK_BTN 1b 將資料套用至文件。
3f cui%3ACheckBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ACBX_ANONYMOUS 38 允許您以匿名使用者的身份登入 FTP 位址。
35 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_TEXT 1b 輸入超連結的名稱。
31 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_TEXT 1b 輸入超連結的名稱。
48 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_INTERNET 18 建立 http 超連結。
38 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ALB_FORM 39 指明是做為文字還是做為按鈕插入超連結。
37 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_INDICATION 30 指定超連結的可見文字或按鈕標籤。
1a CUI_HID_HYPERLINK_INTERNET 18 建立 http 超連結。
3a cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ACB_FRAME b1 輸入用以開啟連結檔案的訊框名稱，或選取清單中預先定義的訊框。若將此方塊保留為空白，連結的檔案會以目前的瀏覽器視窗開啟。
3a cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ACB_FRAME b1 輸入用以開啟連結檔案的訊框名稱，或選取清單中預先定義的訊框。若將此方塊保留為空白，連結的檔案會以目前的瀏覽器視窗開啟。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_BROWSE 40 開啟 Web 瀏覽器，您可以將所需的 URL 載入其中。
36 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_LOGIN 3e 如果使用的是 FTP 位址，請指定您的登入名稱。
38 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ALB_FORM 39 指明是做為文字還是做為按鈕插入超連結。
1a CUI_HID_HYPERDLG_INET_PATH 8b 輸入您想在按一下超連結時開啟的檔案之 URL。如果您未指定目標訊框，檔案會以目前的文件或訊框開啟。
34 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_MAIL%3ALB_FORM 39 指明是做為文字還是做為按鈕插入超連結。
37 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_PASSWD 38 如果使用的是 FTP 位址，請指定您的密碼。
3e cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_INDICATION 30 指定超連結的可見文字或按鈕標籤。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_TARGET 29 開啟[文件中的目標]對話方塊。
46 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_TELNET 1a 建立 Telnet 超連結。
1e CUI_HID_HYPERLINK_MARKWND_TREE 2d 指定要跳換到目標文件中的位置。
3d cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ACB_FRAME b1 輸入用以開啟連結檔案的訊框名稱，或選取清單中預先定義的訊框。若將此方塊保留為空白，連結的檔案會以目前的瀏覽器視窗開啟。
36 cui%3AComboBox%3ARID_SVXPAGE_HYPERLINK_MAIL%3ACB_FRAME b1 輸入用以開啟連結檔案的訊框名稱，或選取清單中預先定義的訊框。若將此方塊保留為空白，連結的檔案會以目前的瀏覽器視窗開啟。
3b cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ABTN_SCRIPT 8b 開啟 [指定巨集] 對話方塊，可在其中指定「滑鼠位在物件之上」或「觸發超連結」等事件的專用程式碼。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ABTN_SCRIPT 8b 開啟 [指定巨集] 對話方塊，可在其中指定「滑鼠位在物件之上」或「觸發超連結」等事件的專用程式碼。
1f CUI_HID_HYPERLINK_MARKWND_CLOSE 59 完整輸入超連結後，按一下[關閉]，以設定連結並退出此對話方塊。
35 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_TEXT 1b 輸入超連結的名稱。
3b cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ALB_FORM 39 指明是做為文字還是做為按鈕插入超連結。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_INTERNET%3AED_INDICATION 30 指定超連結的可見文字或按鈕標籤。
42 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ABTN_SCRIPT 8b 開啟 [指定巨集] 對話方塊，可在其中指定「滑鼠位在物件之上」或「觸發超連結」等事件的專用程式碼。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_INDICATION 30 指定超連結的可見文字或按鈕標籤。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_SCRIPT 8b 開啟 [指定巨集] 對話方塊，可在其中指定「滑鼠位在物件之上」或「觸發超連結」等事件的專用程式碼。
38 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_TEXT 1b 輸入超連結的名稱。
1f CUI_HID_HYPERLINK_MARKWND_APPLY 44 在 [超連結] 對話方塊的 [目標] 欄位中插入該目標。
43 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_INTERNET%3ARB_LINKTYP_FTP 17 建立 FTP 超連結。
34 cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_MAIL%3AED_SUBJECT 39 指定要在新訊息文件主題行中插入的主題。
40 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ARB_LINKTYP_NEWS 24 將新聞地址指定為超連結。
1a CUI_HID_HYPERDLG_MAIL_PATH 63 指定該地址完整的 URL，格式如下：mailto:<EMAIL> 或 news:group.server.com。
40 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ARB_LINKTYP_MAIL 33 將指定的電子郵件地址指定為超連結。
16 CUI_HID_HYPERLINK_MAIL 33 將指定的電子郵件地址指定為超連結。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_MAIL%3ABTN_ADRESSBOOK 27 隱藏或顯示資料來源瀏覽器。
1a CUI_HID_HYPERLINK_DOCUMENT 8b 輸入您想在按一下超連結時開啟的檔案之 URL。如果您未指定目標訊框，檔案會以目前的文件或訊框開啟。
3b cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3AED_TARGET_DOC 41 將超連結的目標指定至在[路徑]下所指定的文件。
3f cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_BROWSE 31 開啟 [文件內的連結目標] 對話方塊。
41 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_DOCUMENT%3ABTN_FILEOPEN 46 開啟 [開啟舊檔] 對話方塊，您可以在其中選取檔案。
19 CUI_HID_HYPERDLG_DOC_PATH 8b 輸入您想在按一下超連結時開啟的檔案之 URL。如果您未指定目標訊框，檔案會以目前的文件或訊框開啟。
44 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ARB_EDITLATER 27 指定建立文件但不立即開啟。
3f cui%3AEdit%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3AED_PATH_NEWDOC 3d 輸入您想在按一下超連結時開啟的檔案之 URL。
45 cui%3AListBox%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ALB_DOCUMENT_TYPES 21 指定新文件的檔案類型。
42 cui%3AImageButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ABTN_CREATE 46 開啟 [選擇路徑] 對話方塊，您可以在其中選取路徑。
42 cui%3ARadioButton%3ARID_SVXPAGE_HYPERLINK_NEWDOCUMENT%3ARB_EDITNOW 36 指定建立新文件並立即開啟以進行編輯。
1d CUI_HID_HYPERLINK_NEWDOCUMENT 36 指定建立新文件並立即開啟以進行編輯。
13 .uno%3APreviousPage 1b 移回文件的上一頁。
d .uno%3APageUp 1b 移回文件的上一頁。
f .uno%3APageDown 21 向前移至文件的下一頁。
f .uno%3ANextPage 21 向前移至文件的下一頁。
15 .uno%3AGoToStartOfDoc 1b 移至文件的第一頁。
10 .uno%3AFirstPage 1b 移至文件的第一頁。
13 .uno%3AGoToEndOfDoc 1e 移至文件的最後一頁。
f .uno%3ALastPage 1e 移至文件的最後一頁。
f .uno%3ACloseWin 18 關閉目前的視窗。
21 DBACCESS_HID_BROWSER_RENAME_ENTRY c7 若要重新命名項目，請呼叫此指令，並輸入新名稱。您也可以選取此項目並按 F2 鍵以達此目的。資料庫必須支援重新命名功能，否則無法啟用此指令。
1e DBACCESS_HID_BROWSER_CLOSECONN 68 關閉與資料來源的連線。請參閱 [選項] 對話方塊中的 [%PRODUCTNAME Base] - [連線]。
18 .uno%3ADSBrowserExplorer 33 開啟與關閉資料來源檔案總管的檢視。
22 DBACCESS_HID_BROWSER_EDIT_DATABASE 2d 開啟所選的資料庫檔案進行編輯。
21 DBACCESS_HID_BROWSER_ADMINISTRATE c4 開啟對話方塊，在註冊的資料庫清單中增加/編輯/移除資料庫檔案。在 [選項] 對話方塊中選擇 [%PRODUCTNAME Base] - [資料庫]，也會開啟相同的對話方塊。
f .uno%3ASortDown 30 按向下順序排序所選欄位中的資料。
11 .uno%3AAutoFilter 36 根據目前所選的資料欄位內容篩選條目。
17 .uno%3ARemoveFilterSort 3f 取消篩選設定，並顯示目前表格中的所有資料。
28 DBACCESS_HID_BROWSER_REFRESH_REBUILDVIEW 5a 重建資料庫表格的檢視。當您變更了表格的結構時，請使用此指令。
e .uno%3ARefresh 18 更新顯示的資料。
13 .uno%3ASbaBrwInsert 51 將已標記記錄的所有欄位插入到目前文件中游標所在的位置。
32 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_TABLE_COL 30 列出所有要插入文件中的資料庫欄。
37 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_DBFMT_FROM_USR 5a 如果沒有接受某些資料欄位的格式資訊，請從清單中指定一種格式。
35 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_AS_TABLE 48 將在資料源瀏覽器中選取的資料作為表格插入文件中。
3b sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ALL_FROM 3d 移除 [表格欄] 清單方塊中的所有資料庫欄位。
3b sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ONE_FROM 3c 移除 [表格欄]清單方塊中所選的資料庫欄位。
36 sw%3APushButton%3ADLG_AP_INSERT_DB_SEL%3APB_TBL_FORMAT 73 開啟 [表格格式] 對話方塊，此方塊允許您定義諸如邊框、背景和欄寬之類的表格屬性。
3a sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_DBFMT_FROM_DB 1b 接受資料庫的格式。
38 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_HEADL_EMPTY 2a 將空白標題行插入文字表格中。
3b sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_DBFMT_FROM_USR 5a 如果沒有接受某些資料欄位的格式資訊，請從清單中指定一種格式。
39 sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ONE_TO 3e 將選取的資料庫欄位移到[表格欄]清單方塊中。
39 sw%3AImageButton%3ADLG_AP_INSERT_DB_SEL%3AIB_DBCOL_ALL_TO 44 將所有列出的資料庫欄位移到[表格欄]清單方塊中。
36 sw%3ACheckBox%3ADLG_AP_INSERT_DB_SEL%3ACB_TABLE_HEADON 39 指定是否要在文字表格的欄中插入標題行。
37 sw%3APushButton%3ADLG_AP_INSERT_DB_SEL%3APB_TBL_AUTOFMT 6b 開啟[自動格式]對話方塊，在此處您可以選取插入此表格時立即套用的格式樣式。
39 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_HEADL_COLNMS 4e 使用資料庫表格的欄位名稱作為文字表格中每一欄的標題。
36 sw%3AListBox%3ADLG_AP_INSERT_DB_SEL%3ALB_TBL_DB_COLUMN 30 指定要插入文字表格中的資料庫欄。
34 sw%3ARadioButton%3ADLG_AP_INSERT_DB_SEL%3ARB_AS_TEXT 48 將在資料源瀏覽器中選取的資料作為文字插入文件中。
28 .uno%3ADataSourceBrowser%2FInsertContent 42 透過已標記的記錄來更新現有資料庫欄位的內容。
17 .uno%3ADSBInsertContent 42 透過已標記的記錄來更新現有資料庫欄位的內容。
11 .uno%3AFilterCrit 1e 允許您設定篩選選項。
1b DBACCESS_HID_DLG_FILTERCRIT 1e 允許您設定篩選選項。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL2 27 指定篩選此欄位所需的數值。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND1 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD2 42 指定目前表格中的欄位名稱，將其設定到引數中。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE3 27 指定篩選此欄位所需的數值。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP1 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOND2 44 對於下列引數，您可在邏輯運算子 AND/OR 之間選擇。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE1 27 指定篩選此欄位所需的數值。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL3 27 指定篩選此欄位所需的數值。
1f .uno%3ADataFilterStandardFilter 2a 指定邏輯條件以篩選表格資料。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD2 42 指定目前表格中的欄位名稱，將其設定到引數中。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND3 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
28 sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_OP2 44 對於下列引數，您可在邏輯運算子 AND/OR 之間選擇。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP3 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD3 42 指定目前表格中的欄位名稱，將其設定到引數中。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD3 42 指定目前表格中的欄位名稱，將其設定到引數中。
31 dbaccess%3AEdit%3ADLG_FILTERCRIT%3AET_WHEREVALUE2 27 指定篩選此欄位所需的數值。
34 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHEREFIELD1 42 指定目前表格中的欄位名稱，將其設定到引數中。
2b sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_FIELD1 42 指定目前表格中的欄位名稱，將其設定到引數中。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOND3 44 對於下列引數，您可在邏輯運算子 AND/OR 之間選擇。
28 sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_OP1 44 對於下列引數，您可在邏輯運算子 AND/OR 之間選擇。
2a sc%3AComboBox%3ARID_SCDLG_FILTER%3AED_VAL1 27 指定篩選此欄位所需的數值。
33 dbaccess%3AListBox%3ADLG_FILTERCRIT%3ALB_WHERECOMP2 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
2a sc%3AListBox%3ARID_SCDLG_FILTER%3ALB_COND2 5f 指定比較運算子，透過它可以連結 [欄位名稱] 和 [數值] 欄位中的條目。
1a DBACCESS_HID_DLG_ORDERCRIT 24 指定資料顯示的排序條件。
10 .uno%3AOrderCrit 24 指定資料顯示的排序條件。
19 CUI_HID_SEARCH_BTN_SEARCH 18 開始或取消搜尋。
17 CUI_HID_SEARCH_WILDCARD 2c 搜尋時允許使用萬用字元 * 或 ?。
13 CUI_HID_SEARCH_CASE 24 指定搜尋時要區分大小寫。
3c cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORNULL 24 指定搜尋不含資料的欄位。
3c cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORTEXT 48 在方塊中輸入搜尋術語，或者從清單中選取搜尋術語。
13 CUI_HID_SEARCH_TEXT 48 在方塊中輸入搜尋術語，或者從清單中選取搜尋術語。
10 .uno%3ARecSearch 21 搜尋資料庫表格和表單。
18 CUI_HID_SEARCH_STARTOVER 87 重新開始搜尋。向前搜尋是從第一條記錄開始重新搜尋。向後搜尋則是從最後一條記錄開始重新搜尋。
18 CUI_HID_SEARCH_FORMATTER 3f 指定在目前文件中搜尋時考量所有的欄位格式。
17 CUI_HID_SEARCH_POSITION 33 指定搜尋術語和欄位內容之間的關係。
3a cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SINGLEFIELD 24 在指定的資料欄位中搜尋。
18 CUI_HID_SEARCH_ALLFIELDS 1b 在所有欄位中搜尋。
18 CUI_HID_SEARCH_BTN_CLOSE 61 關閉對話方塊。上一個搜尋的設定將會一直儲存，直到您退出 %PRODUCTNAME。
1d CUI_HID_SEARCH_FIELDSELECTION 24 在指定的資料欄位中搜尋。
3f cui%3ARadioButton%3ARID_SVXDLG_SEARCHFORM%3ARB_SEARCHFORNOTNULL 24 指定搜尋包含資料的欄位。
17 CUI_HID_SEARCH_BACKWARD 57 指定以相反方向搜尋，即從最後一個資料條目到第一個資料條目。
2f cui%3AListBox%3ARID_SVXDLG_SEARCHFORM%3ALB_FORM 30 指定要在其中進行搜尋的邏輯表單。
16 CUI_HID_SEARCH_REGULAR 24 使用常規表示式進行搜尋。
11 .uno%3AFormFilter 3f 提示資料庫伺服器依指定的條件篩選可見資料。
13 .uno%3AFormFiltered 39 在已篩選的和未篩選的表格檢視之間切換。
15 .uno%3AViewFormAsGrid 33 在表單檢視中時，啟動附加表格檢視。
1c .uno%3ADSBDocumentDataSource 42 在資料來源瀏覽器中顯示連結至目前文件的表格。
2d .uno%3ADataSourceBrowser%2FDocumentDataSource 42 在資料來源瀏覽器中顯示連結至目前文件的表格。
14 .uno%3ASbaExecuteSql 29 執行 SQL 查詢並顯示查詢結果。
13 .uno%3ADBClearQuery 36 清除查詢並從設計視窗中移除全部表格。
39 dbaccess%3ARadioButton%3ADLG_JOIN_TABADD%3ARB_CASE_TABLES 12 僅顯示表格。
20 DBACCESS_HID_JOINSH_ADDTAB_CLOSE 25 關閉 [新增表格] 對話方塊。
3a dbaccess%3ARadioButton%3ADLG_JOIN_TABADD%3ARB_CASE_QUERIES 12 僅顯示查詢。
24 DBACCESS_HID_JOINSH_ADDTAB_TABLELIST 2a 指定要插入設計視窗中的表格。
35 dbaccess%3APushButton%3ADLG_JOIN_TABADD%3APB_ADDTABLE 1e 插入目前選取的表格。
f .uno%3AAddTable 2a 指定要插入設計視窗中的表格。
19 .uno%3ADBChangeDesignMode 2c 顯示查詢的設計檢視或 SQL 檢視。
13 .uno%3ASbaNativeSql 85 在原生 SQL 模式下可以輸入 SQL 指令，這些指令不需經過 $[officename] 解譯，而是直接傳送到資料源中。
16 .uno%3ADBViewFunctions 49 在 [查詢設計] 視窗的設計檢視的下方顯示「函數」列。
17 .uno%3ADBViewTableNames 3a 在 [查詢設計] 下方區域中顯示「表格」列。
14 .uno%3ADBViewAliases 3a 在 [查詢設計] 下方區域中顯示「別名」列。
17 .uno%3ADBDistinctValues 53 透過參數 DISTINCT 延伸 SQL 查詢在目前欄中建立的 Select 陳述式。
13 .uno%3ASelectObject 2a 允許您選取目前文件中的物件。
11 SW_HID_SOURCEVIEW 84 顯示目前 HTML 文件的來源文字。若要檢視新文件的 HTML 原始檔，您必須先將新文件儲存為 HTML 文件。
11 .uno%3ASourceView 84 顯示目前 HTML 文件的來源文字。若要檢視新文件的 HTML 原始檔，您必須先將新文件儲存為 HTML 文件。
14 .uno%3APageStyleName 6f 顯示目前的頁面樣式。連按兩下滑鼠可編輯樣式，按一下滑鼠右鍵可選取其他樣式。
16 .uno%3AStatusPageStyle 6f 顯示目前的頁面樣式。連按兩下滑鼠可編輯樣式，按一下滑鼠右鍵可選取其他樣式。
13 .uno%3ALayoutStatus 6f 顯示目前的頁面樣式。連按兩下滑鼠可編輯樣式，按一下滑鼠右鍵可選取其他樣式。
10 .uno%3AStateZoom 24 指定目前頁面的顯示比例。
11 .uno%3AInsertMode 59 顯示目前的插入模式。您可以在 INSRT = 插入和 OVER = 覆寫之間切換。
14 .uno%3ASelectionMode 79 顯示目前的選擇模式。您可以在 STD = 標準、EXT = 擴展、ADD = 補充、BLK = 區段選擇之間切換。
1a .uno%3AStatusSelectionMode 79 顯示目前的選擇模式。您可以在 STD = 標準、EXT = 擴展、ADD = 補充、BLK = 區段選擇之間切換。
15 .uno%3AModifiedStatus 82 若尚未儲存文件的變更，則狀態列上的此欄位中會顯示一個「*」。這也適用於尚未儲存的新文件。
f .uno%3AModified 82 若尚未儲存文件的變更，則狀態列上的此欄位中會顯示一個「*」。這也適用於尚未儲存的新文件。
13 .uno%3AModifyStatus 82 若尚未儲存文件的變更，則狀態列上的此欄位中會顯示一個「*」。這也適用於尚未儲存的新文件。
12 .uno%3ACurrentTime 15 顯示目前時間。
12 .uno%3ACurrentDate 15 顯示目前日期。
4d cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_MTR_WIDTH 18 定義單塊的寬度。
1a .uno%3AGraphicFilterInvert 63 反轉顏色影像的顏色值，或灰階影像的亮度值。再次套用篩選可復原效果。
4e cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_MTR_HEIGHT 18 定義單塊的高度。
1b .uno%3AGraphicFilterSharpen 2a 套用高通濾波器使影像更清晰。
1b .uno%3AGraphicFilterToolbox 76 [圖片] 工具列上的此圖示可開啟 [圖形篩選] 工具列，讓您將各種篩選運用於所選圖片上。
4f cui%3ANumericField%3ARID_SVX_GRFFILTER_DLG_POSTER%3ADLG_FILTERPOSTER_NUM_POSTER 24 指定影像要減少的顏色數。
1c .uno%3AGraphicFilterSolarize 9f 開啟用於定義曝光的對話方塊。曝光是指一種如同相片顯影期間光線太強而產生的結果一樣的效果。部分顏色發生反轉。
1a .uno%3AGraphicFilterSmooth 33 套用低通濾波器可使影像柔化或模糊。
1a .uno%3AGraphicFilterPoster 33 開啟對話方塊，以確定海報的顏色數。
55 cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_SOLARIZE%3ADLG_FILTERSOLARIZE_MTR_THRESHOLD 48 指定亮度 (以百分比表示)，高於此亮度的像素會曝光。
4a cui%3ACheckBox%3ARID_SVX_GRFFILTER_DLG_MOSAIC%3ADLG_FILTERMOSAIC_CBX_EDGES 21 強化或銳化物件的邊緣。
4f cui%3ACheckBox%3ARID_SVX_GRFFILTER_DLG_SOLARIZE%3ADLG_FILTERSOLARIZE_CBX_INVERT 24 可指定同時反轉全部像素。
19 .uno%3AGraphicFilterSepia 75 所有像素均設定為其灰值，接著依您指定的量縮減綠色及藍色色頻。紅色色頻維持不變。
19 .uno%3AGraphicFilterSobel 60 將影像顯示成炭筆素描。以黑色繪製影像的輪廓，不再顯示原來的顏色。
1a .uno%3AGraphicFilterMosaic 36 將小的像素群組連結成同色的矩形區域。
1a .uno%3AGraphicFilterRelief 2a 顯示用於建立浮雕的對話方塊。
1a .uno%3AGraphicFilterPopart 27 將影像轉換成流行藝術風格。
4b cui%3AMetricField%3ARID_SVX_GRFFILTER_DLG_SEPIA%3ADLG_FILTERSEPIA_MTR_SEPIA 6e 定義時效強度 (以百分比表示)。0% 時顯示所有像素的灰值。100% 時僅保留紅色色頻。
1f .uno%3AGraphicFilterRemoveNoise 24 套用中位篩選可清除雜色。
f .uno%3AGrafMode 81 列出所選圖形物件的檢視屬性。不會變更目前檔案的內嵌或連結圖形物件，只會變更物件的檢視。
e .uno%3AGrafRed 3b 為所選圖形物件指定紅色 RGB 顏色成份比例。
10 .uno%3AGrafGreen 3b 為所選圖形物件指定綠色 RGB 顏色成份比例。
f .uno%3AGrafBlue 35 為所選圖形指定藍色 RGB 顏色成份比例。
14 .uno%3AGrafLuminance 24 指定所選圖形物件的亮度。
13 .uno%3AGrafContrast 33 指定用於檢視所選圖形影像的對比度。
10 .uno%3AGrafGamma 52 指定所選物件檢視的 Gamma 值，此值會影響中間色調值的亮度。
17 .uno%3AGrafTransparence 21 指定圖形物件的透明度。
13 .uno%3AGrafAttrCrop 66 可剪裁插入圖片的顯示部分。僅會剪裁顯示的部分，而不會變更插入的圖片。
b .uno%3ACrop 3c 拖曳八個剪裁控點的任何一個，以剪裁圖片。
1c .uno%3ABasicShapes.trapezoid 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
25 .uno%3ABasicShapes.isosceles-triangle 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1c .uno%3ABasicShapes.rectangle 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
18 .uno%3ABasicShapes.paper 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
12 .uno%3ABasicShapes 42 開啟基本形狀工具列，可用來插入圖形到文件中。
17 .uno%3ABasicShapes.ring 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1b .uno%3ABasicShapes.pentagon 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
20 .uno%3ABasicShapes.round-quadrat 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
22 .uno%3ABasicShapes.round-rectangle 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
16 .uno%3ABasicShapes.can 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
18 .uno%3ABasicShapes.frame 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
18 .uno%3ABasicShapes.cross 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1a .uno%3ABasicShapes.octagon 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
21 .uno%3ABasicShapes.right-triangle 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1c .uno%3ABasicShapes.block-arc 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1a .uno%3ABasicShapes.diamond 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
17 .uno%3ABasicShapes.cube 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1a .uno%3ABasicShapes.hexagon 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
20 .uno%3ABasicShapes.parallelogram 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1d .uno%3ABasicShapes.circle-pie 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1a .uno%3ABasicShapes.ellipse 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
19 .uno%3ABasicShapes.circle 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
1a .uno%3ABasicShapes.quadrat 51 按一下基本形狀工具列上的圖示，然後在文件中拖曳該形狀。
25 .uno%3AArrowShapes.left-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3AArrowShapes.left-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3AArrowShapes.pentagon-right 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AArrowShapes.notched-right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3AArrowShapes.left-right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3AArrowShapes.circular-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AArrowShapes.right-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1e .uno%3AArrowShapes.split-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
22 .uno%3AArrowShapes.s-sharped-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AArrowShapes.quad-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
29 .uno%3AArrowShapes.up-right-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3AArrowShapes.up-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3AArrowShapes.quad-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
20 .uno%3AArrowShapes.up-down-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
24 .uno%3AArrowShapes.split-round-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AArrowShapes.corner-right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1e .uno%3AArrowShapes.right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
29 .uno%3AArrowShapes.up-down--arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AArrowShapes.down-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AArrowShapes.striped-right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AArrowShapes.up-right-down-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1b .uno%3AArrowShapes.up-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
2b .uno%3AArrowShapes.left-right-arrow-callout 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1a .uno%3AArrowShapes.chevron 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3AArrowShapes.up-right-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3AArrowShapes.down-arrow 54 按一下箭號圖案工具列的圖示，然後在文件中拖曳以繪製形狀。
12 .uno%3AArrowShapes 45 開啟箭號圖案工具列，您可在此插入圖形到文件中。
23 .uno%3ACalloutShapes.line-callout-1 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
14 .uno%3ACalloutShapes 48 開啟圖說文字工具列，您可以在此將圖形插入文件中。
22 .uno%3ACalloutShapes.cloud-callout 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3ACalloutShapes.line-callout-3 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3ACalloutShapes.line-callout-2 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
22 .uno%3ACalloutShapes.round-callout 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
2e .uno%3ACalloutShapes.round-rectangular-callout 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
28 .uno%3ACalloutShapes.rectangular-callout 54 按一下圖說文字工具列的圖示，然後在文件中拖曳以繪製形狀。
14 .uno%3AColorSettings 49 您可以使用 [顏色] 工具列編輯選取的物件之某些特性。
2d .uno%3AFlowChartShapes.flowchart-manual-input 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2c .uno%3AFlowChartShapes.flowchart-preparation 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AFlowChartShapes.flowchart-sort 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
31 .uno%3AFlowChartShapes.flowchart-manual-operation 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
28 .uno%3AFlowChartShapes.flowchart-display 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AFlowChartShapes.flowchart-delay 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
31 .uno%3AFlowChartShapes.flowchart-summing-junction 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2b .uno%3AFlowChartShapes.flowchart-terminator 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
33 .uno%3AFlowChartShapes.flowchart-predefined-process 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
28 .uno%3AFlowChartShapes.flowchart-process 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
16 .uno%3AFlowChartShapes 43 開啟 [流程圖] 工具列，在此可將圖形插入文件中。
2a .uno%3AFlowChartShapes.flowchart-connector 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
31 .uno%3AFlowChartShapes.flowchart-internal-storage 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
29 .uno%3AFlowChartShapes.flowchart-decision 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
28 .uno%3AFlowChartShapes.flowchart-collate 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
33 .uno%3AFlowChartShapes.flowchart-off-page-connector 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
29 .uno%3AFlowChartShapes.flowchart-document 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AFlowChartShapes.flowchart-data 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
28 .uno%3AFlowChartShapes.flowchart-extract 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
36 .uno%3AFlowChartShapes.flowchart-direct-access-storage 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
32 .uno%3AFlowChartShapes.flowchart-alternate-process 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2e .uno%3AFlowChartShapes.flowchart-magnetic-disk 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
32 .uno%3AFlowChartShapes.flowchart-sequential-access 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2c .uno%3AFlowChartShapes.flowchart-stored-data 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
26 .uno%3AFlowChartShapes.flowchart-merge 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3AFlowChartShapes.flowchart-or 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2d .uno%3AFlowChartShapes.flowchart-punched-tape 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
25 .uno%3AFlowChartShapes.flowchart-card 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
2e .uno%3AFlowChartShapes.flowchart-multidocument 55 按一下 [流程圖] 工具列的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3AFontworkGalleryFloater 5d 圖示會在美術字型畫廊中開啟，您可在此插入圖形文字藝術到文件中。
17 .uno%3AFormatPaintbrush 9f 先選取部分文字或一個物件，然後按一下此圖示。接著按一下或拖曳到其他文字或按一下某物件，即可套用相同的格式。
18 .uno%3AStarShapes.signet 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
11 .uno%3AStarShapes 49 開啟 [星星及綵帶] 工具列，在此可將圖形插入文件中。
17 .uno%3AStarShapes.star5 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
17 .uno%3AStarShapes.star4 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
23 .uno%3AStarShapes.horizontal-scroll 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1b .uno%3AStarShapes.doorplate 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
18 .uno%3AStarShapes.star12 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
18 .uno%3AStarShapes.star24 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
16 .uno%3AStarShapes.bang 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3AStarShapes.vertical-scroll 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1f .uno%3AStarShapes.concave-star6 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
17 .uno%3AStarShapes.star8 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
17 .uno%3AStarShapes.star6 5e 按一下 [星星及綵帶] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1e .uno%3ASymbolShapes.quad-bevel 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1e .uno%3ASymbolShapes.left-brace 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
13 .uno%3ASymbolShapes 45 開啟符號形狀工具列，您可在此插入圖片到文件中。
1f .uno%3ASymbolShapes.right-brace 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1a .uno%3ASymbolShapes.smiley 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3ASymbolShapes.octagon-bevel 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3ASymbolShapes.forbidden 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
19 .uno%3ASymbolShapes.cloud 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1a .uno%3ASymbolShapes.flower 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1a .uno%3ASymbolShapes.puzzle 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1d .uno%3ASymbolShapes.lightning 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
18 .uno%3ASymbolShapes.moon 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3ASymbolShapes.diamond-bevel 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
1e .uno%3ASymbolShapes.brace-pair 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
20 .uno%3ASymbolShapes.left-bracket 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
17 .uno%3ASymbolShapes.sun 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
21 .uno%3ASymbolShapes.right-bracket 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
20 .uno%3ASymbolShapes.bracket-pair 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
19 .uno%3ASymbolShapes.heart 5b 按一下 [符號形狀] 工具列上的圖示，然後在文件中拖曳以繪製形狀。
2f sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_BACKWARDS 24 從游標目前位置向後搜尋。
30 sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_WRAPAROUND 3f 搜尋整個「說明」頁面，從游標目前位置開始。
30 sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_WHOLEWORDS 1b 僅尋找完整的字詞。
2c sfx2%3APushButton%3ARID_DLG_SEARCH%3APB_FIND 24 尋找搜尋術語的下一實例。
22 SFX2_HID_HELP_TOOLBOXITEM_BACKWARD 15 向後移至上一頁
1f SFX2_HID_HELP_TOOLBOXITEM_INDEX 1b 隱藏或顯示瀏覽窗格
18 SFX2_HID_HELP_TABCONTROL 5c 說明視窗的瀏覽窗格包含標籤頁面 [內容]、[索引]、[搜尋] 和 [書籤]。
15 SFX2_HID_HELP_LISTBOX 59 清單方塊位於最頂端，您可以於其中選擇其他 %PRODUCTNAME 說明模組。
2c sfx2%3AComboBox%3ARID_DLG_SEARCH%3AED_SEARCH 48 輸入要搜尋的文字，或者從清單中選取一個文字條目。
1f SFX2_HID_HELP_TOOLBOXITEM_START 21 移至目前說明主題的首頁
21 SFX2_HID_HELP_TOOLBOXITEM_FORWARD 15 向前移至下一頁
15 SFX2_HID_HELP_TOOLBOX 39 [工具列] 包含用於控制說明系統的重要功能
1f SFX2_HID_HELP_TOOLBOXITEM_PRINT 12 列印目前頁面
11 .uno%3AHelpOnHelp 1e 提供說明系統的摘要。
26 SFX2_HID_HELP_TOOLBOXITEM_SEARCHDIALOG 28 開啟 [在本頁尋找] 對話方塊。
23 SFX2_HID_HELP_TOOLBOXITEM_BOOKMARKS 15 將此頁加入書籤
2f sfx2%3ACheckBox%3ARID_DLG_SEARCH%3ACB_MATCHCASE 18 區分大小寫文字。
31 sfx2%3APushButton%3ATP_HELP_INDEX%3APB_OPEN_INDEX 21 按一下以顯示所選主題。
2a sfx2%3AComboBox%3ATP_HELP_INDEX%3ACB_INDEX 42 按兩下某個條目，或在索引中鍵入要尋找的字詞。
2f sfx2%3ACheckBox%3ATP_HELP_SEARCH%3ACB_FULLWORDS 5a 指定是否對您所輸入的項目執行精確搜尋。僅搜尋完全符合的字詞。
2e sfx2%3APushButton%3ATP_HELP_SEARCH%3APB_SEARCH 3f 按一下，以啟動對所輸入項目的全部文字搜尋。
2b sfx2%3ACheckBox%3ATP_HELP_SEARCH%3ACB_SCOPE 4e 選取這個欄位，就可以只在文件標題中搜尋所輸入的項目。
2c sfx2%3AComboBox%3ATP_HELP_SEARCH%3AED_SEARCH 45 在此輸入搜尋項目。這個搜尋可以不用考慮大小寫。
2b sfx2%3AListBox%3ATP_HELP_SEARCH%3ALB_RESULT 66 列出全文搜尋中所找到的頁面標頭。在所要的項目上連按兩下便可顯示頁面。
33 sfx2%3APushButton%3ATP_HELP_SEARCH%3APB_OPEN_SEARCH 4b 按一下此處，便能在搜尋結果清單中顯示所選取的條目。
1e SFX2_HID_HELP_BOOKMARKS_RENAME 3c 會開啟為「書籤」輸入另一個名稱的對話方塊
1c SFX2_HID_HELP_BOOKMARKS_OPEN 21 會顯示所選取的說明主題
31 sfx2%3AListBox%3ATP_HELP_BOOKMARKS%3ALB_BOOKMARKS 87 在「書籤」上按兩下或按下 (Enter) 鍵，便可開啟指定的說明頁。按一下滑鼠右鍵便可開啟右鍵功能表。
36 sfx2%3AEdit%3ADLG_HELP_ADDBOOKMARK%3AED_BOOKMARK_TITLE 42 顯示書籤頁的名稱。您也可以為書籤鍵入新名稱。
1e SFX2_HID_HELP_BOOKMARKS_DELETE 21 會刪除所選取的「書籤」
1e SFX2_HID_HELP_TABPAGE_CONTENTS 5a 顯示主要說明主題，已使用類似方法排列至檔案管理者中的資料夾。
1a SVX_HID_POPUP_LINEEND_CTRL f 選取方向。
20 .uno%3AExtrusionDirectionFloater 21 開啟「突出方向」視窗。
1c .uno%3AExtrusionDepthFloater 21 開啟「突出深度」視窗。
1e .uno%3AExtrusionSurfaceFloater 21 開啟「突出表面」視窗。
18 .uno%3AExtrusionTiltLeft 24 讓所選物件向左傾斜五度。
16 .uno%3AExtrusionTiltUp 24 讓所選物件向上傾斜五度。
17 .uno%3AExtrusion3DColor 1e 開啟突出色彩工具列。
3c svx%3AMetricField%3ARID_SVX_MDLG_EXTRUSION_DEPTH%3AMTR_DEPTH 15 輸入突出深度。
16 .uno%3AExtrusionToggle 2e 對選取的物件開啟和關閉 3D 效果。
19 .uno%3AExtrusionTiltRight 24 讓所選物件向右傾斜五度。
18 .uno%3AExtrusionTiltDown 24 讓所選物件向下傾斜五度。
1f .uno%3AExtrusionLightingFloater 21 開啟「突出照明」視窗。
14 .uno%3AAutoPilotMenu 54 指導您建立商務信函和私人信函、傳真、會議議程、簡報等等。
1a WIZARDS_HID_LTRWIZARD_BACK 24 檢視前幾步中所做的選擇。
1c WIZARDS_HID_LTRWIZARD_CREATE 5d 自動檔案助理依據您的選擇建立新的文件樣式，並將其儲存在硬碟上。
16 .uno%3AAutoPilotLetter 30 啟動信函文件樣式的自動檔案助理。
1a WIZARDS_HID_LTRWIZARD_NEXT 2d 儲存目前的設定，並繼續下一頁。
38 service%3Acom.sun.star.wizards.letter.CallWizard%3Fstart 30 啟動信函文件樣式的自動檔案助理。
1c EXTENSIONS_HID_ABSPILOT_NEXT 2d 儲存目前的設定，並繼續下一頁。
1f WIZARDS_HID_DLGIMPORT_0_CMDPREV 24 檢視前幾步中所做的選擇。
20 EXTENSIONS_HID_ABSPILOT_PREVIOUS 24 檢視前幾步中所做的選擇。
1f WIZARDS_HID_DLGIMPORT_0_CMDNEXT 2d 儲存目前的設定，並繼續下一頁。
23 WIZARDS_HID_LTRWIZ_OPTPRIVATELETTER 1e 指定要建立私人信函。
13 SW_HID_LETTER_PAGE1 36 指定要建立私人信函還是建立商務信函。
24 WIZARDS_HID_LTRWIZ_OPTBUSINESSLETTER 2a 指定要建立商務信函文件樣式。
23 WIZARDS_HID_LTRWIZ_LSTBUSINESSSTYLE 27 選取您信函範本的設計版式。
23 WIZARDS_HID_LTRWIZ_CHKBUSINESSPAPER 78 指定是否使用已經包含壓印標誌、地址或頁尾行的信紙。精靈接著顯示箋頭版面配置頁面。
22 WIZARDS_HID_LTRWIZ_LSTPRIVATESTYLE 27 選取您信函範本的設計版式。
27 WIZARDS_HID_LTRWIZ_LSTPRIVOFFICIALSTYLE 27 選取您信函範本的設計版式。
28 WIZARDS_HID_LTRWIZ_OPTPRIVOFFICIALLETTER 24 指定要建立正式私人信函。
29 WIZARDS_HID_LTRWIZ_CHKPAPERCOMPANYADDRESS 4c 指定地址已經列印在箋頭信紙上。%PRODUCTNAME 不列印地址。
1e WIZARDS_HID_LTRWIZ_NUMADDRESSY 2a 設定物件與頁面上邊距的間隔。
1b WIZARDS_HID_LTRWIZ_NUMLOGOX 2a 設定物件與頁面左邊距的間隔。
22 WIZARDS_HID_LTRWIZ_NUMFOOTERHEIGHT 6a 輸入頁尾區域的高度，此區域已經壓印在箋頭信紙上。%PRODUCTNAME 不列印該區域。
23 WIZARDS_HID_LTRWIZ_NUMADDRESSHEIGHT 18 定義物件的高度。
1f WIZARDS_HID_LTRWIZ_NUMLOGOWIDTH 18 定義物件的寬度。
20 WIZARDS_HID_LTRWIZ_NUMLOGOHEIGHT 18 定義物件的高度。
26 WIZARDS_HID_LTRWIZ_CHKPAPERCOMPANYLOGO 4f 指定在箋頭信紙上已列印有標誌。%PRODUCTNAME 不會列印標誌。
1b WIZARDS_HID_LTRWIZ_NUMLOGOY 2a 設定物件與頁面上邊距的間隔。
13 SW_HID_LETTER_PAGE2 39 在此區域中選擇一個用於您的樣式的標記。
22 WIZARDS_HID_LTRWIZ_NUMADDRESSWIDTH 18 定義物件的寬度。
21 WIZARDS_HID_LTRWIZ_CHKPAPERFOOTER 52 指定頁尾區域已經列印在箋頭信紙上。%PRODUCTNAME 不列印頁尾。
25 WIZARDS_HID_LTRWIZ_CHKCOMPANYRECEIVER 79 指定您自己的地址要以小型字壓印在寄件者地址上方。%PRODUCTNAME 不使用小型字來列印地址。
1e WIZARDS_HID_LTRWIZ_NUMADDRESSX 2a 設定物件與頁面左邊距的間隔。
1f WIZARDS_HID_LTRWIZ_CHKUSEFOOTER 21 在書信範本中包含頁尾。
20 WIZARDS_HID_LTRWIZ_CHKUSESUBJECT 24 在書信範本中包含主旨列。
21 WIZARDS_HID_LTRWIZ_CHKUSEGREETING 45 在書信範本中包含問候語。從清單方塊中選取文字。
13 SW_HID_LETTER_PAGE3 24 定義寄件者的文字和位置。
1e WIZARDS_HID_LTRWIZ_LSTGREETING 45 在書信範本中包含問候語。從清單方塊中選取文字。
23 WIZARDS_HID_LTRWIZ_CHKUSESALUTATION 45 在書信範本中包含稱謂。從清單方塊中選取該稱謂。
28 WIZARDS_HID_LTRWIZ_CHKUSEADDRESSRECEIVER 2d 在書信範本中包含小型回函地址。
1e WIZARDS_HID_LTRWIZ_CHKUSESIGNS 30 在書信範本中包含商務信函參照行。
1d WIZARDS_HID_LTRWIZ_CHKUSELOGO 21 在信件範本上包括標誌。
22 WIZARDS_HID_LTRWIZ_CHKUSEBENDMARKS 21 在書信範本中包含折痕。
20 WIZARDS_HID_LTRWIZ_LSTSALUTATION 45 在書信範本中包含稱謂。從清單方塊中選取該稱謂。
20 WIZARDS_HID_LTRWIZ_LSTLETTERNORM 4a 選取國家/地區，以便使用該國家/地區專有的書信格式。
27 WIZARDS_HID_LTRWIZ_OPTSENDERPLACEHOLDER 57 從 [選項] 對話方塊的 [%PRODUCTNAME] - [使用者資料]，使用位址資料。
13 SW_HID_LETTER_PAGE4 21 定義收件者資料和稱呼。
22 WIZARDS_HID_LTRWIZ_TXTSENDERSTREET 21 指定寄件者的街道地址。
22 WIZARDS_HID_LTRWIZ_OPTSENDERDEFINE 2d 使用下列文字方塊中的地址資料。
26 WIZARDS_HID_LTRWIZ_OPTRECEIVERDATABASE 30 將地址資料庫欄位插入信函範本中。
29 WIZARDS_HID_LTRWIZ_OPTRECEIVERPLACEHOLDER 33 指定將預留位置欄位插入信函範本中。
26 WIZARDS_HID_LTRWIZ_TXTSENDERSTATE_TEXT 21 指定寄件者的地址資料。
24 WIZARDS_HID_LTRWIZ_TXTSENDERPOSTCODE 21 指定寄件者的地址資料。
20 WIZARDS_HID_LTRWIZ_TXTSENDERCITY 21 指定寄件者的地址資料。
20 WIZARDS_HID_LTRWIZ_TXTSENDERNAME 1b 指定寄件者的姓名。
1c WIZARDS_HID_LTRWIZ_TXTFOOTER 1b 輸入頁尾行的文字。
13 SW_HID_LETTER_PAGE5 27 指定信函中要包含哪些元素。
25 WIZARDS_HID_LTRWIZ_CHKFOOTERNEXTPAGES 2d 選取此選項可隱藏第一頁的頁尾。
27 WIZARDS_HID_LTRWIZ_CHKFOOTERPAGENUMBERS 45 如果想要將頁碼加入信函樣式中，則選取這個欄位。
22 WIZARDS_HID_LTRWIZ_OPTCREATELETTER 4b 儲存並關閉範本，然後根據該範本開啟新的未命名文件。
13 SW_HID_LETTER_PAGE6 45 利用此頁來設定頁尾的外觀，以及頁面邊距的寬度。
1a WIZARDS_HID_LTRWIZ_TXTPATH 5c 輸入範本的路徑和檔案名稱，或按一下 ... 按鈕選取路徑和檔案名稱。
22 WIZARDS_HID_LTRWIZ_TXTTEMPLATENAME 1e 指定文件範本的標題。
1a WIZARDS_HID_LTRWIZ_CMDPATH 5c 輸入範本的路徑和檔案名稱，或按一下 ... 按鈕選取路徑和檔案名稱。
21 WIZARDS_HID_LTRWIZ_OPTMAKECHANGES 3c 儲存範本並使其保持開啟以便對其進行編輯。
1a WIZARDS_HID_FAXWIZARD_BACK be 按一下 [返回] 按鈕，可檢視在前一頁中所選擇的設定。按一下此按鈕並不會修改或刪除目前的設定。從第二頁開始，[返回] 按鈕將處於使用中。
1a WIZARDS_HID_FAXWIZARD_NEXT 75 精靈將會儲存目前設定，並移至下一頁。到達最後一頁時，[下一步] 按鈕將變為不可用。
1c WIZARDS_HID_FAXWIZARD_CREATE ab 精靈將依據您的選擇建立文件範本，並將其儲存下來。依此範本而建立的新文件會出現在工作區域中，其檔案名稱是「UntitledX」。
35 service%3Acom.sun.star.wizards.fax.CallWizard%3Fstart 18 開啟傳真的精靈。
13 .uno%3AAutoPilotFax 18 開啟傳真的精靈。
20 WIZARDS_HID_FAXWIZ_OPTPRIVATEFAX 24 建立私人傳真的傳真範本。
10 SW_HID_FAX_PAGE1 30 定義傳真文件的樣式、標題和格式。
23 WIZARDS_HID_FAXWIZ_LSTBUSINESSSTYLE 1e 指定預先定義的樣式。
21 WIZARDS_HID_FAXWIZ_OPTBUSINESSFAX 24 建立商務傳真的傳真範本。
1b WIZARDS_HID_LSTPRIVATESTYLE 1e 指定預先定義的樣式。
1b WIZARDS_HID_OPTSENDERDEFINE 39 包含問候語。從清單方塊中選取該問候語。
19 WIZARDS_HID_CHKUSESUBJECT 15 包含公司標誌。
1b WIZARDS_HID_TXTSENDERSTREET f 包含頁尾。
19 WIZARDS_HID_LSTSALUTATION 39 包括通訊類型行。從清單方塊中選取該行。
20 WIZARDS_HID_OPTSENDERPLACEHOLDER 33 包含稱謂。從清單方塊中選取該稱謂。
1c WIZARDS_HID_CHKUSESALUTATION 15 包含日期欄位。
18 WIZARDS_HID_CHKUSEFOOTER 33 包含稱謂。從清單方塊中選取該稱謂。
10 SW_HID_FAX_PAGE2 21 指定要列印的傳真元素。
19 WIZARDS_HID_TXTSENDERNAME 39 包含問候語。從清單方塊中選取該問候語。
17 WIZARDS_HID_LSTGREETING 12 包括主題行。
1a WIZARDS_HID_CHKUSEGREETING 39 包括通訊類型行。從清單方塊中選取該行。
20 WIZARDS_HID_CHKFOOTERPAGENUMBERS 1e 輸入寄件者地址資料。
15 WIZARDS_HID_TXTFOOTER 1e 輸入寄件者地址資料。
22 WIZARDS_HID_OPTRECEIVERPLACEHOLDER 1e 輸入寄件者地址資料。
10 SW_HID_FAX_PAGE3 21 指定傳真的寄件者資訊。
1c WIZARDS_HID_FILETEMPLATEPATH 3c 插入資料庫欄位以便之後合併列印傳真文件。
1e WIZARDS_HID_CHKFOOTERNEXTPAGES 1e 輸入寄件者地址資料。
1a WIZARDS_HID_TXTSENDERSTATE 7b 在傳真範本中插入地址的預留位置。之後可在傳真文件中，按一下該預留位置輸入實際資料。
18 WIZARDS_HID_TXTSENDERFAX 1e 輸入寄件者地址資料。
19 WIZARDS_HID_TXTSENDERCITY 7b 選取此選項以在下面的文字方塊中輸入地址資料。此資料會以普通文字形式插入傳真文件中。
1b WIZARDS_HID_TXTTEMPLATENAME 7b 在傳真範本中插入地址的預留位置。之後可在傳真文件中，按一下該預留位置輸入實際資料。
1f WIZARDS_HID_OPTRECEIVERDATABASE 1e 輸入寄件者地址資料。
10 SW_HID_FAX_PAGE4 18 指定收件者資訊。
10 SW_HID_FAX_PAGE5 3f 定義在傳真中要包括的元素，例如日期和主題。
16 .uno%3AAutoPilotAgenda 48 啟動自動檔案助理，以協助您建立會議議程文件樣式。
38 service%3Acom.sun.star.wizards.agenda.CallWizard%3Fstart 48 啟動自動檔案助理，以協助您建立會議議程文件樣式。
1f WIZARDS_HID_AGWIZ_1_CHK_MINUTES 2a 列印用來記錄會議事項的頁面。
23 WIZARDS_HID_AGWIZ_1_LIST_PAGEDESIGN 27 從清單方塊中選取頁面設計。
13 SW_HID_AGENDA_PAGE1 24 指定會議議程的頁面設計。
1d WIZARDS_HID_AGWIZ_2_TXT_TITLE 18 指定會議的標題。
1c WIZARDS_HID_AGWIZ_2_TXT_DATE 18 指定會議的日期。
20 WIZARDS_HID_AGWIZ_2_TXT_LOCATION 18 指定會議的地點。
1c WIZARDS_HID_AGWIZ_2_TXT_TIME 18 指定會議的時間。
13 SW_HID_AGENDA_PAGE2 2a 指定會議的日期、時間和地點。
1d WIZARDS_HID_AGWIZ_3_CHK_BRING 2a 指定是否要列印「請攜帶」行。
1c WIZARDS_HID_AGWIZ_3_CHK_READ 2b 指定是否要列印 「請閱讀」行。
13 SW_HID_AGENDA_PAGE3 3c 在第 3 頁上選取應包含在會議議程中的名稱。
1d WIZARDS_HID_AGWIZ_3_CHK_NOTES 28 指定是否要列印「備註」 行。
24 WIZARDS_HID_AGWIZ_3_CHK_MEETING_TYPE 2d 指定是否要列印「會議類型」行。
22 WIZARDS_HID_AGWIZ_4_CHK_TIMEKEEPER 3c 指定是否要列印可輸入會議主持人的資料行。
21 WIZARDS_HID_AGWIZ_4_CHK_NOTETAKER 3c 指定是否要列印可輸入會議記錄者的資料行。
21 WIZARDS_HID_AGWIZ_4_CHK_CALLED_BY 3c 指定是否要列印可輸入會議召集人的資料行。
27 WIZARDS_HID_AGWIZ_4_CHK_RESOURCEPERSONS 3f 指定是否要列印可輸入裝置負責人員的資料行。
21 WIZARDS_HID_AGWIZ_4_CHK_OBSERVERS 36 指定是否要列印可輸入旁聽者的資料行。
21 WIZARDS_HID_AGWIZ_4_CHK_ATTENDEES 36 指定是否要列印可輸入參與者的資料行。
23 WIZARDS_HID_AGWIZ_4_CHK_FACILITATOR 33 指定是否要列印可輸入主席的資料行。
13 SW_HID_AGENDA_PAGE4 30 您可以在此頁中指定會議議程主題。
1a WIZARDS_HID_AGWIZ_5_BTN_UP 21 將目前的主題列往上移。
1e WIZARDS_HID_AGWIZ_5_BTN_INSERT 2d 將新空白主題列插入目前列之上。
21 WIZARDS_HID_AGWIZ_5_TXT_MINUTES_1 53 輸入議程主題。使用 [向上移動] 和 [向下移動] 按鈕排序主題。
13 SW_HID_AGENDA_PAGE5 3c 指定文件樣式和文件的名稱和所在的資料夾。
1e WIZARDS_HID_AGWIZ_5_BTN_REMOVE 1b 移除目前的主題列。
25 WIZARDS_HID_AGWIZ_5_TXT_RESPONSIBLE_1 53 輸入議程主題。使用 [向上移動] 和 [向下移動] 按鈕排序主題。
1f WIZARDS_HID_AGWIZ_5_TXT_TOPIC_1 53 輸入議程主題。使用 [向上移動] 和 [向下移動] 按鈕排序主題。
1c WIZARDS_HID_AGWIZ_5_BTN_DOWN 21 將目前的主題列往下移。
13 SW_HID_AGENDA_PAGE6 39 在此最後步驟中選擇是否需要會談記錄表。
24 WIZARDS_HID_AGWIZ_6_BTN_TEMPLATEPATH 3f 指定完整路徑，包括待辦事項範本的檔案名稱。
23 WIZARDS_HID_AGWIZ_6_OPT_MAKECHANGES 57 建立和儲存待辦事項範本，然後開啟該範本以進行進一步的編輯。
24 WIZARDS_HID_AGWIZ_6_TXT_TEMPLATEPATH 3f 指定完整路徑，包括待辦事項範本的檔案名稱。
24 WIZARDS_HID_AGWIZ_6_OPT_CREATEAGENDA 5a 建立並儲存待辦事項範本，然後根據該範本開啟新的待辦事項文件。
24 WIZARDS_HID_AGWIZ_6_TXT_TEMPLATENAME 24 指定待辦事項範本的名稱。
24 sd%3APushButton%3ADLG_ASS%3ABUT_LAST 36 返回上一個步驟而不刪除您目前的設定。
1d .uno%3AAutoPilotPresentations 66 使用精靈互動式建立簡報。透過精靈，您可以修改示例範本以配合您的需求。
24 sd%3APushButton%3ADLG_ASS%3ABUT_NEXT 2a 接受新的設定，並移至下一頁。
16 .uno%3ANewPresentation 66 使用精靈互動式建立簡報。透過精靈，您可以修改示例範本以配合您的需求。
c slot%3A10425 66 使用精靈互動式建立簡報。透過精靈，您可以修改示例範本以配合您的需求。
18 SD_HID_SD_AUTOPILOT_OPEN f3 列出您建立並儲存在 Templates 目錄中的簡報，而目錄在 [選項] 對話方塊中的 [%PRODUCTNAME] - [路徑] 下加以指定。若要使用精靈編輯簡報的版面配置與格式，請選取簡報，然後按 [下一步]。
1a SD_HID_SD_AUTOPILOT_REGION 24 列出簡報可用的範本分類。
2a sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_OPEN 2a 顯示之前所建立之簡報的清單。
26 sd%3ACheckBox%3ADLG_ASS%3ACB_STARTWITH 5f 指定您只想要在以 [檔案] - [精靈] - [簡報] 明確地要求時，才啟動精靈。
24 sd%3ACheckBox%3ADLG_ASS%3ACB_PREVIEW 27 指定在預覽視窗中顯示範本。
29 sd%3APushButton%3ADLG_ASS%3APB_PAGE1_OPEN 3d 按一下 [開啟舊檔] 以看到檔案選取對話方塊。
2e sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_TEMPLATE 33 開啟包含各種可變更簡報的清單方塊。
2b sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE1_EMPTY 1f 建立新的 (空白) 簡報。
1d SD_HID_SD_AUTOPILOT_TEMPLATES 24 列出簡報可用的範本分類。
19 SD_HID_SD_AUTOPILOT_PAGE1 36 指定簡報類型，並允許您選取一個範本。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM5 2a 使用文件樣式原來的頁面格式。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM3 6f 如果您要在透明膠紙上列印簡報，則選取這個選項。簡報會填滿無邊框的紙張格式。
28 sd%3AListBox%3ADLG_ASS%3ALB_PAGE2_LAYOUT 48 可讓您選擇您在精靈第 1 頁上所選簡報的投影片設計。
21 SD_HID_SD_AUTOPILOT_PAGETEMPLATES 48 可讓您選擇您在精靈第 1 頁上所選簡報的投影片設計。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM4 9c 如果您要在紙張上列印簡報，則選取這個選項。簡報幾乎填滿紙張格式，而紙張格式會在四周留下不可列印的窄邊框。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM2 57 這個選項會設計頁面比例 36 x 24 的簡報，如同一張小影像投影片。
2d sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE2_MEDIUM1 4f 這個選項會設計頁面比例 4 x 3 的簡報，如同電腦螢幕一般。
19 SD_HID_SD_AUTOPILOT_PAGE2 48 可讓您選擇您在精靈第 1 頁上所選簡報的投影片設計。
27 sd%3ACheckBox%3ADLG_ASS%3ACB_PAGE3_LOGO 4b 指定是否在簡報之間的暫停期間內顯示 $[officename] 標誌。
2a sd%3ATimeField%3ADLG_ASS%3ATMF_PAGE3_BREAK 24 定義簡報之間的暫停時間。
2b sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE3_KIOSK 36 自動執行簡報，並在暫停之後重新開始。
28 sd%3AListBox%3ADLG_ASS%3ALB_PAGE3_EFFECT 18 指定簡報的效果。
27 sd%3AListBox%3ADLG_ASS%3ALB_PAGE3_SPEED 1b 決定此效果的速度。
29 sd%3ATimeField%3ADLG_ASS%3ATMF_PAGE3_TIME 2a 定義每個簡報頁面的持續時間。
19 SD_HID_SD_AUTOPILOT_PAGE3 3f 指定簡報的特殊效果，並決定這些效果的速度。
2a sd%3ARadioButton%3ADLG_ASS%3ARB_PAGE3_LIVE 51 [預設值] 選項會依指定的速度，以全螢幕顯示方式執行簡報。
19 SD_HID_SD_AUTOPILOT_PAGE4 27 指定您的姓名或公司的名稱。
27 sd%3AEdit%3ADLG_ASS%3AEDT_PAGE4_ASKNAME 27 指定您的姓名或公司的名稱。
37 sd%3AMultiLineEdit%3ADLG_ASS%3AEDT_PAGE4_ASKINFORMATION 4e 在此欄位中輸入稍後要在簡報中包含的進一步意圖和構想。
28 sd%3AEdit%3ADLG_ASS%3AEDT_PAGE4_ASKTOPIC 18 指定簡報的主題。
2a sd%3ACheckBox%3ADLG_ASS%3ACB_PAGE5_SUMMARY 24 建立所有簡報內容的摘要。
19 SD_HID_SD_AUTOPILOT_PAGE5 24 建立所有簡報內容的摘要。
1d WIZARDS_HID_DLGFORM_CMDFINISH 3c 按一下即可建立表單，不必再回應其他頁面。
1a WIZARDS_HID_DLGFORM_DIALOG 24 啟動用於建立表單的精靈。
27 WIZARDS_HID_DLGFORM_MASTER_CMDREMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
22 WIZARDS_HID_DLGFORM_SUB_CMDMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
29 WIZARDS_HID_DLGFORM_MASTER_FIELDSSELECTED 2d 顯示新表單中將包括的所有欄位。
29 WIZARDS_HID_QUERYWIZARD_CMDREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
2c WIZARDS_HID_DLGTABLE_CMDMOVEDOWN_PK_SELECTED 3c 按一下將清單中的所選欄位向下移一個項目。
24 WIZARDS_HID_DLGTABLE_FIELDSAVAILABLE 3c 列出所選表格或查詢中之資料庫欄位的名稱。
2a WIZARDS_HID_DLGFORM_MASTER_FIELDSAVAILABLE 3c 列出所選表格或查詢中之資料庫欄位的名稱。
2a WIZARDS_HID_DLGFORM_MASTER_CMDMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
21 WIZARDS_HID_DLGTABLE_CMDREMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
26 WIZARDS_HID_DLGTABLE_CMDREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
25 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
29 WIZARDS_HID_DLGFORM_SUB_CMDREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
19 WIZARDS_HID2_BTN_DOC_DOWN 3c 按一下將清單中的所選欄位向下移一個項目。
24 WIZARDS_HID_DLGTABLE_CMDMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
27 WIZARDS_HID_DLGFORM_SUB_FIELDSAVAILABLE 3c 列出所選表格或查詢中之資料庫欄位的名稱。
24 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEUP 3c 按一下將清單中的所選欄位向上移一個項目。
27 WIZARDS_HID_QUERYWIZARD_CMDMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
27 WIZARDS_HID_DLGFORM_SUB_CMDMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
2d WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
27 WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVEUP 3c 按一下將清單中的所選欄位向上移一個項目。
23 WIZARDS_HID_DLGFORM_MASTER_LBTABLES 2d 指定用於建立表單的表格或查詢。
21 WIZARDS_HID_QUERYWIZARD_CMDMOVEUP 3c 按一下將清單中的所選欄位向上移一個項目。
2f WIZARDS_HID_QUERYWIZARD_CMDFILTERREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
21 WIZARDS_HID_QUERYWIZARD_LSTFIELDS 3c 列出所選表格或查詢中之資料庫欄位的名稱。
2a WIZARDS_HID_DLGTABLE_CMDMOVEUP_PK_SELECTED 3c 按一下將清單中的所選欄位向上移一個項目。
20 WIZARDS_HID_DLGTABLE_CMDMOVEDOWN 3c 按一下將清單中的所選欄位向下移一個項目。
27 WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDS 3c 列出所選表格或查詢中之資料庫欄位的名稱。
24 WIZARDS_HID_QUERYWIZARD_CMDREMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
23 WIZARDS_HID_QUERYWIZARD_CMDMOVEDOWN 3c 按一下將清單中的所選欄位向下移一個項目。
21 WIZARDS_HID_DLGFORM_SUB_CMDMOVEUP 3c 按一下將清單中的所選欄位向上移一個項目。
26 WIZARDS_HID_DLGFORM_MASTER_CMDMOVEDOWN 3c 按一下將清單中的所選欄位向下移一個項目。
17 WIZARDS_HID2_BTN_DOC_UP 3c 按一下將清單中的所選欄位向上移一個項目。
29 WIZARDS_HID_QUERYWIZARD_CMDFILTERMOVEDOWN 3c 按一下將清單中的所選欄位向下移一個項目。
1e WIZARDS_HID_DLGTABLE_CMDMOVEUP 3c 按一下將清單中的所選欄位向上移一個項目。
23 WIZARDS_HID_DLGFORM_SUB_CMDMOVEDOWN 3c 按一下將清單中的所選欄位向下移一個項目。
22 WIZARDS_HID_QUERYWIZARD_CMDMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
2c WIZARDS_HID_DLGFORM_MASTER_CMDREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
1f WIZARDS_HID_DLGTABLE_CMDMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
24 WIZARDS_HID_DLGFORM_SUB_CMDREMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
24 WIZARDS_HID_DLGFORM_CHKCREATESUBFORM 1b 選擇要增加子表單。
20 WIZARDS_HID_DLGFORM_lstRELATIONS 27 選取作為子表單基礎的關係。
25 WIZARDS_HID_DLGFORM_OPTSELECTMANUALLY 3f 按一下增加以手動選擇的欄位為基礎的子表單。
29 WIZARDS_HID_DLGFORM_OPTONEXISTINGRELATION 36 按一下增加以現有關係為基礎的子表單。
20 WIZARDS_HID_DLGFORM_SUB_LBTABLES 30 指定用於建立子表單的表格或查詢。
26 WIZARDS_HID_DLGFORM_SUB_FIELDSSELECTED 30 顯示新子表單中將包括的所有欄位。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK1 5a 在此清單方塊旁的清單方塊中選擇結合到主表單欄位的子表單欄位。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK4 5a 在此清單方塊旁的清單方塊中選擇結合到主表單欄位的子表單欄位。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK3 5a 在此清單方塊旁的清單方塊中選擇結合到主表單欄位的子表單欄位。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK3 5a 在此清單方塊旁的清單方塊中選擇結合到子表單欄位的主表單欄位。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK2 5a 在此清單方塊旁的清單方塊中選擇結合到子表單欄位的主表單欄位。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK1 5a 在此清單方塊旁的清單方塊中選擇結合到子表單欄位的主表單欄位。
29 WIZARDS_HID_DLGFORM_LINKER_LSTMASTERLINK4 5a 在此清單方塊旁的清單方塊中選擇結合到子表單欄位的主表單欄位。
28 WIZARDS_HID_DLGFORM_LINKER_LSTSLAVELINK2 5a 在此清單方塊旁的清單方塊中選擇結合到主表單欄位的子表單欄位。
22 WIZARDS_HID_DLGFORM_CMDLEFTLABELED 45 依欄的方向來對齊資料庫欄位，貼標位於欄位左側。
21 WIZARDS_HID_DLGFORM_CMDTABLESTYLE 2a 將資料庫欄位以表格形式對齊。
21 WIZARDS_HID_DLGFORM_CMDALIGNRIGHT 1e 標籤會向右對齊顯示。
23 WIZARDS_HID_DLGFORM_CMDTOPJUSTIFIED 2a 將貼標排序在相應資料的上方。
20 WIZARDS_HID_DLGFORM_CMDALIGNLEFT 1e 標籤會向左對齊顯示。
21 WIZARDS_HID_DLGFORM_CMDTOPLABELED 42 依欄的方向對齊資料庫欄位，貼標位於欄位上方。
25 WIZARDS_HID_DLGFORM_CHKNOMODIFICATION 1e 選擇要禁止編輯資料。
25 WIZARDS_HID_DLGFORM_OPTDISPLAYALLDATA 3f 建立可用來顯示現有資料及輸入新資料的表單。
21 WIZARDS_HID_DLGFORM_CHKNODELETION 1e 選擇要禁止刪除資料。
21 WIZARDS_HID_DLGFORM_CHKNOADDITION 1e 選擇要禁止新增資料。
22 WIZARDS_HID_DLGFORM_OPTNEWDATAONLY 2a 建立僅用於輸入新資料的表單。
1f WIZARDS_HID_DLGFORM_CMD3DBORDER 25 指定欄位邊框呈現 3D 外觀。
1f WIZARDS_HID_DLGFORM_CMDNOBORDER 1b 指定欄位沒有邊框。
23 WIZARDS_HID_DLGFORM_CMDSIMPLEBORDER 2a 將欄位邊框的外觀指定為平面。
1d WIZARDS_HID_DLGFORM_LSTSTYLES 1e 指定表單的頁面樣式。
1b WIZARDS_HID_DLGFORM_TXTPATH 18 指定表單的名稱。
21 WIZARDS_HID_DLGFORM_OPTMODIFYFORM 45 儲存表單，並以編輯模式開啟，以便變更版面配置。
23 WIZARDS_HID_DLGFORM_OPTWORKWITHFORM 51 儲存表單，並以表單文件的形式開啟，以便輸入和顯示資料。
23 DBACCESS_HID_DOCUMENT_CREATE_REPWIZ 21 啟動精靈，以建立報表。
1c WIZARDS_HID_DLGREPORT_DIALOG 15 選取報告特性。
26 WIZARDS_HID_DLGREPORT_1_FIELDSSELECTED 30 顯示包含在新增報表中的所有欄位。
27 WIZARDS_HID_DLGREPORT_1_FIELDSAVAILABLE 3c 顯示所選表格或查詢中之資料庫欄位的名稱。
24 WIZARDS_HID_DLGREPORT_1_CMDREMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
22 WIZARDS_HID_DLGREPORT_1_CMDMOVEALL 39 按一下以將所有欄位移至箭頭指向的方塊。
27 WIZARDS_HID_DLGREPORT_1_CMDMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
29 WIZARDS_HID_DLGREPORT_1_CMDREMOVESELECTED 3c 按一下以將選取的欄位移至箭頭指向的方塊。
20 WIZARDS_HID_DLGREPORT_1_LBTABLES 2d 選擇用來建立報表的表格或查詢。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_3 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_4 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_2 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_6 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_5 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
22 WIZARDS_HID_DLGREPORT_6_TXTTITLE_1 75 顯示要包含在報表中的欄位名稱。您可以在右側輸入每個欄位的標籤 (將列印在報表中)。
20 WIZARDS_HID_DLGREPORT_2_CMDGROUP 3c 按一下以將選取的欄位移至箭頭指向的方塊。
27 WIZARDS_HID_DLGREPORT_2_PREGROUPINGDEST a4 列出報表據以分組的欄位。若要移除某個分組層級，請選取欄位名稱，然後按一下 [<] 按鈕。您最多可以選擇四個分組層級。
22 WIZARDS_HID_DLGREPORT_2_CMDUNGROUP 3c 按一下以將選取的欄位移至箭頭指向的方塊。
20 WIZARDS_HID_DLGREPORT_2_GROUPING bc 根據您在上一個精靈頁面中的選擇列出欄位。若要將報表依欄位分組，請選取欄位名稱，然後按一下 [>] 按鈕。您最多可以選擇四個分組層級。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND2 24 以向上順序排序欄位內容。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND2 24 以向下順序排序欄位內容。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND3 24 以向上順序排序欄位內容。
1d WIZARDS_HID_DLGREPORT_3_SORT4 2d 選取排序報表所依據的其他欄位。
1d WIZARDS_HID_DLGREPORT_3_SORT1 30 選取排序報表所依據的第一個欄位。
1d WIZARDS_HID_DLGREPORT_3_SORT3 2d 選取排序報表所依據的其他欄位。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND4 24 以向下順序排序欄位內容。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND1 24 以向上順序排序欄位內容。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND1 24 以向下順序排序欄位內容。
23 WIZARDS_HID_DLGREPORT_3_OPTDESCEND3 24 以向下順序排序欄位內容。
22 WIZARDS_HID_DLGREPORT_3_OPTASCEND4 24 以向上順序排序欄位內容。
1d WIZARDS_HID_DLGREPORT_3_SORT2 2d 選取排序報表所依據的其他欄位。
22 WIZARDS_HID_DLGREPORT_4_PAGELAYOUT 6f 定義報表的頁面配置。從指定了頁首、頁尾和頁面背景的範本檔案中載入頁面配置。
20 WIZARDS_HID_DLGREPORT_4_PORTRAIT 24 選取報表的縱向頁面方向。
21 WIZARDS_HID_DLGREPORT_4_LANDSCAPE 24 選取報表的橫向頁面方向。
22 WIZARDS_HID_DLGREPORT_4_DATALAYOUT 5d 定義用於報表的樣式集。樣式指定字型、縮排、表格背景及其他內容。
26 WIZARDS_HID_DLGREPORT_5_OPTUSETEMPLATE 2a 按一下 [完成]，即會儲存報表。
27 WIZARDS_HID_DLGREPORT_5_OPTSTATDOCUMENT 69 將報表儲存為靜態報表。當您開啟靜態報表時，會自動顯示建立報表時的資料。
27 WIZARDS_HID_DLGREPORT_5_OPTEDITTEMPLATE 3f 按一下 [完成]，即會儲存並開啟報表供您編輯。
1d WIZARDS_HID_DLGREPORT_4_TITLE 2d 指定列印在每頁標題行中的標題。
26 WIZARDS_HID_DLGREPORT_5_OPTDYNTEMPLATE 60 將報表儲存為範本。當您開啟動態報表時，會顯示目前最新的資料內容。
34 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE1_OLD_DESIGN 66 從設計清單中載入現有設計，並基於該設計開始執行精靈後續頁面上的步驟。
34 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE1_NEW_DESIGN 30 在精靈的後續頁面中建立新的設計。
1a SD_HID_SD_HTMLEXPORT_PAGE1 30 在精靈的後續頁面中建立新的設計。
33 sd%3APushButton%3ADLG_PUBLISHING%3APAGE1_DEL_DESIGN 2a 從設計清單中刪除選取的設計。
2d sd%3AListBox%3ADLG_PUBLISHING%3APAGE1_DESIGNS 1e 顯示全部現有的設計。
32 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_CHG_AUTO 4b 經過指定的時間後，自動變換頁面，而與簡報的內容無關
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_ASP 83 選取 [ASP] 選項時，WebCast 匯出將建立 ASP 頁面。請注意，HTML 簡報只能由支援 ASP 的 Web 伺服器提供。
32 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_STANDARD 30 使用匯出頁面來建立標準 HTML 頁面。
2e sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_PERL 3f 由 WebCast 匯出用於建立 HTML 頁面和 Perl 程式檔。
31 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_WEBCAST 4a 在 WebCast 匯出中，自動程式檔將以 Perl 或 ASP 支援產生。
2e sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_ENDLESS 4b 最後一張投影片顯示完後，自動重新開始放映 HTML 簡報。
34 sd%3ATimeField%3ADLG_PUBLISHING%3APAGE2_DURATION_TMF 27 定義每個投影片顯示的時間。
35 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_CHG_DEFAULT c6 投影片變換要視您為簡報中的每張投影片設定的時間而定。如果您設定了手動頁面變換，可以透過在鍵盤上按下任何按鍵以引出 HTML 簡報中的新頁面。
30 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_FRAMES 8d 建立帶有訊框的標準 HTML 頁面。匯出的頁面將顯示在主訊框中，左側訊框將以超連結的形式顯示內容目錄。
2c sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_NOTES 1b 指定同時顯示備註。
26 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_CGI 3a 指定產生的 Perl 程式檔之 URL (絕對或相對)。
26 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_URL 54 指定儲存在 Web 伺服器上的已建立 HTML 簡報之 URL (絕對或相對)。
2e sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE2_CONTENT 1b 建立文件的標題頁。
1a SD_HID_SD_HTMLEXPORT_PAGE2 30 使用匯出頁面來建立標準 HTML 頁面。
28 sd%3AEdit%3ADLG_PUBLISHING%3APAGE2_INDEX 46 指定檢視者為查看簡報而要輸入的 URL (絕對或相對)。
2f sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE2_KIOSK 85 建立作為 Kiosk 匯出的預設 HTML 簡報。在該簡報中，經過一段指定的時間後，投影片會自動前進一頁。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_3 30 為高品質投影片顯示選取高解析度。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_JPG 83 將檔案匯出成 JPEG 檔案。壓縮 JPEG 檔案時，其壓縮程度可調整，且該檔案可包含 256 種以上的顏色。
1a SD_HID_SD_HTMLEXPORT_PAGE3 75 將檔案匯出成 PNG 檔案。壓縮 PNG 檔案不會遺失資料，且該檔案可包含 256 種以上的顏色。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_1 69 即使是針對具有許多投影片的簡報，也請選取低解析度來保持較小的檔案大小。
30 sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE3_SLD_SOUND 3f 指定匯出為投影片切換的效果而定義的聲音檔。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_GIF 6c 將檔案匯出成 GIF 檔案。壓縮 GIF 檔案不會遺失資料，該檔案最多可有 256 種顏色。
36 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_RESOLUTION_2 30 為中等大小的簡報選取中等解析度。
2e sd%3AComboBox%3ADLG_PUBLISHING%3APAGE3_QUALITY 92 指定 JPEG 圖形的壓縮比例。壓縮比例為 100% 時，品質最佳但檔案較大。壓縮比例為 25% 時，檔案小但品質較差。
2d sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE3_PNG 75 將檔案匯出成 PNG 檔案。壓縮 PNG 檔案不會遺失資料，且該檔案可包含 256 種以上的顏色。
2b sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_WWW_EDIT 3f 指定您的首頁。出版物中將會插入一個超連結。
29 sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_AUTHOR 21 指定出版物的作者姓名。
30 sd%3AMultiLineEdit%3ADLG_PUBLISHING%3APAGE4_MISC 33 指定要在標題頁面上出現的其他文字。
2d sd%3AEdit%3ADLG_PUBLISHING%3APAGE4_EMAIL_EDIT 1b 指定電子郵件地址。
1a SD_HID_SD_HTMLEXPORT_PAGE4 21 指定出版物的作者姓名。
1a SD_HID_SD_HTMLEXPORT_PAGE5 27 僅插入文字超連結而非按鈕。
2f sd%3ACheckBox%3ADLG_PUBLISHING%3APAGE5_TEXTONLY 27 僅插入文字超連結而非按鈕。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_BACK 4f 開啟 [顏色] 對話方塊，在此處您可以選取簡報的背景顏色。
2e sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_ALINK 58 開啟 [顏色] 對話方塊，在此處您可以選取簡報使用中的連結顏色。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_LINK 52 開啟 [顏色] 對話方塊，在此處您可以選取簡報的超連結顏色。
2e sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_USER 36 可讓您為某些簡報物件定義自己的顏色。
31 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_DEFAULT 32 使用檢視者的 Web 瀏覽器的預設顏色。
1a SD_HID_SD_HTMLEXPORT_PAGE6 30 從目前文件中使用的樣式決定顏色。
2e sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_VLINK 58 開啟 [顏色] 對話方塊，在此處您可以選取簡報訪問過的連結顏色。
2d sd%3APushButton%3ADLG_PUBLISHING%3APAGE6_TEXT 4f 開啟 [顏色] 對話方塊，在此處您可以選取簡報的文字顏色。
33 sd%3ARadioButton%3ADLG_PUBLISHING%3APAGE6_DOCCOLORS 30 從目前文件中使用的樣式決定顏色。
20 EXTENSIONS_HID_GRIDWIZARD_FINISH f 建立物件。
21 EXTENSIONS_HID_GROUPWIZARD_FINISH f 建立物件。
20 EXTENSIONS_HID_LISTWIZARD_FINISH f 建立物件。
27 macro%3A%2F%2F%2FImportWizard.Main.Main 54 複製文件並將其轉換為 $[officename] 所使用的 OpenDocument XML 格式。
1c WIZARDS_HID_DLGIMPORT_DIALOG 54 複製文件並將其轉換為 $[officename] 所使用的 OpenDocument XML 格式。
1f WIZARDS_HID_DLGIMPORT_0_CMDHELP 54 複製文件並將其轉換為 $[officename] 所使用的 OpenDocument XML 格式。
20 WIZARDS_HID_DLGIMPORT_2_CHKEXCEL 4f 將 Microsoft Excel 格式的 *.xls 文件轉換成 Open Office *.ods 文件。
22 WIZARDS_HID_DLGIMPORT_0_CHKLOGFILE 45 在工作目錄中建立可顯示轉換了哪些文件的記錄檔。
25 WIZARDS_HID_DLGIMPORT_0_CHKMATHGLOBAL 98 將 Writer 舊格式的 *.sgl 主控文件轉換成 OpenDocument *.odm 文件，將 Math 舊格式的 *.smf 文件轉換成 OpenDocument *.odf 文件。
1f WIZARDS_HID_DLGIMPORT_0_CHKCALC 48 將 Calc 舊格式的 *.sdc 文件轉換成 OpenDocument *.ods 文件。
22 WIZARDS_HID_DLGIMPORT_0_CHKIMPRESS 93 將 Draw 舊格式的 *.sda 文件轉換成 OpenDocument *.odg 文件，將 Impress 舊格式的 *.sdd 文件轉換成 OpenDocument *.odp 文件。
25 WIZARDS_HID_DLGIMPORT_2_CHKPOWERPOINT 54 將 Microsoft PowerPoint 格式的 *.ppt 文件轉換成 Open Office *.odp 文件。
1f WIZARDS_HID_DLGIMPORT_2_CHKWORD 4e 將 Microsoft Word 格式的 *.doc 文件轉換成 Open Office *.odt 文件。
21 WIZARDS_HID_DLGIMPORT_0_CHKWRITER 4a 將 Writer 舊格式的 *.sdw 文件轉換成 OpenDocument *.odt 文件。
26 WIZARDS_HID_DLGIMPORT_0_OPTSODOCUMENTS 50 將舊的二進位文件轉換成 $[officename] 使用的 OpenDocument 格式。
26 WIZARDS_HID_DLGIMPORT_0_OPTMSDOCUMENTS 4e 將 Microsoft Word 格式的 *.doc 文件轉換成 Open Office *.odt 文件。
2d WIZARDS_HID_DLGIMPORT_2_CMDDOCUMENTPATHSELECT 30 開啟對話方塊，以選取所需的路徑。
26 WIZARDS_HID_DLGIMPORT_2_EDTEMPLATEPATH 24 指定寫入目標檔案的目錄。
2d WIZARDS_HID_DLGIMPORT_2_CMDTEMPLATEPATHSELECT 30 開啟對話方塊，以選取所需的路徑。
22 WIZARDS_HID_DLGIMPORT_2_CBTEMPLATE 1b 指定即將轉換範本。
26 WIZARDS_HID_DLGIMPORT_2_EDDOCUMENTPATH 24 指定寫入目標檔案的目錄。
26 WIZARDS_HID_DLGIMPORT_2_LBDOCUMENTPATH 27 指定包含原始碼檔案的目錄。
26 WIZARDS_HID_DLGIMPORT_2_LBTEMPLATEPATH 27 指定包含原始碼檔案的目錄。
29 WIZARDS_HID_DLGIMPORT_2_CBDOCUMENTRECURSE 42 表示也會在所選目錄的子目錄中搜尋相符的檔案。
29 WIZARDS_HID_DLGIMPORT_2_CBTEMPLATERECURSE 42 表示也會在所選目錄的子目錄中搜尋相符的檔案。
22 WIZARDS_HID_DLGIMPORT_2_CBDOCUMENT 1b 表示文件即將轉換。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON4 4e 將轉換在呼叫轉換器之前選取的範圍中所有的貨幣儲存格。
23 WIZARDS_HID_DLGCONVERT_CBTARGETOPEN 4e 開啟對話方塊，您可以在其中選取放置轉換後檔案的目錄。
21 WIZARDS_HID_DLGCONVERT_CHKPROTECT ab 指定將在轉換期間停用並在其後重新啟用的工作表保護。如果工作表的轉換受密碼保護，螢幕上將顯示要求輸入密碼的對話方塊。
25 WIZARDS_HID_DLGCONVERT_CHECKRECURSIVE 36 指定是否包括所選目錄的所有子檔案夾。
23 WIZARDS_HID_DLGCONVERT_CBSOURCEOPEN 39 開啟對話方塊，以選取所需的目錄或文件。
1f WIZARDS_HID_DLGCONVERT_LISTBOX1 2a 在清單中顯示將要轉換的範圍。
20 WIZARDS_HID_DLGCONVERT_COMBOBOX1 24 指定要轉換成歐元的貨幣。
1d WIZARDS_HID_DLGCONVERT_CBBACK 24 返回歐元轉換器的第一頁。
1f WIZARDS_HID_DLGCONVERT_CBCANCEL 18 關閉歐元轉換器。
1f WIZARDS_HID_DLGCONVERT_TBTARGET 33 指定儲存轉換後檔案的資料夾和路徑。
27 WIZARDS_HID_DLGCONVERT_CHKTEXTDOCUMENTS 49 轉換 $[officename] Writer 文件的欄位和表格中的貨幣金額。
31 macro%3A%2F%2F%2FEuro.AutoPilotRun.StartAutoPilot 84 將 $[officename] Calc 文件中的貨幣金額和 $[officename] Writer 文件的欄位與表格中的貨幣金額轉換成歐元。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON1 3f 所有使用選取儲存格樣式的儲存格都會被轉換。
1d WIZARDS_HID_DLGCONVERT_CBHELP 24 啟動對此對話方塊的說明。
1d WIZARDS_HID_DLGCONVERT_OBFILE 29 轉換單個 $[officename] Calc 檔案。
1d WIZARDS_HID_DLGCONVERT_DIALOG 84 將 $[officename] Calc 文件中的貨幣金額和 $[officename] Writer 文件的欄位與表格中的貨幣金額轉換成歐元。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON3 3c 所有使用中文件內的貨幣儲存格都將被轉換。
1f WIZARDS_HID_DLGCONVERT_TBSOURCE 33 指示要轉換的目錄或單個文件的名稱。
20 WIZARDS_HID_DLGCONVERT_CHECKBOX1 15 轉換整個文件。
24 WIZARDS_HID_DLGCONVERT_OPTIONBUTTON2 45 所有使用中工作表文件內的貨幣儲存格都將被轉換。
1d WIZARDS_HID_DLGCONVERT_CBGOON f 啟動轉換。
1c WIZARDS_HID_DLGCONVERT_OBDIR 65 轉換所選目錄中所有的 $[officename] Calc 文件、$[officename] Writer 文件以及範本。
14 .uno%3AEuroConverter 84 將 $[officename] Calc 文件中的貨幣金額和 $[officename] Writer 文件的欄位與表格中的貨幣金額轉換成歐元。
39 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_KAB 48 若已使用了 KDE Address Book 中的通訊錄，請選取此選項。
44 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OUTLOOKEXPRESS 51 若已使用了 Microsoft Outlook Express 中的通訊錄，請選取此選項。
3a extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_MORK 55 如果您已經使用了 Mozilla 或 Netscape 中的通訊錄，請選取此選項。
c slot%3A10934 4e 此精靈會在 $[officename] 中將現有的通訊錄註冊為資料來源。
1e EXTENSIONS_HID_ABSPILOT_CANCEL 27 結束精靈而不套用任何變更。
49 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION_GROUPWISE 41 若已使用了 Groupwise 中的通訊錄，請選取此選項。
3a extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_LDAP 4b 如果您在 LDAP 伺服器上已經有地址資料，請選取此選項。
3b extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OTHER 60 如果您要在 $[officename] 中將其他資料來源註冊為通訊錄，請選取此選項。
2c extensions%3ATabPage%3ARID_PAGE_SELECTABTYPE 4e 此精靈會在 $[officename] 中將現有的通訊錄註冊為資料來源。
1e EXTENSIONS_HID_ABSPILOT_FINISH 3c 建立與資料來源的連線，並關閉此對話方塊。
3d extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_OUTLOOK 62 若已使用了 Microsoft Outlook (不是 Outlook Express) 中的通訊錄，請選取此選項。
41 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_THUNDERBIRD 3d 若已在 Thunderbird 使用通訊錄，請選取此選項。
21 .uno%3AAutoPilotAddressDataSource 4e 此精靈會在 $[officename] 中將現有的通訊錄註冊為資料來源。
44 extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION_LDAP 46 若已使用了 Evolution LDAP 中的通訊錄，請選取此選項。
3b extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_MACAB 49 若已使用了 OS X Address Book 中的通訊錄，請選取此選項。
17 EXTENSIONS_HID_ABSPILOT 4e 此精靈會在 $[officename] 中將現有的通訊錄註冊為資料來源。
3f extensions%3ARadioButton%3ARID_PAGE_SELECTABTYPE%3ARB_EVOLUTION 41 若已使用了 Evolution 中的通訊錄，請選取此選項。
4b extensions%3APushButton%3ARID_PAGE_ADMININVOKATION%3APB_INVOKE_ADMIN_DIALOG 2d 呼叫可輸入其他設定的對話方塊。
3d extensions%3AListBox%3ARID_PAGE_TABLESELECTION%3ALB_TABLELIST 39 指定用作 $[officename] 範本之通訊錄的表格。
1f DBACCESS_HID_DSADMIN_BROWSECONN 27 使用檔案對話方塊指定位置。
37 extensions%3ACheckBox%3ARID_PAGE_FINAL%3ACB_REGISTER_DS c4 在 %PRODUCTNAME 中註冊新增的資料庫檔案。然後該資料庫會列在資料來源視窗中 (F4)。如果清除此核取方塊，則必須開啟資料庫檔案才能使用該資料庫。
36 extensions%3AEdit%3ARID_PAGE_FINAL%3AET_DATASOURCENAME 18 指定資料源名稱。
34 extensions%3APushButton%3ARID_PAGE_FINAL%3APB_BROWSE 27 使用檔案對話方塊指定位置。
49 extensions%3APushButton%3ARID_PAGE_FIELDMAPPING%3APB_INVOKE_FIELDS_DIALOG 31 開啟 [範本：通訊錄指定] 對話方塊。
1b DESKTOP_HID_FIRSTSTART_USER b0 使用者名稱將用於文件特性、範本以及記錄對文件所作的變更時。此頁面只有在您的電腦中找不到之前安裝的 %PRODUCTNAME 時才會顯示。
1d DESKTOP_HID_FIRSTSTART_DIALOG 65 「啟動精靈」可協助您指定 %PRODUCTNAME 的使用者設定。此精靈只會執行一次。
1e DESKTOP_HID_FIRSTSTART_WELCOME 65 「啟動精靈」可協助您指定 %PRODUCTNAME 的使用者設定。此精靈只會執行一次。
20 DESKTOP_HID_FIRSTSTART_MIGRATION 56 使用此頁面可將之前安裝的 %PRODUCTNAME 版本內的使用者資料匯入。
1e DESKTOP_HID_FIRSTSTART_LICENSE 9f 請閱讀並接受授權合約內容再繼續。您必須捲動到授權合約內容的最後才能繼續。若拒絕授權合約，則會取消安裝作業。
35 service%3Acom.sun.star.wizards.web.CallWizard%3Fstart 3e Web 精靈能協助您維護 Internet 伺服器上的網站。
16 WIZARDS_HID0_WEBWIZARD 3e Web 精靈能協助您維護 Internet 伺服器上的網站。
19 WIZARDS_HID1_LST_SESSIONS 75 選取您想要載入的設定，然後按一下 [載入]。若要以預設設定啟動精靈，請選取 [預設]。
18 WIZARDS_HID1_BTN_DEL_SES 18 刪除選取的設定。
1b WIZARDS_HID2_LST_DOC_EXPORT 39 選取您想要將選取的檔案匯出的檔案格式。
18 WIZARDS_HID2_BTN_REM_DOC 24 從清單中移除選取的檔案。
1a WIZARDS_HID2_TXT_DOC_TITLE 72 輸入選取文件的標題。標題會以選取文件的超連結形式，出現在您網站的索引頁面上。
19 WIZARDS_HID2_TXT_DOC_DESC 1e 輸入選取文件的說明。
1b WIZARDS_HID2_TXT_DOC_AUTHOR 24 輸入選取文件的作者姓名。
18 WIZARDS_HID2_BTN_ADD_DOC 93 開啟對話方塊，在此可選取要上傳至網站的檔案。清單中的順序決定網站索引頁上所顯示之文件超連結的順序。
15 WIZARDS_HID2_LST_DOCS df 列出您想要在網站上發佈的文件。精靈能夠將 %PRODUCTNAME 文件轉換為 HTML、PDF 或 Flash 格式 (某些情況下)，然後再上文件上傳。所有其他ª案會以其原始的檔案格式上傳。
1c WIZARDS_HID3_IL_LAYOUTS_IMG3 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG8 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG9 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG7 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG6 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG2 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG1 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG5 24 選取索引頁面的版面配置。
1c WIZARDS_HID3_IL_LAYOUTS_IMG4 24 選取索引頁面的版面配置。
1d WIZARDS_HID4_CHK_DISPLAY_SIZE 29 在索引頁上顯示檔案大小 (KB)。
20 WIZARDS_HID4_CHK_DISPLAY_UP_DATE 39 在索引頁中包含檔案最後一次修改的日期。
1e WIZARDS_HID4_CHK_DISPLAY_PAGES 2d 在索引頁上顯示您的網站的頁數。
1f WIZARDS_HID4_CHK_DISPLAY_AUTHOR 30 在索引頁中包含文件建立者的名字。
1e WIZARDS_HID4_GRP_OPTIMAIZE_640 39 針對 640x480 像素螢幕解析度來最佳化網站。
1e WIZARDS_HID4_GRP_OPTIMAIZE_800 39 針對 800x600 像素螢幕解析度來最佳化網站。
21 WIZARDS_HID4_CHK_DISPLAY_FILENAME 2d 在索引頁中包含文件的檔案名稱。
1f WIZARDS_HID4_CHK_DISPLAY_F_ICON 2a 在索引頁上顯示檔案格式圖示。
24 WIZARDS_HID4_CHK_DISPLAY_DESCRIPTION 2d 在索引頁中包含文件的摘要資訊。
1f WIZARDS_HID4_GRP_OPTIMAIZE_1024 3a 針對 1024x768 像素螢幕解析度來最佳化網站。
1f WIZARDS_HID4_CHK_DISPLAY_FORMAT 24 在索引頁上顯示檔案格式。
20 WIZARDS_HID4_CHK_DISPLAY_CR_DATE 2d 在索引頁中包含文件的建立日期。
18 WIZARDS_HID5_BTN_BACKGND 21 選取索引頁的背景影像。
16 WIZARDS_HID5_BTN_ICONS 36 選取想要用作索引頁上瀏覽元素的圖示。
17 WIZARDS_HID5_LST_STYLES 21 選取索引頁的顏色方案。
17 WIZARDS_HID_BG_BTN_NONE 24 清除索引頁中的背景影像。
e WIZARDS_HID_BG 32 指定一個背景影像作為 Web 精靈樣式。
18 WIZARDS_HID_BG_BTN_OTHER 4c 開啟 [開啟舊檔] 對話方塊，選取索引頁的背景影像檔案。
17 WIZARDS_HID_IS_BTN_NONE 21 清除索引頁中的圖示集。
e WIZARDS_HID_IS 41 選取用以瀏覽 Web 精靈中 HTML 簡報文件的圖示集。
1e WIZARDS_HID6_DATE_SITE_CREATED 51 為索引頁輸入建立日期。日期是以 HTML 中繼標記的形式存放。
1b WIZARDS_HID6_TXT_SITE_TITLE 50 為索引頁輸入標題。這個元素會顯示在 Web 瀏覽器的標題列。
1e WIZARDS_HID6_DATE_SITE_UPDATED 51 為索引頁輸入修改日期。日期是以 HTML 中繼標記的形式存放。
1b WIZARDS_HID6_TXT_SITE_EMAIL 57 為索引頁輸入電子郵件地址。地址是以 HTML 中繼標記的形式存放。
1f WIZARDS_HID6_TXT_SITE_COPYRIGHT 51 為索引頁輸入版權聲明。聲明是以 HTML 中繼標記的形式存放。
1a WIZARDS_HID6_TXT_SITE_DESC 4b 為索引頁輸入描述。描述是以 HTML 中繼標記的形式存放。
14 WIZARDS_HID7_TXT_FTP a1 將檔案上載到 FTP 伺服器。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
1c WIZARDS_HID7_CHK_PUBLISH_FTP a1 將檔案上載到 FTP 伺服器。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
15 WIZARDS_HID7_CHK_SAVE 27 儲存您在精靈中指定的設定。
16 WIZARDS_HID7_TXT_LOCAL ab 將索引頁和檔案上載到本機目錄。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
1e WIZARDS_HID7_CHK_PUBLISH_LOCAL ab 將索引頁和檔案上載到本機目錄。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
15 WIZARDS_HID7_TXT_SAVE 1e 輸入設定檔案的名稱。
14 WIZARDS_HID7_BTN_FTP 5e 開啟 [FTP 連接] 對話方塊，您可在此編輯和測試 FTP 伺服器的連線設定。
14 WIZARDS_HID7_BTN_ZIP 42 開啟對話方塊，在此您可以指定歸檔檔案的位置。
18 WIZARDS_HID7_BTN_PREVIEW 3e 在作業系統的預設 Web 瀏覽器中開啟您的網頁。
14 WIZARDS_HID7_TXT_ZIP d5 將索引頁和檔案增加到壓縮的歸檔檔案中，再將檔案上載到網站。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
16 WIZARDS_HID7_BTN_LOCAL 2a 開啟對話方塊，以選取資料夾。
1c WIZARDS_HID7_CHK_PUBLISH_ZIP d5 將索引頁和檔案增加到壓縮的歸檔檔案中，再將檔案上載到網站。索引頁將儲存到您指定的位置。所有其他檔案都將儲存到索引頁所在目錄的「myWebsite」目錄中。
18 WIZARDS_HID_FTP_TXT_PATH 44 輸入 FTP 伺服器上的目錄位置，以便在此存放檔案。
14 WIZARDS_HID_FTP_TEST 29 使用目前的設定測試 FTP 連接。
14 WIZARDS_HID_FTP_PASS 2c 輸入存取 FTP 伺服器所需的密碼。
16 WIZARDS_HID_FTP_SERVER 2d 輸入 FTP 伺服器的名稱或 IP 位址。
18 WIZARDS_HID_FTP_BTN_PATH 53 開啟對話方塊，您可在此指定用來存放檔案的 FTP 伺服器目錄。
f WIZARDS_HID_FTP 3a 編輯和測試 Web 精靈的 FTP 伺服器連線測試。
18 WIZARDS_HID_FTP_USERNAME 35 輸入存取 FTP 伺服器所需的使用者名稱。
1d DBACCESS_HID_QRYDGN_ROW_TABLE 3f 在此將列出與所選資料欄位對應的資料庫表格。
1b DBACCESS_HID_CTL_QRYDGNCRIT 1e 選取定義查詢的條件。
26 DBACCESS_HID_QUERY_EDIT_JOINCONNECTION 15 編輯連結特性。
1d DBACCESS_HID_QRYDGN_ROW_ORDER 66 如果您按一下此儲存格，可以在排序選項中進行選取：向上、向下和不排序。
28 dbaccess%3AEdit%3ADLG_SAVE_AS%3AET_TITLE 27 輸入查詢或表格檢視的名稱。
20 DBACCESS_HID_QRYDGN_ROW_FUNCTION 30 選取要在此處的查詢中執行的功能。
1d DBACCESS_HID_QRYDGN_ROW_FIELD 75 輸入您在此查詢中所參照資料欄位的名稱。在下方的列中所做的所有設定都參照此欄位。
2d dbaccess%3AComboBox%3ADLG_SAVE_AS%3AET_SCHEMA 39 輸入已指定給查詢或表格檢視的模式名稱。
1c DBACCESS_HID_QRYDGN_ROW_CRIT 2a 指定篩選資料欄位內容的條件。
1d DBACCESS_HID_QRYDGN_ROW_ALIAS 7e 指定別名。查詢中將列出此別名，而不是欄位名稱。如此將使其能夠使用使用者定義的欄標籤。
1a DBACCESS_HID_CTL_QRYDGNTAB 48 按兩下欄位可以將其新增到查詢中。拖放以定義關係。
1f DBACCESS_HID_QRYDGN_ROW_VISIBLE 61 如果您將資料欄位標示為 [可見的] 特性，該欄位在查詢中將會是可見狀態
20 DBACCESS_HID_DLG_QRY_RIGHT_TABLE 2a 指定要連結的兩個不同的表格。
1f DBACCESS_HID_DLG_QRY_LEFT_TABLE 2a 指定要連結的兩個不同的表格。
21 DBACCESS_HID_DLG_QRY_JOIN_CONTROL bb 在定義關係的 SQL 敘述中插入關鍵字 NATURAL。關係會連結兩個表格中具有相同欄的所有欄。所產生的連結表格針對每對同名欄僅會包含一個欄。
1d DBACCESS_HID_DLG_QRY_JOINTYPE 24 指定所選連結的連結類型。
2c dbaccess%3AEdit%3ADLG_PASSWORD%3AED_PASSWORD 15 輸入新的密碼。
2f dbaccess%3AEdit%3ADLG_PASSWORD%3AED_OLDPASSWORD 12 輸入舊密碼。
33 dbaccess%3AEdit%3ADLG_PASSWORD%3AED_PASSWORD_REPEAT 18 再次輸入新密碼。
29 sfx2%3AEdit%3ADLG_PASSWD%3AED_PASSWD_USER 1e 指定新使用者的名稱。
22 DBACCESS_HID_TAB_ENT_FORMAT_SAMPLE 2e 顯示可用 [...] 按鈕選取的格式碼。
1f DBACCESS_HID_TABDESIGN_TYPECELL 15 指定欄位類型。
29 DBACCESS_HID_TABLEDESIGN_TABED_PRIMARYKEY 57 如果此指令有一個核取標記，則在此行中的資料欄位為主關鍵字。
21 DBACCESS_HID_TABDESIGN_BACKGROUND 2a 此區域為定義表格結構的區域。
1c DBACCESS_HID_TAB_ENT_DEFAULT 2a 指定新資料記錄中的預設數值。
23 DBACCESS_HID_TABLEDESIGN_INSERTROWS 7e 若該表格尚未儲存，請在目列的列上插入一空列。若該表格已儲存，則請在表格結尾插入空列。
25 DBACCESS_HID_TABLE_DESIGN_HELP_WINDOW 15 顯示說明文字。
1f DBACCESS_HID_TABDESIGN_NAMECELL 66 指定資料欄位的名稱。注意資料庫的限制，比如名稱長度、特殊字元及空格。
1b DBACCESS_HID_TAB_ENT_FORMAT 2e 此按鈕開啟 [欄位格式] 對話方塊。
22 DBACCESS_HID_TABDESIGN_COMMENTCELL 1b 指定可選擇的描述。
2c DBACCESS_HID_DLGINDEX_INDEXDETAILS_SORTORDER 15 決定排序順序。
1c DBACCESS_HID_DLGIDX_NEWINDEX 12 建立新索引。
28 dbaccess%3AModalDialog%3ADLG_INDEXDESIGN 42 [索引設計] 對話方塊可讓您編輯目前表格的索引。
14 .uno%3ADBIndexDesign 42 [索引設計] 對話方塊可讓您編輯目前表格的索引。
1d DBACCESS_HID_DLGIDX_DROPINDEX 18 刪除目前的索引。
1e DBACCESS_HID_DLGIDX_RESETINDEX 39 將目前索引重設為啟動對話方塊時的設定。
32 dbaccess%3APushButton%3ADLG_INDEXDESIGN%3APB_CLOSE 18 關閉此對話方塊。
1f DBACCESS_HID_DLGIDX_RENAMEINDEX 1e 重新命名目前的索引。
1d DBACCESS_HID_DLGIDX_SAVEINDEX 27 儲存資料來源中目前的索引。
1d DBACCESS_HID_DLGIDX_INDEXLIST 75 顯示可用的索引。從清單中選取要編輯的索引。對話方塊中會顯示所選索引的詳細資訊。
28 DBACCESS_HID_DLGINDEX_INDEXDETAILS_FIELD 45 顯示目前表格中的欄位清單。您可以選取多個欄位。
31 dbaccess%3ACheckBox%3ADLG_INDEXDESIGN%3ACB_UNIQUE 33 指定目前的索引是否僅允許唯一的值。
1c DBACCESS_HID_CTL_RELATIONTAB 54 在此您可以透過共用資料欄位將目前資料庫的表格連結在一起。
1d DBACCESS_HID_RELDLG_KEYFIELDS 2a 定義用於此關係的關鍵字欄位。
2a DBACCESS_HID_RELATIONDIALOG_RIGHTFIELDCELL 3f 為連結選取的表格的名稱在此會以欄名稱出現。
14 .uno%3ADBAddRelation 36 允許您定義與編輯兩個表格之間的關係。
29 DBACCESS_HID_RELATIONDIALOG_LEFTFIELDCELL 3f 為連結選取的表格的名稱在此會以欄名稱出現。
3b dbaccess%3ACheckBox%3ATAB_WIZ_COPYTABLE%3ACB_PRIMARY_COLUMN 36 自動產生主關鍵字資料欄位，並填入值。
3a dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_APPENDDATA 39 將所要複製的表格的資料新增至現有表格。
34 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_VIEW a5 如果資料庫支援檢視，您只能在將查詢複製於表格容器中時選擇此選項。此選項可讓您如一般表格檢視那樣查閱和編輯查詢。
37 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_DEFDATA 26 建立資料庫表格的 1:1 副本。
30 dbaccess%3AEdit%3ATAB_WIZ_COPYTABLE%3AET_KEYNAME 42 為產生的主關鍵字指定名稱。此名稱是可選擇的。
33 dbaccess%3ARadioButton%3ATAB_WIZ_COPYTABLE%3ARB_DEF 33 僅複製表格定義，不複製對應的資料。
23 DBACCESS_HID_TAB_WIZ_TABLENAME_EDIT 18 指定副本的名稱。
45 dbaccess%3AMultiListBox%3ATAB_WIZ_COLUMN_SELECT%3ALB_NEW_COLUMN_NAMES 30 列出要包含在所複製表格中的欄位。
3e dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMNS_RH 66 增加或移除所選欄位 ([>] 按鈕或 [<] 按鈕) 或所有欄位 ([<<] 按鈕或 [>>] 按鈕)。
3d dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMN_RH 66 增加或移除所選欄位 ([>] 按鈕或 [<] 按鈕) 或所有欄位 ([<<] 按鈕或 [>>] 按鈕)。
3e dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMNS_LH 66 增加或移除所選欄位 ([>] 按鈕或 [<] 按鈕) 或所有欄位 ([<<] 按鈕或 [>>] 按鈕)。
3d dbaccess%3AImageButton%3ATAB_WIZ_COLUMN_SELECT%3AIB_COLUMN_LH 66 增加或移除所選欄位 ([>] 按鈕或 [<] 按鈕) 或所有欄位 ([<<] 按鈕或 [>>] 按鈕)。
45 dbaccess%3AMultiListBox%3ATAB_WIZ_COLUMN_SELECT%3ALB_ORG_COLUMN_NAMES bf 列出可包含在複製表格中的可用資料欄位。若要複製資料欄位，請按一下其名稱，然後按一下 [>] 按鈕。若要複製所有欄位，請按一下 [>>] 按鈕。
35 dbaccess%3APushButton%3ATAB_WIZ_TYPE_SELECT%3APB_AUTO 1b 啟用自動類型識別。
19 DBACCESS_HID_TAB_ENT_TYPE 15 選取欄位類型。
1d DBACCESS_HID_TAB_ENT_TEXT_LEN 21 輸入資料欄位的字元數。
1f DBACCESS_HID_TAB_ENT_COLUMNNAME 4b 顯示所選資料欄位的名稱。您可視需要輸入一個新名稱。
43 dbaccess%3AMultiListBox%3ATAB_WIZ_TYPE_SELECT%3ALB_NEW_COLUMN_NAMES 36 列出將包含在所複製表格中的資料欄位。
37 dbaccess%3ANumericField%3ATAB_WIZ_TYPE_SELECT%3AET_AUTO 2a 輸入用於自動類型識別的行數。
21 DBACCESS_HID_TAB_ENT_BOOL_DEFAULT 26 選取 [是/否] 欄位的預設值。
18 DBACCESS_HID_TAB_ENT_LEN 21 輸入資料欄位的字元數。
1a DBACCESS_HID_TAB_ENT_SCALE 69 輸入資料欄位的小數點位數。此選項僅適用於數字資料欄位或小數點資料欄位。
29 DBACCESS_HID_TAB_NAMEMATCHING_COLS_ASSIGN 7e 列出目標表格中可能的資料欄位。僅會將在來源表格清單中選取的資料欄位包含在目標表格中。
45 dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_DOWN_RIGHT 39 在清單中將選取的條目向下移動一個位置。
3f dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_DOWN 39 在清單中將選取的條目向下移動一個位置。
36 dbaccess%3APushButton%3ATAB_WIZ_NAME_MATCHING%3APB_ALL 27 選取清單中的所有資料欄位。
3d dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_UP 39 在清單中將選取的條目向上移動一個位置。
28 DBACCESS_HID_TAB_NAMEMATCHING_COLS_AVAIL 159 列出來源表格中的資料欄位。若要將來源表格中的資料欄位包含在目標表格中，請選取該資料欄位名稱前的核取方塊。若要將來源表格中的資料欄位的內容對映至目標表格中不同的資料欄位，請按一下來源表格清單中的該資料欄位，然後按一下向上鍵或向下鍵。
43 dbaccess%3AImageButton%3ATAB_WIZ_NAME_MATCHING%3AIB_COLUMN_UP_RIGHT 39 在清單中將選取的條目向上移動一個位置。
37 dbaccess%3APushButton%3ATAB_WIZ_NAME_MATCHING%3APB_NONE 27 清除清單中的所有核取方塊。
28 DBACCESS_HID_DSADMIN_AUTORETRIEVEENABLED 5d 啟用對目前 ODBC 或 JDBC 資料源的自動遞增資料欄位的 $[officename] 支援。
27 DBACCESS_HID_DSADMIN_AUTOINCREMENTVALUE 56 輸入指示資料源自動遞增指定的整數資料欄位的 SQL 指令指定元。
1f DBACCESS_HID_DSADMIN_SQL92CHECK 73 僅接受使用資料源中符合 SQL92 命名規則的字元的名稱。捨棄使用所有其他字元的名稱。
22 DBACCESS_HID_DSADMIN_RETRIEVE_AUTO 53 輸入傳回主關鍵字資料欄位最後一個自動遞增值的 SQL 陳述式。
1f DBACCESS_HID_DSADMIN_USECATALOG b6 使用 [分類] 目前的資料來源。此選項適用於 ODBC 資料來源為資料庫伺服器時。如果 ODBC 資料來源為 dBASE 驅動程式，請勿選取此核取方塊。
21 DBACCESS_HID_DSADMIN_ODBC_OPTIONS 63 在此文字欄位中可以選擇性地輸入額外的驅動程式設定，只要那是必要的。
23 DBACCESS_HID_DSADMIN_DBASE_INDICIES 5c 開啟 [索引] 對話方塊，您可在此管理目前 dBASE 資料庫中的表格索引。
20 DBACCESS_HID_DSADMIN_SHOWDELETED 7b 顯示檔案中的所有記錄，包含標記為刪除的記錄。如果選取此核取方塊，則您無法刪除記錄。
36 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_REMOVE 35 將選取的表格標誌移至[自由標誌]清單。
37 dbaccess%3AListBox%3ADLG_DBASE_INDEXES%3ALB_FREEINDEXES 2a 列出可指定給表格的可用索引。
39 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_REMOVEALL 32 將所有表格標誌移至[自由標誌]清單。
33 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_ADD 2f 將選取的索引移至[表格標誌]清單。
36 dbaccess%3AImageButton%3ADLG_DBASE_INDEXES%3AIB_ADDALL 32 將所有自由標誌移至[表格標誌]清單。
38 dbaccess%3AListBox%3ADLG_DBASE_INDEXES%3ALB_TABLEINDEXES 2d 列出所選資料庫表格的目前索引。
33 dbaccess%3AComboBox%3ADLG_DBASE_INDEXES%3ACB_TABLES 2a 選取要建立索引的資料庫表格。
34 dbaccess%3AMultiLineEdit%3ADLG_DIRECTSQL%3AME_STATUS 35 顯示您執行 SQL 指令的結果，包括錯誤。
32 dbaccess%3APushButton%3ADLG_DIRECTSQL%3APB_EXECUTE 3d 執行您在 [要執行的指令] 方塊中輸入的指令。
31 dbaccess%3AMultiLineEdit%3ADLG_DIRECTSQL%3AME_SQL 26 輸入要執行的 SQL 管理指令。
2f dbaccess%3AListBox%3ADLG_DIRECTSQL%3ALB_HISTORY 7a 列出先前執行過的 SQL 指令。若要再次執行指令，請按一下此指令，然後按一下 [執行查詢]。
27 DBACCESS_HID_DSADMIN_SUPPRESS_VERSIONCL c8 某些資料庫可藉由將版本編號指定給變更的欄位，以追蹤每項條目的變更。欄位每變更一次，此號碼即遞增 1。顯示資料庫表格中條目的內部版本編號。
d .uno%3ASortup 51 從字母表的起始字母開始，以向上順序排序表格名稱的清單。
1c DBACCESS_HID_TAB_PAGE_LBUSER 2a 選取您要修改其設定的使用者。
1c DBACCESS_HID_TAB_PAGE_PBUSER 30 增加可存取所選資料庫的新使用者。
1f DBACCESS_HID_TAB_PAGE_TBLGRANTS 45 顯示且允許您編輯所選使用者對資料庫的存取權限。
1e DBACCESS_HID_TAB_PAGE_PBCHGPWD 39 變更用於存取該資料庫的目前使用者密碼。
22 DBACCESS_HID_TAB_PAGE_PBUSERDELETE 18 移除所選使用者。
31 dbaccess%3AListBox%3ADLG_ADABASSTAT%3ALB_DATADEVS 2f 顯示 DATADEVSPACE 檔案的路徑和名稱。
34 dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_TRANSACTIONLOG 31 顯示 TRANSACTIONLOG 檔案的路徑和名稱。
2e dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_FREESIZE 42 以百萬位元為單位顯示此資料庫中的可用空間量。
39 dbaccess%3ANumericField%3ADLG_ADABASSTAT%3AET_MEMORYUSING 3c 以百分比的形式顯示此資料庫的已用空間量。
2a dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_SIZE 3c 以百萬位元為單位顯示此資料庫的全部大小。
31 dbaccess%3AEdit%3ADLG_ADABASSTAT%3AET_SYSDEVSPACE 2e 顯示 SYSDEVSPACE 檔案的路徑和名稱。
24 DBACCESS_HID_DLG_ADABAS_DATADEVSPACE 22 輸入資料 DEVSPACE 的路徑。
26 DBACCESS_HID_DLG_ADABAS_TRANSACTIONLOG 21 輸入作業記錄檔的路徑。
1e DBACCESS_HID_DLG_ADABAS_CONUSR ab 輸入您要給予其修改資料庫某些參數的有限控制權限之使用者名稱。一般而言，不會改變預設設定的 Control 使用者的名稱和密碼。
2b DBACCESS_HID_DLG_ADABAS_TRANSACTIONLOG_SIZE 3c 以百萬位元組為單位輸入此交易檔案的大小。
23 DBACCESS_HID_DLG_ADABAS_SYSDEVSPACE 22 輸入系統 DEVSPACE 的路徑。
1b DBACCESS_HID_DLG_ADABAS_USR 80 輸入由 Adabas 在內部使用的網域使用者的名稱。一般而言，網域使用者名稱和密碼的預設值不變。
29 DBACCESS_HID_DLG_ADABAS_DATADEVSPACE_SIZE 5b 以百萬位元組為單位在此處輸入此資料庫的大小。最大大小為 100 MB。
1e DBACCESS_HID_DLG_ADABAS_SYSPWD f 輸入密碼。
1e DBACCESS_HID_DLG_ADABAS_SYSUSR 24 輸入資料庫管理員的名稱。
25 DBACCESS_HID_DLG_ADABAS_PBSYSDEVSPACE 45 尋找要用來儲存此檔案的目錄，然後按一下 [確定]。
26 DBACCESS_HID_DLG_ADABAS_PBDATADEVSPACE 45 尋找要用來儲存此檔案的目錄，然後按一下 [確定]。
21 DBACCESS_HID_DLG_ADABAS_DOMAINPWD f 輸入密碼。
22 DBACCESS_HID_DLG_ADABAS_CACHE_SIZE 3f 以百萬位元組為單位輸入此資料緩衝區的大小。
1e DBACCESS_HID_DLG_ADABAS_CONPWD f 輸入密碼。
1e DBACCESS_HID_DLG_ADABAS_DBNAME 1b 輸入資料庫的名稱。
28 DBACCESS_HID_DLG_ADABAS_PBTRANSACTIONLOG 45 尋找要用來儲存此檔案的目錄，然後按一下 [確定]。
1b HID_DSADMIN_ESCAPE_DATETIME 34 核取以使用 ODBC 一致的日期/時間文字。
1b DBACCESS_HID_DSADMIN_SCHEMA 32 允許在 SELECT 陳述式中使用模式名稱。
24 DBACCESS_HID_DSADMIN_ENABLEOUTERJOIN 4f 將退出序列用於外部連結。此退出序列的語法為 {oj outer-join}
1f HID_DSADMIN_PRIMARY_KEY_SUPPORT 5f Enable to overrule Base's heuristics used to detect whether the database supports primary keys.
1c DBACCESS_HID_DSADMIN_CATALOG ac 使用分類目前的資料來源。此選項適用於 ODBC 資料來源為資料庫伺服器時。如果 ODBC 資料來源是 dBASE 驅動程式，請勿選取此選項。
2a DBACCESS_HID_DSADMIN_CHECK_REQUIRED_FIELDS d2 當您輸入新條目或更新表單中的現存條目時，若是將連結到資料庫欄的欄位保留空白，但是此資料庫欄必須輸入內容，則會出現一則訊息，警告您此欄位為空。
27 DBACCESS_HID_DSADMIN_PARAMETERNAMESUBST 3b 使用問號 (?) 替代資料來源中已命名的參數。
27 DBACCESS_HID_DSADMIN_SUPPRESS_VERSIONCL 39 在資料庫表格中顯示記錄的內部版本號碼。
26 HID_DSADMIN_AS_BEFORE_CORRELATION_NAME 9e 有些資料庫會在名稱及其別名之間使用關鍵字「AS」，有些資料庫則會使用空格。請啟用此選項，以在別名前面插入 AS。
26 DBACCESS_HID_DSADMIN_BOOLEANCOMPARISON 2a 選取要使用的布林值比較類型。
26 DBACCESS_HID_DSADMIN_IGNOREDRIVER_PRIV 36 忽略由資料庫驅動程式提供的存取權限。
25 DBACCESS_HID_DSADMIN_APPENDTABLEALIAS 3b 在 SELECT 陳述式中為表格名稱增補一個別名。
20 DBACCESS_HID_DSADMIN_DOSLINEENDS 7a 選取此選項，可使用成對的 CR + LF 程式碼結束每個文字行 (適合 DOS 與 Windows 作業系統使用)。
28 DBACCESS_HID_DSADMIN_IGNOREINDEXAPPENDIX 2f 以 ASC 或 DESC 陳述式建立一個索引。
1c DBACCESS_HID_DSADMIN_CHARSET 45 選取要用來在 $[officename] 中檢視此資料庫的字元集。
24 DBACCESS_HID_DSADMIN_SPECIAL_MESSAGE 24 選取要連線資料庫的類型。
1f DBACCESS_HID_DSADMIN_DBASE_PATH 2b 輸入包含 dBASE 檔案的目錄路徑。
2d DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLCLASS 42 輸入可連接至資料來源的 JDBC 驅動程式類別名稱。
2e DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLDBNAME 1b 輸入資料庫的名稱。
3c dbaccess%3ACheckBox%3APAGE_CONNECTION%3ACB_PASSWORD_REQUIRED 51 若加以核取，則會要求使用者輸入存取該資料庫所需的密碼。
1e DBACCESS_HID_DSADMIN_CALC_PATH 39 輸入要作為資料庫使用的試算表文件路徑。
31 DBACCESS_HID_PAGE_DBWIZARD_JDBC_PB_TESTMYSQLCLASS 36 透過 JDBC 驅動程式類來測試資料庫連線。
1e DBACCESS_HID_DSADMIN_FLAT_PATH 24 輸入文字檔的資料夾路徑。
3b dbaccess%3APushButton%3APAGE_CONNECTION%3APB_TESTCONNECTION 2a 使用目前設定測試資料庫連線。
20 DBACCESS_HID_DLG_DATABASE_WIZARD 4e 「資料庫精靈」可建立包含資料庫相關資訊的資料庫檔案。
3a dbaccess%3ARadioButton%3APAGE_GENERAL%3ARB_OPENEXISTINGDOC 66 從最近使用的檔案清單或從檔案選取對話方塊中，選取要開啟的資料庫檔案。
39 DBACCESS_HID_PAGE_DBWIZARD_GENERAL_RB_GETEXISTINGDATABASE 4e 選取此選項可建立一個資料庫文件供現有資料庫連接使用。
36 DBACCESS_HID_PAGE_DBWIZARD_GENERAL_RB_CREATEDBDATABASE 2a 選擇此選項可建立新的資料庫。
36 dbaccess%3APushButton%3APAGE_GENERAL%3APB_OPENDOCUMENT ac 開啟可以從中選取資料庫檔案的檔案選取對話方塊。在檔案選取對話方塊中按一下 [開啟] 或 [確定]，立即開啟檔案，並退出精靈。
33 dbaccess%3AListBox%3APAGE_GENERAL%3ALB_DOCUMENTLIST 82 從最近使用的檔案清單中，選取要開啟的資料庫檔案。按一下 [完成] 立即開啟檔案，並退出精靈。
1b DBACCESS_HID_DSADMIN_DSTYPE 30 選取現有資料庫連接的資料庫類型。
36 DBACCESS_HID_PAGE_DBWIZARD_FINAL_RB_REGISTERDATASOURCE 114 選取此選項可在您的 %PRODUCTNAME 使用者副本內註冊資料庫。註冊完畢後，該資料庫會顯示在 [檢視] - [資料來源] 視窗中。您必須註冊資料庫，才可將資料庫欄位插入文件 ([插入] - [欄位] - [其他]) 或合併列印中。
34 DBACCESS_HID_PAGE_DBWIZARD_FINAL_CB_STARTTABLEWIZARD 48 選取此選項可在結束「資料庫精靈」後呼叫表格精靈。
3a DBACCESS_HID_PAGE_DBWIZARD_FINAL_RB_DONTREGISTERDATASOURCE 51 選取此選項可使資料庫資訊只保持在所建立的資料庫文件內。
32 DBACCESS_HID_PAGE_DBWIZARD_FINAL_CB_OPENAFTERWARDS 57 選取此選項可顯示資料庫文件視窗，您可在其中編輯資料庫結構。
37 DBACCESS_HID_PAGE_DBWIZARD_MSACCESS_PB_MSACCESSLOCATION 39 按一下此選項開啟一個檔案選擇對話方塊。
37 DBACCESS_HID_PAGE_DBWIZARD_MSACCESS_ET_MSACCESSLOCATION 21 指定資料庫檔案的路徑。
2f DBACCESS_HID_PAGE_DBWIZARD_ADABAS_PB_ADABASNAME 39 按一下此選項開啟一個檔案選擇對話方塊。
2f DBACCESS_HID_PAGE_DBWIZARD_ADABAS_ET_ADABASNAME 21 輸入資料庫檔案的名稱。
3e DBACCESS_HID_PAGE_DBWIZARD_AUTHENTIFICATION_ET_GENERALUSERNAME 2b 使用者名稱最多可含 18 個字元。
28 DBACCESS_HID_PAGE_DBWIZARD_ADO_PB_ADOURL 39 按一下此選項開啟一個檔案選擇對話方塊。
46 DBACCESS_HID_PAGE_DBWIZARD_AUTHENTIFICATION_CB_GENERALPASSWORDREQUIRED 28 密碼必須包含 3 到 18 個字元。
28 DBACCESS_HID_PAGE_DBWIZARD_ADO_ET_ADOURL 19 輸入資料來源 URL。
31 DBACCESS_HID_PAGE_DBWIZARD_DBASE_ET_DBASELOCATION 25 輸入 dBASE *.dbf 檔案的路徑。
31 DBACCESS_HID_PAGE_DBWIZARD_DBASE_PB_DBASELOCATION 21 開啟路徑選取對話方塊。
2a DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_JDBCURL e1 輸入資料庫的 URL。例如，如果使用 MySQL JDBC 驅動程式，請輸入 "jdbc:mysql://<Servername>/<name of the database>". 如需有關 JDBC 驅動程式的更多資訊，請參閱此驅動程式附帶的文件。
20 DBACCESS_HID_DSADMIN_DRIVERCLASS 24 輸入 JDBC 驅動程式的名稱。
2c DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLPORT e1 輸入資料庫的 URL。例如，如果使用 MySQL JDBC 驅動程式，請輸入 "jdbc:mysql://<Servername>/<name of the database>". 如需有關 JDBC 驅動程式的更多資訊，請參閱此驅動程式附帶的文件。
32 DBACCESS_HID_PAGE_DBWIZARD_JDBC_ET_MYSQLHOSTSERVER 37 hostname 是指執行 MySQL 資料庫的機器名稱。
29 DBACCESS_HID_PAGE_DBWIZARD_LDAP_CB_USESSL 4f 建立透過安全通訊端階層 (SSL) 連至 LDAP 伺服器的安全連接。
20 DBACCESS_HID_DSADMIN_LDAP_BASEDN 3f 輸入搜尋 LDAP 資料庫的起點，例如，「dc=com」。
2d DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_PORTNUMBER 34 輸入 LDAP 伺服器的連接埠，通常是 389。
29 DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_BASEDN 3f 輸入搜尋 LDAP 資料庫的起點，例如，「dc=com」。
22 DBACCESS_HID_DSADMIN_LDAP_HOSTNAME 42 使用「ldap.server.com」格式輸入 LDAP 伺服器的名稱。
2d DBACCESS_HID_PAGE_DBWIZARD_LDAP_ET_HOSTSERVER 42 使用「ldap.server.com」格式輸入 LDAP 伺服器的名稱。
24 DBACCESS_HID_DSADMIN_LDAP_PORTNUMBER 34 輸入 LDAP 伺服器的連接埠，通常是 389。
32 DBACCESS_HID_PAGE_DBWIZARD_MYSQL_RB_CONNECTVIAJDBC 39 連結至在系統級上設定的現有 JDBC 資料源。
32 DBACCESS_HID_PAGE_DBWIZARD_MYSQL_RB_CONNECTVIAODBC 39 連結至在系統級上設定的現有 ODBC 資料源。
37 DBACCESS_HID_PAGE_DBWIZARD_ODBC_ET_NAMEOFODBCDATASOURCE 21 輸入資料庫檔案的路徑。
37 DBACCESS_HID_PAGE_DBWIZARD_ODBC_PB_NAMEOFODBCDATASOURCE 42 按一下此選項可開啟選擇 ODBC 資料來源對話方塊：
30 DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLECLASS 22 輸入資料庫伺服器的 URL。
2f DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLEPORT 2d 輸入資料庫伺服器的連接埠號碼。
35 DBACCESS_HID_PAGE_DBWIZARD_ORACLE_ET_ORACLEHOSTSERVER 23 輸入 Oracle 資料庫的名稱。
39 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_PB_SPREADSHEETPATH 39 按一下此選項開啟一個檔案選擇對話方塊。
45 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_CB_SPREADSHEETPASSWORDREQUIRED 3f 選取此選項可向資料庫文件的使用者請求密碼。
39 DBACCESS_HID_PAGE_DBWIZARD_SPREADSHEET_ET_SPREADSHEETPATH 33 輸入該試算表檔案的路徑和檔案名稱。
28 DBACCESS_HID_DSADMIN_THOUSANDS_SEPARATOR 6e 輸入或選取在文字檔案中用作千位分隔符的字元，例如，逗號 (1,000) 或句點 (1.000)。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_ET_LOCATIONTEXTFILE fd 輸入一或多個文字檔案的路徑。如果您只需要一個文字檔案，可使用檔名的任一副檔名。如果您輸入資料夾名稱，該資料夾內的文字檔案必須具有副檔名 *.csv 才會被識別為文字資料庫的檔案。
26 DBACCESS_HID_DSADMIN_DECIMAL_SEPARATOR 6a 輸入或選取在文字檔案中用作小數分隔符的字元，例如，句點 (0.5) 或逗號 (0,5)。
24 DBACCESS_HID_DSADMIN_FIELD_SEPARATOR 45 輸入或選取用於分隔文字檔案中的資料欄位的字元。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_PB_LOCATIONTEXTFILE 39 按一下此選項開啟一個檔案選擇對話方塊。
23 DBACCESS_HID_DSADMIN_TEXT_SEPARATOR 45 輸入或選取用於識別文字檔案中的文字欄位的字元。
2f DBACCESS_HID_PAGE_DBWIZARD_TEXT_ET_OWNEXTENSION 45 按一下以存取自訂檔案。在文字方塊中輸入副檔名。
31 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSCSVFILES 20 按一下以存取 csv 檔案。
33 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSOTHERFILES 45 按一下以存取自訂檔案。在文字方塊中輸入副檔名。
31 DBACCESS_HID_PAGE_DBWIZARD_TEXT_RB_ACCESSTXTFILES 20 按一下以存取 txt 檔案。
f .uno%3ADBRename 6c 重新命名選取的物件。根據資料庫的不同，一些名稱、字元與名稱長度可能無效。
f .uno%3ADBDelete 30 刪除所選表格、查詢、表單或報表。
1f .uno%3ADBDatabasePropertiesMenu 15 開啟子功能表。
d .uno%3ADBOpen 2d 開啟上次儲存狀態中選取的物件。
16 .uno%3ADBConvertToView cc 將選取的查詢轉換為檢視。原始查詢將保留於您的資料庫檔案中，且將在資料庫伺服器上生成一個附加檢視。您必需擁有寫入權限以將檢視新增至資料庫。
1b .uno%3ADBDSAdvancedSettings 25 開啟 [進階特性] 對話方塊。
21 DBACCESS_HID_BROWSER_TABLE_DELETE 30 刪除所選表格、查詢、表單或報表。
d .uno%3ADBEdit 4b 開啟一個視窗，用以編輯所選表格、查詢、表單或報表。
29 .uno%3ADBNewFormAutoPilotWithPreSelection 40 啟動 [表單精靈] 用於選取的表格、查詢或檢視。
19 .uno%3ADBDSConnectionType 1e 開啟 [連線類型精靈]。
15 .uno%3ADBDSProperties 28 開啟 [資料庫特性] 對話方塊。
2b .uno%3ADBNewReportAutoPilotWithPreSelection 40 啟動 [報表精靈] 用於選取的表格、查詢或檢視。
1f DBACCESS_HID_BROWSER_TABLE_EDIT 4b 開啟一個視窗，用以編輯所選表格、查詢、表單或報表。
19 .uno%3ADBSendReportAsMail f3 開啟預設的電子郵件應用程式以傳送新的電子郵件。所選報表會以附件形式附加在電子郵件中。您可輸入主旨、收件者和郵件內文。匯出動態報表時可將報表匯出成資料庫內容的副本。
1b .uno%3ADBSendReportToWriter 6c 將所選報表匯出成文字文件。匯出動態報表時可將報表匯出成資料庫內容的副本。
49 dbaccess%3AImageButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_NEWFOLDER 36 按一下以在資料庫檔案中建立新資料夾。
42 dbaccess%3AImageButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_UP 36 按一下以移到資料夾階層結構的上一層。
43 dbaccess%3APushButton%3ADLG_COLLECTION_VIEW%3ABTN_EXPLORERFILE_SAVE 36 按一下即可將表單儲存在資料庫檔案中。
40 dbaccess%3AEdit%3ADLG_COLLECTION_VIEW%3AED_EXPLORERFILE_FILENAME 27 輸入已儲存表單的檔案名稱。
10 .uno%3ADBNewView 27 在設計模式下開啟新的檢視。
11 .uno%3ADBNewTable 1b 開啟表格設計檢視。
12 .uno%3ADBNewReport 43 啟動所選表格、檢視或查詢的 [報表產生器] 視窗。
12 .uno%3ADBNewFolder 54 開啟一對話方塊，您可在其中將新資料夾儲存在資料庫檔案中。
10 .uno%3ADBNewForm 2d 在表單模式下開啟新的文字文件。
14 .uno%3ADBNewQuerySql 26 在 SQL 模式下開啟新的查詢。
13 .uno%3ADBNewViewSQL 26 在 SQL 模式下開啟新的檢視。
11 .uno%3ADBNewQuery 27 在設計模式下開啟新的查詢。
28 DBACCESS_HID_BROWSER_TABLE_CREATE_DESIGN 1b 開啟表格設計檢視。
23 DBACCESS_HID_DSADMIN_TABLE_SELECTOR 67 開啟 [表格篩選器] 對話方塊，您可在其中指定要顯示或隱藏哪些資料庫表格。
14 .uno%3ADBTableFilter 67 開啟 [表格篩選器] 對話方塊，您可在其中指定要顯示或隱藏哪些資料庫表格。
12 .uno%3ADBUserAdmin 4f 開啟 [使用者管理] 對話方塊 (如果資料庫支援此功能的話)。
12 .uno%3ADBDirectSQL 40 開啟 SQL 對話方塊，您可在其中輸入 SQL 陳述式。
17 .uno%3ADBRelationDesign 5d Opens the Relation Design view and checks whether the database connection supports relations.
17 .uno%3ADBDisablePreview 33 啟用或停用資料庫視窗中的預覽功能。
10 .uno%3ADBPreview 15 開啟子功能表。
d .uno%3ADBSort 15 開啟子功能表。
1c .uno%3ADBDatabaseObjectsMenu 15 開啟子功能表。
1b .uno%3ADBShowDocInfoPreview 36 預覽視窗可顯示表單或報表的文件資訊。
17 .uno%3ADBShowDocPreview 27 預覽可顯示表單或報表文件。
12 .uno%3ADBViewForms 3c 選取表單容器並在細節檢視中顯示所有表單。
13 .uno%3ADBViewTables 3c 選取表格容器並在細節檢視中顯示所有表格。
14 .uno%3ADBViewQueries 3c 選取查詢容器並在細節檢視中顯示所有查詢。
16 .uno%3ADBRefreshTables f 更新表格。
14 .uno%3ADBViewReports 3c 選取報表容器並在細節檢視中顯示所有報表。
45 dbaccess%3APushButton%3ATP_SAVE_DBDOC_AS%3APB_BROWSE_SAVE_AS_LOCATION a2 選擇位置及檔案名稱以儲存新的資料庫檔案。新檔案預設會與舊的檔案同名，而舊的檔案名稱會重新命名為字串「備份」。
32 dbaccess%3AMultiLineEdit%3ATP_SUMMARY%3AED_CHANGES 42 此清單會顯示所有原先套用至資料庫檔案的變更。
17 .uno%3ADBMigrateScripts 8d [資料庫文件巨集遷移精靈] 會將目前的巨集從舊的 Base 檔案之子文件，移至新的 Base 檔案之巨集儲存區域。
36 svtools%3ACheckBox%3ADLG_LOGIN%3ACB_LOGIN_SAVEPASSWORD dd 選取此選項可在目前的 %PRODUCTNAME 階段作業期間再次與同一資料來源連接時，一直使用同一使用者名稱和密碼，系統不會再顯示對話方塊要求您輸入使用者名稱和密碼。
2e svtools%3AEdit%3ADLG_LOGIN%3AED_LOGIN_PASSWORD 27 輸入密碼以連接至資料來源。
2e svtools%3AEdit%3ADLG_LOGIN%3AED_LOGIN_USERNAME 30 輸入使用者名稱以連接至資料來源。
17 WIZARDS_HID_QUERYWIZARD 36 「查詢精靈」可協助您設計資料庫查詢。
21 WIZARDS_HID_QUERYWIZARD_LSTTABLES 24 指定用於建立查詢的表格。
24 WIZARDS_HID_QUERYWIZARD_LSTSELFIELDS 2d 顯示新查詢中將包括的所有欄位。
1d WIZARDS_HID_QUERYWIZARD_SORT2 60 指定對建立之查詢進行排序的其他欄位 (如果之前的排序欄位相同的話)。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND4 3f 按一下此選項可按照字母或數字向下順序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND1 3f 按一下此選項可按照字母或數字向下順序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND3 3f 按一下此選項可按照字母或數字向下順序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND4 3f 按一下此選項可按照字母或數字向上順序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND2 3f 按一下此選項可按照字母或數字向上順序排序。
1d WIZARDS_HID_QUERYWIZARD_SORT4 60 指定對建立之查詢進行排序的其他欄位 (如果之前的排序欄位相同的話)。
1d WIZARDS_HID_QUERYWIZARD_SORT3 60 指定對建立之查詢進行排序的其他欄位 (如果之前的排序欄位相同的話)。
23 WIZARDS_HID_QUERYWIZARD_OPTDESCEND2 3f 按一下此選項可按照字母或數字向下順序排序。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND1 3f 按一下此選項可按照字母或數字向上順序排序。
1d WIZARDS_HID_QUERYWIZARD_SORT1 30 指定對建立之查詢進行排序的欄位。
22 WIZARDS_HID_QUERYWIZARD_OPTASCEND3 3f 按一下此選項可按照字母或數字向上順序排序。
23 WIZARDS_HID_QUERYWIZARD_OPTMATCHALL 41 選取此選項可使用邏輯 AND 按所有條件篩選查詢。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_1 18 輸入篩選條件值。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_2 24 選取篩選條件的欄位名稱。
23 WIZARDS_HID_QUERYWIZARD_OPTMATCHANY 40 選取此選項可使用邏輯 OR 按任意條件篩選查詢。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_1 24 選取篩選條件的欄位名稱。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_1 15 選取篩選條件。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_2 15 選取篩選條件。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_3 18 輸入篩選條件值。
22 WIZARDS_HID_QUERYWIZARD_TXTVALUE_2 18 輸入篩選條件值。
25 WIZARDS_HID_QUERYWIZARD_LSTOPERATOR_3 15 選取篩選條件。
26 WIZARDS_HID_QUERYWIZARD_LSTFIELDNAME_3 24 選取篩選條件的欄位名稱。
2e WIZARDS_HID_QUERYWIZARD_LSTAGGREGATEFUNCTION_1 15 選取集合函數。
29 WIZARDS_HID_QUERYWIZARD_BTNAGGREGATEMINUS 21 移除控制項的最後一列。
2c WIZARDS_HID_QUERYWIZARD_LSTAGGREGATEFIELDS_1 1b 選取數值欄位名稱。
2f WIZARDS_HID_QUERYWIZARD_OPTAGGREGATEDETAILQUERY 2d 選取此選項可顯示所有查詢記錄。
28 WIZARDS_HID_QUERYWIZARD_BTNAGGREGATEPLUS 1b 附加新的控制項列。
30 WIZARDS_HID_QUERYWIZARD_OPTAGGREGATESUMMARYQUERY 33 選取此選項只能顯示集合函數的結果。
2a WIZARDS_HID_QUERYWIZARD_LSTFILTERSELFIELDS 2a 顯示用來分組查詢的所有欄位。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_1 15 選取分組條件。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_1 24 選取分組條件的欄位名稱。
28 WIZARDS_HID_QUERYWIZARD_OPTGROUPMATCHANY 40 選取此選項可使用邏輯 OR 按任意條件分組查詢。
28 WIZARDS_HID_QUERYWIZARD_OPTGROUPMATCHALL 41 選取此選項可使用邏輯 AND 按所有條件分組查詢。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_1 18 輸入分組條件值。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_2 18 輸入分組條件值。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_3 15 選取分組條件。
2b WIZARDS_HID_QUERYWIZARD_LSTFILTEROPERATOR_2 15 選取分組條件。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_2 24 選取分組條件的欄位名稱。
28 WIZARDS_HID_QUERYWIZARD_TXTFILTERVALUE_3 18 輸入分組條件值。
2c WIZARDS_HID_QUERYWIZARD_LSTFILTERFIELDNAME_3 24 選取分組條件的欄位名稱。
22 WIZARDS_HID_QUERYWIZARD_TXTTITLE_1 1e 輸入欄位名稱的別名。
26 WIZARDS_HID_QUERYWIZARD_OPTMODIFYQUERY 3c 選取此選項可儲存查詢並開啟它以進行編輯。
22 WIZARDS_HID_QUERYWIZARD_TXTSUMMARY 18 顯示查詢的摘要。
25 WIZARDS_HID_QUERYWIZARD_TXTQUERYTITLE 18 輸入查詢的名稱。
27 WIZARDS_HID_QUERYWIZARD_OPTDISPLAYQUERY 1e 選擇儲存並顯示查詢。
2e REPORTDESIGN_CHECKBOX_RID_DATETIME_DLG_CB_DATE 76 啟用 [包含日期] 將日期欄位插入報表的使用中區域。日期欄位顯示執行報表的目前日期。
2e REPORTDESIGN_CHECKBOX_RID_DATETIME_DLG_CB_TIME 76 啟用 [包含時間] 將時間欄位插入報表的使用中區域。時間欄位顯示執行報表的目前時間。
1a .uno%3AInsertDateTimeField 70 您可以選擇 [插入] - [日期和時間]，以開啟 [報表產生器] 的 [日期和時間] 對話方塊。
14 .uno%3AGreatestWidth 60 選取兩個或多個物件，然後按一下此圖示以調整物件的大小到最大寬度。
15 .uno%3ASmallestHeight 60 選取兩個或多個物件，然後按一下此圖示以調整物件的大小到最小高度。
18 .uno%3ASectionAlignRight 5a 選取兩個或多個物件，然後按一下此圖示將物件對齊區域的右邊界。
17 .uno%3ASectionShrinkTop 33 縮小所選區段以移除頂端的空白空間。
14 .uno%3ASmallestWidth 60 選取兩個或多個物件，然後按一下此圖示以調整物件的大小到最小寬度。
17 .uno%3ASectionAlignLeft 5a 選取兩個或多個物件，然後按一下此圖示將物件對齊區域的左邊界。
11 .uno%3AHFixedLine 24 將水平線插入到目前區域。
16 .uno%3ASectionAlignTop 5a 選取兩個或多個物件，然後按一下此圖示將物件對齊區域的上邊界。
14 .uno%3ASectionShrink 3c 縮小所選區段以移除頂端及底端的空白空間。
15 .uno%3AGreatestHeight 60 選取兩個或多個物件，然後按一下此圖示以調整物件的大小到最大高度。
1a .uno%3ASectionShrinkBottom 33 縮小所選區段以移除底端的空白空間。
19 .uno%3ASectionAlignBottom 5a 選取兩個或多個物件，然後按一下此圖示將物件對齊區域的下邊界。
11 .uno%3AVFixedLine 24 將垂直線插入到目前區域。
26 REPORTDESIGN_HID_RPT_PROP_PREEVALUATED 45 如果啟用 [預先估算]，只會在報表完成時計算函數。
28 REPORTDESIGN_HID_RPT_PROP_INITIALFORMULA 41 輸入公式計算所用的初始值。通常設定為 0 或 1。
12 .uno%3ANewFunction 9e 在 [報表助手] 的右鍵功能表中，您會看到與 [報表產生器] 檢視中相同的指令，以及建立新函數或刪除函數的其他指令。
21 REPORTDESIGN_HID_RPT_PROP_FORMULA 3a 輸入定義函數的公式。使用 OpenFormula 語法。
26 REPORTDESIGN_HID_REPORT_NAVIGATOR_TREE a3 按一下 [報表助手] 中的項目。[報表產生器] 檢視中會選取對應的物件或區域。在項目上按一下滑鼠右鍵以開啟右鍵功能表。
28 REPORTDESIGN_HID_RPT_PROP_DEEPTRAVERSING b1 如果啟用 [深度遍歷]，則會考量階層的所有低層以求得函數值。這適用於行編號。如果未啟用 [深度遍歷]，則僅會計算階層的第一層。
16 .uno%3AReportNavigator 5e [報表助手] 顯示報表的結構。您可以使用 [報表助手] 將函數插入報表。
32 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_N 9 第 N 頁
3b REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_BOTTOMPAGE 15 頁面底端 (頁尾)
37 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_N_OF_M 15 第 N 頁 (共 M 頁)
38 REPORTDESIGN_RADIOBUTTON_RID_PAGENUMBERS_RB_PAGE_TOPPAGE 15 頁面頂端 (頁首)
32 REPORTDESIGN_LISTBOX_RID_PAGENUMBERS_LST_ALIGNMENT 6 對齊
22 EXTENSIONS_HID_PROP_VERTICAL_ALIGN c 垂直對齊
27 REPORTDESIGN_HID_RPT_PROP_RPT_POSITIONY 1e 設定所選物件的 Y 位置
25 REPORTDESIGN_HID_RPT_PROP_PRESERVEIRI cf 若是圖片，您可以指定將圖片做為檔案連結插入，或做為 Base 檔案的內嵌物件插入。內嵌選項會增加 Base 檔案的大小，而連結選項到了其他電腦則無法使用。
2e REPORTDESIGN_HID_RPT_PROP_PRINTWHENGROUPCHANGE 15 群組變更時列印
34 REPORTDESIGN_HID_RPT_PROP_CONDITIONALPRINTEXPRESSION 50 如果條件式列印表示式經評估為 TRUE，則會列印選取的物件。
21 REPORTDESIGN_HID_RPT_PROP_VISIBLE 64 執行的報表不會顯示隱藏的物件。此物件在 [報表產生器] 檢視中仍可看到。
23 REPORTDESIGN_HID_RPT_PROP_BACKCOLOR 3c 設定所選物件在螢幕上及列印時的背景顏色。
2a REPORTDESIGN_HID_RPT_PROP_PAGEHEADEROPTION 63 指定頁首的列印環境：在所有頁面上，或在不含報表首或報表尾的頁面上。
23 REPORTDESIGN_HID_RPT_PROP_DATAFIELD 46 在 [資料] 標籤頁中，您可以變更要顯示的資料內容。
22 REPORTDESIGN_HID_RPT_PROP_RPT_FONT 24 選取所選文字物件的字型。
27 REPORTDESIGN_HID_RPT_PROP_RPT_POSITIONX 1e 設定所選物件的 X 位置
26 REPORTDESIGN_HID_RPT_PROP_KEEPTOGETHER 6f 如果目前物件超過目前頁面大小，[保持在一起] 可指定從新頁面頂端開始列印物件。
26 REPORTDESIGN_HID_RPT_PROP_FORCENEWPAGE 5b [強迫跳頁] 指定是否要在新頁面上列印目前區段及 (或) 下一個區段。
24 REPORTDESIGN_HID_RPT_PROP_RPT_HEIGHT 1e 定義所選物件的高度。
2a REPORTDESIGN_HID_RPT_PROP_PAGEFOOTEROPTION 60 指定頁尾的列印環境：在所有頁面上，或在不含報表首或報表尾的頁面上
29 REPORTDESIGN_HID_RPT_PROP_BACKTRANSPARENT 33 指定所選物件的背景要透明或不透明。
2b REPORTDESIGN_HID_RPT_PROP_GROUPKEEPTOGETHER 54 群組預設會依頁或欄保持在一起。您也必須啟用 [保持在一起]。
1e REPORTDESIGN_HID_RPT_FIELD_SEL cb 在 [內容] 方塊中選取一個表格並離開該方塊後，會自動顯示 [增加欄位] 視窗。您也可以按一下工具列上的 [增加欄位] 圖示，或選擇 [檢視] - [增加欄位]。
23 REPORTDESIGN_HID_RPT_PROP_RPT_WIDTH 1e 設定所選物件的寬度。
25 REPORTDESIGN_HID_RPT_PROP_NEWROWORCOL 7c [新增的列或欄] 針對多欄設計，指定是否要在新列或新欄上列印目前區段及 (或) 下一個區段。
2d REPORTDESIGN_HID_RPT_PROP_PRINTREPEATEDVALUES 18 指定列印重複值。
13 .uno%3ASelectReport 64 若要顯示整份報表的 [資料] 或 [一般] 標籤頁，請選擇 [編輯] - [選取報表]。
41 reportdesign%3AListBox%3ARID_GROUPS_SORTING%3ALST_KEEPTOGETHERLST 48 選取群組在同一頁上要保持在一起所依據的細節層級。
31 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_ORDER 15 選取排序順序。
35 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_HEADERLST 24 選取顯示或隱藏 [群組首]。
28 REPORTDESIGN_HID_RPT_GROUPSORT_MOVE_DOWN 2a 在清單中將所選欄位向下移動。
1b .uno%3ADbSortingAndGrouping a1 在 [報表產生器] 的 [排序及群組] 對話方塊中，可以定義在報表中應排序哪些欄位，而哪些欄位應保持在一起以形成群組。
24 REPORTDESIGN_HID_RPT_FIELDEXPRESSION 39 按一下以開啟您可以從中選取欄位的清單。
25 REPORTDESIGN_HID_RPT_GROUPSORT_DELETE 21 在清單中移除所選欄位。
40 REPORTDESIGN_NUMERICFIELD_RID_GROUPS_SORTING_ED_GROUPINTERVALLST 33 輸入記錄群組時所依據的群組區間值。
36 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_GROUPONLST 42 選取在每次值變更時，或為其他特性建立新群組。
1e REPORTDESIGN_HID_RPT_GROUPSBRW 8d 列出將用於排序或群組的欄位。最上方的欄位有最高的優先權，第二個欄位有第二高的優先權，以此類推。
35 REPORTDESIGN_LISTBOX_RID_GROUPS_SORTING_LST_FOOTERLST 24 選取顯示或隱藏 [群組尾]。
26 REPORTDESIGN_HID_RPT_GROUPSORT_MOVE_UP 2a 在清單中將所選欄位向上移動。
1b WIZARDS_HID_DLGTABLE_DIALOG 36 「表格精靈」可協助您建立資料庫表格。
1d WIZARDS_HID_DLGTABLE_LBTABLES 93 選取範例表格之一。然後從左側清單方塊內的該表格中選取欄位。重複此步驟，直到選取所有需要的欄位為止。
20 WIZARDS_HID_DLGTABLE_OPTBUSINESS 36 選取商務分類就只能看到商務範例表格。
23 WIZARDS_HID_DLGTABLE_FIELDSSELECTED 2d 顯示新表格中將包括的所有欄位。
1f WIZARDS_HID_DLGTABLE_OPTPRIVATE 36 選取私人分類就只能看到私人範例表格。
1c WIZARDS_HID_DLGTABLE_COLNAME 51 顯示所選資料欄位的名稱。如果需要，您可以輸入新的名稱。
23 DBACCESS_HID_TAB_AUTOINCREMENTVALUE 5f 輸入指示資料來源以自動遞增指定的整數資料欄位的 SQL 指令限定符號。
1d DBACCESS_HID_TAB_ENT_REQUIRED 30 如設為 [是]，此欄位不得留為空白。
1c WIZARDS_HID_DLGTABLE_CMDPLUS 33 在清單方塊中增加一個新的資料欄位。
1d WIZARDS_HID_DLGTABLE_CMDMINUS 27 從清單方塊中移除所選欄位。
25 WIZARDS_HID_DLGTABLE_LB_SELFIELDNAMES 24 選取欄位以編輯欄位資訊。
22 DBACCESS_HID_TAB_ENT_AUTOINCREMENT 48 若設為 [是]，則此資料欄位的值會由資料庫引擎產生。
24 WIZARDS_HID_DLGTABLE_CK_PK_AUTOVALUE 9a 選取此選項可自動插入一個數值並遞增每筆新記錄的欄位值。資料庫必須支援自動遞增功能才能使用 [自動值] 功能。
2e WIZARDS_HID_DLGTABLE_CK_PK_AUTOVALUE_AUTOMATIC 9a 選取此選項可自動插入一個數值並遞增每筆新記錄的欄位值。資料庫必須支援自動遞增功能才能使用 [自動值] 功能。
25 WIZARDS_HID_DLGTABLE_OPT_PK_AUTOMATIC 3f 選取此選項可自動增加主關鍵字作為其他欄位。
24 WIZARDS_HID_DLGTABLE_CHK_USEPRIMEKEY ce 選擇建立主關鍵字。將主關鍵字增加到每個資料庫表格，以唯一識別每筆記錄。對於 %PRODUCTNAME 內的某些資料庫系統而言，必須使用主關鍵字才能編輯表格。
28 WIZARDS_HID_DLGTABLE_FIELDS_PK_AVAILABLE 51 選取一個欄位並按一下 > 可將它增加到主關鍵字欄位清單中。
23 WIZARDS_HID_DLGTABLE_OPT_PK_SEVERAL 54 選取此選項可根據好幾個現有欄位的組合來建立一個主關鍵字。
24 WIZARDS_HID_DLGTABLE_LB_PK_FIELDNAME 15 選取欄位名稱。
2a WIZARDS_HID_DLGTABLE_CMDREMOVE_PK_SELECTED 90 選取一個欄位並按一下 < 可將它從主關鍵字欄位清單中移除。建立主關鍵字可從上向下連結此清單中的欄位。
22 WIZARDS_HID_DLGTABLE_OPT_PK_SINGLE 3f 選擇使用具有唯一值的現有欄位作為主關鍵字。
28 WIZARDS_HID_DLGTABLE_OPT_STARTFORMWIZARD 7b 選取此選項以根據此表格建立表單。此表單會以表單精靈上次使用的設定建立於文字文件上。
24 WIZARDS_HID_DLGTABLE_OPT_MODIFYTABLE 4e 選取此選項可儲存表格設計，並開啟該表格供您輸入資料。
26 WIZARDS_HID_DLGTABLE_OPT_WORKWITHTABLE 24 選擇儲存並編輯表格設計。
20 WIZARDS_HID_DLGTABLE_LST_CATALOG 4d 選取用於表格的目錄。(資料庫必須支援目錄功能才能使用)
1d WIZARDS_HID_DLGTABLE_TXT_NAME 15 指定表格名稱。
1f WIZARDS_HID_DLGTABLE_LST_SCHEMA 4d 選取用於表格的模式。(資料庫必須支援模式功能才能使用)
15 .uno%3ADBReportRename 1e 重新命名選取的報表。
12 .uno%3ADBQueryOpen 48 開啟選取的查詢，以便您可以輸入、編輯或刪除記錄。
13 .uno%3ADBReportEdit 3c 開啟選取的報表，以便您可以變更版面配置。
14 .uno%3ADBQueryRename 1e 重新命名選取的查詢。
14 .uno%3ADBTableDelete 18 刪除選取的表格。
13 .uno%3ADBFormRename 1e 重新命名選取的表單。
12 .uno%3ADBTableOpen 48 開啟選取的表格，以便您可以輸入、編輯或刪除記錄。
11 .uno%3ADBFormOpen 48 開啟選取的表單，以便您可以輸入、編輯或刪除記錄。
12 .uno%3ADBTableEdit 36 開啟選取的表格，以便您可以變更結構。
11 .uno%3ADBFormEdit 3c 開啟選取的表單，以便您可以變更版面配置。
14 .uno%3ADBQueryDelete 18 刪除選取的查詢。
12 .uno%3ADBQueryEdit 36 開啟選取的查詢，以便您可以變更結構。
15 .uno%3ADBReportDelete 18 刪除選取的報表。
14 .uno%3ADBTableRename 1e 重新命名選取的表格。
13 .uno%3ADBReportOpen 48 開啟選取的報表，以便您可以輸入、編輯或刪除記錄。
13 .uno%3ADBFormDelete 18 刪除選取的表單。
3c SVX_HID_SVX_CHINESE_TRANSLATION_RB_CONVERSION_TO_TRADITIONAL 42 按一下將字元間隔套用到選取的美術字型物件上。
26 .uno%3AFontworkCharacterSpacingFloater 27 開啟美術字型字元間距視窗。
c FontWork1TBO 3c 按一下將對齊套用到選取的美術字型物件上。
c FontWork2TBO 24 輸入美術字型字元間隔值。
20 .uno%3AFontworkSameLetterHeights 63 將所選美術字型物件的字母高度從一般設定切換成與所有物件相同的高度。
1f .uno%3AFontworkAlignmentFloater 21 開啟對齊美術字型視窗。
19 .uno%3AFontworkShapeTypes 6f 開啟美術字型形狀工具列。按一下形狀，將形狀套用到選取的所有美術字型物件上。
39 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPALLOTHERS 3f 保留所有其他使用者的變更，使您的變更無效。
37 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPALLMINE 3c 保留您的所有變更，使所有的其他變更無效。
32 sc%3ACheckBox%3ARID_SCDLG_SHAREDOCUMENT%3ACB_SHARE d8 啟用可與其他使用者共用目前的文件。停用則不共用該文件。這將使自從您上次開啟或儲存本文件以來，其他使用者在這段時間中套用但尚未儲存的編輯變成無效。
14 .uno%3AShareDocument 61 開啟 [共用文件] 對話方塊，您可於其中啟用或停用文件的協同作業共用。
16 SC_HID_SCDLG_CONFLICTS 91 若相同內容已由不同使用者所變更，即會出現 [解決衝突] 對話方塊。請針對每個衝突，決定要保留哪項變更。
34 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPMINE 2d 保留您的變更，使其他變更無效。
35 sc%3APushButton%3ARID_SCDLG_CONFLICTS%3ABTN_KEEPOTHER 39 保留其他使用者的變更，使您的變更無效。
18 .uno%3AFormFilterExecute 73 如果您按一下 [表單篩選] 工具列上的套用以表單為基礎的篩選圖示，則將會套用篩選。
15 .uno%3AFormFilterExit 77 如果您在 [表單篩選] 工具列上的 [關閉] 按鈕上按一下，則表單會不使用篩選而直接顯示。
1e .uno%3AConfigureToolboxVisible 4b 會開啟一個對話方塊，您可在此新增、編輯和移除圖示。
16 SVX_HID_FM_IS_NOT_NULL ca 您可以在此區域中將篩選條件直接作為文字進行編輯。如果要檢查欄位是否有內容，您可以選取篩選條件 "empty" (SQL:"Is Null") 或 "not empty" (SQL: "Is not Null")。
1a .uno%3AFormFilterNavigator 64 若要將數種篩選條件與布林型 OR 連接，請按一下篩選列上的篩選助手圖示。
19 SVX_HID_FM_FILTER_IS_NULL ca 您可以在此區域中將篩選條件直接作為文字進行編輯。如果要檢查欄位是否有內容，您可以選取篩選條件 "empty" (SQL:"Is Null") 或 "not empty" (SQL: "Is not Null")。
18 SVX_HID_FILTER_NAVIGATOR 152 [篩選助手] 中將顯示已設定的篩選條件。一旦您設定了篩選，即可在 [篩選助手] 的底端看到一個空白篩選條目。您可以按一下單詞 "Or" 來選取此條目。選取了空白篩選條目，則可在表單中輸入其他篩選條件。這些條件透過布林型 OR 與先前定義的條件連結。
f SVX_HID_FM_EDIT ca 您可以在此區域中將篩選條件直接作為文字進行編輯。如果要檢查欄位是否有內容，您可以選取篩選條件 "empty" (SQL:"Is Null") 或 "not empty" (SQL: "Is not Null")。
1e SVX_HID_CTL_FONTWORK_FAVORITES d2 選取「美術字型」樣式，再按一下 [確定]，將「美術字型」插入您的文件。在文件中按兩下或 Ctrl+按兩下該「美術字型」，以進入文字編輯模式，並變更文字。
12 .uno%3ASaveGraphic c3 若要匯出 Writer 中的點陣圖：在點陣圖上按一下滑鼠右鍵，接著選擇 [儲存圖形]。您會看到 [匯出圖形] 對話方塊。請輸入檔案名稱並選取檔案類型。
17 SFX2_HID_SIDEBAR_WINDOW 76 The sidebar provides frequently used tools, grouped in decks. Click on a tab in the vertical tab bar to choose a deck.
26 .HelpId%3AStartCenter%3ATemplateButton 3a [範本] 圖示會開啟 [範本和文件] 對話方塊。
22 .HelpId%3AStartCenter%3AMathButton 39 每個文件圖示都會開啟指定類型的新文件。
22 .HelpId%3AStartCenter%3ADrawButton 39 每個文件圖示都會開啟指定類型的新文件。
24 .HelpId%3AStartCenter%3AWriterButton 39 每個文件圖示都會開啟指定類型的新文件。
20 .HelpId%3AStartCenter%3ADBButton 39 每個文件圖示都會開啟指定類型的新文件。
22 .HelpId%3AStartCenter%3AOpenButton 36 [開啟文件] 圖示代表檔案開啟對話方塊。
22 .HelpId%3AStartCenter%3ACalcButton 39 每個文件圖示都會開啟指定類型的新文件。
25 .HelpId%3AStartCenter%3AImpressButton 39 每個文件圖示都會開啟指定類型的新文件。
15 FWK_HID_BACKINGWINDOW 45 按一下任一圖示，即可開啟新文件或檔案對話方塊。
c .uno%3AAbout 3f 顯示一般的程式資訊，例如版本號碼和版權等。
12 .uno%3AHelpSupport 2a 顯示有關取得支援方法的資訊。
f .uno%3AHelpMenu 4b 使用說明功能表，可以啟動和控制 $[officename] 說明系統。
13 .uno%3AOnlineUpdate 115 啟用 %PRODUCTNAME 的網際網路連線。若您需要代理伺服器，請檢查 [%PRODUCTNAME] - [喜好設定][工具] - [選項] - [網際網路] 中的 [%PRODUCTNAME 代理伺服器] 設定。然後選擇 [檢查更新] 以檢查是否有可用的新版 Office 套件。
10 .uno%3AHelpIndex 39 開啟目前應用程式的 $[officename] 說明主頁。
2c service%3Acom.sun.star.tab.tabreg%3Fpurchase 66 %PRODUCTNAME 試用版會顯示此功能表指令。請選擇以開啟 [購買 %PRODUCTNAME 精靈]。
13 .uno%3AExtendedHelp 4e 在下一次點按滑鼠之前，啟動滑鼠指標下的延伸說明提示。
e RID_ENVTOOLBOX 6f 標準列位於 $[officename] 視窗的頂端。每個 $[officename] 應用程式都可以使用此工具列。
18 .HelpId%3Atableobjectbar 75 The Table Bar contains functions you need when working with tables. It appears when you move the cursor into a table.
19 SVX_HID_OFA_HYPERLINK_DLG ad 使用 [超連結] 位址欄在文件中建立和編輯超連結，還可以在此位址欄內輸入搜尋條件，以使用可用的 Internet 搜尋引擎來進行搜尋。
17 .uno%3ADSBInsertColumns 51 將已標記記錄的所有欄位插入到目前文件中游標所在的位置。
14 .uno%3ADSBFormLetter 34 啟動 [合併列印精靈] 以建立套印信件。
11 .uno%3APrevRecord 18 移至上一條記錄。
11 .uno%3ALastRecord 1b 移至最後一條記錄。
12 .uno%3AFirstRecord 18 移至第一條記錄。
e .uno%3ARecSave 3c 儲存新的資料條目。變更會在資料庫中註冊。
13 .uno%3ADeleteRecord 2d 刪除記錄。刪除前需要確認查詢。
10 .uno%3ANewRecord 15 建立新的記錄。
11 .uno%3ANextRecord 18 移至下一條記錄。
15 .uno%3AAbsoluteRecord 4b 顯示目前記錄的編號。輸入一個編號以移至相應的記錄。
e .uno%3ARecUndo 1e 允許您復原資料條目。
e .uno%3AGridUse 30 指定您僅可在網格點之間移動物件。
14 .uno%3ABezierConvert 39 將曲線轉換成直線，或將直線轉換成曲線。
12 .uno%3ABezierClose 18 封閉線條或曲線。
13 .uno%3ABezierInsert 39 啟動插入模式。使用此模式可以插入接點。
1c .uno%3ABezierEliminatePoints 2a 標記要刪除的目前點或所選點。
11 .uno%3ABezierMove 24 啟動可以移動接點的模式。
13 .uno%3ABezierSmooth 27 轉換角點或對稱點為平滑點。
11 .uno%3ABezierEdge 2d 將選取的單點或數點轉換成角點。
14 .uno%3ABezierCutLine 84 分割曲線圖示可以分割一條曲線。選取要在這些位置分割曲線的一個或多個接點，然後按一下此圖示
13 .uno%3ABezierDelete 8b 使用刪除接點圖示以刪除一個或多個選取的接點。如果您想要選取多個點，請同時按 Shift 鍵和相應的點。
12 HID_BEZIER_TOOLBOX 6d [編輯接點] 列的出現時機為當您選取一個多邊形物件，然後按一下 [編輯接點] 時。
16 .uno%3ABezierSymmetric 36 此圖示會將角點或平滑點轉換成對稱點。
1a CUI_HID_OFADLG_TREELISTBOX 30 請選取要顯示或變更其預設的區域。
18 .uno%3AOptionsTreeDialog 45 這個指令會開啟一個用於自訂程式配置的對話方塊。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_CITY 1b 輸入您居住的城市。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_ZIP 2a 在此欄位中輸入您的郵遞區號。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_MAIL 1b 輸入電子郵件地址。
2e cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_POSITION 30 在此欄位中輸入您在公司中的職位。
24 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FAX 2a 在此欄位中輸入您的傳真號碼。
2b cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_EMAIL 1b 輸入電子郵件地址。
29 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_FAX 2a 在此欄位中輸入您的傳真號碼。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_CITY 1b 輸入您居住的城市。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_CITY 1b 輸入您居住的城市。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_COUNTRY 15 輸入您的省市。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COMP 2a 在此欄位中輸入您公司的名稱。
2c cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_STREET 2a 在此欄位中輸入您街道的名稱。
30 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_ZIPCODE 2a 在此欄位中輸入您的郵遞區號。
28 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_COUNTRY 15 輸入您的省市。
30 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TELCOMPANY 1e 在此欄位中輸入工號。
2a cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_NAME 15 輸入您的姓氏。
2d cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_COMPANY 2a 在此欄位中輸入您公司的名稱。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_FAX 2a 在此欄位中輸入您的傳真號碼。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_SHORTNAME 1b 輸入您的姓名縮寫。
17 CUI_HID_OPTIONS_GENERAL 36 使用此標籤頁來輸入或編輯使用者資料。
29 cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_PLZ 2a 在此欄位中輸入您的郵遞區號。
26 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_MAIL 1b 輸入電子郵件地址。
2a sw%3AEdit%3ATP_PRIVATE_DATA%3AED_FIRSTNAME 15 輸入您的名字。
29 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_COUNTRY 15 輸入您的省市。
27 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_STREET 2a 在此欄位中輸入您街道的名稱。
2a sw%3AEdit%3ATP_BUSINESS_DATA%3AED_POSITION 30 在此欄位中輸入您在公司中的職位。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TELPRIVAT 30 在此欄位中輸入您的私人電話號碼。
2b cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_TITLE 24 在此欄位中輸入您的稱謂。
25 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_ZIP 2a 在此欄位中輸入您的郵遞區號。
25 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_NAME 15 輸入您的姓氏。
28 sw%3AEdit%3ATP_BUSINESS_DATA%3AED_STREET 2a 在此欄位中輸入您街道的名稱。
2f cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_FIRSTNAME 15 輸入您的名字。
2a cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_CITY 1b 輸入您居住的城市。
2e cui%3AEdit%3ARID_SFXPAGE_GENERAL%3AED_US_STATE 15 輸入您的省市。
29 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_SHORTCUT 1b 輸入您的姓名縮寫。
26 sw%3AEdit%3ATP_PRIVATE_DATA%3AED_TITLE 24 在此欄位中輸入您的稱謂。
2c cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_FILTER ac 指定如何將在左側選取的文件類型總是儲存為此檔案類型。您可以在 [另存新檔] 對話方塊中，為目前的文件選取另一種檔案類型。
29 cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_APP 39 為您要定義的預設檔案格式指定文件類型。
38 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_NOPRETTYPRINTING 54 儲存文件時，%PRODUCTNAME 會寫入 XML 資料而不會縮排或另外換行。
34 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ACB_LOAD_SETTINGS 39 載入文件與在其中儲存的使用者特定設定。
38 sfx2%3ACheckBox%3ARID_DLG_ALIEN_WARNING%3ACB_WARNING_OFF af 您可以選擇在以非 OpenDocument 格式或未在 [選項] 對話方塊的 [載入/儲存] - [一般] 中設為預設格式的格式儲存文件時，取得警告訊息。
35 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_RELATIVE_FSYS 3d 選取此方塊，以在檔案系統中相對式儲存 URL。
35 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_RELATIVE_INET 3a 選取此方塊，在網際網路中相對式儲存 URL。
37 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_WARNALIENFORMAT af 您可以選擇在以非 OpenDocument 格式或未在 [選項] 對話方塊的 [載入/儲存] - [一般] 中設為預設格式的格式儲存文件時，取得警告訊息。
33 cui%3ANumericField%3ARID_SFXPAGE_SAVE%3AED_AUTOSAVE 33 為自動回復選項指定時間區間 (分鐘)。
2f cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_DOCINFO 5c 指定每次選取 [另存新檔] 指令時，螢幕上都將顯示 [屬性] 對話方塊。
31 cui%3AListBox%3ARID_SFXPAGE_SAVE%3ALB_ODF_VERSION fa 某些公司或組織可能需要具備 ODF 1.0/1.1 格式的 ODF 文件。您可以選取該格式，以儲存於清單方塊中。此較舊版的格式無法儲存所有新的功能，因此建議您盡可能使用新格式的 ODF 1.2 (Extended)。
2e cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_BACKUP bd 無論何時儲存文件，都將文件的前一版本儲存為備份副本。%PRODUCTNAME 每次建立備份副本時，都會替代前一備份副本。備份副本的副檔名為 .BAK。
30 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ABTN_AUTOSAVE 92 指定 %PRODUCTNAME 儲存需要的資訊，以便在發生損壞的情況時，回復所有開啟的文件。您可以指定儲存時間間隔。
36 cui%3ACheckBox%3ARID_SFXPAGE_SAVE%3ACB_LOAD_DOCPRINTER 136 若為啟用，會連同文件載入印表機設定。如果您未手動變更 [列印] 對話方塊中的印表機，則可能會在遠方的印表機上列印文件。若為停用，會使用您的標準印表機列印此文件。不論是否核取此選項，目前的印表機設定皆會隨文件儲存。
14 CUI_HID_OPTIONS_SAVE 6d 在 [一般] 區域中，您可以選取用於儲存文件的預設值，還可以選取預設檔案格式。
2e cui%3APushButton%3ARID_SFXPAGE_PATH%3ABTN_PATH 44 按一下以顯示 [選取路徑] 或 [編輯路徑] 對話方塊。
32 cui%3APushButton%3ARID_SFXPAGE_PATH%3ABTN_STANDARD 45 [預設] 按鈕可重設全部所選條目為預先定義的路徑。
14 CUI_HID_OPTIONS_PATH 63 本節包含 $[officename] 中重要資料夾的預設路徑。使用者可以編輯這些路徑。
18 CUI_HID_OPTPATH_CTL_PATH 72 若要修改清單項目，請按一下該項目，然後按一下 [編輯]。您也可以連按兩下該項目。
33 cui%3AListBox%3ARID_SVXDLG_MULTIPATH%3ALB_MULTIPATH 42 包含已增加的路徑清單。標記新檔案的預設路徑。
3b cui%3APushButton%3ARID_SVXDLG_MULTIPATH%3ABTN_ADD_MULTIPATH 74 開啟 [選取路徑] 對話方塊選取其他資料夾，或利用 [開啟舊檔] 對話方塊選取其他檔案。
41 cui%3ANumericField%3ARID_SVXDLG_LNG_ED_NUM_PREBREAK%3AED_PREBREAK 36 鍵入連字符前後必須存在的最小字元數。
3a cui%3APushButton%3ARID_SFXDLG_EDITDICT%3APB_DELETE_REPLACE 2d 移除目前自訂字典中標記的字詞。
3d cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_EDIT_DIC 67 開啟 [編輯自訂字典] 對話方塊，在此您可以擴充自訂字典，或編輯現有條目。
18 CUI_HID_OPTIONS_DICT_NEW 79 開啟 [新增字典] 對話方塊，在此您可以命名新的使用者自訂字典或例外字典，並指定語言。
1d CUI_HID_CLB_EDIT_MODULES_DICS 21 列出可用的使用者字典。
31 cui%3AListBox%3ARID_SFXDLG_EDITDICT%3ALB_ALLDICTS 1b 指定要編輯的書籍。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_MODULES_EDIT 48 若要編輯語言模組，請先選取模組然後按一下 [編輯]。
19 CUI_HID_CLB_LINGU_MODULES 21 包含已安裝的語言模組。
19 CUI_HID_OPTIONS_DICT_EDIT 67 開啟 [編輯自訂字典] 對話方塊，在此您可以擴充自訂字典，或編輯現有條目。
15 CUI_HID_OPTIONS_LINGU 3c 指定拼字檢查、同義詞詞典和連字符的屬性。
1b CUI_HID_LNGDLG_NUM_PREBREAK 36 鍵入連字符前後必須存在的最小字元數。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_DEL_DIC 4b 如果所選字典沒有防寫入保護，則在確認之後將其刪除。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_OPTIONS_EDIT 51 如果要變更某個值，請選取相應的條目，然後按一下 [編輯]。
19 CUI_HID_CLB_LINGU_OPTIONS 2a 定義拼字檢查和連字符的選項。
2a cui%3AEdit%3ARID_SFXDLG_EDITDICT%3AED_WORD 78 您可以鍵入新的字詞，以加入到字典中。在下面的清單中，將會看到目前自訂字典的內容。
31 cui%3AListBox%3ARID_SFXDLG_EDITDICT%3ALB_DICTLANG 27 指定目前自訂字典的新語言。
37 cui%3APushButton%3ARID_SFXDLG_EDITDICT%3APB_NEW_REPLACE 86 將 [字詞] 文字欄位中的字詞加入目前自訂字典中。使用例外字典時，也會加入 [建議] 欄位中的字詞。
30 cui%3ACheckBox%3ARID_SFXDLG_NEWDICT%3ABTN_EXCEPT 3c 指定您是否希望在文件中避免使用某些字詞。
2d cui%3AEdit%3ARID_SFXDLG_NEWDICT%3AED_DICTNAME 21 指定新自訂字典的名稱。
12 .uno%3ASpellOnline 3f 輸入時自動進行拼字檢查，並用底線標記錯誤。
2d cui%3AEdit%3ARID_SFXDLG_EDITDICT%3AED_REPLACE 85 此輸入欄位只有當您編輯例外字典時有用。該欄位會顯示對 [字詞] 文字方塊中目前字詞的其他建議。
30 cui%3AListBox%3ARID_SFXDLG_NEWDICT%3ALB_DICTLANG 42 透過選取某種語言，您可以限制自訂字典的使用。
3c cui%3APushButton%3ARID_SFXPAGE_LINGU%3APB_LINGU_DICS_NEW_DIC 79 開啟 [新增字典] 對話方塊，在此您可以命名新的使用者自訂字典或例外字典，並指定語言。
46 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_DOWN 36 將清單方塊中所選模組的優先度降一級。
44 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_UP 39 將清單方塊中所選模組的優先度提昇一級。
20 CUI_HID_CLB_EDIT_MODULES_MODULES 63 為所選的模組指定語言和可用的拼字檢查、連字符以及同義詞詞典子模組。
42 cui%3AListBox%3ARID_SVXDLG_EDIT_MODULES%3ALB_EDIT_MODULES_LANGUAGE 15 指定模組語言。
46 cui%3APushButton%3ARID_SVXDLG_EDIT_MODULES%3APB_EDIT_MODULES_PRIO_BACK 36 按一下此處復原目前清單方塊中的變更。
30 cui%3AImageButton%3ARID_SVXPAGE_COLOR%3ABTN_SAVE 6a 開啟 [另存新檔] 對話方塊，您可以在其中將目前的顏色表儲存在指定的名稱下。
32 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_WORK_ON ec Opens the Color Picker. First highlight a color in the Color table. Then click Edit to open the Color Picker. Select a color in the Color Picker and click OK. The selected color replaces the color that is highlighted in the Color table.
2e cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_ADD 15 增加新的顏色。
31 cui%3APushButton%3ARID_SVXPAGE_COLOR%3ABTN_MODIFY 55 Changes the current color. Note that the color is overwritten without a confirmation.
15 CUI_HID_TPCOLOR_RGB_2 6 綠色
31 cui%3AListBox%3ARID_SVXPAGE_COLOR%3ALB_COLORMODEL 7a 若要修改，請選取顏色模型：紅色 - 綠色 - 藍色 (RGB) 或藍綠色 - 紫紅色 - 黃色 - 黑色 (CMYK)。
15 CUI_HID_OPTIONS_COLOR 57 允許您從顏色表格中選取顏色、編輯現有顏色或者定義新的顏色。
15 CUI_HID_TPCOLOR_RGB_3 6 藍色
15 CUI_HID_TPCOLOR_RGB_1 6 紅色
2c cui%3AListBox%3ARID_SVXPAGE_COLOR%3ALB_COLOR 57 包含一份可用顏色的清單。若要選取一種顏色，請從清單中選取。
30 cui%3AImageButton%3ARID_SVXPAGE_COLOR%3ABTN_LOAD 46 存取 [開啟舊檔] 對話方塊，您可以在其中選取調色板
29 cui%3AEdit%3ARID_SVXPAGE_COLOR%3AEDT_NAME 5d 指定所選顏色的名稱。您也可以在定義新顏色時在此欄位中鍵入名稱。
2c cui%3AMetricField%3AColorPicker%3ABrightness 2b Sets the Brightness in the HSB color model.
25 cui%3AMetricField%3AColorPicker%3AHex 64 Sets the Red, Green, and Blue color value in the RGB color model, using a hexadecimal format RRGGBB.
25 cui%3AMetricField%3AColorPicker%3AHue 24 Sets the Hue in the HSB color model.
25 cui%3ARadioButton%3AColorPicker%3AHue 24 Sets the Hue in the HSB color model.
27 cui%3AMetricField%3AColorPicker%3AGreen 32 Sets the Green color value in the RGB color model.
29 cui%3AControl%3AColorPicker%3AColorSlider ba In the color slider, you will see the range of values that you can change, depending on the selected option button. Click inside the color slider to change values in the big color field.
2a cui%3AControl%3AColorPicker%3AColorPreview 6a In the left half of the preview field, you will always see the current result of your work in this dialog.
2c cui%3ARadioButton%3AColorPicker%3ASaturation 2b Sets the Saturation in the HSB color model.
26 cui%3AMetricField%3AColorPicker%3ABlue 31 Sets the Blue color value in the RGB color model.
27 cui%3ARadioButton%3AColorPicker%3AGreen 32 Sets the Green color value in the RGB color model.
26 cui%3ARadioButton%3AColorPicker%3ABlue 31 Sets the Blue color value in the RGB color model.
25 cui%3AMetricField%3AColorPicker%3AKey 42 Sets the Black color value or key (black) in the CMYK color model.
29 cui%3AMetricField%3AColorPicker%3AMagenta 35 Sets the Magenta color value in the CMYK color model.
2c cui%3ARadioButton%3AColorPicker%3ABrightness 2b Sets the Brightness in the HSB color model.
28 cui%3AMetricField%3AColorPicker%3AYellow 34 Sets the Yellow color value in the CMYK color model.
26 cui%3AMetricField%3AColorPicker%3ACyan 32 Sets the Cyan color value in the CMYK color model.
28 cui%3AControl%3AColorPicker%3AColorField 38 In the big color field, you click to select a new color.
2c cui%3AMetricField%3AColorPicker%3ASaturation 2b Sets the Saturation in the HSB color model.
25 cui%3AMetricField%3AColorPicker%3ARed 30 Sets the Red color value in the RGB color model.
25 cui%3ARadioButton%3AColorPicker%3ARed 30 Sets the Red color value in the RGB color model.
2b cui%3AControl%3AColorPicker%3AColorPrevious 64 In the right half of the preview field, you will see the original color from the parent tab, Colors.
1f cui%3AModalDialog%3AColorPicker 25 Allows you to define your own colors.
2b cui%3AListBox%3AOFA_TP_MISC%3ALB_HELPFORMAT 27 選擇 $[officename] 說明的樣式。
33 cui%3APushButton%3AOFA_TP_MISC%3APB_HELPAGENT_RESET 66 按一下 [重設說明代理程式]，以復原顯示「說明代理程式」的預設情形清單。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_EXTHELP 72 當您將游標置於圖示、功能表指令或對話方塊的控制項上時，會顯示簡短的說明文字。
2f cui%3ANumericField%3AOFA_TP_MISC%3ANF_YEARVALUE 3f 定義日期範圍，於其中系統可識別兩位數年份。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_FILEDLG 72 指定是否使用 $[officename] 對話方塊開啟及儲存文件。否則將使用作業系統的對話方塊。
2b cui%3ACheckBox%3AOFA_TP_MISC%3ACB_HELPAGENT 9d 指定在所選情形下，說明代理程式將會自動顯示。按一下 [說明代理程式] 視窗會看到有關於目前上下文資訊的說明頁。
2b cui%3ACheckBox%3AOFA_TP_MISC%3ACB_DOCSTATUS 30 指定是否將列印文件視為一次修改。
29 cui%3ACheckBox%3AOFA_TP_MISC%3ACB_TOOLTIP 7b 顯示圖示名稱和更多泡泡說明資訊，例如，在有許多章節的文件中捲動時，會顯示章節名稱。
35 cui%3AComboBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_FONT2 27 輸入或選取替代字型的名稱。
39 cui%3AListBox%3ARID_SVX_FONT_SUBSTITUTION%3ALB_FONTHEIGHT 3d 選取用於顯示 HTML 和 Basic 原始碼的字型大小。
18 CUI_HID_OFA_SUBST_DELETE 1e 刪除選取的字型替代。
17 CUI_HID_OFA_SUBST_APPLY 1e 套用選取的字型替代。
37 cui%3ACheckBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_NONPROP 4f 核取此選項後，僅顯示 [字型] 清單方塊中不成比例的字型。
1a CUI_HID_OFA_FONT_SUBST_CLB da 列出原始字型與即將加以替代的字型。選取 [自動] 可替代字型，即使系統上已安裝了原始的字型亦然。選取 [僅限螢幕] 可僅替代螢幕字型且一律不會替代列印字型。
1d CUI_HID_OFA_FONT_SUBSTITUTION bd 以您選擇的字型替代某一字型。替代僅會取代顯示在螢幕上的字型，或顯示在螢幕上以及列印的字型。替代不會變更在文件中儲存的字型設定。
37 cui%3AListBox%3ARID_SVX_FONT_SUBSTITUTION%3ALB_FONTNAME 37 選取用於顯示 HTML 和 Basic 原始碼的字型。
35 cui%3AComboBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_FONT1 2a 輸入或選取要替代的字型名稱。
38 cui%3ACheckBox%3ARID_SVX_FONT_SUBSTITUTION%3ACB_USETABLE 27 啟用您定義的字型替代設定。
29 cui%3AListBox%3AOFA_TP_VIEW%3ALB_MOUSEPOS 54 指定是否將滑鼠指標置於新開啟的對話方塊中，以及如何放置。
30 cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_USE_HARDACCELL 54 直接存取圖形顯示配接卡的硬體功能，可改善螢幕顯示的效果。
2b svx%3ACheckBox%3AOFA_TP_VIEW%3ACB_SELECTION 9e 若已啟用，將會使用透明色顯示 Writer 中的文字選取，以及 Calc 中的儲存格選取。若沒有啟用，將會以互補色顯示選取。
19 .uno%3ARestoreEditingView 78 指定是否還原為上次使用的文件檢視。在文件上次儲存時有效的許多檢視特性都將會還原。
24 SVX_LISTBOX_OFA_TP_VIEW_LB_ICONSTYLE 3c 為工具列與對話方塊中的圖示選取圖示樣式。
23 SVX_LISTBOX_OFA_TP_VIEW_LB_ICONSIZE 27 指定工具列圖示的顯示大小。
2c cui%3AListBox%3AOFA_TP_VIEW%3ALB_MOUSEMIDDLE 24 定義滑鼠中間按鈕的功能。
32 cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONTANTIALIASING 30 選取以將文字的螢幕外觀變得平滑。
29 cui%3AListBox%3AOFA_TP_VIEW%3ALB_ICONSIZE 27 指定工具列圖示的顯示大小。
33 cui%3ANumericField%3AOFA_TP_VIEW%3ANF_AA_POINTLIMIT 39 輸入要套用美化字型功能的最小字型大小。
27 SVX_CHECKBOX_OFA_TP_VIEW_CB_SYSTEM_FONT 63 指定使用系統字型以顯示所有功能表和對話方塊。否則使用其他安裝字型。
30 svx%3ACheckBox%3AOFA_TP_VIEW%3ACB_USE_ANTIALIASE b4 您可以啟用與停用圖形的邊緣圓滑設定 (若支援此功能)。啟用邊緣圓滑設定後，大部分的圖形物件看起來會較為平滑，而較少人工痕跡。
2e cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONT_HISTORY 7d 在 [格式] 列上的 [字型名稱] 方塊中，在清單最上方列出您在目前文件中使用的最後五種字型。
2b cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_FONT_SHOW 68 在對應的字型中顯示可選取字型的名稱，例如在 [格式化] 列上的 [字型] 方塊。
2e svx%3AMetricField%3AOFA_TP_VIEW%3AMF_SELECTION 5c 選取透明選取之透明層級。預設值為 75%。您可以選取 10% 到 90% 的值。
12 .uno%3ARefreshView 4a 按 Ctrl+Shift+R 組合鍵可復原或重新整理目前文件的檢視。
2b cui%3AListBox%3AOFA_TP_VIEW%3ALB_MENU_ICONS 90 顯示對應功能表項目旁的圖示。選項包括 [自動]、[隱藏] 及 [顯示]。[自動] 會根據系統設定與主題顯示圖示。
2d cui%3ACheckBox%3AOFA_TP_VIEW%3ACB_SYSTEM_FONT 63 指定使用系統字型以顯示所有功能表和對話方塊。否則使用其他安裝字型。
13 CUI_HID_OFA_TP_VIEW 15 指定檢視選項。
2a cui%3AListBox%3AOFA_TP_VIEW%3ALB_ICONSTYLE 3c 為工具列與對話方塊中的圖示選取圖示樣式。
2f cui%3AMetricField%3AOFA_TP_VIEW%3AMF_WINDOWSIZE 6d 在使用者介面元素 (如對話方塊和圖示標籤) 中使用字型大小的百分位值顯示比例。
40 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_CONVERTTOGREYSCALES 27 指定所有顏色僅以灰階列印。
44 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_NORMAL 61 高列印品質對應於 300dpi 的解析度。一般列印品質對應於 200dpi 的解析度。
39 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_TRANSPARENCY 69 如果您總是希望當文件中包含透明物件時，系統會警告您，請標記此核取方塊。
36 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_PAPERSIZE 57 如果列印目前文件需要使用特定的紙張大小，請標記此核取方塊。
47 sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEBITMAPS_TRANSPARENCY 5d 如果標記此欄位，點陣圖列印品質的降低也會套用到物件的透明區域。
45 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_OPTIMAL 61 高列印品質對應於 300dpi 的解析度。一般列印品質對應於 200dpi 的解析度。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCETRANSPARENCY_NONE 30 使用此選項將永遠不會列印透明度。
3a sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEBITMAPS 51 指定使用降低的品質列印點陣圖。解析度只能減小不能增大。
3f sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_PRINTFILEOUTPUT 45 指定列印設定要套用直接列印還是套用列印成檔案。
3d sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_PRINTEROUTPUT 45 指定列印設定要套用直接列印還是套用列印成檔案。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEGRADIENTS_STRIPES 30 指定列印漸層色圖案條紋的最大數。
3f sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCETRANSPARENCY 87 如果您標記此欄位，透明物件將會如一般、非透明物件列印，視您在下列兩個選項按鈕中的選擇而定。
45 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEGRADIENTS_COLOR 39 指定僅以一種單一中間色列印漸層色圖案。
3d sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_PAPERORIENTATION 57 如果列印目前文件需要使用特定的紙張方向，請標記此核取方塊。
3c sfx2%3ACheckBox%3ATP_COMMONPRINTOPTIONS%3ACB_REDUCEGRADIENTS 4b 如果標記此欄位，將會使用降低的品質列印漸層色圖案。
44 sfx2%3AListBox%3ATP_COMMONPRINTOPTIONS%3ALB_REDUCEBITMAPS_RESOLUTION 53 指定最佳列印品質，以 dpi 為單位。解析度只能減小不能增大。
4a sfx2%3ANumericField%3ATP_COMMONPRINTOPTIONS%3ANF_REDUCEGRADIENTS_STEPCOUNT 30 指定列印漸層色圖案條紋的最大數。
48 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCEBITMAPS_RESOLUTION 53 指定最佳列印品質，以 dpi 為單位。解析度只能減小不能增大。
47 sfx2%3ARadioButton%3ATP_COMMONPRINTOPTIONS%3ARB_REDUCETRANSPARENCY_AUTO 66 指定只有在透明區域所佔面積不超過整個頁面的四分之一時，才列印透明度。
36 cui%3ATimeField%3AOFA_TP_MEMORY%3ATF_GRAPHICOBJECTTIME 51 指定每一個圖形在快取中保存的時間 (以小時和分鐘為單位)。
3a cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_GRAPHICOBJECTCACHE 4b 指定大於所選百萬位元組的物件將不會儲存在此快取內。
2c cui%3ANumericField%3AOFA_TP_MEMORY%3AED_UNDO 48 您可以透過從清單中選取數值，來指定可復原的步數。
30 cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_OLECACHE 35 選擇在快取中保留 OLE 物件的最大數目。
2f cui%3ACheckBox%3AOFA_TP_MEMORY%3ACB_QUICKLAUNCH 81 若要 $[officename] 啟用快速啟動，請標記此核取方塊。只有已安裝快速啟動模組，才可使用此選項。
34 cui%3ANumericField%3AOFA_TP_MEMORY%3ANF_GRAPHICCACHE 27 指定所有圖形的總快取大小。
27 CUI_HID_OPTIONS_COLORCONFIG_NAME_SCHEME 1e 輸入顏色方案的名稱。
3a cui%3APushButton%3ARID_SVXPAGE_COLORCONFIG%3APB_SAVESCHEME 45 將目前的設定儲存為可在以後重新載入的顏色方案。
38 cui%3AListBox%3ARID_SVXPAGE_COLORCONFIG%3ALB_COLORSCHEME 21 選取要使用的顏色方案。
29 CUI_HID_OPTIONS_COLORCONFIG_COLORLIST_WIN 2d 選取用於使用者介面元素的顏色。
3c cui%3APushButton%3ARID_SVXPAGE_COLORCONFIG%3APB_DELETESCHEME 5f 刪除 [顏色方案] 方塊中顯示的顏色方案。您無法刪除 [預設] 顏色方案。
43 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_PAGE_PREVIEWS 39 將作業系統的高對比度設定用於頁面預覽。
3d cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_TIPHELP 30 說明提示停留您輸入的秒數後隱藏。
43 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_TEXTSELECTION 21 在唯讀文件中顯示游標。
48 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ACCESSIBILITY_TOOL bd 允許使用協助工具，如外部螢幕讀取器、盲文裝置或語音識別輸入裝置。只有先在您的電腦上安裝 Java 運行時間環境，您才可以啟用協助支援。
44 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_AUTO_DETECT_HC 53 當系統背景顏色非常暗時，將 %PRODUCTNAME 切換到高對比度模式。
4a cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_AUTOMATIC_FONT_COLOR 5c 使用系統顏色設定顯示 %PRODUCTNAME 中的字型。此選項僅影響螢幕顯示。
41 cui%3ANumericField%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ANF_TIPHELP 24 輸入說明提示顯示的秒數。
47 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ANIMATED_GRAPHICS 3a 在 %PRODUCTNAME 中預覽動畫圖形，如 GIF 影像。
44 cui%3ACheckBox%3ARID_SVXPAGE_ACCESSIBILITYCONFIG%3ACB_ANIMATED_TEXTS 3e 在 %PRODUCTNAME 中預覽動態文字，如閃動和捲動。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTPS_PORT 2a 輸入相應代理伺服器的連接埠。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_NOPROXYFOR 4b 指定不需要任何代理伺服器的伺服器名稱，用分號分隔。
32 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_FTP_PROXY 26 輸入 FTP 代理伺服器的名稱。
34 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTPS_PROXY 49 鍵入 HTTPS 代理伺服器的名稱。在右側欄位鍵入連接埠。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTP_PROXY 27 輸入 HTTP 代理伺服器的名稱。
31 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_FTP_PORT 2a 輸入相應代理伺服器的連接埠。
35 cui%3AListBox%3ARID_SVXPAGE_INET_PROXY%3ALB_PROXYMODE 27 指定代理伺服器定義的類型。
33 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_SOCKS_PORT 2a 輸入相應代理伺服器的連接埠。
32 cui%3AEdit%3ARID_SVXPAGE_INET_PROXY%3AED_HTTP_PORT 2a 輸入相應代理伺服器的連接埠。
15 CUI_HID_OPTIONS_PROXY 27 指定代理伺服器定義的類型。
33 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_ADD 30 按一下此按鈕將新的配置加入清單。
16 CUI_HID_OPTIONS_SEARCH 3d 使用此標籤頁定義在 Internet 中搜尋時的設定。
36 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_CHANGE 33 按一下此按鈕套用對現有配置的修改。
2d cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_URL 66 此文字方塊顯示搜尋機器的 URL 和查詢指令的第一部份。也可以直接輸入 URL。
31 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_POSTFIX 36 後綴提供指令序列，在搜尋的結尾傳送。
34 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_AND 5b 設定進階搜尋喜好設定 (和、或、精確) 以及定義和顯示結果的方式。
33 cui%3AListBox%3ARID_SVXPAGE_INET_SEARCH%3ALB_SEARCH 1e 顯示可用的搜尋引擎。
36 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_DELETE 66 按一下此按鈕從清單刪除選取的搜尋引擎。此時將不會出現任何安全性回應。
33 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_SEPARATOR 6f 如果您在執行搜尋時輸入了一個以上的字詞，這些字詞將會由此處指定的字元隔開。
33 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_OR 5b 設定進階搜尋喜好設定 (和、或、精確) 以及定義和顯示結果的方式。
33 cui%3APushButton%3ARID_SVXPAGE_INET_SEARCH%3APB_NEW 30 使用此按鈕可以加入新的搜尋引擎。
36 cui%3ARadioButton%3ARID_SVXPAGE_INET_SEARCH%3ARB_EXACT 5b 設定進階搜尋喜好設定 (和、或、精確) 以及定義和顯示結果的方式。
35 cui%3AEdit%3ARID_SVXPAGE_INET_SEARCH%3AED_SEARCH_NAME 24 顯示所選搜尋引擎的名稱。
31 cui%3AListBox%3ARID_SVXPAGE_INET_SEARCH%3AED_CASE 2a 確定執行搜尋時要大小寫相符。
31 cui%3AEdit%3ARID_SVXPAGE_INET_MAIL%3AED_MAILERURL 2a 輸入電子郵件程式路徑和名稱。
37 cui%3APushButton%3ARID_SVXPAGE_INET_MAIL%3APB_MAILERURL 42 開啟一個檔案對話方塊，供您選取電子郵件程式。
37 uui%3AEdit%3ADLG_UUI_MASTERPASSWORD%3AED_MASTERPASSWORD 1b 輸入主密碼以繼續。
44 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_MASTERPASSWORD 28 開啟 [輸入主密碼] 對話方塊。
41 cui%3ACheckBox%3ARID_SVXPAGE_INET_SECURITY%3ACB_SEC_SAVEPASSWORDS a5 若啟用，%PRODUCTNAME 會安全儲存從 Web 伺服器存取檔案時所使用的全部密碼。您可以在輸入主密碼後，從清單中擷取這些密碼。
41 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_CONNECTIONS 60 需要主密碼。若主密碼正確，則會顯示 [儲存的 Web 連線資訊] 對話方塊。
3c cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_REMOVEINFO cf 選取此選項會一律將使用者資料從檔案特性中移除。若未選取此選項，您仍然可以使用 [檔案] - [特性] - [一般] 上的 [重設] 按鈕，移除目前文件的個人資訊。
3a cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_SIGNDOCS cd 選取此選項可在您嘗試簽署內含已記錄之變更、版本、欄位、對其他來源 (例如連結的區段或連結的圖片) 的參照或備註的文件時，顯示一個警告對話方塊。
3e cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_MACROSEC 28 開啟 [巨集安全性] 對話方塊。
3f cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_REMOVEALL 21 從清單中移除所有項目。
3c cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_REMOVE 24 從清單中移除選取的項目。
3f cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_CTRLHYPERLINK 87 若啟用，您必須按住 Ctrl 鍵，同時按一下超連結才可進入該連結。若未啟用，按一下即可開啟超連結。
3b cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_PRINTDOCS 75 選取此選項可在您嘗試列印內含已記錄之變更或備註的文件時，顯示一個警告對話方塊。
3e cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_SAVESENDDOCS 87 選取此選項可在您嘗試儲存或傳送內含已記錄之變更、版本或備註的文件時，顯示一個警告對話方塊。
3c cui%3APushButton%3ARID_SVXDLG_WEBCONNECTION_INFO%3APB_CHANGE 4b 開啟對話方塊，您可於此處檢視並變更選取項目的密碼。
3f uui%3AEdit%3ADLG_UUI_MASTERPASSWORD_CRT%3AED_MASTERPASSWORD_CRT 12 輸入主密碼。
3b cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_CREATEPDF a1 選取此選項可在您嘗試將文件匯出成 PDF 格式 (會在 Writer 中顯示已記錄之變更或是顯示備註) 時，顯示一個警告對話方塊。
45 cui%3APushButton%3ARID_SVXPAGE_INET_SECURITY%3APB_SEC_SECURITYOPTIONS 31 開啟 [安全性選項與警告] 對話方塊。
42 uui%3AEdit%3ADLG_UUI_MASTERPASSWORD_CRT%3AED_MASTERPASSWORD_REPEAT 1b 再輸入一次主密碼。
3e cui%3ACheckBox%3ARID_SVXDLG_SECURITY_OPTIONS%3ACB_RECOMMENDPWD 91 選取即可在檔案儲存對話方塊中一律啟用 [使用密碼儲存] 選項。取消選取此選項，預設不使用密碼儲存檔案。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE2 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
33 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_STARBASIC 5a 標記此核取方塊，可在匯出為 HTML 格式時納入 $[officename] Basic 指令。
30 cui%3AListBox%3ARID_OFAPAGE_HTMLOPT%3ALB_CHARSET 24 選取用於匯出的適當字元集
2f cui%3AListBox%3ARID_OFAPAGE_HTMLOPT%3ALB_EXPORT 2a 定義有關匯出 HTML 文件的設定。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE5 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE3 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
33 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_LOCAL_GRF e1 標記此核取方塊，可在使用 FTP 進行上傳時自動將內嵌圖片上傳至網際網路伺服器。使用 [另存新檔] 對話方塊儲存文件，並輸入完整的 FTP URL 作為網際網路中的檔案名稱。
39 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_PRINT_EXTENSION 55 If you mark this field, the print layout of the current document is exported as well.
3b cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_STARBASIC_WARNING 7d 如果已經標記此欄位，則在匯出為 HTML 時將顯示一則警告，指出將會遺失 %PRODUCTNAME Basic 巨集。
36 cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_UNKNOWN_TAGS 63 如果希望將 $[officename] 無法識別的標記作為欄位匯入，請標記此核取方塊。
3c cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_NUMBERS_ENGLISH_US cc 若未核取，即會依據 [選項] 對話方塊中的 [語言設定] - [語言] - [語言環境設定] 設定來解譯數字。若加以核取，則會以「英文 (美國)」語言環境解譯數字。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE1 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE7 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
3a cui%3ACheckBox%3ARID_OFAPAGE_HTMLOPT%3ACB_IGNORE_FONTNAMES 6c 標記此核取方塊忽略匯入時的所有字型設定。將使用 HTML 頁面樣式中定義的字型。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE6 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
33 cui%3ANumericField%3ARID_OFAPAGE_HTMLOPT%3ANF_SIZE4 75 使用微調按鈕 Size 1 到 Size 7 為 HTML <font size=1> 到 <font size=7> 標記定義其各自的字型大小。
14 .uno%3ASwEditOptions 78 這些設定確定如何處理 $[officename] 中所建立的文字文件。還可以定義目前文字文件的設定。
2d sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_ANY_RULER 4b 啟用標尺。使用下列兩個核取方塊來選取要顯示的標尺。
27 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_GRF 30 指定是否在螢幕上顯示圖形和物件。
31 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_SMOOTH_SCROLL 21 啟動平滑頁面捲動功能。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HRULER 48 顯示水平標尺。從對應的清單中選取所需的定量單位。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HANDLE 59 將控點 (分割矩形上的八個點) 顯示為不具有 3D 效果的簡單正方形。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_POSTIT d9 顯示備註。按一下備註可編輯文字。使用 [助手] 中的右鍵功能表，可尋找或刪除備註。使用備註的右鍵功能表，可刪除此備註或所有備註，或是此作者的所有備註。
27 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_TBL 24 顯示包含在文件中的表格。
30 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VRULER_RIGHT 24 將垂直標尺與右邊框對齊。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_HSCROLL 15 顯示水平捲軸。
29 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_FIELD 33 在文件中顯示欄位名稱而非欄位內容。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_DRWFAST 30 顯示包含在文件中的繪圖和控制項。
16 .uno%3ATableBoundaries 9c 若要顯示表格邊界，請在任何表格上按一下右鍵，然後選擇 [表格邊界]，或選擇 Writer 文件中的[表格] - [隱藏網格線]。
2a sw%3AListBox%3ATP_CONTENT_OPT%3ALB_HMETRIC 48 顯示水平標尺。從對應的清單中選取所需的定量單位。
29 sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_CROSS 94 移動框架時顯示框架周圍的輔助線。您可以選取 [移動時顯示輔助線] 選項以顯示使用線性值的物件的精確位置。
29 sw%3AListBox%3ATP_CONTENT_OPT%3ALB_METRIC 1e 指定 HTML 文件的單位。
2a sw%3AListBox%3ATP_CONTENT_OPT%3ALB_VMETRIC 48 顯示垂直標尺。從對應的清單中選取所需的定量單位。
2a sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VRULER 48 顯示垂直標尺。從對應的清單中選取所需的定量單位。
2d sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_BIGHANDLE 36 顯示放大的控點 (分割矩形上的八個點)。
2b sw%3ACheckBox%3ATP_CONTENT_OPT%3ACB_VSCROLL 15 顯示垂直捲軸。
26 sw%3AComboBox%3ATP_STD_FONT%3ALB_LABEL 2d 指定用於影像和表格標籤的字型。
25 sw%3AComboBox%3ATP_STD_FONT%3ALB_LIST 3c 指定用於清單、編號和所有導出樣式的字型。
26 sw%3AComboBox%3ATP_STD_FONT%3ALB_TITLE 21 指定要用於標頭的字型。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_INDEX_SIZE 15 指定字型大小。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_LABEL_SIZE 15 指定字型大小。
24 sw%3AComboBox%3ATP_STD_FONT%3ALB_IDX 36 指定用於索引、字母索引及目錄的字型。
29 sw%3AComboBox%3ATP_STD_FONT%3ALB_STANDARD 36 指定要用於 [預設]「段落樣式」的字型。
f SW_HID_STD_FONT 2d 指定用於文件中基本字型的設定。
28 sw%3ACheckBox%3ATP_STD_FONT%3ACB_DOCONLY 2a 指定設定僅適用於目前的文件。
2b sw%3AMetricBox%3ATP_STD_FONT%3ALB_LIST_SIZE 15 指定字型大小。
2f sw%3AMetricBox%3ATP_STD_FONT%3ALB_STANDARD_SIZE 15 指定字型大小。
2c sw%3AMetricBox%3ATP_STD_FONT%3ALB_TITLE_SIZE 15 指定字型大小。
2e sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PROSPECT 45 Select the Brochure option to print your document in brochure format.
2b sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_LEFTP 4a Specifies whether to print all left (even numbered) pages of the document.
38 .HelpID%3Avcl%3APrintDialog%3APrintHiddenText%3ACheckBox 33 啟用此選項可列印標示為隱藏的文字。
36 .HelpId%3Avcl%3APrintDialog%3APrintControls%3ACheckBox 4b Specifies whether the form control fields of the text document are printed.
3b .HelpID%3Avcl%3APrintDialog%3APrintAnnotationMode%3AListBox 2a 指定是否要列印文件中的備註。
2d sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_CTRLFLD 4b Specifies whether the form control fields of the text document are printed.
14 SW_HID_OPTPRINT_PAGE 36 指定文字文件或 HTML 文件中的列印設定。
2c sw_CheckBox_TP_OPTPRINT_PAGE_CB_PROSPECT_RTL 54 核取可使用從右至左程序檔的正確順序，來列印小手冊的頁面。
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_BLACK_FONT 30 Specifies whether to always print text in black.
28 SW%3ALISTBOX%3ATP_OPTPRINT_PAGE%3ALB_FAX 7b 如果您已在電腦上安裝了傳真軟體並希望直接從文字文件發送傳真，請選取要使用的傳真機。
2d sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_REVERSE 72 Specifies whether to reverse the printing order. The last page of the document will then be the first one printed.
3c .HelpId%3Avcl%3APrintDialog%3APrintPageBackground%3ACheckBox 75 指定是否要在列印的文件中包含插入頁面背景內的顏色和物件 ([格式] - [頁面] - [背景])。
40 .HelpId%3Avcl%3APrintDialog%3APrintPicturesAndObjects%3ACheckBox 41 Specifies whether the graphics of your text document are printed.
2a sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PGRF 41 Specifies whether the graphics of your text document are printed.
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_SINGLEJOBS 62 Specifies that each new print job will begin on a new page even if you are using a duplex printer.
38 .HelpID%3Avcl%3APrintDialog%3APrintEmptyPages%3ACheckBox 133 若啟用此選項，便會列印自動插入的空白頁。這最適用於雙面列印。例如在書籍中，「章節」段落樣式已設為一律以奇數頁起始。如果上一章結束於奇數頁，%PRODUCTNAME 會插入一頁編號偶數的空白頁。此選項控制是否要列印該偶數頁。
2b SW%3ARADIOBUTTON%3ATP_OPTPRINT_PAGE%3ARB_NO 2a 指定是否要列印文件中的備註。
2c sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_RIGHTP 4a Specifies whether to print all right (odd numbered) pages of the document.
30 sw_CheckBox_TP_OPTPRINT_PAGE_CB_TEXT_PLACEHOLDER 72 啟用此選項可列印文字預留位置。停用此選項可在列印輸出中保留文字預留位置空白。
38 .HelpId%3Avcl%3APrintDialog%3APrintBlackFonts%3ACheckBox 30 Specifies whether to always print text in black.
30 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_BACKGROUND 75 指定是否要在列印的文件中包含插入頁面背景內的顏色和物件 ([格式] - [頁面] - [背景])。
34 sw%3ACheckBox%3ATP_OPTPRINT_PAGE%3ACB_PAPERFROMSETUP a9 For printers with multiple trays, the "Paper tray from printer settings" option specifies whether the paper tray used is specified by the system settings of the printer.
2f sw_CheckBox_TP_OPTPRINT_PAGE_CB_PRINTEMPTYPAGES 133 若啟用此選項，便會列印自動插入的空白頁。這最適用於雙面列印。例如在書籍中，「章節」段落樣式已設為一律以奇數頁起始。如果上一章結束於奇數頁，%PRODUCTNAME 會插入一頁編號偶數的空白頁。此選項控制是否要列印該偶數頁。
3d .HelpId%3Avcl%3APrintDialog%3APrintTextPlaceholder%3ACheckBox 72 啟用此選項可列印文字預留位置。停用此選項可在列印輸出中保留文字預留位置空白。
2b sw_CheckBox_TP_OPTPRINT_PAGE_CB_HIDDEN_TEXT 33 啟用此選項可列印標示為隱藏的文字。
14 SW_HID_OPTTABLE_PAGE 27 定義文字文件中表格的屬性。
1d .uno%3ATableNumberRecognition 45 指定文字表格中的數字識別為數字並格式化為數字。
30 sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_FIXPROP 30 指定列或欄的變更會影響整個表格。
30 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_COLMOVE 24 指定移動欄時要使用的值。
32 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMALIGNMENT 36 指定數字在儲存格中總是向右下方對齊。
2c sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_BORDER 2d 指定表格儲存格依預設具有邊框。
30 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_DONT_SPLIT 39 指定表格不被任何類型的換行和分頁分割。
2c sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_HEADER 45 指定表格首列以「表格標題」段落樣式進行格式化。
32 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_COLINSERT 2a 指定插入欄時要使用的預設值。
32 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_ROWINSERT 2a 指定插入列時要使用的預設值。
30 sw%3AMetricField%3ATP_OPTTABLE_PAGE%3AMF_ROWMOVE 24 指定移動列時要使用的值。
33 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMFORMATTING 45 指定文字表格中的數字識別為數字並格式化為數字。
2c sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_FIX 3c 指定列或欄的變更僅影響其對應的相鄰區域。
33 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_REPEAT_HEADER 42 指定是否在分頁後的新頁上繼續套用該表格標題。
2c sw%3ARadioButton%3ATP_OPTTABLE_PAGE%3ARB_VAR 30 指定列或欄的變更會影響表格大小。
37 sw%3ACheckBox%3ATP_OPTTABLE_PAGE%3ACB_NUMFMT_FORMATTING 87 如果未標記 [識別數字格式]，則僅接受儲存格中設定格式的輸入。任何其他輸入會將格式重設為文字。
38 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLINDENT 81 使用直接定位游標時，段落的左縮排設定在您按一下直接定位游標的水平位置處。段落向左對齊。
2e sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_FLD_HIDDEN 4a 顯示由 [條件文字] 或 [隱藏的文字] 欄位所隱藏的文字。
31 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_ALLOW_IN_PROT 4b 指定可以在受保護區域設定游標，但無法進行任何變更。
12 SW_HID_OPTSHDWCRSR 6c 在 $[officename] 文字文件和 HTML 文件中，定義特定字元和直接定位游標的顯示設定。
2f sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_CHAR_HIDDEN 61 當選取圖示非列印字元開啟/關閉時，顯示使用「隱藏」字元格式的文字。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SHYPH 13d 指定是否顯示使用者自訂分隔符。即透過按 指令+連字符 (-) Ctrl+連字符 (-) 組合鍵在字詞內輸入的隱藏分隔符。如果字詞中帶有使用者自訂分隔符，則該字詞只在行尾插入了使用者自訂分隔符的位置進行分隔，而與是否啟動自動連字符無關。
33 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_FLD_HIDDEN_PARA 5e 如果使用 [隱藏的段落] 欄位插入了文字，請指定是否顯示隱藏的段落。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_BREAK 7c 顯示透過 Shift+Enter 組合鍵插入的所有換行。這些換行可以建立新的行，但不會開始新的段落。
28 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_PARA 53 指定是否顯示段落分隔符。段莀½分隔符還包含段落格式資訊。
30 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SHDWCRSONOFF 1b 啟用直接定位游標。
37 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLSPACE 7b 使用直接定位游標時，將在新段落中插入對應數量的定位鍵和空格，直至到達按一下的位置。
35 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLTAB 69 使用直接定位游標時，新段落中將會加入足夠多的定位鍵，直至到達所按位置。
29 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_SPACE 3c 指定是否使用點來表示文字中的每一個空格。
38 sw%3ARadioButton%3ATP_OPTSHDWCRSR%3ARB_SHDWCRSFILLMARGIN f3 設定使用直接定位游標時的段落對齊。段落將套用左對齊、置中或右對齊的格式，具體的對齊方式取決於滑鼠所按的位置。按一下滑鼠之前，游標透過一個三角形顯示設定的對齊方式。
27 sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_TAB 24 指定定位符顯示為小箭頭。
2a sw%3ACheckBox%3ATP_OPTSHDWCRSR%3ACB_HSPACE bd 指定不斷行空格字元顯示為灰色方塊。不斷行空格字元不會在行尾中斷，可使用 Command+Shift+空格鍵Ctrl+Shift+空格鍵 捷徑鍵輸入不斷行空格字元。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_CHG_ATTR 72 定義在文件中如何顯示對文字屬性的變更。這些變更會影響粗體、斜體或底線等屬性。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_DEL_ATTR a0 指定在刪除文字時如何顯示文件中的變更。如果記錄了文字刪除，則文字會以選取的屬性 (如刪除線) 顯示，而不會刪除。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_MARKPOS 3f 定義是否要標記變更的行及其在文件中的位置。
29 sw%3AListBox%3ATP_REDLINE_OPT%3ALB_LC_COL 3c 指定反白顯示文字中已變更行所使用的顏色。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_DEL_COL cc 您也可以選擇要顯示每種記錄變更類型的顏色。當您在清單中選擇條件「依作者設定」時，顏色會自動由 $[officename] 決定，再修改為符合每項變更的作者。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_INS_COL cc 您也可以選擇要顯示每種記錄變更類型的顏色。當您在清單中選擇條件「依作者設定」時，顏色會自動由 $[officename] 決定，再修改為符合每項變更的作者。
2b sw%3AListBox%3ATP_REDLINE_OPT%3ALB_INS_ATTR 39 指定在插入文字時如何顯示文件中的變更。
2a sw%3AListBox%3ATP_REDLINE_OPT%3ALB_CHG_COL cc 您也可以選擇要顯示每種記錄變更類型的顏色。當您在清單中選擇條件「依作者設定」時，顏色會自動由 $[officename] 決定，再修改為符合每項變更的作者。
12 SW_HID_REDLINE_OPT 27 定義如何顯示文件中的變更。
2d sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_NEVER 24 載入文件時永不更新連結。
2f sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_NEVER 24 載入文件時永不更新連結。
2f sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_REQUEST 36 載入文件時，需經請求後才可更新連結。
31 sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_REQUEST 36 載入文件時，需經請求後才可更新連結。
30 sc%3ARadioButton%3ARID_SCPAGE_LAYOUT%3ARB_ALWAYS 24 載入文件時總是更新連結。
37 sw%3ACheckBox%3ATP_OPTLOAD_PAGE%3ACB_AUTO_UPDATE_FIELDS 9f 當畫面內容更新顯示時，所有欄位的內容會自動更新。即使取消核取此方塊，每次執行特殊條件時仍然會更新某些欄位。
37 sw%3ACheckBox%3ATP_OPTLOAD_PAGE%3ACB_AUTO_UPDATE_CHARTS 95 指定是否自動更新圖表。當 Writer 表格儲存格值變更且游標離開該儲存格時，便會自動更新顯示儲存格值的圖表。
13 SW_HID_OPTLOAD_PAGE 24 指定文字文件的一般設定。
2b sw%3AMetricField%3ATP_OPTLOAD_PAGE%3AMF_TAB 27 指定各個定位鍵之間的間隔。
2a sw%3AListBox%3ATP_OPTLOAD_PAGE%3ALB_METRIC 24 指定文字文件的定量單位。
2e sw%3ARadioButton%3ATP_OPTLOAD_PAGE%3ARB_ALWAYS 24 載入文件時總是更新連結。
37 sw%3APushButton%3ATP_OPTCOMPATIBILITY_PAGE%3APB_DEFAULT 68 按一下以使用此標籤頁的設定值作為使用 %PRODUCTNAME 的進一步階段作業預設值。
1c SW_HID_OPTCOMPATIBILITY_PAGE 77 指定文字文件的相容性設定。這些選項將協助您在匯入 Microsoft Word 文件時微調 %PRODUCTNAME。
38 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACB_USE_PRINTER_METRICS 51 指定列印以及格式化螢幕上的顯示所套用的印表機字型尺度。
30 sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ALB_CHARSTYLE 15 指定字元樣式。
2c sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ALB_LEVEL 33 在開始編號的位置指定標頭或章節級。
29 sw%3AEdit%3ATP_OPTCAPTION_PAGE%3AEDT_TEXT 3c 定義標題或章節級的編號後面要顯示的字元。
15 SW_HID_OPTCAPTION_CLB 3c 選取這些自動標籤設定將會有效的物件類型。
2b sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ABOX_POS 27 確定標籤相對於物件的位置。
31 sw%3AComboBox%3ATP_OPTCAPTION_PAGE%3ABOX_CATEGORY 1e 指定所選物件的分類。
33 sw%3ACheckBox%3ATP_OPTCAPTION_PAGE%3ACB_APPLYBORDER 33 將物件的邊框和陰影套用至標籤訊框。
2d sw%3AEdit%3ATP_OPTCAPTION_PAGE%3AED_SEPARATOR 3c 定義標題或章節級的編號後面要顯示的字元。
2e sw%3AListBox%3ATP_OPTCAPTION_PAGE%3ABOX_FORMAT 1e 指定所需的編號類型。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SYNCHRONIZE 33 指定是否對稱地變更目前的網格設定。
34 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_GRID_VISIBLE 1b 指定是否顯示網格。
12 .uno%3AGridVisible 1b 指定是否顯示網格。
14 SVX_HID_OPTIONS_GRID a5 指定文件頁面上可調整網格的設定。此網格有助於您確定物件的精確位置。還可以使此網格與「有磁性的」座標網格相一致。
3a svx%3ANumericField%3ARID_SVXPAGE_GRID%3ANUM_FLD_DIVISION_X 4c Specify the number of intermediate spaces between grid points on the X-axis.
35 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_DRAW_Y 3f 定義在 Y 軸上需要的定量單位中網格點的間隔。
15 .uno%3AHelplinesFront 27 在所有物件上層設定座標線。
10 .uno%3AGridFront 30 在所有物件上層設定看得見的網格。
35 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_DRAW_X 3c 定義在 X 軸上的網格點之間間隔的定量單位。
3a svx%3ANumericField%3ARID_SVXPAGE_GRID%3ANUM_FLD_DIVISION_Y 4c Specify the number of intermediate spaces between grid points on the Y-axis.
34 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_USE_GRIDSNAP 5a Specifies whether to move frames, drawing elements, and controls only between grid points.
13 .uno%3AToolsOptions d5 定義試算表的各種設定，要顯示的內容、輸入儲存格條目後游標的移動方向。還可以定義排序清單、確定小數位數，並確定記錄變更與反白顯示變更所需的設定。
14 .uno%3AScEditOptions d5 定義試算表的各種設定，要顯示的內容、輸入儲存格條目後游標的移動方向。還可以定義排序清單、確定小數位數，並確定記錄變更與反白顯示變更所需的設定。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_HSCROLL 39 指定是否要顯示文件視窗底部的水平捲軸。
12 .uno%3ANoteVisible 5e 若要永久顯示備註，請在該儲存格的右鍵功能表選取 [顯示備註] 指令。
32 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_PAGEBREAKS 36 指定是否在定義的列印範圍內檢視分頁。
2e sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ANCHOR 49 指定選取插入的物件 (例如圖形) 時是否顯示標號圖示。
2b sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_NIL 1b 指定是否顯示零值。
15 SC_HID_SCPAGE_CONTENT 75 定義顯示%PRODUCTNAME Calc 主視窗的哪些元素。您也可以顯示或隱藏表格中的反白顯示數值。
2c sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_CLIP e7 如果儲存格包含較儲存格寬度寬的文字，這些文字會蓋過同一列的相鄰空白儲存格而顯示。如果沒有空白的相鄰儲存格，則儲存格邊框會出現一個小三角形，指出文字未結束。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ANNOT a3 指定以儲存格右上角的小矩形表示存在備註。您必須啟用 [選項] 對話方塊中 [%PRODUCTNAME] - [一般] 下的提示，才會顯示備註。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_RFIND a5 請指定公式中的每個參照均用顏色反白顯示。一旦選取了包含參照的儲存格用於編輯，儲存格範圍將被帶有顏色的邊框圍住。
2b sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_DRAW 36 定義是否顯示或隱藏文件中的繪圖物件。
2d sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_VALUE d0 標記 [反白顯示數值] 方塊，依類型顯示不同顏色的儲存格內容。文字儲存格會格式化為黑色，公式會為綠色，而數字儲存格則為藍色，無論其顯示格式為何。
32 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_BIGHANDLES 49 指定顯示的控點比一般控點 (選取方塊上的八個點) 大。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_HANDLES 62 指定是否將控點 (選取方塊上的八個點) 顯示為沒有 3D 效果的簡單正方形。
30 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_SYNCZOOM 87 若核取此選項，則所有試算表將以相同的縮放係數顯示。若未核取，則每個試算表有各自的縮放係數。
2d sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_OBJGRF 2d 定義是否顯示或隱藏物件和圖形。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_OUTLINE 6f 如果您已經定義了大綱，[大綱圖示] 選項將指定是否在試算表邊框處檢視大綱圖示。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_VSCROLL 39 指定是否要顯示文件視窗右側的垂直捲軸。
34 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_ROWCOLHEADER 2a 指定是否顯示列標題和欄標題。
2e sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_DIAGRAM 30 定義是否顯示或隱藏文件中的圖表。
2f sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_FORMULA 36 指定儲存格中是顯示公式還是顯示結果。
2c sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_GRID 30 指定是否顯示儲存格之間的網格線。
2e sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_TBLREG 3f 指定是否在試算表文件的底部顯示試算表分頁。
31 sc%3ACheckBox%3ARID_SCPAGE_CONTENT%3ACB_GUIDELINE 51 指定移動繪圖、訊框、圖形和其他物件時是否要檢視輔助線。
2c sc%3AListBox%3ARID_SCPAGE_CONTENT%3ALB_COLOR 2a 指定目前文件中的網格線顏色。
2b sc%3AListBox%3ARID_SCPAGE_LAYOUT%3ALB_ALIGN 3d 確定按 Enter 鍵後游標在試算表中的移動方向。
2f sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_REPLWARN 5a 請指定當您將儲存格從剪貼簿貼至非空白的儲存格範圍時顯示警告。
2d sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_FORMAT 5a 指定是否將選取的儲存格之格式屬性自動套用至相鄰的空白儲存格。
2d sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_EXPREF ba 指定在參照範圍的相鄰位置插入欄或列時，是否擴充參照。要擴充參照，則插入欄或列的參照範圍原來在所需的方向上至少包含兩個儲存格。
13 .uno%3ASetInputMode 3d 允許您在按 Enter 鍵後立即編輯選取的儲存格。
2a sc%3AListBox%3ARID_SCPAGE_LAYOUT%3ALB_UNIT 24 定義試算表中的定量單位。
2e sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_TEXTFMT 51 指定列印以及格式化螢幕上的顯示所套用的印表機字型尺度。
2d sc%3AMetricField%3ARID_SCPAGE_LAYOUT%3AMF_TAB 18 定義定位鍵間隔。
2e sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_MARKHDR 4b 指定是否要反白顯示所選欄或列之中的欄標題和列標題。
2f sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_EDITMODE 3d 允許您在按 Enter 鍵後立即編輯選取的儲存格。
2c sc%3ACheckBox%3ARID_SCPAGE_LAYOUT%3ACB_ALIGN 3d 確定按 Enter 鍵後游標在試算表中的移動方向。
2e sc%3AListBox%3ARID_SCPAGE_USERLISTS%3ALB_LISTS 48 顯示所有可用的清單。可以選取這些清單以進行編輯。
17 SC_HID_SCPAGE_USERLISTS a8 [排序清單] 對話方塊中顯示所有的使用者自訂清單。您也可以定義和編輯自己的清單。排序清單只能使用文字，不能使用數字。
30 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_ADD 2b 在 [清單] 方塊中加入新的清單。
30 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_NEW 31 在 [條目] 方塊中輸入新清單的內容。
36 sc%3AMultiLineEdit%3ARID_SCPAGE_USERLISTS%3AED_ENTRIES 3c 顯示目前所選清單的內容。可以編輯此內容。
31 sc%3APushButton%3ARID_SCPAGE_USERLISTS%3ABTN_COPY 109 在[複製清單來源] 方塊中複製儲存格的內容。如果您選擇參照到相關的欄列，則在按下按鈕後，螢幕上會顯示 [複製清單] 對話方塊。您可以使用此對話方塊定義參照是否要依據欄或列轉換成排序清單。
2e sc%3AEdit%3ARID_SCPAGE_USERLISTS%3AED_COPYFROM 82 定義要複製的試算表和儲存格，以將其包含在 [清單] 方塊中。試算表中目前的所選範圍是預設值。
36 sc%3ARadioButton%3ARID_SCDLG_COLORROW%3ABTN_GROUP_COLS 43 選取 [欄] 選項，將所選欄的內容匯總成一個清單。
36 sc%3ARadioButton%3ARID_SCDLG_COLORROW%3ABTN_GROUP_ROWS 43 選取 [列] 選項，將所選列的內容匯總成一個清單。
25 sc%3AModalDialog%3ARID_SCDLG_COLORROW 39 允許您將標記的儲存格複製到排序清單中。
12 SC_HID_SCPAGE_CALC 32 Defines the calculation settings for spreadsheets.
2a sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_CASE 4b 指定在比較儲存格內容時是否區分文字中字母的大小寫。
31 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATESC10 6f 將 1/1/1900 設定為初始日期。對於含有日期條目的 StarCalc 1.0 試算表，請使用此設定。
30 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATESTD 27 將 12/30/1899 設定為初始日期。
24 sc%3AEdit%3ARID_SCPAGE_CALC%3AED_EPS 72 指定兩個連續循環步驟結果的差值。如果循環的結果小於最小變更值，則循環將停止。
2c sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_LOOKUP b1 指定您可以使用任意儲存格中的文字作為該文字下方的欄或文字右側列的標籤。該文字至少要含有一個字，並且不能包含任何運算子。
2a sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_CALC 10c 指定是否使用試算表中所顯示的四捨五入值進行計算。顯示圖表時將採用顯示的數值。如果未標記 [顯示的精確度] 選項，則以四捨五入的數值顯示數字，但在進行內部計算時會使用未經四捨五入的數字。
31 sc%3ARadioButton%3ARID_SCPAGE_CALC%3ABTN_DATE1904 64 將 1/1/1904 設定為初始日期。對於以外來格式匯入的試算表，請使用此設定。
32 sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_GENERAL_PREC dd You can specify the maximum number of decimal places that are shown by default for cells with General number format. If not enabled, cells with General number format show as many decimal places as the column width allows.
2b sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_MATCH 112 指定您為 Calc 資料庫功能所設定的搜尋條件必須完全符合整個儲存格。若標記了 [搜尋範圍 = 和 <> 必須適用全部的儲存格] 方塊，$[officename] Calc 在資料庫功能中搜尋儲存格時的運作方式會與 MS Excel 完全相同。
2d sc%3ANumericField%3ARID_SCPAGE_CALC%3AED_PREC dd You can specify the maximum number of decimal places that are shown by default for cells with General number format. If not enabled, cells with General number format show as many decimal places as the column width allows.
2b sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_REGEX 39 指定在搜尋及字串比較時啟用常規表達式。
2e sc%3ANumericField%3ARID_SCPAGE_CALC%3AED_STEPS 1b 設定最大迭代步數。
2d sc%3ACheckBox%3ARID_SCPAGE_CALC%3ABTN_ITERATE 99 指定帶有反覆式參照的公式 (即需要不斷重複計算才能得到結果的公式)，在進行了特定次數的重複計算後是否繼續。
30 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_INSERT 33 指定反白顯示文件中插入部份的顏色。
31 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_CONTENT 27 指定儲存格內容變更的顏色。
17 SC_HID_SCPAGE_OPREDLINE 6a 在 [變更] 對話方塊中，可以指定反白顯示文件中所記錄的變更所需的各種選項。
30 sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_REMOVE 33 指定反白顯示文件中刪除部份的顏色。
2e sc%3AListBox%3ARID_SCPAGE_OPREDLINE%3ACLB_MOVE 36 指定反白顯示移動的儲存格內容的顏色。
35 sc%3ACheckBox%3ARID_SCPAGE_PRINT%3ABTN_SELECTEDSHEETS ca 指定僅列印所選試算表的內容，即使在 [檔案] – [列印] 對話方塊或 [格式] – [列印範圍] 對話方塊中指定了更大的範圍。不列印未選取之試算表的內容。
35 sc%3ACheckBox%3ARID_SCPAGE_PRINT%3ABTN_SKIPEMPTYPAGES 48 指定不列印未包含儲存格內容或繪圖物件的空白頁面。
14 .uno%3ASdEditOptions 8a 為新增的簡報文件定義各種設定，例如要顯示的內容、使用的定量單位、是否以及如何執行網格對齊等。
36 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_MOVE_OUTLINE 4c %PRODUCTNAME 在移動每一個物件時，會顯示此物件的輪廓線。
33 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_HELPLINES 2d 指定移動物件時是否顯示輔助線。
16 SD_HID_SD_OPTIONS_VIEW 1e 指定可用的顯示模式。
2f sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_RULER 3c 指定是否在工作區域的頂端和左側顯示標尺。
38 sd%3ACheckBox%3ATP_OPTIONS_CONTENTS%3ACBX_HANDLES_BEZIER cd 如果先前選取了貝茲曲線，則顯示所有貝茲曲線接點的控制點。如果不標記 [貝茲曲線編輯程式內的全部控點] 選項，則僅顯示所選貝茲曲線接點的控制點。
1a SD_HID_SD_OPTIONS_CONTENTS 1e 指定可用的顯示模式。
14 .uno%3AHelplinesMove 2d 指定移動物件時是否顯示輔助線。
38 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_BEZ_ANGLE 1e 定義減少點數的角度。
38 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_SNAP_AREA b6 定義滑鼠指標和物件輪廓之間的對齊間隔。若滑鼠指標比在 [對齊範圍] 控制項中所選取的間隔還要近時，$[officename] Impress 會對齊座標點。
11 .uno%3ASnapBorder 45 指定是否將圖形物件的輪廓與最近的頁面邊距對齊。
16 SD_HID_SD_OPTIONS_SNAP 33 定義建立和移動物件所需的網格設定。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_BORDER 45 指定是否將圖形物件的輪廓與最近的頁面邊距對齊。
36 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_HELPLINES 48 釋放滑鼠時將所拖曳物件的邊緣與最近的座標線對齊。
34 svx%3AMetricField%3ARID_SVXPAGE_GRID%3AMTR_FLD_ANGLE 61 指定圖形物件僅能在 [旋轉時] 控制項中選取的旋轉角度範圍內進行旋轉。
33 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_POINTS 4b 指定是否將圖形物件的輪廓與最近的圖形物件各點對齊。
10 .uno%3ASnapFrame 4b 指定是否將圖形物件的輪廓與最近的圖形物件邊框對齊。
2e svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_ROTATE 61 指定圖形物件僅能在 [旋轉時] 控制項中選取的旋轉角度範圍內進行旋轉。
13 .uno%3AHelplinesUse 48 釋放滑鼠時將所拖曳物件的邊緣與最近的座標線對齊。
2d svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_ORTHO 6f 指定在建立或移動圖形物件時套用的限定範圍：垂直方向、水平方向或對角 (45 度)。
11 .uno%3ASnapPoints 4b 指定是否將圖形物件的輪廓與最近的圖形物件各點對齊。
32 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_SNAP_FRAME 4b 指定是否將圖形物件的輪廓與最近的圖形物件邊框對齊。
30 svx%3ACheckBox%3ARID_SVXPAGE_GRID%3ACBX_BIGORTHO 10a 指定在按 Shift 鍵時 (不釋放滑鼠按鈕)，基於矩形較長的邊建立一個正方形。這也適用於橢圓 (基於橢圓最長的直徑建立一個圓)。如果未標記 [較長的邊] 方塊，則會基於較短的邊或直徑建立正方形或圓。
32 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_PAGETILE 7e 指定以平鋪格式列印頁面。如果頁面或投影片比紙張小，將在一頁紙上列印多個頁面或投影片。
2b SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_DATE 21 指定是否列印目前日期。
31 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_DEFAULT 36 指定列印時不需要進一步調整頁面比例。
33 SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_HIDDEN_PAGES 33 指定是否列印簡報中目前隱藏的頁面。
17 SD_HID_SD_PRINT_OPTIONS 36 指定繪圖文件或簡報文件中的列印設定。
32 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_PAGESIZE 66 指定是否按比例縮小超出目前印表機邊距的物件，使其適合印表機中的紙張。
33 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_GRAYSCALE 2a 指定是否將彩色作為灰階列印。
2b sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_BACK 2b 選取 [背面] 列印小手冊的背面。
31 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_BOOKLET 3d 選取 [小手冊] 選項，以小手冊格式列印文件。
2f sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_PAPERBIN 45 確定使用的送紙匣是在印表機設定中定義的送紙匣。
2c sd%3ACheckBox%3ATP_PRINT_OPTIONS%3ACBX_FRONT 28 選取 [正面] 列印小手冊正面。
34 sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_BLACKWHITE 21 指定要以黑白列印文件。
2b SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_TIME 24 指定是否列印目前的時間。
2f sd%3ARadioButton%3ATP_PRINT_OPTIONS%3ARBT_COLOR 27 指定是否以原來的顏色列印。
2f SD%3ACHECKBOX%3ATP_PRINT_OPTIONS%3ACBX_PAGENAME 21 指定是否列印頁面名稱。
34 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACB_MERGE_PARA_DIST 60 指定 $[officename] Impress 計算段落間隔的方法與 Microsoft PowerPoint 完全相同。
41 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_PRESENTER_SCREEN 79 Specifies that you always want the Presenter Screen to start together with the presentation, in a multiple-monitor setup.
39 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_CROOK_NO_CONTORTION 5d 扭曲此物件時，保持貝茲曲線點和平面繪圖物件彼此之間的相對對齊。
2a sd%3AComboBox%3ATP_OPTIONS_MISC%3ACB_SCALE 27 在標尺上確定繪圖顯示比例。
36 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_MASTERPAGE_CACHE 3f 指定為了顯示主頁上的物件，是否要使用快取。
12 .uno%3APickThrough 39 指定是否透過按一下文字來選取文字框架。
39 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_TEMPLATE 59 指定透過 [檔案] - [開啟新檔] - [簡報] 來開啟簡報時是否啟動精靈。
31 sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_PICKTHROUGH 39 指定是否透過按一下文字來選取文字框架。
2f sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_QUICKEDIT 6e If on, you can edit text immediately after clicking a text object. If off, you must double-click to edit text.
2a sd%3AListBox%3ATP_OPTIONS_MISC%3ALB_METRIC 1e 確定簡報的定量單位。
3d sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_MARKED_HIT_MOVES_ALWAYS 95 指定要在 [旋轉] 工具啟用的情況下移動物件。如果未標記 [物件可自動移動]，則 [旋轉] 工具僅可用於旋轉物件。
16 SD_HID_SD_OPTIONS_MISC 33 定義繪圖文件或簡報文件的一般選項。
3c sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_START_WITH_ACTUAL_PAGE 36 指定始終從目前的投影片開始放映簡報。
2a sd%3ACheckBox%3ATP_OPTIONS_MISC%3ACBX_COPY 69 If enabled, a copy is created when you move an object while holding down the Ctrl key (Mac: Command key).
10 .uno%3AQuickEdit 6e If on, you can edit text immediately after clicking a text object. If off, you must double-click to edit text.
34 sd%3AMetricField%3ATP_OPTIONS_MISC%3AMTR_FLD_TABSTOP 21 定義定位鍵之間的間隔。
17 .uno%3ASdGraphicOptions 93 定義繪圖文件的全域設定，其中包括依預設要顯示的內容、要使用的顯示比例、網格對齊以及要列印的內容等。
14 .uno%3ASmEditOptions 8b 定義所有新公式文件的列印格式和列印選項。當您從 %PRODUCTNAME Math 中直接列印公式時，會套用這些選項。
d tobeannounced 4a Saves only those symbols which each formula that are used in that formula.
36 starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_ZOOM 3f 依指定的放大比例縮小或放大列印公式的大小。
3f starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_ORIGINAL_SIZE 36 依目前的字型大小列印公式而不必調整。
2e STARMATH_CHECKBOX_RID_PRINTOPTIONPAGE_CB_FRAME 33 為列印輸出中的公式區域套用細邊框。
3c starmath%3ACheckBox%3ARID_PRINTOPTIONPAGE%3ACB_EQUATION_TEXT 46 指定是否在列印輸出的底端包括 [指令] 視窗的內容。
31 STARMATH_CHECKBOX_RID_PRINTOPTIONPAGE_CB_TITLEROW 36 指定是否在列印輸出中包括文件的名稱。
3d starmath%3ACheckBox%3ARID_PRINTOPTIONPAGE%3ACB_IGNORE_SPACING 30 指定移除位於行尾的空格萬用字元。
36 starmath%3AMetricField%3ARID_PRINTOPTIONPAGE%3AMF_ZOOM 3f 依指定的放大比例縮小或放大列印公式的大小。
3d starmath%3ARadioButton%3ARID_PRINTOPTIONPAGE%3ARB_FIT_TO_PAGE 36 依列印輸出所使用的頁面格式調整公式。
16 SFX2_HID_PRINT_OPTIONS 30 定義對所有文件均有效的公式設定。
28 starmath%3ATabPage%3ARID_PRINTOPTIONPAGE 30 定義對所有文件均有效的公式設定。
41 cui%3AListBox%3ARID_OPTPAGE_CHART_DEFCOLORS%3ALB_CHART_COLOR_LIST 2d 顯示適用於資料系列的所有顏色。
44 cui%3APushButton%3ARID_OPTPAGE_CHART_DEFCOLORS%3APB_RESET_TO_DEFAULT 30 復原在安裝程式時定義的顏色設定。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_PBAS_CODE da 將 Microsoft 文件的 Basic 程式碼作為特殊的 $[officename] Basic 模組與文件一同載入和儲存。在 $[officename] Basic IDE 中的 Sub 和 End Sub 之間可看到停用的 Microsoft Basic 程式碼。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_WBAS_CODE da 將 Microsoft 文件的 Basic 程式碼作為特殊的 $[officename] Basic 模組與文件一同載入和儲存。在 $[officename] Basic IDE 中的 Sub 和 End Sub 之間可看到停用的 Microsoft Basic 程式碼。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_PBAS_STG fe 指定只要文件仍載入在 $[officename] 中，包含在文件內的原始 Microsoft Basic 程式碼就會保留在特殊的內部記憶體中。以 Microsoft 格式儲存文件時，Microsoft Basic 就會隨程式碼再次儲存，且形式不變。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_WBAS_STG fe 指定只要文件仍載入在 $[officename] 中，包含在文件內的原始 Microsoft Basic 程式碼就會保留在特殊的內部記憶體中。以 Microsoft 格式儲存文件時，Microsoft Basic 就會隨程式碼再次儲存，且形式不變。
37 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_CODE da 將 Microsoft 文件的 Basic 程式碼作為特殊的 $[officename] Basic 模組與文件一同載入和儲存。在 $[officename] Basic IDE 中的 Sub 和 End Sub 之間可看到停用的 Microsoft Basic 程式碼。
36 svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_STG fe 指定只要文件仍載入在 $[officename] 中，包含在文件內的原始 Microsoft Basic 程式碼就會保留在特殊的內部記憶體中。以 Microsoft 格式儲存文件時，Microsoft Basic 就會隨程式碼再次儲存，且形式不變。
3a svx%3ACheckBox%3ARID_OFAPAGE_MSFILTEROPT%3ACB_EBAS_EXECTBL aa 將載入 VBA (Visual Basic for Applications) 程式碼並準備執行。若未核取此核取方塊，VBA 程式碼會加註為註解以進行檢查，但不會執行。
1b CUI_HID_OFAPAGE_MSFLTR2_CLB ac [清單方塊] 欄位會顯示成對的 OLE 物件條目，該 OLE 物件可以在載入至 $[officename] (L) 中及 (或) 儲存為 Microsoft 格式 (S) 時進行轉換。
33 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_ASIANSUPPORT 62 啟動亞洲語言支援。現在，您可以修改 %PRODUCTNAME 中相應的亞洲語言設定。
32 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_CURRENT_DOC 39 指定的預設語言設定僅對目前的文件有效。
2e cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_CURRENCY 39 指定用於貨幣格式和貨幣欄位的預設貨幣。
37 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_DECIMALSEPARATOR 69 指定當您按下數字小鍵盤上的每個鍵時，使用您的系統中設定的小數點符號鍵。
31 cui%3ACheckBox%3AOFA_TP_LANGUAGES%3ACB_CTLSUPPORT 74 啟動複合文字版面配置支援。您可以修改對應於 %PRODUCTNAME 中複合文字版面配置的設定。
33 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_LOCALESETTING 6a 指定國家/地區設定中的語言環境設定。這會影響編號、貨幣和定量單位的設定。
33 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_USERINTERFACE bb 選取用於使用者介面的語言，例如用於功能表、對話方塊、說明檔案的語言。您必須安裝至少一種其他語言套件或多種語言版本的 %PRODUCTNAME。
32 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_COMPLEX_LANG 3c 指定用於複合文字版面配置拼字檢查的語言。
30 cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_ASIAN_LANG 42 指定亞洲語言字母表中用於拼字檢查功能的語言。
2f cui%3AListBox%3AOFA_TP_LANGUAGES%3ALB_WEST_LANG 3c 指定西文字母表中用於拼字檢查功能的語言。
41 cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_PUNCT_KANA_COMP 2d 指定壓縮標點符號與日文片假名。
3e cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_CHAR_KERNING 2d 指定字元間隔僅適用於西文文字。
30 cui%3AEdit%3ARID_SVXPAGE_ASIAN_LAYOUT%3AED_START 2d 指定不應單獨顯示在行首的字元。
36 cui%3AListBox%3ARID_SVXPAGE_ASIAN_LAYOUT%3ALB_LANGUAGE 3f 指定要定義第一個字元和最後一個字元的語言。
3c cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_PUNCT_COMP 1e 指定僅壓縮標點符號。
2e cui%3AEdit%3ARID_SVXPAGE_ASIAN_LAYOUT%3AED_END 2d 指定不應單獨顯示在行尾的字元。
37 cui%3ACheckBox%3ARID_SVXPAGE_ASIAN_LAYOUT%3ACB_STANDARD 60 當您標記 [預設值]時，下列兩個文字方塊會由所選語言的預設字元填入：
39 cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_NO_COMP 18 指定完全不壓縮。
3c cui%3ARadioButton%3ARID_SVXPAGE_ASIAN_LAYOUT%3ARB_CHAR_PUNCT 42 指定字元間隔會套用於西文文字和亞洲標點符號。
41 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_BAVA_HAFA 30 指定在搜尋中視為相等處理的選項。
46 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_OLD_KANA_FORMS 30 指定在搜尋中視為相等處理的選項。
43 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_MIDDLE_DOT 1b 指定要忽略的字元。
42 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_SESHE_ZEJE 30 指定在搜尋中視為相等處理的選項。
47 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_TSITHICHI_DHIZI 30 指定在搜尋中視為相等處理的選項。
47 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_FULL_HALF_WIDTH 30 指定在搜尋中視為相等處理的選項。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_WHITESPACES 1b 指定要忽略的字元。
3c cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_KIKU 30 指定在搜尋中視為相等處理的選項。
3c cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_CASE 30 指定在搜尋中視為相等處理的選項。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_IGNORE_PUNCTUATION 1b 指定要忽略的字元。
4a cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_VARIANT_FORM_KANJI 30 指定在搜尋中視為相等處理的選項。
44 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_CONTRACTIONS 30 指定在搜尋中視為相等處理的選項。
49 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_REPEAT_CHAR_MARKS 30 指定在搜尋中視為相等處理的選項。
4b cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_PROLONGED_SOUNDMARK 30 指定在搜尋中視為相等處理的選項。
45 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_HYUFYU_BYUVYU 30 指定在搜尋中視為相等處理的選項。
41 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_DIZI_DUZU 30 指定在搜尋中視為相等處理的選項。
48 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_MINUS_DASH_CHOON 30 指定在搜尋中視為相等處理的選項。
3d cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_IAIYA 30 指定在搜尋中視為相等處理的選項。
49 cui%3ACheckBox%3ARID_SVXPAGE_JSEARCH_OPTIONS%3ACB_MATCH_HIRAGANA_KATAKANA 30 指定在搜尋中視為相等處理的選項。
41 cui%3ARadioButton%3ARID_SVXPAGE_OPTIONS_CTL%3ARB_MOVEMENT_LOGICAL 72 按向右鍵將文字游標移向目前文字的結尾。按向左鍵將文字游標移向目前文字的開頭。
35 cui%3AListBox%3ARID_SVXPAGE_OPTIONS_CTL%3ALB_NUMERALS ae 選取在所有 %PRODUCTNAME 模組中文字、物件中的文字、欄位和控制項內使用的數字類型。僅有 %PRODUCTNAME Calc 中的儲存格內容不受影響。
40 cui%3ARadioButton%3ARID_SVXPAGE_OPTIONS_CTL%3ARB_MOVEMENT_VISUAL 54 按向右鍵將文字游標向右移動。按向左鍵將文字游標向左移動。
3e cui%3ACheckBox%3ARID_SVXPAGE_OPTIONS_CTL%3ACB_SEQUENCECHECKING 33 對諸如泰語的語言啟用序列輸入檢查。
3d cui%3ANumericField%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ANF_TIMEOUT 39 定義閒置已保留連線的時間 (以秒為單位)。
3f cui%3ACheckBox%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ACB_DRIVERPOOLING 73 從清單中選取驅動程式，並標記 [保留使用這個驅動程式] 核取方塊，以便保留其連線。
3c cui%3ACheckBox%3ARID_OFAPAGE_CONNPOOLOPTIONS%3ACB_POOL_CONNS 21 指定是否保留所選連線。
24 DBACCESS_HID_DSBROWSER_DISCONNECTING 21 指定是否保留所選連線。
36 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_DELETE 24 從清單中移除選取的項目。
34 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_EDIT 3d 開啟 [資料庫連結] 對話方塊編輯選取的項目。
33 cui%3APushButton%3ARID_SFXPAGE_DBREGISTER%3ABTN_NEW 37 開啟 [資料庫連結] 對話方塊建立新項目。
17 CUI_HID_DBPATH_CTL_PATH 6c 列出所有已註冊之資料庫的註冊名稱和資料庫檔案。在項目上連按兩下進行編輯。
27 cui%3AEdit%3ADLG_DOCUMENTLINK%3AET_NAME 4f 輸入資料庫的名稱。%PRODUCTNAME 使用此名稱來存取此資料庫。
33 cui%3APushButton%3ADLG_DOCUMENTLINK%3APB_BROWSEFILE 4e 開啟一個檔案對話方塊，您可以在其中選取該資料庫檔案。
38 cui%3APushButton%3ARID_SVXPAGE_IMPROVEMENT%3APB_SHOWDATA 90 按一下按鈕可顯示目前收集的資料。此資料會連同結束目前階段作業前所收集到的任何資料一起在下次傳送。
33 cui%3ARadioButton%3ARID_SVXPAGE_IMPROVEMENT%3ARB_NO 43 按一下 [否，我不想參與] 即可停用自動反應意見。
34 cui%3ARadioButton%3ARID_SVXPAGE_IMPROVEMENT%3ARB_YES 40 按一下 [是，我願意參與…] 啟用自動反應意見。
3a cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_PARAMETER 29 開啟[Java 啟動參數] 對話方塊。
19 CUI_HID_OPTIONS_JAVA_LIST b0 選取要使用的 JRE。在部分系統上，您必須等候片刻，才會填入清單。在部分系統上，必須重新啟動 %PRODUCTNAME，才可使用變更的設定。
3a cui%3ACheckBox%3ARID_SVXPAGE_OPTIONS_JAVA%3ACB_JAVA_ENABLE 38 可讓您在 %PRODUCTNAME 中執行 Java 應用程式。
3a cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_CLASSPATH 24 開啟[類別路徑] 對話方塊。
34 cui%3APushButton%3ARID_SVXPAGE_OPTIONS_JAVA%3APB_ADD 32 增加路徑到電腦上的 JRE 根資料夾中。
39 cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_ADDPATH 36 選取資料夾並將資料夾新增至類別路徑。
33 cui%3AListBox%3ARID_SVXDLG_JAVA_CLASSPATH%3ALB_PATH 36 指定 Java 類別或 Java 類別程式庫的位置。
3c cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_ADDARCHIVE 4f 選取 jar 或 zip 格式的歸檔檔案，並將檔案新增至類別路徑。
3d cui%3APushButton%3ARID_SVXDLG_JAVA_CLASSPATH%3APB_REMOVE_PATH 70 在清單中選取一個歸檔或資料夾，然後按一下 「移除」，以便從類別路徑移除物件。
37 cui%3AListBox%3ARID_SVXDLG_JAVA_PARAMETER%3ALB_ASSIGNED 6f 列出指定的 JRE 啟動參數。若要移除啟動參數，請選取參數，然後按一下 「移除」。
35 cui%3AEdit%3ARID_SVXDLG_JAVA_PARAMETER%3AED_PARAMETER 6f 在指令行中輸入 JRE 啟動參數。按一下 [指定] 將參數新增至可用啟動參數的清單中。
38 cui%3APushButton%3ARID_SVXDLG_JAVA_PARAMETER%3APB_REMOVE 23 刪除選取的 JRE 啟動參數。
38 cui%3APushButton%3ARID_SVXDLG_JAVA_PARAMETER%3APB_ASSIGN 2f 將目前的 JRE 啟動參數加入清單中。
3a xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_LOW ba 巨集可設定為自動啟動，同時有可能會執行會造成損壞的動作，例如，刪除或重新命名檔案。在開啟其他作者的文件時，不建議使用此設定。
3d xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_MEDIUM e8 可在 [信任的來源] 標籤頁上設定受信任的來源。可執行來自受信任之來源的簽署巨集。此外，還可執行來自受信任之檔案位置的任何巨集。所有其他巨集則必須經過您的確認。
3f xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_VERYHIGH 88 可在 [信任的來源] 標籤頁上設定受信任的檔案位置。如此即能執行來自受信任之檔案位置的任何巨集。
3b xmlsecurity%3ARadioButton%3ARID_XMLSECTP_SECLEVEL%3ARB_HIGH bb 可在 [信任的來源] 標籤頁上設定受信任的來源。只能執行來自受信任之來源的簽署巨集。此外，還可執行來自受信任之檔案位置的任何巨集。
4a xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3APB_REMOVE_TRUSTCERT 30 從信任的憑證清單中移除所選憑證。
48 xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3APB_VIEW_TRUSTCERT 34 開啟所選憑證的 [檢視憑證] 對話方塊。
4a xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3AFL_ADD_TRUSTFILELOC 54 開啟一個資料夾選擇對話方塊。選取可執行所有巨集的資料夾。
43 xmlsecurity%3AListBox%3ARID_XMLSECTP_TRUSTSOURCES%3ALB_TRUSTFILELOC 51 只有從下列其中一個位置開啟文件巨集，才可執行文件巨集。
4d xmlsecurity%3APushButton%3ARID_XMLSECTP_TRUSTSOURCES%3AFL_REMOVE_TRUSTFILELOC 36 從信任的檔案位置清單中移除所選資料夾
28 XMLSECURITY_HID_XMLSEC_CTRL_TRUSTSOURCES 1b 列出受信任的憑證。
2b sw%3ANumericField%3ATP_MAILCONFIG%3ANF_PORT 18 輸入 SMTP 連接埠。
26 sw%3AEdit%3ATP_MAILCONFIG%3AED_REPLYTO 2a 輸入用於回覆電子郵件的地址。
26 sw%3AEdit%3ATP_MAILCONFIG%3AED_ADDRESS 30 輸入回覆時要使用的電子郵件地址。
29 sw%3APushButton%3ATP_MAILCONFIG%3APB_TEST 43 開啟 [測試帳號設定] 對話方塊測試目前的設定值。
33 sw%3APushButton%3ATP_MAILCONFIG%3APB_AUTHENTICATION 79 開啟 [伺服器認證] 對話方塊，您可在其中指定用於安全地傳送電子郵件的伺服器認證設定。
2a sw%3ACheckBox%3ATP_MAILCONFIG%3ACB_REPLYTO 6a 使用您在 [回覆] 地址文字方塊中所輸入的電子郵件地址作為回覆電子郵件地址。
23 SW_CHECKBOX_TP_MAILCONFIG_CB_SECURE 3c 如果可用，則使用安全連接來傳送電子郵件。
25 sw%3AEdit%3ATP_MAILCONFIG%3AED_SERVER 1e 輸入 SMTP 伺服器名稱。
2a sw%3AEdit%3ATP_MAILCONFIG%3AED_DISPLAYNAME 15 輸入您的名稱。
3a cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYDAY 15 每天檢查一次。
39 cui%3APushButton%3ARID_SVXPAGE_ONLINEUPDATE%3APB_CHECKNOW f 立刻檢查。
3c cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYMONTH 15 每月檢查一次。
38 cui%3ACheckBox%3ARID_SVXPAGE_ONLINEUPDATE%3ACB_AUTOCHECK 68 標記以定期檢查線上更新，然後選取 %PRODUCTNAME 檢查線上更新的時間間隔頻率。
3b cui%3ARadioButton%3ARID_SVXPAGE_ONLINEUPDATE%3ARB_EVERYWEEK 2a 每週檢查一次。此為預設設定。
39 sw%3ANumericField%3ADLG_MM_SERVERAUTHENTICATION%3ANF_PORT 30 輸入 POP3 或 IMAP 伺服器上的連接埠。
33 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_SERVER 3a 輸入 POP 3 或 IMAP 郵件伺服器的伺服器名稱。
35 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_USERNAME 2a 輸入 SMTP 伺服器的使用者名稱。
37 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_INPASSWORD f 輸入密碼。
38 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_IMAP 29 指定內送郵件伺服器使用 IMAP。
46 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_SEP_AUTHENTICATION 42 選擇您的 SMTP 伺服器是否需要使用者名稱和密碼。
3f sw%3ACheckBox%3ADLG_MM_SERVERAUTHENTICATION%3ACB_AUTHENTICATION 36 啟用以 SMTP 傳送電子郵件時所需的認證。
38 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_POP3 2a 指定內送郵件伺服器使用 POP 3。
37 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_INUSERNAME 2a 輸入 IMAP 伺服器的使用者名稱。
40 sw%3ARadioButton%3ADLG_MM_SERVERAUTHENTICATION%3ARB_SMPTAFTERPOP 42 選擇是否需要先讀取電子郵件才能傳送電子郵件。
38 sw%3AEdit%3ADLG_MM_SERVERAUTHENTICATION%3AED_OUTPASSWORD 21 輸入使用者名稱的密碼。
36 sw%3APushButton%3ADLG_MM_TESTACCOUNTSETTINGS%3APB_STOP 3a 按一下 [停止] 按鈕手動停止測試階段作業。
21 SW_HID_MM_TESTACCOUNTSETTINGS_TLB 45 在頂部的清單方塊中，會列出測試階段作業的結果。
3a sw%3AMultiLineEdit%3ADLG_MM_TESTACCOUNTSETTINGS%3AED_ERROR 58 您可在 [錯誤] 清單方塊中讀取測試設定時遭遇之任何錯誤的說明。
2a XMLSECURITY_HID_XMLSEC_CTRL_VIEWSIGNATURES 55 [檢視證書] 對話方塊的 [證書路徑] 頁會顯示證書的位置和狀態。
22 XMLSECURITY_HID_XMLSEC_TP_CERTPATH 55 [檢視證書] 對話方塊的 [證書路徑] 頁會顯示證書的位置和狀態。
43 xmlsecurity%3AMultiLineEdit%3ARID_XMLSECTP_CERTPATH%3AML_CERTSTATUS 55 [檢視證書] 對話方塊的 [證書路徑] 頁會顯示證書的位置和狀態。
24 XMLSECURITY_HID_XMLSEC_CTRL_ELEMENTS 52 [檢視證書] 對話方塊的 [細節] 頁會顯示有關證書的詳細資訊。
3f xmlsecurity%3AMultiLineEdit%3ARID_XMLSECTP_DETAILS%3AML_ELEMENT 48 使用數值清單方塊可檢視數值並將其複製到剪貼簿上。
21 XMLSECURITY_HID_XMLSEC_TP_DETAILS 52 [檢視證書] 對話方塊的 [細節] 頁會顯示有關證書的詳細資訊。
21 XMLSECURITY_HID_XMLSEC_TP_GENERAL 52 [檢視證書] 對話方塊的 [一般] 頁會顯示有關證書的基本資訊。
