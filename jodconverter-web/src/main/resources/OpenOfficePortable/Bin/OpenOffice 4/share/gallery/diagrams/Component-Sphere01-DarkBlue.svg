<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 12.0.1, SVG Export Plug-In . SVG Version: 6.00 Build 51448)  -->
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" [
	<!ENTITY ns_svg "http://www.w3.org/2000/svg">
	<!ENTITY ns_xlink "http://www.w3.org/1999/xlink">
]>
<svg  version="1.1" id="Layer_1" xmlns="&ns_svg;" xmlns:xlink="&ns_xlink;" width="107" height="100" viewBox="0 0 107 100"
	 overflow="visible" enable-background="new 0 0 107 100" xml:space="preserve">
<g>
	<g>
		<defs>
			<circle id="XMLID_1_" cx="53.5" cy="50" r="44.362"/>
		</defs>
		<radialGradient id="XMLID_10_" cx="36.5645" cy="16.4126" r="66.4111" gradientUnits="userSpaceOnUse">
			<stop  offset="0" style="stop-color:#00CCFF"/>
			<stop  offset="1" style="stop-color:#000066"/>
		</radialGradient>
		<use xlink:href="#XMLID_1_"  fill="url(#XMLID_10_)"/>
		<clipPath id="XMLID_11_">
			<use xlink:href="#XMLID_1_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter" filterUnits="userSpaceOnUse" x="2.974" y="-22.047" width="100.296" height="85.939">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="2.974" y="-22.047" width="100.296" height="85.939" id="XMLID_12_">
			<g clip-path="url(#XMLID_11_)" filter="url(#Adobe_OpacityMaskFilter)">
				
					<linearGradient id="XMLID_13_" gradientUnits="userSpaceOnUse" x1="184.0684" y1="-381.2578" x2="238.2313" y2="-337.6631" gradientTransform="matrix(0.8962 0.4437 -0.4437 0.8962 -290.3355 261.1143)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_13_)" d="M101.429,50.715C88.317,77.197,57.546,88.689,32.7,76.387
					C7.853,64.086-1.659,32.646,11.452,6.164c13.111-26.48,43.883-37.975,68.73-25.672C105.028-7.205,114.54,24.234,101.429,50.715z
					"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.3991 0.9169 -0.9169 0.3991 51.1075 -36.1347)" opacity="0.45" clip-path="url(#XMLID_11_)" mask="url(#XMLID_12_)" fill="#FFFFFF" cx="53.121" cy="20.923" rx="41.105" ry="51.666"/>
	</g>
	<g>
		<defs>
			<circle id="XMLID_6_" cx="53.5" cy="50" r="44.362"/>
		</defs>
		<clipPath id="XMLID_14_">
			<use xlink:href="#XMLID_6_" />
		</clipPath>
		<defs>
			<filter id="Adobe_OpacityMaskFilter_1_" filterUnits="userSpaceOnUse" x="-12.345" y="-15.869" width="87.346" height="99.065">
				<feColorMatrix  type="matrix" values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 1 0"/>
			</filter>
		</defs>
		<mask maskUnits="userSpaceOnUse" x="-12.345" y="-15.869" width="87.346" height="99.065" id="XMLID_15_">
			<g clip-path="url(#XMLID_14_)" filter="url(#Adobe_OpacityMaskFilter_1_)">
				
					<linearGradient id="XMLID_2_" gradientUnits="userSpaceOnUse" x1="-93.6875" y1="135.1377" x2="-39.5245" y2="178.7324" gradientTransform="matrix(0.978 -0.2087 0.2087 0.978 75.1313 -127.7948)">
					<stop  offset="0" style="stop-color:#FFFFFF"/>
					<stop  offset="1" style="stop-color:#000000"/>
				</linearGradient>
				<path fill="url(#XMLID_2_)" d="M87.694,27.021C93.86,55.92,76.878,84.037,49.763,89.822s-54.096-12.953-60.26-41.852
					C-16.663,19.072,0.319-9.045,27.435-14.83S81.528-1.877,87.694,27.021z"/>
			</g>
		</mask>
		
			<ellipse transform="matrix(0.8822 0.4709 -0.4709 0.8822 19.5445 -10.7862)" opacity="0.3" clip-path="url(#XMLID_14_)" mask="url(#XMLID_15_)" fill="#FFFFFF" cx="31.327" cy="33.664" rx="41.105" ry="51.667"/>
	</g>
	
		<ellipse transform="matrix(0.9343 -0.3565 0.3565 0.9343 -3.4489 14.1139)" opacity="0.7" fill="#FFFFFF" cx="36.563" cy="16.413" rx="8.527" ry="4.939"/>
	
		<ellipse transform="matrix(0.6848 -0.7287 0.7287 0.6848 -9.7366 25.7783)" opacity="0.93" fill="#FFFFFF" cx="24.934" cy="24.146" rx="3.119" ry="2.389"/>
</g>
</svg>
