<?xml version="1.0"?>
<oor:data xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:oor="http://openoffice.org/2001/registry"><dependency file="main"/><oor:component-schema oor:name="Effects" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates><group oor:name="LabelType"><prop oor:name="Label" oor:type="xs:string" oor:localized="true"/></group><group oor:name="PresetCategory"><prop oor:name="Label" oor:type="xs:string" oor:localized="true"/><prop oor:name="Effects" oor:type="oor:string-list" oor:localized="false"/></group></templates><component><group oor:name="UserInterface"><set oor:name="Effects" oor:node-type="LabelType"/><set oor:name="Transitions" oor:node-type="LabelType"/><set oor:name="Properties" oor:node-type="LabelType"/></group><group oor:name="Presets"><set oor:name="Entrance" oor:node-type="PresetCategory"/><set oor:name="Emphasis" oor:node-type="PresetCategory"/><set oor:name="Exit" oor:node-type="PresetCategory"/><set oor:name="MotionPaths" oor:node-type="PresetCategory"/><set oor:name="Misc" oor:node-type="PresetCategory"/></group></component></oor:component-schema><oor:component-schema oor:name="ImpressWindowState" oor:package="org.openoffice.Office.UI" xml:lang="en-US"><templates/><component><group oor:name="UIElements"><set oor:name="States" oor:node-type="WindowStateType" oor:component="org.openoffice.Office.UI.WindowState"/></group></component></oor:component-schema><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Effects" oor:package="org.openoffice.Office.UI"><node oor:name="UserInterface"><node oor:name="Effects"><node oor:name="ooo-entrance-appear" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Appear</value></prop></node><node oor:name="ooo-entrance-fly-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fly In</value></prop></node><node oor:name="ooo-entrance-venetian-blinds" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds</value></prop></node><node oor:name="ooo-entrance-box" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box</value></prop></node><node oor:name="ooo-entrance-checkerboard" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Checkerboard</value></prop></node><node oor:name="ooo-entrance-circle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Circle</value></prop></node><node oor:name="ooo-entrance-fly-in-slow" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fly in Slow</value></prop></node><node oor:name="ooo-entrance-diamond" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diamond</value></prop></node><node oor:name="ooo-entrance-dissolve-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Dissolve In</value></prop></node><node oor:name="ooo-entrance-fade-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade In</value></prop></node><node oor:name="ooo-entrance-flash-once" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flash Once</value></prop></node><node oor:name="ooo-entrance-peek-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Peek In</value></prop></node><node oor:name="ooo-entrance-plus" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Plus</value></prop></node><node oor:name="ooo-entrance-random-bars" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Bars</value></prop></node><node oor:name="ooo-entrance-spiral-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spiral In</value></prop></node><node oor:name="ooo-entrance-split" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split</value></prop></node><node oor:name="ooo-entrance-stretchy" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Stretchy</value></prop></node><node oor:name="ooo-entrance-diagonal-squares" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares</value></prop></node><node oor:name="ooo-entrance-swivel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Swivel</value></prop></node><node oor:name="ooo-entrance-wedge" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wedge</value></prop></node><node oor:name="ooo-entrance-wheel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel</value></prop></node><node oor:name="ooo-entrance-wipe" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe</value></prop></node><node oor:name="ooo-entrance-zoom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Zoom</value></prop></node><node oor:name="ooo-entrance-random" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Effects</value></prop></node><node oor:name="ooo-entrance-boomerang" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Boomerang</value></prop></node><node oor:name="ooo-entrance-bounce" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bounce</value></prop></node><node oor:name="ooo-entrance-colored-lettering" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Colored Lettering</value></prop></node><node oor:name="ooo-entrance-movie-credits" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Movie Credits</value></prop></node><node oor:name="ooo-entrance-ease-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Ease In</value></prop></node><node oor:name="ooo-entrance-float" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Float</value></prop></node><node oor:name="ooo-entrance-turn-and-grow" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn and Grow</value></prop></node><node oor:name="ooo-entrance-breaks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Put on the Breaks</value></prop></node><node oor:name="ooo-entrance-pinwheel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pinwheel</value></prop></node><node oor:name="ooo-entrance-rise-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Rise Up</value></prop></node><node oor:name="ooo-entrance-falling-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Falling In</value></prop></node><node oor:name="ooo-entrance-thread" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Thread</value></prop></node><node oor:name="ooo-entrance-unfold" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Unfold</value></prop></node><node oor:name="ooo-entrance-whip" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Whip</value></prop></node><node oor:name="ooo-entrance-ascend" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Ascend</value></prop></node><node oor:name="ooo-entrance-center-revolve" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Center Revolve</value></prop></node><node oor:name="ooo-entrance-fade-in-and-swivel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade in and Swivel</value></prop></node><node oor:name="ooo-entrance-descend" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Descend</value></prop></node><node oor:name="ooo-entrance-sling" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sling</value></prop></node><node oor:name="ooo-entrance-spin-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spin In</value></prop></node><node oor:name="ooo-entrance-compress" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Compress</value></prop></node><node oor:name="ooo-entrance-magnify" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Magnify</value></prop></node><node oor:name="ooo-entrance-curve-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curve Up</value></prop></node><node oor:name="ooo-entrance-fade-in-and-zoom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade in and Zoom</value></prop></node><node oor:name="ooo-entrance-glide" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Glide</value></prop></node><node oor:name="ooo-entrance-expand" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Expand</value></prop></node><node oor:name="ooo-entrance-flip" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flip</value></prop></node><node oor:name="ooo-entrance-fold" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fold</value></prop></node><node oor:name="ooo-emphasis-fill-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Fill Color</value></prop></node><node oor:name="ooo-emphasis-font" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Font</value></prop></node><node oor:name="ooo-emphasis-font-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Font Color</value></prop></node><node oor:name="ooo-emphasis-font-size" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Font Size</value></prop></node><node oor:name="ooo-emphasis-font-style" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Font Style</value></prop></node><node oor:name="ooo-emphasis-grow-and-shrink" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Grow and Shrink</value></prop></node><node oor:name="ooo-emphasis-line-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Change Line Color</value></prop></node><node oor:name="ooo-emphasis-spin" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spin</value></prop></node><node oor:name="ooo-emphasis-transparency" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Transparency</value></prop></node><node oor:name="ooo-emphasis-bold-flash" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bold Flash</value></prop></node><node oor:name="ooo-emphasis-color-over-by-word" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Color Over by Word</value></prop></node><node oor:name="ooo-emphasis-reveal-underline" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Reveal Underline</value></prop></node><node oor:name="ooo-emphasis-color-blend" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Color Blend</value></prop></node><node oor:name="ooo-emphasis-color-over-by-letter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Color Over by Letter</value></prop></node><node oor:name="ooo-emphasis-complementary-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Complementary Color</value></prop></node><node oor:name="ooo-emphasis-complementary-color-2" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Complementary Color 2</value></prop></node><node oor:name="ooo-emphasis-contrasting-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Contrasting Color</value></prop></node><node oor:name="ooo-emphasis-darken" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Darken</value></prop></node><node oor:name="ooo-emphasis-desaturate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Desaturate</value></prop></node><node oor:name="ooo-emphasis-flash-bulb" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flash Bulb</value></prop></node><node oor:name="ooo-emphasis-lighten" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Lighten</value></prop></node><node oor:name="ooo-emphasis-vertical-highlight" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical Highlight</value></prop></node><node oor:name="ooo-emphasis-flicker" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flicker</value></prop></node><node oor:name="ooo-emphasis-grow-with-color" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Grow With Color</value></prop></node><node oor:name="ooo-emphasis-shimmer" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Shimmer</value></prop></node><node oor:name="ooo-emphasis-teeter" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Teeter</value></prop></node><node oor:name="ooo-emphasis-blast" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Blast</value></prop></node><node oor:name="ooo-emphasis-blink" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Blink</value></prop></node><node oor:name="ooo-emphasis-style-emphasis" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Style Emphasis</value></prop></node><node oor:name="ooo-emphasis-bold-reveal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bold Reveal</value></prop></node><node oor:name="ooo-emphasis-wave" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wave</value></prop></node><node oor:name="ooo-exit-venetian-blinds" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds</value></prop></node><node oor:name="ooo-exit-box" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box</value></prop></node><node oor:name="ooo-exit-checkerboard" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Checkerboard</value></prop></node><node oor:name="ooo-exit-circle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Circle</value></prop></node><node oor:name="ooo-exit-crawl-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Crawl Out</value></prop></node><node oor:name="ooo-exit-diamond" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diamond</value></prop></node><node oor:name="ooo-exit-disappear" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Disappear</value></prop></node><node oor:name="ooo-exit-dissolve" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Dissolve</value></prop></node><node oor:name="ooo-exit-flash-once" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flash Once</value></prop></node><node oor:name="ooo-exit-fly-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fly Out</value></prop></node><node oor:name="ooo-exit-peek-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Peek Out</value></prop></node><node oor:name="ooo-exit-plus" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Plus</value></prop></node><node oor:name="ooo-exit-random-bars" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Bars</value></prop></node><node oor:name="ooo-exit-random" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Effects</value></prop></node><node oor:name="ooo-exit-split" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split</value></prop></node><node oor:name="ooo-exit-diagonal-squares" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares</value></prop></node><node oor:name="ooo-exit-wedge" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wedge</value></prop></node><node oor:name="ooo-exit-wheel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel</value></prop></node><node oor:name="ooo-exit-wipe" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe</value></prop></node><node oor:name="ooo-exit-contract" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Contract</value></prop></node><node oor:name="ooo-exit-fade-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade Out</value></prop></node><node oor:name="ooo-exit-fade-out-and-swivel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade out and Swivel</value></prop></node><node oor:name="ooo-exit-fade-out-and-zoom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade out and Zoom</value></prop></node><node oor:name="ooo-exit-ascend" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Ascend</value></prop></node><node oor:name="ooo-exit-center-revolve" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Center Revolve</value></prop></node><node oor:name="ooo-exit-collapse" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Collapse</value></prop></node><node oor:name="ooo-exit-colored-lettering" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Colored Lettering</value></prop></node><node oor:name="ooo-exit-descend" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Descend</value></prop></node><node oor:name="ooo-exit-ease-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Ease Out</value></prop></node><node oor:name="ooo-exit-turn-and-grow" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn and Grow</value></prop></node><node oor:name="ooo-exit-sink-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sink Down</value></prop></node><node oor:name="ooo-exit-spin-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spin Out</value></prop></node><node oor:name="ooo-exit-stretchy" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Stretchy</value></prop></node><node oor:name="ooo-exit-unfold" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Unfold</value></prop></node><node oor:name="ooo-exit-zoom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Zoom</value></prop></node><node oor:name="ooo-exit-boomerang" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Boomerang</value></prop></node><node oor:name="ooo-exit-bounce" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bounce</value></prop></node><node oor:name="ooo-exit-movie-credits" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Movie Credits</value></prop></node><node oor:name="ooo-exit-curve-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curve Down</value></prop></node><node oor:name="ooo-exit-flip" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flip</value></prop></node><node oor:name="ooo-exit-float" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Float</value></prop></node><node oor:name="ooo-exit-fold" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fold</value></prop></node><node oor:name="ooo-exit-glide" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Glide</value></prop></node><node oor:name="ooo-exit-breaks" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Put on the Breaks</value></prop></node><node oor:name="ooo-exit-magnify" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Magnify</value></prop></node><node oor:name="ooo-exit-pinwheel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pinwheel</value></prop></node><node oor:name="ooo-exit-sling" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sling</value></prop></node><node oor:name="ooo-exit-spiral-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spiral Out</value></prop></node><node oor:name="ooo-exit-swish" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Swish</value></prop></node><node oor:name="ooo-exit-swivel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Swivel</value></prop></node><node oor:name="ooo-exit-thread" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Thread</value></prop></node><node oor:name="ooo-exit-whip" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Whip</value></prop></node><node oor:name="ooo-motionpath-4-point-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">4 Point Star</value></prop></node><node oor:name="ooo-motionpath-5-point-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">5 Point Star</value></prop></node><node oor:name="ooo-motionpath-6-point-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">6 Point Star</value></prop></node><node oor:name="ooo-motionpath-8-point-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">8 Point Star</value></prop></node><node oor:name="ooo-motionpath-circle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Circle</value></prop></node><node oor:name="ooo-motionpath-crescent-moon" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Crescent Moon</value></prop></node><node oor:name="ooo-motionpath-diamond" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diamond</value></prop></node><node oor:name="ooo-motionpath-equal-triangle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Equal Triangle</value></prop></node><node oor:name="ooo-motionpath-oval" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Oval</value></prop></node><node oor:name="ooo-motionpath-heart" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heart</value></prop></node><node oor:name="ooo-motionpath-hexagon" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Hexagon</value></prop></node><node oor:name="ooo-motionpath-octagon" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Octagon</value></prop></node><node oor:name="ooo-motionpath-parallelogram" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Parallelogram</value></prop></node><node oor:name="ooo-motionpath-pentagon" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pentagon</value></prop></node><node oor:name="ooo-motionpath-right-triangle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Right Triangle</value></prop></node><node oor:name="ooo-motionpath-square" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Square</value></prop></node><node oor:name="ooo-motionpath-teardrop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Teardrop</value></prop></node><node oor:name="ooo-motionpath-trapezoid" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Trapezoid</value></prop></node><node oor:name="ooo-motionpath-arc-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Arc Down</value></prop></node><node oor:name="ooo-motionpath-arc-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Arc Left</value></prop></node><node oor:name="ooo-motionpath-arc-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Arc Right</value></prop></node><node oor:name="ooo-motionpath-arc-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Arc Up</value></prop></node><node oor:name="ooo-motionpath-bounce-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bounce Left</value></prop></node><node oor:name="ooo-motionpath-bounce-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bounce Right</value></prop></node><node oor:name="ooo-motionpath-curvy-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curvy Left</value></prop></node><node oor:name="ooo-motionpath-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Left</value></prop></node><node oor:name="ooo-motionpath-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Right</value></prop></node><node oor:name="ooo-motionpath-spiral-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spiral Left</value></prop></node><node oor:name="ooo-motionpath-spiral-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spiral Right</value></prop></node><node oor:name="ooo-motionpath-sine-wave" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Sine Wave</value></prop></node><node oor:name="ooo-motionpath-s-curve-1" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">S Curve 1</value></prop></node><node oor:name="ooo-motionpath-s-curve-2" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">S Curve 2</value></prop></node><node oor:name="ooo-motionpath-heartbeat" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Heartbeat</value></prop></node><node oor:name="ooo-motionpath-curvy-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curvy Right</value></prop></node><node oor:name="ooo-motionpath-decaying-wave" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Decaying Wave</value></prop></node><node oor:name="ooo-motionpath-diagonal-down-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Down Right</value></prop></node><node oor:name="ooo-motionpath-diagonal-up-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Up Right</value></prop></node><node oor:name="ooo-motionpath-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Down</value></prop></node><node oor:name="ooo-motionpath-funnel" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Funnel</value></prop></node><node oor:name="ooo-motionpath-spring" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Spring</value></prop></node><node oor:name="ooo-motionpath-stairs-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Stairs Down</value></prop></node><node oor:name="ooo-motionpath-turn-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn Down</value></prop></node><node oor:name="ooo-motionpath-turn-down-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn Down Right</value></prop></node><node oor:name="ooo-motionpath-turn-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn Up</value></prop></node><node oor:name="ooo-motionpath-turn-up-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn Up Right</value></prop></node><node oor:name="ooo-motionpath-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Up</value></prop></node><node oor:name="ooo-motionpath-wave" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wave</value></prop></node><node oor:name="ooo-motionpath-zigzag" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Zigzag</value></prop></node><node oor:name="ooo-motionpath-bean" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Bean</value></prop></node><node oor:name="ooo-motionpath-buzz-saw" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Buzz Saw</value></prop></node><node oor:name="ooo-motionpath-curved-square" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curved Square</value></prop></node><node oor:name="ooo-motionpath-curved-x" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curved X</value></prop></node><node oor:name="ooo-motionpath-curvy-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Curvy Star</value></prop></node><node oor:name="ooo-motionpath-figure-8-four" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Figure 8 Four</value></prop></node><node oor:name="ooo-motionpath-horizontal-figure-8" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal Figure 8</value></prop></node><node oor:name="ooo-motionpath-inverted-square" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Inverted Square</value></prop></node><node oor:name="ooo-motionpath-inverted-triangle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Inverted Triangle</value></prop></node><node oor:name="ooo-motionpath-loop-de-loop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Loop de Loop</value></prop></node><node oor:name="ooo-motionpath-neutron" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Neutron</value></prop></node><node oor:name="ooo-motionpath-peanut" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Peanut</value></prop></node><node oor:name="ooo-motionpath-clover" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Clover</value></prop></node><node oor:name="ooo-motionpath-pointy-star" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Pointy Star</value></prop></node><node oor:name="ooo-motionpath-swoosh" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Swoosh</value></prop></node><node oor:name="ooo-motionpath-vertical-figure-8" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical Figure 8</value></prop></node><node oor:name="ooo-media-start" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Start media</value></prop></node><node oor:name="ooo-media-stop" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">End media</value></prop></node><node oor:name="ooo-media-toggle-pause" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Toggle pause</value></prop></node></node><node oor:name="Properties"><node oor:name="basic" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Basic</value></prop></node><node oor:name="special" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Special</value></prop></node><node oor:name="moderate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Moderate</value></prop></node><node oor:name="exciting" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exciting</value></prop></node><node oor:name="subtle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Subtle</value></prop></node><node oor:name="linesandcurves" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Lines and Curves</value></prop></node><node oor:name="vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical</value></prop></node><node oor:name="horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal</value></prop></node><node oor:name="in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">In</value></prop></node><node oor:name="across" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Across</value></prop></node><node oor:name="down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Down</value></prop></node><node oor:name="up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Up</value></prop></node><node oor:name="from-bottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From bottom</value></prop></node><node oor:name="from-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From left</value></prop></node><node oor:name="from-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From right</value></prop></node><node oor:name="from-top" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top</value></prop></node><node oor:name="from-bottom-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From bottom left</value></prop></node><node oor:name="from-bottom-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From bottom right</value></prop></node><node oor:name="from-top-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top left</value></prop></node><node oor:name="from-top-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top right</value></prop></node><node oor:name="horizontal-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal in</value></prop></node><node oor:name="horizontal-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Horizontal out</value></prop></node><node oor:name="vertical-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical in</value></prop></node><node oor:name="vertical-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Vertical out</value></prop></node><node oor:name="out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Out</value></prop></node><node oor:name="out-from-screen-center" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Out from screen center</value></prop></node><node oor:name="in-from-screen-center" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">In from screen center</value></prop></node><node oor:name="in-slightly" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">In slightly</value></prop></node><node oor:name="out-slightly" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Out slightly</value></prop></node><node oor:name="left-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Left down</value></prop></node><node oor:name="left-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Left up</value></prop></node><node oor:name="right-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Right up</value></prop></node><node oor:name="right-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Right down</value></prop></node><node oor:name="to-bottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To bottom</value></prop></node><node oor:name="to-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To left</value></prop></node><node oor:name="to-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To right</value></prop></node><node oor:name="to-top" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To top</value></prop></node><node oor:name="to-bottom-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To bottom-left</value></prop></node><node oor:name="to-bottom-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To bottom-right</value></prop></node><node oor:name="to-top-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To top-left</value></prop></node><node oor:name="to-top-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">To top-right</value></prop></node><node oor:name="clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Clockwise</value></prop></node><node oor:name="counter-clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Counter-clockwise</value></prop></node><node oor:name="downward" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Downward</value></prop></node><node oor:name="from-bottom-right-horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From bottom-right horizontal</value></prop></node><node oor:name="from-bottom-right-vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From bottom-right vertical</value></prop></node><node oor:name="from-center-clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From center clockwise</value></prop></node><node oor:name="from-center-counter-clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From center counter-clockwise</value></prop></node><node oor:name="from-top-left-clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top-left clockwise</value></prop></node><node oor:name="from-top-left-horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top-left horizontal</value></prop></node><node oor:name="from-top-left-vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top-left vertical</value></prop></node><node oor:name="from-top-right-counter-clockwise" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From top-right counter-clockwise</value></prop></node><node oor:name="left-to-bottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From left to bottom</value></prop></node><node oor:name="left-to-top" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From left to top</value></prop></node><node oor:name="right-to-bottom" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From right to bottom</value></prop></node><node oor:name="right-to-top" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">From right to top</value></prop></node></node><node oor:name="Transitions"><node oor:name="venetian-blinds-horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds Horizontal</value></prop></node><node oor:name="venetian-blinds-vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds Vertical</value></prop></node><node oor:name="box-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box In</value></prop></node><node oor:name="box-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Box Out</value></prop></node><node oor:name="checkerboard-across" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Checkerboard Across</value></prop></node><node oor:name="checkerboard-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Checkerboard Down</value></prop></node><node oor:name="comb-horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Comb Horizontal</value></prop></node><node oor:name="comb-vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Comb Vertical</value></prop></node><node oor:name="cover-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Down</value></prop></node><node oor:name="cover-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Left</value></prop></node><node oor:name="cover-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Right</value></prop></node><node oor:name="cover-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Up</value></prop></node><node oor:name="cover-left-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Left-Down</value></prop></node><node oor:name="cover-left-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Left-Up</value></prop></node><node oor:name="cover-right-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Right-Down</value></prop></node><node oor:name="cover-right-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cover Right-Up</value></prop></node><node oor:name="cut" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cut</value></prop></node><node oor:name="cut-through-black" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Cut Through Black</value></prop></node><node oor:name="dissolve" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Dissolve</value></prop></node><node oor:name="fade-smoothly" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade Smoothly</value></prop></node><node oor:name="fade-through-black" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fade Through Black</value></prop></node><node oor:name="zoom-rotate-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Newsflash</value></prop></node><node oor:name="push-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Push Down</value></prop></node><node oor:name="push-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Push Left</value></prop></node><node oor:name="push-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Push Right</value></prop></node><node oor:name="push-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Push Up</value></prop></node><node oor:name="random-bars-horizontal" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Bars Horizontal</value></prop></node><node oor:name="random-bars-vertical" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Bars Vertical</value></prop></node><node oor:name="shape-circle" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Shape Circle</value></prop></node><node oor:name="shape-diamond" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Shape Diamond</value></prop></node><node oor:name="shape-plus" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Shape Plus</value></prop></node><node oor:name="split-horizontal-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Horizontal In</value></prop></node><node oor:name="split-horizontal-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Horizontal Out</value></prop></node><node oor:name="split-vertical-in" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Vertical In</value></prop></node><node oor:name="split-vertical-out" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Split Vertical Out</value></prop></node><node oor:name="diagonal-squares-left-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares Left-Down</value></prop></node><node oor:name="diagonal-squares-left-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares Left-Up</value></prop></node><node oor:name="diagonal-squares-right-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares Right-Down</value></prop></node><node oor:name="diagonal-squares-right-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Diagonal Squares Right-Up</value></prop></node><node oor:name="uncover-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Down</value></prop></node><node oor:name="uncover-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Left</value></prop></node><node oor:name="uncover-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Right</value></prop></node><node oor:name="uncover-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Up</value></prop></node><node oor:name="uncover-left-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Left-Down</value></prop></node><node oor:name="uncover-left-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Left-Up</value></prop></node><node oor:name="uncover-right-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Right-Down</value></prop></node><node oor:name="uncover-right-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Uncover Right-Up</value></prop></node><node oor:name="wedge" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wedge</value></prop></node><node oor:name="wheel-clockwise-1-spoke" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel Clockwise, 1 Spoke</value></prop></node><node oor:name="wheel-clockwise-2-spokes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel Clockwise, 2 Spokes</value></prop></node><node oor:name="wheel-clockwise-3-spokes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel Clockwise, 3 Spokes</value></prop></node><node oor:name="wheel-clockwise-4-spokes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel Clockwise, 4 Spokes</value></prop></node><node oor:name="wheel-clockwise-8-spokes" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wheel Clockwise, 8 Spokes</value></prop></node><node oor:name="wipe-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe Down</value></prop></node><node oor:name="wipe-left" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe Left</value></prop></node><node oor:name="wipe-right" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe Right</value></prop></node><node oor:name="wipe-up" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Wipe Up</value></prop></node><node oor:name="random-transition" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Random Transition</value></prop></node><node oor:name="tile-flip" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Flipping tiles</value></prop></node><node oor:name="outside-cube" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Outside turning cube</value></prop></node><node oor:name="revolving-circles" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Revolving circles</value></prop></node><node oor:name="turning-helix" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turning helix</value></prop></node><node oor:name="inside-cube" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Inside turning cube</value></prop></node><node oor:name="fall" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fall</value></prop></node><node oor:name="turn-around" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn around</value></prop></node><node oor:name="iris" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Iris</value></prop></node><node oor:name="turn-down" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Turn down</value></prop></node><node oor:name="rochade" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Rochade</value></prop></node><node oor:name="venetian3dv" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds 3D Vertical</value></prop></node><node oor:name="venetian3dh" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Venetian Blinds 3D Horizontal</value></prop></node><node oor:name="static" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Static</value></prop></node><node oor:name="finedissolve" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Fine Dissolve</value></prop></node></node></node><node oor:name="Presets"><node oor:name="Entrance"><node oor:name="basic" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Basic</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-entrance-appear;ooo-entrance-fly-in;ooo-entrance-venetian-blinds;ooo-entrance-box;ooo-entrance-checkerboard;ooo-entrance-circle;ooo-entrance-fly-in-slow;ooo-entrance-diamond;ooo-entrance-dissolve-in;ooo-entrance-flash-once;ooo-entrance-peek-in;ooo-entrance-plus;ooo-entrance-random-bars;ooo-entrance-split;ooo-entrance-diagonal-squares;ooo-entrance-wedge;ooo-entrance-wheel;ooo-entrance-wipe;ooo-entrance-random</value></prop></node><node oor:name="special" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Special</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-entrance-fade-in;ooo-entrance-fade-in-and-swivel;ooo-entrance-fade-in-and-zoom;ooo-entrance-expand</value></prop></node><node oor:name="moderate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Moderate</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-entrance-stretchy;ooo-entrance-zoom;ooo-entrance-colored-lettering;ooo-entrance-ease-in;ooo-entrance-turn-and-grow;ooo-entrance-rise-up;ooo-entrance-unfold;ooo-entrance-ascend;ooo-entrance-center-revolve;ooo-entrance-descend;ooo-entrance-spin-in;ooo-entrance-compress</value></prop></node><node oor:name="exciting" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exciting</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-entrance-spiral-in;ooo-entrance-swivel;ooo-entrance-boomerang;ooo-entrance-bounce;ooo-entrance-movie-credits;ooo-entrance-float;ooo-entrance-breaks;ooo-entrance-pinwheel;ooo-entrance-falling-in;ooo-entrance-thread;ooo-entrance-whip;ooo-entrance-sling;ooo-entrance-magnify;ooo-entrance-curve-up;ooo-entrance-glide;ooo-entrance-flip;ooo-entrance-fold</value></prop></node></node><node oor:name="Emphasis"><node oor:name="basic" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Basic</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-emphasis-fill-color;ooo-emphasis-font;ooo-emphasis-font-color;ooo-emphasis-font-size;ooo-emphasis-font-style;ooo-emphasis-line-color;ooo-emphasis-spin;ooo-emphasis-transparency</value></prop></node><node oor:name="special" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Special</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-emphasis-bold-flash;ooo-emphasis-color-over-by-word;ooo-emphasis-reveal-underline;ooo-emphasis-color-blend;ooo-emphasis-color-over-by-letter;ooo-emphasis-complementary-color;ooo-emphasis-complementary-color-2;ooo-emphasis-contrasting-color;ooo-emphasis-darken;ooo-emphasis-desaturate;ooo-emphasis-flash-bulb;ooo-emphasis-lighten</value></prop></node><node oor:name="moderate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Moderate</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-emphasis-flicker;ooo-emphasis-grow-with-color;ooo-emphasis-teeter;ooo-emphasis-shimmer</value></prop></node><node oor:name="exciting" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exciting</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-emphasis-blast;ooo-emphasis-bold-reveal;ooo-emphasis-style-emphasis;ooo-emphasis-wave;ooo-emphasis-blink</value></prop></node></node><node oor:name="Exit"><node oor:name="basic" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Basic</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-exit-disappear;ooo-exit-fly-out;ooo-exit-venetian-blinds;ooo-exit-box;ooo-exit-checkerboard;ooo-exit-circle;ooo-exit-crawl-out;ooo-exit-diamond;ooo-exit-dissolve;ooo-exit-flash-once;ooo-exit-peek-out;ooo-exit-plus;ooo-exit-random-bars;ooo-exit-diagonal-squares;ooo-exit-split;ooo-exit-wedge;ooo-exit-wheel;ooo-exit-wipe;ooo-exit-random</value></prop></node><node oor:name="special" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Special</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-exit-fade-out;ooo-exit-fade-out-and-swivel;ooo-exit-fade-out-and-zoom;ooo-exit-contract</value></prop></node><node oor:name="moderate" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Moderate</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-exit-collapse;ooo-exit-zoom;ooo-exit-colored-lettering;ooo-exit-ease-out;ooo-exit-turn-and-grow;ooo-exit-sink-down;ooo-exit-unfold;ooo-exit-descend;ooo-exit-center-revolve;ooo-exit-ascend;ooo-exit-spin-out;ooo-exit-stretchy</value></prop></node><node oor:name="exciting" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Exciting</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-exit-swivel;ooo-exit-boomerang;ooo-exit-bounce;ooo-exit-movie-credits;ooo-exit-float;ooo-exit-breaks;ooo-exit-pinwheel;ooo-exit-swish;ooo-exit-thread;ooo-exit-whip;ooo-exit-sling;ooo-exit-magnify;ooo-exit-curve-down;ooo-exit-glide;ooo-exit-flip;ooo-exit-fold</value></prop></node></node><node oor:name="MotionPaths"><node oor:name="basic" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Basic</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-motionpath-4-point-star;ooo-motionpath-5-point-star;ooo-motionpath-6-point-star;ooo-motionpath-8-point-star;ooo-motionpath-circle;ooo-motionpath-crescent-moon;ooo-motionpath-diamond;ooo-motionpath-equal-triangle;ooo-motionpath-oval;ooo-motionpath-heart;ooo-motionpath-left;ooo-motionpath-right;ooo-motionpath-hexagon;ooo-motionpath-octagon;ooo-motionpath-parallelogram;ooo-motionpath-pentagon;ooo-motionpath-right-triangle;ooo-motionpath-square;ooo-motionpath-teardrop;ooo-motionpath-trapezoid</value></prop></node><node oor:name="linesandcurves" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Lines and Curves</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-motionpath-arc-down;ooo-motionpath-arc-left;ooo-motionpath-arc-right;ooo-motionpath-arc-up;ooo-motionpath-bounce-left;ooo-motionpath-bounce-right;ooo-motionpath-curvy-left;ooo-motionpath-curvy-right;ooo-motionpath-decaying-wave;ooo-motionpath-diagonal-down-right;ooo-motionpath-diagonal-up-right;ooo-motionpath-down;ooo-motionpath-funnel;ooo-motionpath-heartbeat;ooo-motionpath-left;ooo-motionpath-right;ooo-motionpath-s-curve-1;ooo-motionpath-s-curve-2;ooo-motionpath-sine-wave;ooo-motionpath-spiral-left;ooo-motionpath-spiral-right;ooo-motionpath-spring;ooo-motionpath-stairs-down;ooo-motionpath-turn-down;ooo-motionpath-turn-down-right;ooo-motionpath-turn-up;ooo-motionpath-turn-up-right;ooo-motionpath-up;ooo-motionpath-wave;ooo-motionpath-zigzag</value></prop></node><node oor:name="special" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Special</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-motionpath-bean;ooo-motionpath-buzz-saw;ooo-motionpath-clover;ooo-motionpath-curved-square;ooo-motionpath-curvy-star;ooo-motionpath-figure-8-four;ooo-motionpath-horizontal-figure-8;ooo-motionpath-inverted-square;ooo-motionpath-inverted-triangle;ooo-motionpath-loop-de-loop;ooo-motionpath-neutron;ooo-motionpath-peanut;ooo-motionpath-pointy-star;ooo-motionpath-swoosh;ooo-motionpath-vertical-figure-8</value></prop></node></node><node oor:name="Misc"><node oor:name="media" oor:op="replace"><prop oor:name="Label" oor:type="xs:string"><value xml:lang="en-US">Media</value></prop><prop oor:name="Effects" oor:type="oor:string-list"><value oor:separator=";">ooo-media-start;ooo-media-toggle-pause;ooo-media-stop</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="ImpressWindowState" oor:package="org.openoffice.Office.UI"><node oor:name="UIElements"><node oor:name="States"><node oor:name="private:resource/toolbar/extrusionobjectbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Settings</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/drawingobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Line and Filling</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/3dobjectsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">3D-Objects</value></prop></node><node oor:name="private:resource/toolbar/alignmentbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Align</value></prop></node><node oor:name="private:resource/toolbar/arrowsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Arrows</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/choosemodebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Mode</value></prop></node><node oor:name="private:resource/toolbar/commontaskbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>1,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Presentation</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/connectorsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Connectors</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fullscreenbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Style" oor:type="xs:int"><value>2</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Full Screen</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/ellipsesbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Circles and Ovals</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/formtextobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Box Formatting</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsfilterbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Filter</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formsnavigationbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Navigation</value></prop><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/moreformcontrols" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">More Controls</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/formdesign" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Form Design</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/fontworkobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/fontworkshapetype" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Fontwork Shape</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graphicobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Picture</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/graffilterbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Graphic Filter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/outlinetoolbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>1,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Outline</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/insertbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Insert</value></prop></node><node oor:name="private:resource/toolbar/linesbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Lines</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/basicshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Basic Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/symbolshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Symbol Shapes</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/arrowshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Block Arrows</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/flowchartshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Flowchart</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/calloutshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Callouts</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/starshapes" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Stars and Banners</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optionsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Options</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/rectanglesbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Rectangles</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/positionbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Position</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/slideviewtoolbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Slide Sorter</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/slideviewobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Slide View</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/standardbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/textbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/textobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Text Formatting</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/toolbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Drawing</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/tableobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Table</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/zoombar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Zoom</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node><node oor:name="private:resource/toolbar/gluepointsobjectbar" oor:op="replace"><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Gluepoints</value></prop><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/bezierobjectbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Edit Points</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/viewerbar" oor:op="replace"><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Standard (Viewing Mode)</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/mediaobjectbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>0,1</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>1</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Media Playback</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/colorbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Color</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/commentsbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Comments</value></prop></node><node oor:name="private:resource/toolbar/masterviewtoolbar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="Locked" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="Pos" oor:type="xs:string"><value>500,100</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Master View</value></prop><prop oor:name="NoClose" oor:type="xs:boolean"><value>true</value></prop><prop oor:name="ContextSensitive" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/optimizetablebar" oor:op="replace"><prop oor:name="Docked" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Optimize</value></prop><prop oor:name="HideFromToolbarMenu" oor:type="xs:boolean"><value>true</value></prop></node><node oor:name="private:resource/toolbar/findbar" oor:op="replace"><prop oor:name="DockPos" oor:type="xs:string"><value>1,0</value></prop><prop oor:name="DockingArea" oor:type="xs:int"><value>0</value></prop><prop oor:name="UIName" oor:type="xs:string"><value xml:lang="en-US">Find</value></prop><prop oor:name="Visible" oor:type="xs:boolean"><value>false</value></prop></node></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="MS PowerPoint 97" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>sdfilt</value></prop><prop oor:name="UIName"><value xml:lang="x-default">Microsoft PowerPoint 97/2000/XP</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>impress_MS_PowerPoint_97</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="MS PowerPoint 97 Vorlage" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>sdfilt</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>impress_MS_PowerPoint_97_Vorlage</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_StarOffice_XML_Draw" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>XML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>draw_StarOffice_XML_Draw</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="StarOffice XML (Impress)" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN ALIEN PREFERRED ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>XML</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>impress_StarOffice_XML_Impress</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_StarOffice_XML_Impress_Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH OWN ALIEN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLV</value></prop><prop oor:name="FileFormatVersion"><value>6200</value></prop><prop oor:name="Type"><value>impress_StarOffice_XML_Impress_Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_pdf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.PDF.PDFDialog</value></prop><prop oor:name="FilterService"><value>com.sun.star.comp.PDF.PDFFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PDF - Portable Document Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pdf_Portable_Document_Format</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="placeware_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Impress.PlaceWareExportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PWP - PlaceWare</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pwp_PlaceWare</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress8" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN DEFAULT PREFERRED ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>XML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>impress8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="NSO Impress UOF2" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Writer.XmlFilterAdaptor</value></prop><prop oor:name="UserData"><value oor:separator=",">com.sun.star.documentconversion.XSLTFilter,,com.sun.star.comp.Impress.XMLOasisImporter,com.sun.star.comp.Impress.XMLOasisExporter,../share/xslt/import/uof2/uof2odf.xsl,../share/xslt/export/uof2/odf2uof.xsl</value></prop><prop oor:name="FileFormatVersion"><value>1</value></prop><prop oor:name="Type"><value>impress_NSO_UOF2</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress8_template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE TEMPLATEPATH OWN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>CXMLV</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>impress8_template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress8_draw" oor:op="replace"><prop oor:name="Flags"><value>IMPORT EXPORT TEMPLATE OWN ENCRYPTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>XML</value></prop><prop oor:name="FileFormatVersion"><value>6800</value></prop><prop oor:name="Type"><value>draw8</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="Impress MS PowerPoint 2007 XML" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.oox.ppt.PowerPointImport</value></prop><prop oor:name="UserData"/><prop oor:name="FileFormatVersion"/><prop oor:name="Type"><value>MS PowerPoint 2007 XML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="Impress MS PowerPoint 2007 XML Template" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN 3RDPARTYFILTER TEMPLATE TEMPLATEPATH PREFERRED</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.oox.ppt.PowerPointImport</value></prop><prop oor:name="UserData"/><prop oor:name="FileFormatVersion"/><prop oor:name="Type"><value>MS PowerPoint 2007 XML Template</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="draw_StarOffice_XML_Draw" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxd</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.draw</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>StarOffice XML (Draw)</value></prop><prop oor:name="UIName"><value>%oooxmlformatname% %oooxmlformatversion% Zeichnung</value></prop><prop oor:name="ClipboardFormat"><value>Draw 6.0</value></prop></node><node oor:name="impress_MS_PowerPoint_97" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ppt pps</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-powerpoint</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS PowerPoint 97</value></prop><prop oor:name="UIName"><value>Microsoft PowerPoint 97/2000/XP</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="impress_MS_PowerPoint_97_Vorlage" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pot</value></prop><prop oor:name="MediaType"><value>application/vnd.ms-powerpoint</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MS PowerPoint 97 Vorlage</value></prop><prop oor:name="UIName"><value>MS PowerPoint 97/2000 Template</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="impress_StarOffice_XML_Impress" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sxi</value></prop><prop oor:name="MediaType"><value>application/vnd.sun.xml.impress</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>StarOffice XML (Impress)</value></prop><prop oor:name="UIName"><value>%oooxmlformatname% %oooxmlformatversion% Präsentation</value></prop><prop oor:name="ClipboardFormat"><value>Impress 6.0</value></prop></node><node oor:name="impress_StarOffice_XML_Impress_Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sti</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>impress_StarOffice_XML_Impress_Template</value></prop><prop oor:name="UIName"><value>Impress 6.0 Template</value></prop><prop oor:name="ClipboardFormat"><value>Impress 6.0</value></prop></node><node oor:name="pdf_Portable_Document_Format" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pdf</value></prop><prop oor:name="MediaType"><value>application/pdf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>PDF - Portable Document Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pwp_PlaceWare" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pwp</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>PWP - PlaceWare</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="impress8" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"><value>private:factory/simpress*</value></prop><prop oor:name="Extensions"><value>odp</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.presentation</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>impress8</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Impress 8</value></prop><prop oor:name="ClipboardFormat"><value>Impress 8</value></prop></node><node oor:name="impress_NSO_UOF2" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.filters.XMLFilterDetect</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>uop</value></prop><prop oor:name="MediaType"><value>application/xml</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>NSO Impress UOF2</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Uniform Office Format 2 presentation</value></prop><prop oor:name="ClipboardFormat"><value>doctype:uop:UOF2</value></prop></node><node oor:name="impress8_template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>otp</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.presentation-template</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>impress8_template</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Impress 8 Template</value></prop><prop oor:name="ClipboardFormat"><value>Impress 8 Template</value></prop></node><node oor:name="draw8" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"><value>private:factory/sdraw*</value></prop><prop oor:name="Extensions"><value>odg</value></prop><prop oor:name="MediaType"><value>application/vnd.oasis.opendocument.graphics</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>draw8</value></prop><prop oor:name="UIName"><value xml:lang="en-US">Draw 8</value></prop><prop oor:name="ClipboardFormat"><value>Draw 8</value></prop></node><node oor:name="MS PowerPoint 2007 XML" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pptm pptx</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Impress MS PowerPoint 2007 XML</value></prop><prop oor:name="UIName"><value xml:lang="x-default">Microsoft PowerPoint 2007 XML</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="MS PowerPoint 2007 XML Template" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.oox.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>potm potx</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>true</value></prop><prop oor:name="PreferredFilter"><value>Impress MS PowerPoint 2007 XML Template</value></prop><prop oor:name="UIName"><value>Microsoft PowerPoint 2007 XML Template</value></prop><prop oor:name="ClipboardFormat"/></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Common" oor:package="org.openoffice.Office"><node oor:name="Menus"><node oor:name="New"><node oor:name="m2" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>private:factory/simpress?slot=6686</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Presentation</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_default</value></prop></node></node><node oor:name="Wizard"><node oor:name="m5" oor:op="replace"><prop oor:name="URL" oor:type="xs:string"><value>slot:10425</value></prop><prop oor:name="Title"><value xml:lang="en-US">~Presentation...</value></prop><prop oor:name="TargetName" oor:type="xs:string"><value>_self</value></prop><prop oor:name="ImageIdentifier" oor:type="xs:string"><value>private:image/3216</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:package="org.openoffice.Office" oor:name="Embedding"><node oor:name="Objects"><node oor:name="9176E48A-637A-4D1F-803B-99D9BFAC1047" oor:op="replace"><prop oor:name="ObjectFactory"><value>com.sun.star.embed.OOoEmbeddedObjectFactory</value></prop><prop oor:name="ObjectDocumentServiceName"><value>com.sun.star.presentation.PresentationDocument</value></prop><prop oor:name="ObjectMiscStatus"/><prop oor:name="ObjectVerbs"><value>PRIMARY SHOW OPEN HIDE UIACTIVATE IPACTIVATE SAVECOPYAS</value></prop></node></node><node oor:name="ObjectNames"><node oor:name="Impress" oor:op="replace"><prop oor:name="ObjectUIName"><value xml:lang="en-US">%PRODUCTNAME %PRODUCTVERSION Presentation</value></prop><prop oor:name="ClassID"><value>9176E48A-637A-4D1F-803B-99D9BFAC1047</value></prop></node></node></oor:component-data><oor:component-data oor:name="Jobs" oor:package="org.openoffice.Office"><node oor:name="Jobs"><node oor:name="com.sun.star.presentation.PresenterScreenJob" oor:op="replace"><prop oor:name="Service"><value>com.sun.star.comp.presentation.PresenterScreenJob</value></prop><prop oor:name="Context"><value>com.sun.star.presentation.PresentationDocument</value></prop></node></node><node oor:name="Events"><node oor:name="onDocumentOpened" oor:op="fuse"><node oor:name="JobList"><node oor:name="com.sun.star.presentation.PresenterScreenJob" oor:op="replace"/></node></node></node></oor:component-data><oor:component-data oor:name="ProtocolHandler" oor:package="org.openoffice.Office"><node oor:name="HandlerSet"><node oor:name="com.sun.star.comp.presentation.PresenterProtocolHandler" oor:op="replace"><prop oor:name="Protocols"><value>vnd.com.sun.star.presentation.PresenterScreen:*</value></prop></node></node></oor:component-data><oor:component-schema xml:lang="en-US" oor:package="org.openoffice.Office" oor:name="PresenterScreen"><templates><group oor:name="BitmapDescriptor"><prop oor:name="Name" oor:type="xs:string"/><prop oor:name="Text" oor:type="xs:string" oor:localized="true"/><prop oor:name="NormalFileName" oor:type="xs:string"/><prop oor:name="MouseOverFileName" oor:type="xs:string"/><prop oor:name="ButtonDownFileName" oor:type="xs:string"/><prop oor:name="DisabledFileName" oor:type="xs:string"/><prop oor:name="SelectedFileName" oor:type="xs:string"/><prop oor:name="MaskFileName" oor:type="xs:string"><value/></prop><prop oor:name="XOffset" oor:type="xs:int"><value>0</value></prop><prop oor:name="YOffset" oor:type="xs:int"><value>0</value></prop><prop oor:name="XHotSpot" oor:type="xs:int"/><prop oor:name="YHotSpot" oor:type="xs:int"/><prop oor:name="ReplacementColor" oor:type="xs:hexBinary"/><prop oor:name="HorizontalTexturingMode" oor:type="xs:string"/><prop oor:name="VerticalTexturingMode" oor:type="xs:string"><value>Once</value></prop></group><group oor:name="ThemeBorderSize"><prop oor:name="Left" oor:type="xs:int"/><prop oor:name="Top" oor:type="xs:int"/><prop oor:name="Right" oor:type="xs:int"/><prop oor:name="Bottom" oor:type="xs:int"/></group><group oor:name="Font"><prop oor:name="FamilyName" oor:type="xs:string"/><prop oor:name="Size" oor:type="xs:int"/><prop oor:name="Style" oor:type="xs:string"/><prop oor:name="Color" oor:type="xs:hexBinary"/><prop oor:name="Anchor" oor:type="xs:string"/><prop oor:name="XOffset" oor:type="xs:int"/><prop oor:name="YOffset" oor:type="xs:int"/></group><group oor:name="ToolBarDescription"><node-ref oor:name="Font" oor:node-type="Font"/><node-ref oor:name="Icon" oor:node-type="BitmapDescriptor"/><prop oor:name="Text" oor:type="xs:string" oor:localized="true"/><prop oor:name="Action" oor:type="xs:string"/></group><group oor:name="ToolBarEntry"><prop oor:name="Name" oor:type="xs:string"/><prop oor:name="Type" oor:type="xs:string"/><node-ref oor:name="Normal" oor:node-type="ToolBarDescription"/><node-ref oor:name="MouseOver" oor:node-type="ToolBarDescription"/><node-ref oor:name="Selected" oor:node-type="ToolBarDescription"/><node-ref oor:name="Disabled" oor:node-type="ToolBarDescription"/></group><group oor:name="ToolBar"><set oor:name="Entries" oor:node-type="ToolBarEntry"/></group><group oor:name="Button"><prop oor:name="Name" oor:type="xs:string"/><node-ref oor:name="Font" oor:node-type="Font"/><node-ref oor:name="Icon" oor:node-type="BitmapDescriptor"/><prop oor:name="Text" oor:type="xs:string" oor:localized="true"/><prop oor:name="Action" oor:type="xs:string"/></group><group oor:name="HelpEntry"><prop oor:name="Left" oor:type="xs:string" oor:localized="true"/><prop oor:name="Right" oor:type="xs:string" oor:localized="true"/></group><group oor:name="PaneStyle"><prop oor:name="StyleName" oor:type="xs:string"/><prop oor:name="ParentStyle" oor:type="xs:string"><value/></prop><node-ref oor:name="TitleFont" oor:node-type="Font"/><node-ref oor:name="InnerBorderSize" oor:node-type="ThemeBorderSize"/><node-ref oor:name="OuterBorderSize" oor:node-type="ThemeBorderSize"/><set oor:name="BorderBitmapList" oor:node-type="BitmapDescriptor"/></group><group oor:name="ViewStyle"><prop oor:name="StyleName" oor:type="xs:string"/><prop oor:name="ParentStyle" oor:type="xs:string"><value/></prop><node-ref oor:name="Font" oor:node-type="Font"/><node-ref oor:name="Background" oor:node-type="BitmapDescriptor"/></group><group oor:name="Style_Association"><prop oor:name="ResourceURL" oor:type="xs:string"/><prop oor:name="StyleName" oor:type="xs:string"/></group><group oor:name="PresenterString"><prop oor:name="String" oor:type="xs:string" oor:localized="true"/></group><group oor:name="ViewDescription"><prop oor:name="ViewURL" oor:type="xs:string"/><prop oor:name="StyleName" oor:type="xs:string"/><prop oor:name="Title" oor:type="xs:string" oor:localized="true"><value/></prop><prop oor:name="AccessibleTitle" oor:type="xs:string" oor:localized="true"><value/></prop><prop oor:name="IsOpaque" oor:type="xs:boolean"><value>false</value></prop><set oor:name="Strings" oor:node-type="PresenterString"/></group><group oor:name="PresenterTheme"><prop oor:name="ThemeName" oor:type="xs:string"/><prop oor:name="ParentTheme" oor:type="xs:string"><value/></prop><prop oor:name="BitmapSourceExtension" oor:type="xs:string"><value/></prop><node-ref oor:name="Background" oor:node-type="BitmapDescriptor"/><set oor:name="PaneStyles" oor:node-type="PaneStyle"/><set oor:name="ViewStyles" oor:node-type="ViewStyle"/><set oor:name="StyleAssociations" oor:node-type="Style_Association"/><set oor:name="Bitmaps" oor:node-type="BitmapDescriptor"/><set oor:name="Fonts" oor:node-type="Font"/></group><group oor:name="PresenterComponentLayout"><prop oor:name="PaneURL" oor:type="xs:string"/><prop oor:name="ViewURL" oor:type="xs:string"/><prop oor:name="RelativeX" oor:type="xs:double"/><prop oor:name="RelativeY" oor:type="xs:double"/><prop oor:name="RelativeWidth" oor:type="xs:double"/><prop oor:name="RelativeHeight" oor:type="xs:double"/></group><group oor:name="PresenterLayout"><prop oor:name="LayoutName" oor:type="xs:string"/><prop oor:name="ParentLayout" oor:type="xs:string"><value/></prop><set oor:name="Layout" oor:node-type="PresenterComponentLayout"/></group></templates><component><group oor:name="PresenterScreenSettings"><set oor:name="ToolBars" oor:node-type="ToolBar"/><set oor:name="Buttons" oor:node-type="Button"/><group oor:name="ScrollBar"><set oor:name="Bitmaps" oor:node-type="BitmapDescriptor"/></group><group oor:name="SlideSorter"><node-ref oor:name="BorderSize" oor:node-type="ThemeBorderSize"/><set oor:name="CurrentSlideBorderBitmaps" oor:node-type="BitmapDescriptor"/></group><group oor:name="HelpView"><set oor:name="HelpStrings" oor:node-type="HelpEntry"/></group></group><group oor:name="Presenter"><prop oor:name="StartAlways" oor:type="xs:boolean"><value>false</value></prop><set oor:name="Themes" oor:node-type="PresenterTheme"/><prop oor:name="CurrentTheme" oor:type="xs:string"><value>DefaultTheme</value></prop><set oor:name="Views" oor:node-type="ViewDescription"/><set oor:name="Layouts" oor:node-type="PresenterLayout"/><prop oor:name="CurrentLayout" oor:type="xs:string"><value>DefaultLayout</value></prop><set oor:name="Accessibility" oor:node-type="PresenterString"/><prop oor:name="InitialViewMode" oor:type="xs:int"><value>0</value></prop></group></component></oor:component-schema><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:package="org.openoffice.Office" oor:name="PresenterScreen"><node oor:name="PresenterScreenSettings"><node oor:name="ToolBars"><node oor:name="ToolBar" oor:op="replace"><node oor:name="Entries"><node oor:name="a" oor:op="replace"><prop oor:name="Name"><value>PreviousSlide</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Previous</value></prop><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlidePreviousNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:PrevSlide</value></prop><node oor:name="Font"><prop oor:name="Size"><value>12</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlidePreviousMouseOver.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlidePreviousDisabled.png</value></prop></node></node></node><node oor:name="b" oor:op="replace"><prop oor:name="Name"><value>NextEffect</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Next</value></prop><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonEffectNextNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:NextEffect</value></prop><node oor:name="Font"><prop oor:name="Size"><value>12</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonEffectNextMouseOver.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonEffectNextDisabled.png</value></prop></node></node></node><node oor:name="c" oor:op="replace"><prop oor:name="Name"><value>Notes</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Notes</value></prop><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonNotesNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:ShowNotes</value></prop><node oor:name="Font"><prop oor:name="Size"><value>12</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonNotesMouseOver.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node></node><node oor:name="Selected"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonNotesSelected.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseNotes</value></prop></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonNotesDisabled.png</value></prop></node></node></node><node oor:name="d" oor:op="replace"><prop oor:name="Name"><value>SlideSorter</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Slides</value></prop><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlideSorterNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:ShowSlideSorter</value></prop><node oor:name="Font"><prop oor:name="Size"><value>12</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlideSorterMouseOver.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node></node><node oor:name="Selected"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlideSorterSelected.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseSlideSorter</value></prop></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlideSorterDisabled.png</value></prop></node></node></node><node oor:name="e" oor:op="replace"><prop oor:name="Type"><value>VerticalSeparator</value></prop><node oor:name="Normal"><node oor:name="Font"><prop oor:name="Color"><value>76797C</value></prop></node></node></node><node oor:name="f" oor:op="replace"><prop oor:name="Type"><value>ChangeOrientation</value></prop></node><node oor:name="g" oor:op="replace"><prop oor:name="Type"><value>CurrentTimeLabel</value></prop><node oor:name="Normal"><node oor:name="Font"><prop oor:name="Size"><value>18</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>ffffff</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node></node><node oor:name="h" oor:op="replace"><prop oor:name="Type"><value>HorizontalSeparator</value></prop><node oor:name="Normal"><node oor:name="Font"><prop oor:name="Color"><value>71767a</value></prop></node></node></node><node oor:name="i" oor:op="replace"><prop oor:name="Type"><value>PresentationTimeLabel</value></prop><node oor:name="Normal"><node oor:name="Font"><prop oor:name="Size"><value>26</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>ffe969</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node></node><node oor:name="j" oor:op="replace"><prop oor:name="Type"><value>ChangeOrientation</value></prop></node><node oor:name="k" oor:op="replace"><prop oor:name="Type"><value>VerticalSeparator</value></prop><node oor:name="Normal"><node oor:name="Font"><prop oor:name="Color"><value>76797C</value></prop></node></node></node><node oor:name="l" oor:op="replace"><prop oor:name="Name"><value>Help</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Help</value></prop><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonHelpNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:ShowHelp</value></prop><node oor:name="Font"><prop oor:name="Size"><value>12</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop></node></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonHelpMouseOver.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node></node><node oor:name="Selected"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonHelpSelected.png</value></prop></node><node oor:name="Font"><prop oor:name="Color"><value>FFFFFF</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseHelp</value></prop></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonHelpDisabled.png</value></prop></node></node></node></node></node><node oor:name="NotesToolBar" oor:op="replace"><node oor:name="Entries"><node oor:name="a" oor:op="replace"><prop oor:name="Name"><value>Zoom</value></prop><prop oor:name="Type"><value>Label</value></prop><node oor:name="Normal"><prop oor:name="Text"><value xml:lang="en-US">Zoom</value></prop><node oor:name="Font"><prop oor:name="Size"><value>14</value></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop></node></node></node><node oor:name="b" oor:op="replace"><prop oor:name="Name"><value>Grow</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonPlusNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:GrowNotesFont</value></prop></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonPlusMouseOver.png</value></prop></node></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonPlusDisabled.png</value></prop></node></node></node><node oor:name="c" oor:op="replace"><prop oor:name="Name"><value>Shrink</value></prop><prop oor:name="Type"><value>Button</value></prop><node oor:name="Normal"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonMinusNormal.png</value></prop></node><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:ShrinkNotesFont</value></prop></node><node oor:name="MouseOver"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonMinusMouseOver.png</value></prop></node></node><node oor:name="Disabled"><node oor:name="Icon"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonMinusDisabled.png</value></prop></node></node></node></node></node></node><node oor:name="Buttons"><node oor:name="SlideSorterCloser" oor:op="replace"><prop oor:name="Name"><value>SlideSorterCloser</value></prop><prop oor:name="Text"><value xml:lang="en-US">Close</value></prop><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseSlideSorter</value></prop></node><node oor:name="NotesViewCloser" oor:op="replace"><prop oor:name="Name"><value>NotesViewCloser</value></prop><prop oor:name="Text"><value xml:lang="en-US">Close</value></prop><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseNotes</value></prop></node><node oor:name="HelpViewCloser" oor:op="replace"><prop oor:name="Name"><value>HelpViewCloser</value></prop><prop oor:name="Text"><value xml:lang="en-US">Close</value></prop><prop oor:name="Action"><value>vnd.com.sun.star.presentation.PresenterScreen:CloseHelp</value></prop></node></node><node oor:name="ScrollBar"><node oor:name="Bitmaps"><node oor:name="up" oor:op="replace"><prop oor:name="Name"><value>Up</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarArrowUpNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarArrowUpMouseOver.png</value></prop><prop oor:name="DisabledFileName"><value>private:graphicrepository/presenter/ScrollbarArrowUpDisabled.png</value></prop></node><node oor:name="down" oor:op="replace"><prop oor:name="Name"><value>Down</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarArrowDownNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarArrowDownMouseOver.png</value></prop><prop oor:name="DisabledFileName"><value>private:graphicrepository/presenter/ScrollbarArrowDownDisabled.png</value></prop></node><node oor:name="pagervertical" oor:op="replace"><prop oor:name="Name"><value>PagerVertical</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarPagerMiddleNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarPagerMiddleMouseOver.png</value></prop></node><node oor:name="thumbtop" oor:op="replace"><prop oor:name="Name"><value>ThumbTop</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarThumbTopNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarThumbTopMouseOver.png</value></prop></node><node oor:name="thumbbottom" oor:op="replace"><prop oor:name="Name"><value>ThumbBottom</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarThumbBottomNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarThumbBottomMouseOver.png</value></prop></node><node oor:name="thumbvertical" oor:op="replace"><prop oor:name="Name"><value>ThumbVertical</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ScrollbarThumbMiddleNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ScrollbarThumbMiddleMouseOver.png</value></prop></node></node></node><node oor:name="SlideSorter"><node oor:name="BorderSize"><prop oor:name="Left"><value>9</value></prop><prop oor:name="Top"><value>9</value></prop><prop oor:name="Right"><value>9</value></prop><prop oor:name="Bottom"><value>9</value></prop></node><node oor:name="CurrentSlideBorderBitmaps"><node oor:name="TopLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideTopLeft.png</value></prop></node><node oor:name="Top" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideTop.png</value></prop></node><node oor:name="TopRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideTopRight.png</value></prop></node><node oor:name="Left" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideLeft.png</value></prop></node><node oor:name="Right" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideRight.png</value></prop></node><node oor:name="BottomLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideBottomLeft.png</value></prop></node><node oor:name="Bottom" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideBottom.png</value></prop></node><node oor:name="BottomRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderCurrentSlideBottomRight.png</value></prop></node></node></node><node oor:name="HelpView"><node oor:name="HelpStrings"><node oor:name="a" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Left click, right or down arrow, spacebar, page down, enter, return, 'N'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Next slide, or next effect</value></prop></node><node oor:name="b" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Right click, left or up arrow, page up, backspace, 'P'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Previous slide, or previous effect</value></prop></node><node oor:name="c" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="d" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Home</value></prop><prop oor:name="Right"><value xml:lang="en-US">First slide</value></prop></node><node oor:name="e" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">End</value></prop><prop oor:name="Right"><value xml:lang="en-US">Last slide</value></prop></node><node oor:name="f" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="g" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Alt-Page Up</value></prop><prop oor:name="Right"><value xml:lang="en-US">Previous slide without effects</value></prop></node><node oor:name="h" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Alt-Page Down</value></prop><prop oor:name="Right"><value xml:lang="en-US">Next slide without effects</value></prop></node><node oor:name="i" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="j" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">'B', '.'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Blacks/Unblacks the screen</value></prop></node><node oor:name="k" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">'W', ','</value></prop><prop oor:name="Right"><value xml:lang="en-US">Whites/Unwhites the screen</value></prop></node><node oor:name="l" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="m" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Esc, '-'</value></prop><prop oor:name="Right"><value xml:lang="en-US">End slide show</value></prop></node><node oor:name="n" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="o" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Number followed by Enter</value></prop><prop oor:name="Right"><value xml:lang="en-US">Go to that slide</value></prop></node><node oor:name="p" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="q" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">'G', 'S'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Grow/Shrink size of notes font</value></prop></node><node oor:name="r" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">'A', 'Z'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Scroll notes up/down</value></prop></node><node oor:name="s" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">'H', 'L'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Move caret in notes view backward/forward</value></prop></node><node oor:name="t" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US"> </value></prop><prop oor:name="Right"><value xml:lang="en-US"> </value></prop></node><node oor:name="u" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Ctrl-'1'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Shows the Presenter Console</value></prop></node><node oor:name="v" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Ctrl-'2'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Shows the Presentation Notes</value></prop></node><node oor:name="w" oor:op="replace"><prop oor:name="Left"><value xml:lang="en-US">Ctrl-'3'</value></prop><prop oor:name="Right"><value xml:lang="en-US">Shows the Slides Overview</value></prop></node></node></node></node><node oor:name="Presenter"><node oor:name="Themes"><node oor:name="Theme_1" oor:op="replace"><prop oor:name="ThemeName"><value>DefaultTheme</value></prop><node oor:name="PaneStyles"><node oor:name="PaneStyle_1" oor:op="replace"><prop oor:name="StyleName"><value>DefaultPaneStyle</value></prop><node oor:name="TitleFont"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>14</value></prop><prop oor:name="Color"><value>B3B7BC</value></prop><prop oor:name="Anchor"><value>Center</value></prop><prop oor:name="YOffset"><value>-7</value></prop></node><node oor:name="InnerBorderSize"><prop oor:name="Left"><value>6</value></prop><prop oor:name="Top"><value>6</value></prop><prop oor:name="Right"><value>6</value></prop><prop oor:name="Bottom"><value>6</value></prop></node><node oor:name="OuterBorderSize"><prop oor:name="Left"><value>20</value></prop><prop oor:name="Top"><value>28</value></prop><prop oor:name="Right"><value>20</value></prop><prop oor:name="Bottom"><value>29</value></prop></node><node oor:name="BorderBitmapList"><node oor:name="Top" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderTop.png</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="TopLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderTopLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="TopRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderTopRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="Left" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop></node><node oor:name="Right" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop></node><node oor:name="BottomLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderBottomLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node><node oor:name="BottomRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderBottomRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node><node oor:name="Bottom" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderBottom.png</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node></node></node><node oor:name="PaneStyle_2" oor:op="replace"><prop oor:name="StyleName"><value>ActivePaneStyle</value></prop><prop oor:name="ParentStyle"><value>DefaultPaneStyle</value></prop><node oor:name="TitleFont"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>14</value></prop><prop oor:name="Color"><value>FFFFFF</value></prop><prop oor:name="Anchor"><value>Center</value></prop><prop oor:name="YOffset"><value>-10</value></prop></node><node oor:name="BorderBitmapList"><node oor:name="Top" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveTop.png</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="TopLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveTopLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="TopRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveTopRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop><prop oor:name="YOffset"><value>6</value></prop></node><node oor:name="Left" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop></node><node oor:name="Right" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop></node><node oor:name="BottomLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveBottomLeft.png</value></prop><prop oor:name="XOffset"><value>6</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node><node oor:name="BottomRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveBottomRight.png</value></prop><prop oor:name="XOffset"><value>-6</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node><node oor:name="Bottom" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveBottom.png</value></prop><prop oor:name="YOffset"><value>-6</value></prop></node></node></node><node oor:name="PaneStyle_3" oor:op="replace"><prop oor:name="StyleName"><value>ToolbarPaneStyle</value></prop><node oor:name="InnerBorderSize"><prop oor:name="Left"><value>4</value></prop><prop oor:name="Top"><value>4</value></prop><prop oor:name="Right"><value>4</value></prop><prop oor:name="Bottom"><value>4</value></prop></node><node oor:name="OuterBorderSize"><prop oor:name="Left"><value>8</value></prop><prop oor:name="Top"><value>8</value></prop><prop oor:name="Right"><value>8</value></prop><prop oor:name="Bottom"><value>0</value></prop></node><node oor:name="BorderBitmapList"><node oor:name="Top" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarTop.png</value></prop><prop oor:name="YOffset"><value>4</value></prop></node><node oor:name="TopLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarTopLeft.png</value></prop><prop oor:name="XOffset"><value>4</value></prop><prop oor:name="YOffset"><value>4</value></prop></node><node oor:name="TopRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarTopRight.png</value></prop><prop oor:name="XOffset"><value>-4</value></prop><prop oor:name="YOffset"><value>4</value></prop></node><node oor:name="Left" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarLeft.png</value></prop><prop oor:name="XOffset"><value>4</value></prop></node><node oor:name="Right" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarRight.png</value></prop><prop oor:name="XOffset"><value>-4</value></prop></node><node oor:name="BottomLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarLeft.png</value></prop><prop oor:name="XOffset"><value>4</value></prop></node><node oor:name="BottomRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarRight.png</value></prop><prop oor:name="XOffset"><value>-4</value></prop></node><node oor:name="Bottom" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderToolbarBottom.png</value></prop><prop oor:name="YOffset"><value>-4</value></prop></node></node></node><node oor:name="PaneStyle_4" oor:op="replace"><prop oor:name="StyleName"><value>ActiveBottomCalloutPaneStyle</value></prop><prop oor:name="ParentStyle"><value>ActivePaneStyle</value></prop><node oor:name="OuterBorderSize"><prop oor:name="Bottom"><value>52</value></prop></node><node oor:name="BorderBitmapList"><node oor:name="BottomCallout" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/BorderActiveBottomCallout.png</value></prop><prop oor:name="YOffset"><value>-6</value></prop><prop oor:name="XHotSpot"><value>49</value></prop><prop oor:name="YHotSpot"><value>41</value></prop></node></node></node></node><node oor:name="ViewStyles"><node oor:name="ViewStyle_1" oor:op="replace"><prop oor:name="StyleName"><value>DefaultViewStyle</value></prop><node oor:name="Font"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Size"><value>20</value></prop><prop oor:name="Color"><value>FFFFFF</value></prop></node><node oor:name="Background"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ViewBackground.png</value></prop><prop oor:name="HorizontalTexturingMode"><value>Repeat</value></prop><prop oor:name="VerticalTexturingMode"><value>Repeat</value></prop><prop oor:name="ReplacementColor"><value>33000000</value></prop></node></node><node oor:name="ViewStyle_2" oor:op="replace"><prop oor:name="StyleName"><value>NotesViewStyle</value></prop><prop oor:name="ParentStyle"><value>DefaultViewStyle</value></prop><node oor:name="Font"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>26</value></prop><prop oor:name="Color"><value>d1d6dc</value></prop></node></node><node oor:name="ViewStyle_3" oor:op="replace"><prop oor:name="StyleName"><value>SlideSorterViewStyle</value></prop><prop oor:name="ParentStyle"><value>DefaultViewStyle</value></prop></node><node oor:name="ViewStyle_4" oor:op="replace"><prop oor:name="StyleName"><value>HelpViewStyle</value></prop><prop oor:name="ParentStyle"><value>DefaultViewStyle</value></prop><node oor:name="Font"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>20</value></prop><prop oor:name="Color"><value>ffffff</value></prop></node></node></node><node oor:name="Background"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/Background.png</value></prop><prop oor:name="ReplacementColor"><value>E4EFF9</value></prop><prop oor:name="HorizontalTexturingMode"><value>Repeat</value></prop><prop oor:name="VerticalTexturingMode"><value>Stretch</value></prop></node><node oor:name="Bitmaps"><node oor:name="ButtonFrameLeft" oor:op="replace"><prop oor:name="Name"><value>ButtonFrameLeft</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonFrameLeftNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ButtonFrameLeftMouseOver.png</value></prop><prop oor:name="YOffset"><value>2</value></prop></node><node oor:name="ButtonFrameCenter" oor:op="replace"><prop oor:name="Name"><value>ButtonFrameCenter</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonFrameCenterNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ButtonFrameCenterMouseOver.png</value></prop><prop oor:name="YOffset"><value>2</value></prop></node><node oor:name="ButtonFrameRight" oor:op="replace"><prop oor:name="Name"><value>ButtonFrameRight</value></prop><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonFrameRightNormal.png</value></prop><prop oor:name="MouseOverFileName"><value>private:graphicrepository/presenter/ButtonFrameRightMouseOver.png</value></prop><prop oor:name="YOffset"><value>2</value></prop></node><node oor:name="LabelLeft" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/LabelMouseOverLeft.png</value></prop></node><node oor:name="LabelCenter" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/LabelMouseOverCenter.png</value></prop></node><node oor:name="LabelRight" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/LabelMouseOverRight.png</value></prop></node><node oor:name="MousePointerNextEffect" oor:op="replace"><prop oor:name="NormalFileName"><value>private:graphicrepository/presenter/ButtonSlideNextNormal.png</value></prop></node></node><node oor:name="Fonts"><node oor:name="ButtonFont" oor:op="replace"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>18</value></prop><prop oor:name="Color"><value>b3b7bc</value></prop></node><node oor:name="ButtonMouseOverFont" oor:op="replace"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>18</value></prop><prop oor:name="Color"><value>ffffff</value></prop></node><node oor:name="SlideSorterLabelFont" oor:op="replace"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>20</value></prop><prop oor:name="Color"><value>ffffff</value></prop></node><node oor:name="PendingSlideNumberFont" oor:op="replace"><prop oor:name="FamilyName"><value/></prop><prop oor:name="Style"><value>Bold</value></prop><prop oor:name="Size"><value>24</value></prop><prop oor:name="Color"><value>e02050</value></prop></node></node><node oor:name="StyleAssociations"><node oor:name="PreviewPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Pane1</value></prop><prop oor:name="StyleName"><value>ActivePaneStyle</value></prop></node><node oor:name="PreviewView" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/CurrentSlidePreview</value></prop><prop oor:name="StyleName"><value>DefaultViewStyle</value></prop></node><node oor:name="NextSlidePreviewPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Pane2</value></prop><prop oor:name="StyleName"><value>DefaultPaneStyle</value></prop></node><node oor:name="NextSlidePreviewView" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/NextSlidePreview</value></prop><prop oor:name="StyleName"><value>DefaultViewStyle</value></prop></node><node oor:name="ToolBarPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Pane4</value></prop><prop oor:name="StyleName"><value>ToolbarPaneStyle</value></prop></node><node oor:name="ToolBarView" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/ToolBar</value></prop><prop oor:name="StyleName"><value>DefaultViewStyle</value></prop></node><node oor:name="NotesPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Pane3</value></prop><prop oor:name="StyleName"><value>ActivePaneStyle</value></prop></node><node oor:name="NotesView" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/Notes</value></prop><prop oor:name="StyleName"><value>NotesViewStyle</value></prop></node><node oor:name="SlideSorter" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/SlideSorter</value></prop><prop oor:name="StyleName"><value>SlideSorterViewStyle</value></prop></node><node oor:name="OverlayPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Overlay</value></prop><prop oor:name="StyleName"><value>ActivePaneStyle</value></prop></node><node oor:name="HelpView" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/view/Presenter/Help</value></prop><prop oor:name="StyleName"><value>HelpViewStyle</value></prop></node><node oor:name="HelpPane" oor:op="replace"><prop oor:name="ResourceURL"><value>private:resource/pane/Presenter/Pane6</value></prop><prop oor:name="StyleName"><value>ActivePaneStyle</value></prop></node></node></node></node><node oor:name="Views"><node oor:name="CurrentSlidePreview" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/CurrentSlidePreview</value></prop><prop oor:name="Title"><value xml:lang="en-US">Current Slide (%CURRENT_SLIDE_NUMBER% of %SLIDE_COUNT%)</value></prop><prop oor:name="AccessibleTitle"><value xml:lang="en-US">Current Slide, %CURRENT_SLIDE_NAME%, %CURRENT_SLIDE_NUMBER% of %SLIDE_COUNT%</value></prop><prop oor:name="IsOpaque"><value>true</value></prop><node oor:name="Strings"><node oor:name="ClickToExitPresentationText" oor:op="replace"><prop oor:name="String"><value xml:lang="en-US">Click to exit presentation...</value></prop></node><node oor:name="ClickToExitPresentationTitle" oor:op="replace"><prop oor:name="String"><value xml:lang="en-US">Current Slide (end)</value></prop></node></node></node><node oor:name="NextSlidePreview" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/NextSlidePreview</value></prop><prop oor:name="Title"><value xml:lang="en-US">Next Slide</value></prop></node><node oor:name="ToolBar" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/ToolBar</value></prop></node><node oor:name="NotesView" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/Notes</value></prop><prop oor:name="Title"><value xml:lang="en-US">Notes</value></prop></node><node oor:name="SlideSorter" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/SlideSorter</value></prop><prop oor:name="Title"><value xml:lang="en-US"/></prop><prop oor:name="AccessibleTitle"><value xml:lang="en-US">Slide Overview, %CURRENT_SLIDE_NAME%, %CURRENT_SLIDE_NUMBER% of %SLIDE_COUNT%</value></prop></node><node oor:name="HelpView" oor:op="replace"><prop oor:name="ViewURL"><value>private:resource/view/Presenter/Help</value></prop><prop oor:name="Title"><value xml:lang="en-US">Help</value></prop></node></node><node oor:name="Layouts"><node oor:name="DefaultLayout" oor:op="replace"><prop oor:name="LayoutName"><value>DefaultLayout</value></prop><node oor:name="Layout"><node oor:name="CurrentSlidePreview" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Pane1</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/CurrentSlidePreview</value></prop><prop oor:name="RelativeX"><value>0.05</value></prop><prop oor:name="RelativeY"><value>0.05</value></prop><prop oor:name="RelativeWidth"><value>0.50</value></prop><prop oor:name="RelativeHeight"><value>0.50</value></prop></node><node oor:name="NextSlidePreview" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Pane2</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/NextSlidePreview</value></prop><prop oor:name="RelativeX"><value>0.60</value></prop><prop oor:name="RelativeY"><value>0.05</value></prop><prop oor:name="RelativeWidth"><value>0.35</value></prop><prop oor:name="RelativeHeight"><value>0.35</value></prop></node><node oor:name="ToolBar" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Pane4</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/ToolBar</value></prop><prop oor:name="RelativeX"><value>0.60</value></prop><prop oor:name="RelativeY"><value>0.45</value></prop><prop oor:name="RelativeWidth"><value>0.25</value></prop><prop oor:name="RelativeHeight"><value>0.1</value></prop></node><node oor:name="NotesView" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Pane3</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/Notes</value></prop><prop oor:name="RelativeX"><value>0.05</value></prop><prop oor:name="RelativeY"><value>0.60</value></prop><prop oor:name="RelativeWidth"><value>0.9</value></prop><prop oor:name="RelativeHeight"><value>0.35</value></prop></node><node oor:name="SlideSorter" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Overlay</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/SlideSorter</value></prop><prop oor:name="RelativeX"><value>0.05</value></prop><prop oor:name="RelativeY"><value>0.05</value></prop><prop oor:name="RelativeWidth"><value>0.95</value></prop><prop oor:name="RelativeHeight"><value>0.8</value></prop></node><node oor:name="HelpView" oor:op="replace"><prop oor:name="PaneURL"><value>private:resource/pane/Presenter/Pane6</value></prop><prop oor:name="ViewURL"><value>private:resource/view/Presenter/Help</value></prop><prop oor:name="RelativeX"><value>0.05</value></prop><prop oor:name="RelativeY"><value>0.05</value></prop><prop oor:name="RelativeWidth"><value>0.95</value></prop><prop oor:name="RelativeHeight"><value>0.8</value></prop></node></node></node></node><node oor:name="Accessibility"><node oor:name="Console" oor:op="replace"><prop oor:name="String"><value xml:lang="en-US">Presenter Console</value></prop></node><node oor:name="Preview" oor:op="replace"><prop oor:name="String"><value xml:lang="en-US">Current Slide Info</value></prop></node><node oor:name="Notes" oor:op="replace"><prop oor:name="String"><value xml:lang="en-US">Presenter Notes</value></prop></node></node></node></oor:component-data><oor:component-data xmlns:install="http://openoffice.org/2004/installation" oor:name="Setup" oor:package="org.openoffice"><node oor:name="Office"><node oor:name="Factories"><node oor:name="com.sun.star.presentation.PresentationDocument" oor:op="replace"><prop oor:name="ooSetupFactoryDocumentService" oor:finalized="true"><value>com.sun.star.presentation.PresentationDocument</value></prop><prop oor:name="ooSetupFactoryCommandConfigRef"><value>DrawImpressCommands</value></prop><prop oor:name="ooSetupFactoryActualFilter" oor:finalized="true"><value>impress8</value></prop><prop oor:name="ooSetupFactoryActualTemplateFilter" oor:finalized="true"><value>impress8_template</value></prop><prop oor:name="ooSetupFactoryDefaultFilter"><value>impress8</value></prop><prop oor:name="ooSetupFactoryEmptyDocumentURL" oor:finalized="true"><value>private:factory/simpress</value></prop><prop oor:name="ooSetupFactoryWindowAttributes"><value>,,,;4;</value></prop><prop oor:name="ooSetupFactoryIcon"><value>8</value></prop><prop oor:name="ooSetupFactoryTemplateFile"><value/></prop><prop oor:name="ooSetupFactorySystemDefaultTemplateChanged"><value>false</value></prop><prop oor:name="ooSetupFactoryShortName"><value>simpress</value></prop><prop oor:name="ooSetupFactoryUIName"><value>Impress</value></prop><prop oor:name="ooSetupFactoryWindowStateConfigRef"><value>ImpressWindowState</value></prop><prop oor:name="ooSetupFactoryCmdCategoryConfigRef"><value>GenericCategories</value></prop></node></node></node></oor:component-data></oor:data>
