/**************************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 *************************************************************/
// this code is bound to the events generated by the buttons in the dialog
// it will close the dialog or find and highlight the text entered in the
// dialog (depending on the button pressed)
import com.sun.star.uno.*;
import com.sun.star.awt.*;
import com.sun.star.lang.*;
import com.sun.star.beans.*;
import com.sun.star.util.*;
import com.sun.star.script.framework.browse.DialogFactory;

// Get the ActionEvent object from the ARGUMENTS list
ActionEvent event = (ActionEvent) ARGUMENTS[0];

// Each argument is of type Any so we must use the AnyConverter class to
// convert it into the interface or primitive type we expect
XButton button = (XButton)AnyConverter.toObject(
    new Type(XButton.class), event.Source);

// We can now query for the model of the button and get its properties
XControl control = (XControl)UnoRuntime.queryInterface(XControl.class, button);
XControlModel cmodel = control.getModel();
XPropertySet pset = (XPropertySet)UnoRuntime.queryInterface(
    XPropertySet.class, cmodel);

if (pset.getPropertyValue("Label").equals("Exit"))
{
    // We can get the XDialog in which this control appears by calling
    // getContext() on the XControl interface
    XDialog xDialog = (XDialog)UnoRuntime.queryInterface(
        XDialog.class, control.getContext());
    
    // Close the dialog
    xDialog.endExecute();
}
else
{
    // We can get the list of controls for this dialog by calling
    // getContext() on the XControl interface of the button
    XControlContainer controls = (XControlContainer)UnoRuntime.queryInterface(
        XControlContainer.class, control.getContext());

    // Now get the text field control from the list
    XTextComponent textField = (XTextComponent)
        UnoRuntime.queryInterface(
            XTextComponent.class, controls.getControl("HighlightTextField"));

    String searchKey = textField.getText();

    // highlight the text in red
    java.awt.Color cRed = new java.awt.Color(255, 0, 0);
    int red = cRed.getRGB();

    XReplaceable replaceable = (XReplaceable)
        UnoRuntime.queryInterface(XReplaceable.class, XSCRIPTCONTEXT.getDocument()); 

    XReplaceDescriptor descriptor =
        (XReplaceDescriptor) replaceable.createReplaceDescriptor();

    // Gets a XPropertyReplace object for altering the properties
    // of the replaced text
    XPropertyReplace xPropertyReplace = (XPropertyReplace)
        UnoRuntime.queryInterface(XPropertyReplace.class, descriptor);

    // Sets the replaced text property fontweight value to Bold
    PropertyValue wv = new PropertyValue("CharWeight", -1,
        new Float(com.sun.star.awt.FontWeight.BOLD),
            com.sun.star.beans.PropertyState.DIRECT_VALUE);

    // Sets the replaced text property color value to RGB parameter
    PropertyValue cv = new PropertyValue("CharColor", -1,
        new Integer(red),
            com.sun.star.beans.PropertyState.DIRECT_VALUE);

    // Apply the properties
    PropertyValue[] props = new PropertyValue[] { cv, wv }; 

    try {
        xPropertyReplace.setReplaceAttributes(props);

        // Only matches whole words and case sensitive
        descriptor.setPropertyValue(
            "SearchCaseSensitive", new Boolean(true));
        descriptor.setPropertyValue("SearchWords", new Boolean(true));
    }
    catch (com.sun.star.beans.UnknownPropertyException upe) {
        System.err.println("Error setting up search properties");
        return;
    }
    catch (com.sun.star.beans.PropertyVetoException pve) {
        System.err.println("Error setting up search properties");
        return;
    }
    catch (com.sun.star.lang.WrappedTargetException wte) {
        System.err.println("Error setting up search properties");
        return;
    }

    // Replaces all instances of searchKey with new Text properties
    // and gets the number of instances of the searchKey 
    descriptor.setSearchString(searchKey); 
    descriptor.setReplaceString(searchKey); 
    replaceable.replaceAll(descriptor);
}

// BeanShell OpenOffice.org scripts should always return 0
return 0;
