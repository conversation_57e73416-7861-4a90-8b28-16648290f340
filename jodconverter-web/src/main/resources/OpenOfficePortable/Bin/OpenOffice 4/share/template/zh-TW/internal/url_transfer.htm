<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 3.2//EN">
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<HTML>
<HEAD>
	<META HTTP-EQUIV="CONTENT-TYPE" CONTENT="text/html; charset=utf-8">
	<TITLE>URL</TITLE>
	<META NAME="GENERATOR" CONTENT="StarOffice 6.0  (Win32)">
	<META NAME="AUTHOR" CONTENT="Volker Ahrendt">
	<META NAME="CREATED" CONTENT="20010920;15533875">
	<META NAME="CHANGEDBY" CONTENT="Volker Ahrendt">
	<META NAME="CHANGED" CONTENT="20020422;15061023">
	<STYLE>
	<!--
		TD P.western { font-family: "MSung Light TC", monospace; so-language:  }
		TD P.cjk { font-family: "MSung Light TC", monospace; so-language: zh-TW }
		TD P.ctl { font-family: "MSung Light TC", monospace }
		P.western { font-family: "MSung Light TC", monospace; so-language:  }
		P.cjk { font-family: "MSung Light TC", monospace; so-language: zh-TW }
		P.ctl { font-family: "MSung Light TC", monospace }
		TH P { text-align: left }
		TH P.western { font-family: "MSung Light TC", monospace; so-language:  }
		TH P.cjk { font-family: "MSung Light TC", monospace; so-language: zh-TW }
		TH P.ctl { font-family: "MSung Light TC", monospace }
	-->
	</STYLE>
</HEAD>
<BODY LANG="de-DE">
<P LANG="" CLASS="western"><BR><BR>
</P>
<P LANG="" CLASS="western"><BR><BR>
</P>
<DL>
	<DL>
		<DL>
			<DD>
			<TABLE WIDTH=529 BORDER=0 CELLPADDING=4 CELLSPACING=0>
				<COL WIDTH=33>
				<COL WIDTH=480>
				<THEAD>
					<TR>
						<TH COLSPAN=2 WIDTH=521 VALIGN=TOP>
							<P LANG="" CLASS="western"><SPAN LANG="zh-TW">插入</SPAN>
							Web <SPAN LANG="zh-TW">地址</SPAN>
							</P>
							<P LANG="" CLASS="western" ALIGN=LEFT><BR>
							</P>
						</TH>
					</TR>
				</THEAD>
				<TBODY>
					<TR VALIGN=TOP>
						<TD WIDTH=33>
							<P LANG="" CLASS="western"><B>1.</B></P>
						</TD>
						<TD WIDTH=480>
							<P LANG="" CLASS="western"><SPAN LANG="zh-TW">在</SPAN>
							OpenOffice.org <SPAN LANG="zh-TW">的</SPAN> URL <SPAN LANG="zh-TW">地址欄位內鍵入一個</SPAN>
							URL <SPAN LANG="zh-TW">地址或者在超連結欄內尋找一個</SPAN>
							URL <SPAN LANG="zh-TW">地址。</SPAN></P>
						</TD>
					</TR>
					<TR VALIGN=TOP>
						<TD WIDTH=33>
							<P LANG="" CLASS="western"><B>2.</B></P>
						</TD>
						<TD WIDTH=480>
							<P LANG="" CLASS="western"><SPAN LANG="zh-TW">選中這個顯示在</SPAN>
							URL <SPAN LANG="zh-TW">欄位內的</SPAN> URL (<SPAN LANG="zh-TW">例如</SPAN>
							<A HREF="http://www.sun.com/"></A><A HREF="http://www.sun.com/">http://www.sun.com</A><A HREF="http://www.sun.com/"></A>)<SPAN LANG="zh-TW">。</SPAN></P>
						</TD>
					</TR>
					<TR VALIGN=TOP>
						<TD WIDTH=33>
							<P LANG="" CLASS="western"><B>3.</B></P>
						</TD>
						<TD WIDTH=480>
							<P LANG="" CLASS="western"><SPAN LANG="zh-TW">現在選擇功能表的命令編輯和复製或者採用組合鍵</SPAN>CTRL
							+ C <SPAN LANG="zh-TW">。</SPAN></P>
						</TD>
					</TR>
					<TR VALIGN=TOP>
						<TD WIDTH=33>
							<P LANG="" CLASS="western"><B>4.</B></P>
						</TD>
						<TD WIDTH=480>
							<P LANG="" CLASS="western"><SPAN LANG="zh-TW">游標移動在文件內，在功能表命令內選擇編輯和插入或者按一下組合鍵</SPAN>
							CTRL+V <SPAN LANG="zh-TW">。</SPAN></P>
						</TD>
					</TR>
				</TBODY>
			</TABLE>
		</DL>
	</DL>
</DL>
<P LANG="" CLASS="western" STYLE="page-break-before: auto; page-break-after: auto">
<BR><BR>
</P>
</BODY>
</HTML>
