<?xml version="1.0"?>
<!--***********************************************************
 * 
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 * 
 ***********************************************************-->
<anim:seq xmlns:office="urn:oasis:names:tc:openoffice:xmlns:office:1.0" xmlns:style="urn:oasis:names:tc:openoffice:xmlns:style:1.0" xmlns:text="urn:oasis:names:tc:openoffice:xmlns:text:1.0" xmlns:table="urn:oasis:names:tc:openoffice:xmlns:table:1.0" xmlns:draw="urn:oasis:names:tc:openoffice:xmlns:drawing:1.0" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="urn:oasis:names:tc:openoffice:xmlns:meta:1.0" xmlns:number="urn:oasis:names:tc:openoffice:xmlns:datastyle:1.0" xmlns:pres="urn:oasis:names:tc:openoffice:xmlns:presentation:1.0" xmlns:svg="http://www.w3.org/2000/svg" xmlns:chart="urn:oasis:names:tc:openoffice:xmlns:chart:1.0" xmlns:dr3d="urn:oasis:names:tc:openoffice:xmlns:dr3d:1.0" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="urn:oasis:names:tc:openoffice:xmlns:form:1.0" xmlns:script="urn:oasis:names:tc:openoffice:xmlns:script:1.0" xmlns:ooo="http://openoffice.org/2004/office" xmlns:ooow="http://openoffice.org/2004/writer" xmlns:oooc="http://openoffice.org/2004/calc" xmlns:dom="http://www.w3.org/2001/xml-events" xmlns:smil="http://www.w3.org/2001/SMIL20" xmlns:anim="urn:oasis:names:tc:openoffice:xmlns:animation:1.0" pres:node-type="main-sequence">

	<anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
		    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-appear">
				<anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
			</anim:par>
		</anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
	    <anim:par smil:begin="0" smil:fill="hold">
			<anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-venetian-blinds" pres:preset-sub-type="vertical">
				<anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
				<anim:transitionFilter smil:dur="0.5" smil:type="blindsWipe" smil:subtype="horizontal"/>
			</anim:par>
		</anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-venetian-blinds" pres:preset-sub-type="horizontal">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="blindsWipe" smil:subtype="vertical"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-box" pres:preset-sub-type="in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="irisWipe" smil:subtype="rectangle" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-box" pres:preset-sub-type="out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="irisWipe" smil:subtype="rectangle"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-checkerboard" pres:preset-sub-type="across">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="checkerBoardWipe" smil:subtype="across"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-checkerboard" pres:preset-sub-type="downward">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="checkerBoardWipe" smil:subtype="down"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-circle" pres:preset-sub-type="in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="ellipseWipe" smil:subtype="horizontal" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-circle" pres:preset-sub-type="out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="ellipseWipe" smil:subtype="horizontal"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in-slow" pres:preset-sub-type="from-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="y" smil:values="1+height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in-slow" pres:preset-sub-type="from-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="x" smil:values="0-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in-slow" pres:preset-sub-type="from-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="x" smil:values="1+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in-slow" pres:preset-sub-type="from-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="y" smil:values="0-height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diamond" pres:preset-sub-type="in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="irisWipe" smil:subtype="diamond" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diamond" pres:preset-sub-type="out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="irisWipe" smil:subtype="diamond"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-dissolve-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="dissolve"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-flash-once">
        <anim:set smil:begin="0" smil:dur="1" smil:attributeName="visibility" smil:to="visible"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="1+height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="0-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="1+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="0-height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-bottom-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="0-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="1+height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-bottom-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="1+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="1+height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-top-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="0-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="0-height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fly-in" pres:preset-sub-type="from-top-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="1+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="0-height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-peek-in" pres:preset-sub-type="from-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromBottom"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-peek-in" pres:preset-sub-type="from-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromLeft"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-peek-in" pres:preset-sub-type="from-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromRight"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-peek-in" pres:preset-sub-type="from-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromTop"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-plus" pres:preset-sub-type="in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fourBoxWipe" smil:subtype="cornersIn"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-plus" pres:preset-sub-type="out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fourBoxWipe" smil:subtype="cornersIn" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-random-bars" pres:preset-sub-type="vertical">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="randomBarWipe" smil:subtype="horizontal"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-random-bars" pres:preset-sub-type="horizontal">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="randomBarWipe" smil:subtype="vertical"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-random"/>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-split" pres:preset-sub-type="horizontal-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="horizontal" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-split" pres:preset-sub-type="horizontal-out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="horizontal"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-split" pres:preset-sub-type="vertical-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="vertical" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-split" pres:preset-sub-type="vertical-out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="vertical"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diagonal-squares" pres:preset-sub-type="left-to-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalRight"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diagonal-squares" pres:preset-sub-type="left-to-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalLeft" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diagonal-squares" pres:preset-sub-type="right-to-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalLeft"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-diagonal-squares" pres:preset-sub-type="right-to-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalRight" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wedge">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fanWipe" smil:subtype="centerTop"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-clock-wipe" pres:preset-sub-type="clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="clockWipe" smil:subtype="clockwiseTwelve"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-clock-wipe" pres:preset-sub-type="counter-clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="clockWipe" smil:subtype="clockwiseTwelve" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wheel" pres:preset-sub-type="1">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="oneBlade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wheel" pres:preset-sub-type="2">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="twoBladeVertical"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wheel" pres:preset-sub-type="3">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="threeBlade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wheel" pres:preset-sub-type="4">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="fourBlade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wheel" pres:preset-sub-type="8">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="eightBlade"/>
    </anim:par>
    </anim:par>
    </anim:par>

<!-- ooo-entrance-snake-wipe -->
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-snake-wipe" pres:preset-sub-type="from-top-left-horizontal">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="snakeWipe" smil:subtype="topLeftHorizontal"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-snake-wipe" pres:preset-sub-type="from-top-left-vertical">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="snakeWipe" smil:subtype="topLeftVertical"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-snake-wipe" pres:preset-sub-type="from-bottom-right-vertical">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
		<anim:transitionFilter smil:dur="0.5" smil:type="snakeWipe" smil:subtype="topLeftVertical" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-snake-wipe" pres:preset-sub-type="from-bottom-right-horizontal">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="snakeWipe" smil:subtype="topLeftHorizontal" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>

<!-- ooo-entrance-spiral-wipe -->
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spiral-wipe" pres:preset-sub-type="from-top-left-clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="spiralWipe" smil:subtype="topLeftClockwise"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spiral-wipe" pres:preset-sub-type="from-top-right-counter-clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="spiralWipe" smil:subtype="topRightCounterClockwise"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spiral-wipe" pres:preset-sub-type="from-center-clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="spiralWipe" smil:subtype="topLeftClockwise" smil:direction="reverse"/>/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spiral-wipe" pres:preset-sub-type="from-center-counter-clockwise">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="spiralWipe" smil:subtype="topRightCounterClockwise" smil:direction="reverse"/>/>
    </anim:par>
    </anim:par>
    </anim:par>

<!-- ooo-entrance-wipe -->
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wipe" pres:preset-sub-type="from-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="topToBottom" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wipe" pres:preset-sub-type="from-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="leftToRight"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wipe" pres:preset-sub-type="from-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="leftToRight" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-wipe" pres:preset-sub-type="from-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="topToBottom"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-expand">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="width" smil:values="width*0.70;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fade-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fade-in-and-swivel" anim:iterate-type="by-letter" anim:iterate-interval="0.2s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="2" smil:fill="hold" smil:attributeName="width" smil:values="0;1" smil:keyTimes="0;1" anim:formula="width*sin(2.5*pi*$)"/>
        <anim:animate smil:dur="2" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fade-in-and-zoom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-ascend">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="y+.1;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-center-revolve">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="0.4" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.4" smil:fill="hold" smil:attributeName="y" smil:values="y+0.31;y+0.31" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.4" smil:dur="0.6" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="x" smil:values="x;x+0.0242;x+0.0479;x+0.0704;x+0.0911;x+0.1096;x+0.1254;x+0.1381;x+0.1474;x+0.1531;x+0.1550;x+0.1531;x+0.1474;x+0.1381;x+0.1254;x+0.1096;x+0.0911;x+0.0704;x+0.0479;x+0.0242;x" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:animate smil:begin="0.4" smil:dur="0.6" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="y" smil:values="y+0.31;y+0.308;y+0.3024;y+0.2931;y+0.2804;y+0.2646;y+0.2461;y+0.2253;y+0.2029;y+0.1792;y+0.155;y+0.1307;y+0.1071;y+0.0846;y+0.0639;y+0.0454;y+0.0296;y+0.0169;y+0.0076;y+0.0019;y" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:preset-property="Color1;Color2" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-colored-lettering" anim:iterate-type="by-letter" anim:iterate-interval="0.04s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.08" smil:attributeName="color" smil:values="#333399;#009999" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
        <anim:animate smil:dur="0.08" smil:attributeName="fill-color" smil:values="#333399;#009999" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
        <anim:set smil:dur="0.08" smil:attributeName="fill" smil:to="solid"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:decelerate="1" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-compress">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="width" smil:values="width+.3;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-descend">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="y-.1;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-ease-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="x-.2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="barWipe" smil:subtype="leftToRight" smil:direction="reverse"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-turn-and-grow" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="rotate" smil:values="90;0" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-rise-up">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.9" smil:fill="hold" smil:decelerate="1" smil:attributeName="y" smil:values="y+1;y-.03" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.9" smil:dur="0.1" smil:fill="hold" smil:accelerate="1" smil:attributeName="y" smil:values="y-.03;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:decelerate="1" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spin-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="rotate" smil:values="360;0" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="across">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="downward">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>    
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-bottom">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y+height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-top">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y-height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-top-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y-height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-bottom-right">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x+width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y+height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-top-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y-height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-stretchy" pres:preset-sub-type="from-bottom-left">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x-width/2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y+height/2;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>   
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-unfold" anim:iterate-type="by-letter" anim:iterate-interval="0.1s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="x-.1;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="in-from-screen-center">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="0.5;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="0.5;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="in-slightly">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="2/3*width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="2/3*height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="out">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="4*width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="4*height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="out-from-screen-center">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="(6*min(max(width*height,.3),1)-7.4)/-.7*width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="(6*min(max(width*height,.3),1)-7.4)/-.7*height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="0.5;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="1+(6*min(max(width*height,.3),1)-7.4)/-.7*height/2;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-zoom" pres:preset-sub-type="out-slightly">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="4/3*width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="4/3*height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-boomerang">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="rotate" smil:values="-90;0" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="width" smil:values="width;width*.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:fill="hold" smil:accelerate="0.5" smil:attributeName="width" smil:values="width*.05;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="x" smil:values="x+.4;x" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:decelerate="0.5" smil:attributeName="y" smil:values="y-.2;y+.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:fill="hold" smil:accelerate="0.5" smil:attributeName="y" smil:values="y+.1;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:begin="0" smil:dur="1" smil:decelerate="0.5" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-bounce">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:begin="0" smil:dur="0.58" smil:type="barWipe" smil:subtype="topToBottom"/>
        <anim:animate smil:begin="0" smil:dur="1.822" smil:attributeName="x" smil:values="x-0.25;x" smil:keyTimes="0;1" smil:keySplines="0,0;0.14,0.36;0.43,0.73;0.71,0.91;1,1"/>
        <anim:animate smil:begin="0" smil:dur="0.664" smil:attributeName="y" smil:values="0.5;1" smil:keyTimes="0;1" anim:formula="y-sin(pi*$)/3" smil:keySplines="0,0;0.25,0.07;0.5,0.2;0.75,0.467;1,1"/>
        <anim:animate smil:begin="0.664" smil:dur="0.664" smil:attributeName="y" smil:values="0;1" smil:keyTimes="0;1" anim:formula="y-sin(pi*$)/9" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animate smil:begin="1.324" smil:dur="0.332" smil:attributeName="y" smil:values="0;1" smil:keyTimes="0;1" anim:formula="y-sin(pi*$)/27" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animate smil:begin="1.656" smil:dur="0.164" smil:attributeName="y" smil:values="0;1" smil:keyTimes="0;1" anim:formula="y-sin(pi*$)/81" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animateTransform smil:begin="0.65" smil:dur="0.026" smil:to="1,0.6" svg:type="scale"/>
        <anim:animateTransform smil:begin="0.676" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.312" smil:dur="0.026" smil:to="1,0.8" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.338" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.642" smil:dur="0.026" smil:to="1,0.9" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.668" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.808" smil:dur="0.026" smil:to="1,0.95" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.834" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-movie-credits">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="15" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="15" smil:fill="hold" smil:attributeName="y" smil:values="y+1;y-1" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-curve-up">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animateTransform smil:begin="0" smil:dur="1" smil:fill="hold" smil:decelerate="0.5" smil:from="2.5,2.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateMotion smil:begin="0" smil:dur="1" smil:fill="hold" smil:decelerate="0.5" svg:path="M -0.46736 0.92887  C -0.37517 0.88508  -0.02552 0.75279  0.0908 0.66613  C  0.20747 0.57948  0.21649 0.50394  0.23177 0.40825  C 0.24705 0.31256  0.22118 0.15964   0.18264 0.09152  C 0.1441 0.02341  0.03802 0.0  0.0 0.0"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-flip" anim:iterate-type="by-letter" anim:iterate-interval="0.1s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:autoReverse="true" smil:attributeName="width" smil:by="(-width*2)"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:fill="hold" smil:decelerate="0.5" smil:autoReverse="true" smil:attributeName="x" smil:by="(width*0.50)"/>
        <anim:animate smil:begin="0" smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:from="(-height/2)" smil:to="(y)"/>
        <anim:animateTransform smil:begin="0" smil:dur="1" smil:fill="hold" smil:by="360" svg:type="rotate"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-float">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.8" smil:decelerate="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="0.8" smil:fill="hold" smil:decelerate="1" smil:attributeName="rotate" smil:values="-90;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.8" smil:fill="hold" smil:decelerate="1" smil:attributeName="x" smil:values="x+0.4;x-0.05" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.8" smil:fill="hold" smil:decelerate="1" smil:attributeName="y" smil:values="y-0.4;y+0.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.8" smil:dur="0.2" smil:fill="hold" smil:accelerate="1" smil:attributeName="x" smil:values="x-0.05;x" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.8" smil:dur="0.2" smil:fill="hold" smil:accelerate="1" smil:attributeName="y" smil:values="y+0.1;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:text-only="true" smil:begin="0" smil:fill="hold" smil:accelerate="1" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-fold">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width*2.5;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height*0.01;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="height+1;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="1" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-glide">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width*0.05;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x-.2;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-breaks">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:begin="0" smil:dur="0.6" smil:fill="hold" smil:attributeName="x" smil:from="(-width/2)" smil:to="(x)"/>
        <anim:animate smil:begin="0.6" smil:dur="0.2" smil:fill="hold" smil:decelerate="0.5" smil:autoReverse="true" smil:attributeName="skewX" smil:from="0" smil:to="-1"/>
        <anim:animateTransform smil:begin="0.6" smil:dur="0.2" smil:fill="hold" smil:decelerate="1" smil:autoReverse="true" smil:from="1,1" smil:to="0.8,1" svg:type="scale"/>
        <anim:animate smil:begin="0.6" smil:dur="0.2" smil:fill="hold" smil:decelerate="1" smil:autoReverse="true" smil:attributeName="x" smil:by="(height/3+width*0.1)" smil:additive="sum"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-magnify">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="0.77" smil:decelerate="1" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animateTransform smil:dur="0.77" smil:decelerate="1" smil:from="0.1,0.1" smil:to="2,4.5" svg:type="scale"/>
        <anim:animateTransform smil:begin="0.77" smil:dur="1.23" smil:fill="hold" smil:accelerate="1" smil:from="2,4.5" smil:to="1,1" svg:type="scale"/>
        <anim:set smil:dur="0.77" smil:fill="hold" smil:attributeName="x" smil:to="(0.5)"/>
        <anim:animate smil:begin="0.77" smil:dur="1.23" smil:fill="hold" smil:accelerate="1" smil:attributeName="x" smil:from="(0.5)" smil:to="(x)"/>
        <anim:set smil:dur="0.77" smil:fill="hold" smil:attributeName="y" smil:to="(y+0.4)"/>
        <anim:animate smil:begin="0.77" smil:dur="1.23" smil:fill="hold" smil:accelerate="1" smil:attributeName="y" smil:from="(y+0.4)" smil:to="(y)"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-pinwheel">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade"/>
        <anim:animate smil:dur="2" smil:fill="hold" smil:attributeName="rotate" smil:values="720;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="2" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="2" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-sling">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="rotate" smil:values="90;90;90;0" smil:keyTimes="0;0.8;0.8;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="-1;0.949999988079071;x" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-spiral-in">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="width" smil:values="0;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="height" smil:values="0;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="x" smil:values="0;1" smil:keyTimes="0;1" anim:formula="x+(cos(-2*pi*(1-$))*-x-sin(-2*pi*(1-$))*(1-y))*(1-$)"/>
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="y" smil:values="0;1" smil:keyTimes="0;1" anim:formula="y+(sin(-2*pi*(1-$))*-x+cos(-2*pi*(1-$))*(1-y))*(1-$)"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" smil:accelerate="0.5" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-falling-in" anim:iterate-type="by-letter" anim:iterate-interval="0.5s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:set smil:begin="0" smil:dur="0.455" smil:fill="hold" smil:attributeName="rotate" smil:to="-45"/>
        <anim:animate smil:begin="0.455" smil:dur="0.455" smil:fill="hold" smil:attributeName="rotate" smil:values="-45;45;0" smil:keyTimes="0;0.699;1"/>
        <anim:animate smil:begin="0" smil:dur="0.455" smil:fill="hold" smil:attributeName="y" smil:values="y-1;y-(0.354*width-0.172*height)" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.455" smil:dur="0.156" smil:fill="hold" smil:decelerate="0.5" smil:autoReverse="true" smil:attributeName="y" smil:values="y-(0.354*width-0.172*height);y-(0.354*width-0.172*height)-height/2" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.864" smil:dur="0.136" smil:fill="hold" smil:attributeName="y" smil:values="y-(0.354*width-0.172*height);y" smil:keyTimes="0;1"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-swivel" pres:preset-sub-type="vertical">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="width" smil:values="0;1" smil:keyTimes="0;1" anim:formula="width*sin(2.5*pi*$)"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-swivel" pres:preset-sub-type="horizontal">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="width" smil:values="width;width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:fill="hold" smil:attributeName="height" smil:values="0;1" smil:keyTimes="0;1" anim:formula="height*sin(2.5*pi*$)"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="1" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-thread">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height/20;height/20;height" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width+.3;width+.3;width" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x-.3;x;x" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-entrance-whip" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="visible"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="x" smil:values="x;x+.1;x" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="height" smil:values="height/10;height+.01;height" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" smil:attributeName="width" smil:values="width/10;width+.01;width" smil:keyTimes="0;0.5;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:keySplines="0,0;0.5,1;1,1" smil:type="fade" smil:subtype="crossfade"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="FillColor;ColorStyle;Accelerate;Decelerate;AutoReverse" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-fill-color" pres:preset-sub-type="2">
        <anim:animateColor smil:dur="2" smil:fill="hold" smil:attributeName="fill-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="2" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="FontStyle" pres:text-only="true" smil:begin="0" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-font">
        <anim:set smil:dur="indefinite" anim:sub-item="text" smil:attributeName="font-family" smil:to="Times New Roman"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="CharColor;ColorStyle;Accelerate;Decelerate;AutoReverse" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-font-color" pres:preset-sub-type="2">
        <anim:animateColor smil:dur="2" smil:fill="hold" anim:sub-item="text" smil:attributeName="color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="CharHeight" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-font-size" pres:preset-sub-type="2">
        <anim:animate smil:dur="2" smil:fill="hold" anim:sub-item="text" smil:attributeName="font-size" smil:to="1.5pt"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="CharDecoration" pres:text-only="true" smil:begin="0" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-font-style" pres:preset-sub-type="1">
        <anim:set smil:dur="indefinite" anim:sub-item="text" smil:attributeName="font-style" smil:to="normal"/>
        <anim:set smil:dur="indefinite" anim:sub-item="text" smil:attributeName="font-weight" smil:to="bold"/>
        <anim:set smil:dur="indefinite" anim:sub-item="text" smil:attributeName="text-underline" smil:to="none"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="LineColor;ColorStyle;Accelerate;Decelerate;AutoReverse" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-line-color" pres:preset-sub-type="2">
        <anim:animateColor smil:dur="2" smil:fill="hold" smil:attributeName="stroke-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="2" smil:fill="hold" smil:attributeName="stroke" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Scale;Accelerate;Decelerate;AutoReverse" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-grow-and-shrink">
        <anim:animateTransform smil:dur="2" smil:fill="hold" smil:by="1.5,1.5" svg:type="scale"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Rotate;Accelerate;Decelerate;AutoReverse" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-spin">
        <anim:animateTransform smil:dur="2" smil:fill="hold" smil:by="360" svg:type="rotate"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Transparency" smil:begin="0" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-transparency">
        <anim:set smil:dur="indefinite" smil:attributeName="opacity" smil:to="0.5"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-bold-flash">
        <anim:animate smil:dur="2" smil:fill="hold" anim:sub-item="text" smil:attributeName="font-weight" smil:values="normal;bold;normal;normal" smil:keyTimes="0;0.5;0.6;1" smil:calcMode="discrete"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:preset-property="Color" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-color-over-by-word" anim:iterate-type="by-letter" anim:iterate-interval="0.2s">
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:to="#333399"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:to="#333399"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-reveal-underline" anim:iterate-type="by-letter" anim:iterate-interval="0.2s">
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="text-underline" smil:to="solid"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-color-blend">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:preset-property="Color" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-color-over-by-letter" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:set smil:dur="0.5" smil:fill="hold" smil:autoReverse="true" smil:attributeName="color" smil:to="#333399"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:autoReverse="true" smil:attributeName="fill-color" smil:to="#333399"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:autoReverse="true" smil:attributeName="fill" smil:to="solid"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-complementary-color">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-complementary-color-2">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(-120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(-120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(-120,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-contrasting-color">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(180,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(180,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(180,0%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-darken">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(0,-12%,-25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(0,-12%,-25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(0,-12%,-25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-desaturate">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(0,-70%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(0,-70%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(0,-70%,0%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-flash-bulb">
        <anim:transitionFilter smil:dur="0.5" smil:keySplines="0,0;0.2,0.5;0.8,0.5;1,0" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animateTransform smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:by="1.05,1.05" svg:type="scale"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-lighten">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="color" smil:by="hsl(0,12%,25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="fill-color" smil:by="hsl(0,12%,25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" smil:attributeName="stroke-color" smil:by="hsl(0,12%,25%)" anim:color-interpolation="hsl" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" smil:begin="0" smil:fill="remove" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-vertical-highlight">
        <anim:animateColor smil:begin="0" smil:dur="1.5" smil:fill="hold" smil:accelerate="0.5" smil:autoReverse="true" smil:attributeName="color" smil:to="#333399" smil:keySplines="0,0;0.33333,1;1,1" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:begin="0" smil:dur="1.5" smil:fill="hold" smil:accelerate="0.5" smil:autoReverse="true" smil:attributeName="fill-color" smil:to="#333399" smil:keySplines="0,0;0.33333,1;1,1" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="3" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
        <anim:animateTransform smil:begin="0" smil:dur="1.5" smil:fill="hold" smil:accelerate="0.5" smil:autoReverse="true" smil:from="1,1" smil:to="1,1.4" smil:keySplines="0,0;0.33333,1;1,1" svg:type="scale"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-flicker">
        <anim:animateColor smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:attributeName="color" smil:to="#ffffff" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:attributeName="fill-color" smil:to="#ffffff" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:attributeName="fill" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:preset-property="Color" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-grow-with-color" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:animateColor smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="fill-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="fill" smil:to="solid"/>
        <anim:animate smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="font-size" smil:to="1.5pt"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-shimmer" anim:iterate-type="by-letter" anim:iterate-interval="0.025s">
        <anim:animateTransform smil:begin="0" smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:to="0.8,1" svg:type="scale"/>
        <anim:animate smil:begin="0" smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:attributeName="x" smil:by="(width*0.10)"/>
        <anim:animate smil:begin="0" smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:attributeName="y" smil:by="(-width*0.10)"/>
        <anim:animateTransform smil:begin="0" smil:dur="0.25" smil:fill="hold" smil:autoReverse="true" smil:by="-8" svg:type="rotate"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-teeter">
        <anim:animateColor smil:dur="0.1" smil:fill="hold" smil:attributeName="color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:dur="0.1" smil:fill="hold" smil:attributeName="fill-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:dur="0.1" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
        <anim:animateTransform smil:begin="0" smil:dur="0.1" smil:fill="hold" smil:by="2" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.2" smil:dur="0.2" smil:fill="hold" smil:by="-4" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.4" smil:dur="0.2" smil:fill="hold" smil:by="4" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.6" smil:dur="0.2" smil:fill="hold" smil:by="-4" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.8" smil:dur="0.2" smil:fill="hold" smil:by="2" svg:type="rotate"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-blast">
        <anim:animateColor smil:begin="0.1" smil:dur="1.9" smil:fill="hold" smil:attributeName="color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:animateColor smil:begin="0.1" smil:dur="1.9" smil:fill="hold" smil:attributeName="fill-color" smil:to="#333399" anim:color-interpolation="rgb" anim:color-interpolation-direction="clockwise"/>
        <anim:set smil:begin="0.1" smil:dur="1.9" smil:fill="hold" smil:attributeName="fill" smil:to="solid"/>
        <anim:animateTransform smil:begin="0" smil:dur="0.2" smil:fill="hold" smil:from="1,1" smil:to="1,0.05" svg:type="scale"/>
        <anim:animateTransform smil:begin="0.2" smil:dur="0.2" smil:fill="hold" smil:from="1,0.05" smil:to="1.2,1.5" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.4" smil:dur="0.6" smil:fill="hold" smil:to="1.2,1.5" svg:type="scale"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-blink">
        <anim:animate smil:dur="1" smil:fill="hold" smil:attributeName="visibility" smil:values="hidden;visible" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-bold-reveal" anim:iterate-type="by-letter" anim:iterate-interval="0.025s">
        <anim:set smil:dur="indefinite" anim:sub-item="text" smil:attributeName="font-weight" smil:to="bold"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Color" pres:text-only="true" smil:begin="0" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-style-emphasis">
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="color" smil:to="#333399"/>
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="font-style" smil:to="italic"/>
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="font-weight" smil:to="bold"/>
        <anim:set smil:dur="0.5" smil:fill="hold" anim:sub-item="text" smil:attributeName="text-underline" smil:to="solid"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="emphasis" pres:preset-id="ooo-emphasis-wave" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:animateMotion smil:begin="0" smil:dur="0.25" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" smil:autoReverse="true" svg:path="M 0.0 0.0 L 0.0 -0.07213"/>
        <anim:animateTransform smil:begin="0" smil:dur="0.125" smil:fill="hold" smil:by="25" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.125" smil:dur="0.125" smil:fill="hold" smil:by="-25" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.25" smil:dur="0.125" smil:fill="hold" smil:by="-25" svg:type="rotate"/>
        <anim:animateTransform smil:begin="0.375" smil:dur="0.125" smil:fill="hold" smil:by="25" svg:type="rotate"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-venetian-blinds" pres:preset-sub-type="vertical">
        <anim:transitionFilter smil:dur="0.5" smil:type="blindsWipe" smil:subtype="horizontal" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-venetian-blinds" pres:preset-sub-type="horizontal">
        <anim:transitionFilter smil:dur="0.5" smil:type="blindsWipe" smil:subtype="vertical" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-box" pres:preset-sub-type="in">
        <anim:transitionFilter smil:dur="0.5" smil:type="irisWipe" smil:subtype="rectangle" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-box" pres:preset-sub-type="out">
        <anim:transitionFilter smil:dur="0.5" smil:type="irisWipe" smil:subtype="rectangle" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-checkerboard" pres:preset-sub-type="across">
        <anim:transitionFilter smil:dur="0.5" smil:type="checkerBoardWipe" smil:subtype="across" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-checkerboard" pres:preset-sub-type="downward">
        <anim:transitionFilter smil:dur="0.5" smil:type="checkerBoardWipe" smil:subtype="down" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-circle" pres:preset-sub-type="in">
        <anim:transitionFilter smil:dur="2" smil:type="ellipseWipe" smil:subtype="horizontal" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-circle" pres:preset-sub-type="out">
        <anim:transitionFilter smil:dur="2" smil:type="ellipseWipe" smil:subtype="horizontal" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-crawl-out" pres:preset-sub-type="from-bottom">
        <anim:animate smil:dur="5" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:attributeName="y" smil:values="y;1+height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="4.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-crawl-out" pres:preset-sub-type="from-left">
        <anim:animate smil:dur="5" smil:attributeName="x" smil:values="x;0-width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="4.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-crawl-out" pres:preset-sub-type="from-right">
        <anim:animate smil:dur="5" smil:attributeName="x" smil:values="x;1+width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="4.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-crawl-out" pres:preset-sub-type="from-top">
        <anim:animate smil:dur="5" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:attributeName="y" smil:values="y;0-height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="4.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diamond" pres:preset-sub-type="in">
        <anim:transitionFilter smil:dur="2" smil:type="irisWipe" smil:subtype="diamond" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diamond" pres:preset-sub-type="out">
        <anim:transitionFilter smil:dur="2" smil:type="irisWipe" smil:subtype="diamond" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-disappear">
        <anim:set smil:begin="0" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-dissolve">
        <anim:transitionFilter smil:dur="0.5" smil:type="dissolve" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-flash-once">
        <anim:animate smil:dur="1" smil:attributeName="visibility" smil:values="hidden;visible" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-bottom">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;1+height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-left">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;0-width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-right">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;1+width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-top">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;0-height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-bottom-left">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;0-width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;1+height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-bottom-right">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;1+width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;1+height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-top-left">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;0-width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;0-height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction;Accelerate;Decelerate" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fly-out" pres:preset-sub-type="from-top-right">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;1+width/2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;0-height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-peek-out" pres:preset-sub-type="from-bottom">
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromBottom" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-peek-out" pres:preset-sub-type="from-left">
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromLeft" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-peek-out" pres:preset-sub-type="from-right">
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromRight" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-peek-out" pres:preset-sub-type="from-top">
        <anim:transitionFilter smil:dur="0.5" smil:type="slideWipe" smil:subtype="fromTop" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-plus" pres:preset-sub-type="in">
        <anim:transitionFilter smil:dur="2" smil:type="fourBoxWipe" smil:subtype="cornersIn" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-plus" pres:preset-sub-type="out">
        <anim:transitionFilter smil:dur="2" smil:type="fourBoxWipe" smil:subtype="cornersIn" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-random-bars" pres:preset-sub-type="vertical">
        <anim:transitionFilter smil:dur="0.5" smil:type="randomBarWipe" smil:subtype="horizontal" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-random-bars" pres:preset-sub-type="horizontal">
        <anim:transitionFilter smil:dur="0.5" smil:type="randomBarWipe" smil:subtype="vertical" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-random"/>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-split" pres:preset-sub-type="horizontal-in">
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="horizontal" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-split" pres:preset-sub-type="horizontal-out">
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="horizontal" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-split" pres:preset-sub-type="vertical-in">
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="vertical" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-split" pres:preset-sub-type="vertical-out">
        <anim:transitionFilter smil:dur="0.5" smil:type="barnDoorWipe" smil:subtype="vertical" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diagonal-squares" pres:preset-sub-type="left-to-bottom">
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalRight" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diagonal-squares" pres:preset-sub-type="left-to-top">
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalRight" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diagonal-squares" pres:preset-sub-type="right-to-bottom">
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalLeft" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-diagonal-squares" pres:preset-sub-type="right-to-top">
        <anim:transitionFilter smil:dur="0.5" smil:type="waterfallWipe" smil:subtype="horizontalLeft" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wedge">
        <anim:transitionFilter smil:dur="2" smil:type="fanWipe" smil:subtype="centerTop" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wheel" pres:preset-sub-type="1">
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="oneBlade" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wheel" pres:preset-sub-type="2">
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="twoBladeVertical" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wheel" pres:preset-sub-type="3">
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="threeBlade" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wheel" pres:preset-sub-type="4">
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="fourBlade" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Spokes" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wheel" pres:preset-sub-type="8">
        <anim:transitionFilter smil:dur="2" smil:type="pinWheelWipe" smil:subtype="eightBlade" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wipe" pres:preset-sub-type="from-bottom">
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="topToBottom" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wipe" pres:preset-sub-type="from-left">
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="leftToRight" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wipe" pres:preset-sub-type="from-right">
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="leftToRight" smil:mode="out" smil:direction="reverse"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-wipe" pres:preset-sub-type="from-top">
        <anim:transitionFilter smil:dur="0.5" smil:type="barWipe" smil:subtype="topToBottom" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-contract">
        <anim:animate smil:dur="1" smil:attributeName="width" smil:values="width;width*0.70" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fade-out">
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fade-out-and-swivel" anim:iterate-type="by-letter" anim:iterate-interval="0.2s">
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="2" smil:attributeName="width" smil:values="width;0.92*width;0.71*width;0.38*width;0;-0.38*width;-0.71*width;-0.92*width;-width;-0.92*width;-0.71*width;-0.38*width;0;0.38*width;0.71*width;0.92*width;width;0.92*width;0.71*width;0.38*width;0" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:animate smil:dur="2" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fade-out-and-zoom">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-ascend">
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y-.1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-center-revolve">
        <anim:animate smil:begin="0" smil:dur="0.6" smil:decelerate="0.5" smil:attributeName="x" smil:values="x;x+0.0242;x+0.0479;x+0.0704;x+0.0911;x+0.1096;x+0.1254;x+0.1381;x+0.1474;x+0.1531;x+0.155;x+0.1531;x+0.1474;x+0.1381;x+0.1254;x+0.1096;x+0.0911;x+0.0704;x+0.0479;x+0.0242;x" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:animate smil:begin="0.6" smil:dur="0.4" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.6" smil:decelerate="0.5" smil:attributeName="y" smil:values="y;y+0.0019;y+0.0076;y+0.0169;y+0.0296;y+0.0454;y+0.0639;y+0.0846;y+0.1071;y+0.1307;y+0.155;y+0.1792;y+0.2029;y+0.2253;y+0.2461;y+0.2646;y+0.2804;y+0.2931;y+0.3024;y+0.308;y+0.31" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:animate smil:begin="0.6" smil:dur="0.4" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:begin="0.9" smil:dur="0.1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-collapse" pres:preset-sub-type="across">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:preset-property="Color1;Color2" pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-colored-lettering" anim:iterate-type="by-letter" anim:iterate-interval="0.04s">
        <anim:animate smil:dur="0.08" smil:attributeName="color" smil:values="#009999;#333399" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
        <anim:animate smil:dur="0.08" smil:attributeName="fill-color" smil:values="#009999;#333399" smil:keyTimes="0;0.5" smil:calcMode="discrete"/>
        <anim:set smil:dur="0.08" smil:attributeName="fill" smil:to="solid"/>
        <anim:set smil:begin="0.079" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-descend">
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y+.1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-ease-out">
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x-.2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-turn-and-grow" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:animate smil:dur="1" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="rotate" smil:values="0;90" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-sink-down">
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.1" smil:decelerate="1" smil:attributeName="y" smil:values="y;y-.03" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.1" smil:dur="0.9" smil:accelerate="1" smil:attributeName="y" smil:values="y;y+1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="1" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-spin-out">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="rotate" smil:values="0;360" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="1" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-stretchy">
        <anim:animate smil:dur="1" smil:attributeName="width" smil:values="width;width+.3" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-unfold" anim:iterate-type="by-letter" anim:iterate-interval="0.1s">
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x-.1" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="out">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="544">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;0.5" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;0.5" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="out-slightly">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;2/3*width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;2/3*height" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="in">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;4*width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;4*height" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="20">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;(6*min(max(width*height,.3),1)-7.4)/-.7*width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;(6*min(max(width*height,.3),1)-7.4)/-.7*height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;1+(6*min(max(width*height,.3),1)-7.4)/-.7*height/2" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Zoom" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-zoom" pres:preset-sub-type="in-slightly">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;4/3*width" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;4/3*height" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-boomerang">
        <anim:transitionFilter smil:begin="0" smil:dur="1" smil:accelerate="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="y" smil:values="y;y+.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:decelerate="0.5" smil:attributeName="y" smil:values="y;y-.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="x" smil:values="x;x+.4" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="width" smil:values="width;width*.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:decelerate="0.5" smil:attributeName="width" smil:values="width;width/.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="rotate" smil:values="0;-90" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-boomerang">
        <anim:transitionFilter smil:begin="0" smil:dur="1" smil:accelerate="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="y" smil:values="y;y+.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:decelerate="0.5" smil:attributeName="y" smil:values="y;y-.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="x" smil:values="x;x+.4" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="width" smil:values="width;width*.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:decelerate="0.5" smil:attributeName="width" smil:values="width;width/.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.5" smil:dur="0.5" smil:accelerate="0.5" smil:attributeName="rotate" smil:values="0;-90" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-bounce">
        <anim:transitionFilter smil:begin="1.82" smil:dur="0.18" smil:accelerate="0.5" smil:type="barWipe" smil:subtype="topToBottom" smil:mode="out"/>
        <anim:animate smil:begin="0" smil:dur="1.822" smil:attributeName="x" smil:values="x;x+0.25" smil:keyTimes="0;1" smil:keySplines="0,0;0.14,0.31;0.43,0.73;0.71,0.91;1,1"/>
        <anim:animate smil:begin="1.822" smil:dur="0.178" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="0.664" smil:attributeName="y" smil:values="y;y+0.026;y+0.052;y+0.078;y+0.103;y+0.151;y+0.196;y+0.236;y+0.270;y+0.297;y+0.317;y+0.329;y+0.333" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.3;0.4;0.5;0.6;0.7;0.8;0.9;1" smil:keySplines="0,0;0.25,0.07;0.5,0.2;0.75,0.467;1,1"/>
        <anim:animate smil:begin="0.664" smil:dur="0.664" smil:attributeName="y" smil:values="y;y-0.034;y-0.065;y-0.090;y-0.106;y-0.111;y-0.106;y-0.090;y-0.065;y-0.034;y" smil:keyTimes="0;0.1;0.2;0.3;0.4;0.5;0.6;0.7;0.8;0.9;1" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animate smil:begin="1.324" smil:dur="0.332" smil:attributeName="y" smil:values="y;y-0.011;y-0.022;y-0.030;y-0.035;y-0.037;y-0.035;y-0.030;y-0.022;y-0.011;y" smil:keyTimes="0;0.1;0.2;0.3;0.4;0.5;0.6;0.7;0.8;0.9;1" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animate smil:begin="1.656" smil:dur="0.164" smil:attributeName="y" smil:values="y;y-0.004;y-0.007;y-0.010;y-0.012;y-0.0123;y-0.012;y-0.010;y-0.007;y-0.004;y" smil:keyTimes="0;0.1;0.2;0.3;0.4;0.5;0.6;0.7;0.8;0.9;1" smil:keySplines="0,0;0.125,0.2665;0.25,0.4;0.375,0.465;0.5,0.5;0.625,0.535;0.75,0.6;0.875,0.7335;1,1"/>
        <anim:animate smil:begin="1.82" smil:dur="0.18" smil:accelerate="0.5" smil:attributeName="y" smil:values="y;y+height" smil:keyTimes="0;1"/>
        <anim:animateTransform smil:begin="0.62" smil:dur="0.026" smil:to="1,0.6" svg:type="scale"/>
        <anim:animateTransform smil:begin="0.646" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.312" smil:dur="0.026" smil:to="1,0.8" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.338" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.642" smil:dur="0.026" smil:to="1,0.9" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.668" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.808" smil:dur="0.026" smil:to="1,0.95" svg:type="scale"/>
        <anim:animateTransform smil:begin="1.834" smil:dur="0.166" smil:decelerate="0.5" smil:to="1,1" svg:type="scale"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-movie-credits">
        <anim:animate smil:dur="15" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="15" smil:attributeName="y" smil:values="y-1;y+1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="14.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-curve-down">
        <anim:animateTransform smil:begin="0" smil:dur="1" smil:accelerate="0.5" smil:from="1,1" smil:to="2.5,2.5" svg:type="scale"/>
        <anim:animateMotion smil:begin="0" smil:dur="1" smil:accelerate="0.5" svg:path="M 0.0000 0.0000 C 0.03802 0.0 0.1441 0.02341 0.1826 0.0915 C 0.22118 0.15964 0.24705 0.31256 0.2318 0.4083 C 0.21649 0.50394 0.20747 0.57948 0.0908 0.6661 C -0.02552 0.75279 -0.37517 0.88508 -0.4674 0.9289"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-flip" anim:iterate-type="by-letter" anim:iterate-interval="0.1s">
        <anim:animate smil:begin="0" smil:dur="0.5" smil:autoReverse="true" smil:attributeName="width" smil:from="(width)" smil:to="(-width*2)"/>
        <anim:animate smil:begin="0" smil:dur="0.5" smil:decelerate="0.5" smil:autoReverse="true" smil:attributeName="x" smil:by="(width*0.50)"/>
        <anim:animate smil:begin="0" smil:dur="1" smil:attributeName="y" smil:from="(y)" smil:to="(1+height/2)"/>
        <anim:animateTransform smil:begin="0" smil:dur="1" smil:by="360" svg:type="rotate"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-float">
        <anim:transitionFilter smil:begin="0.2" smil:dur="0.8" smil:accelerate="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:begin="0.2" smil:dur="0.8" smil:accelerate="1" smil:attributeName="rotate" smil:values="0;-90" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.2" smil:decelerate="1" smil:attributeName="x" smil:values="x;x-0.05" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.2" smil:decelerate="1" smil:attributeName="y" smil:values="y;y+0.1" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.2" smil:dur="0.8" smil:accelerate="1" smil:attributeName="x" smil:values="x;x+0.4+0.05" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0.2" smil:dur="0.8" smil:accelerate="1" smil:attributeName="y" smil:values="y;y-0.4-0.1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:text-only="true" smil:begin="0" smil:fill="hold" smil:decelerate="1" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-fold">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;width*2.5" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;height*0.01" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;height+1" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:decelerate="1" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-glide">
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;width*0.05" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x-.2" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-breaks">
        <anim:animate smil:begin="0" smil:dur="1" smil:attributeName="x" smil:from="(x)" smil:to="(x+1)"/>
        <anim:animate smil:begin="0" smil:dur="0.2" smil:accelerate="0.5" smil:attributeName="skewX" smil:from="0" smil:to="-1"/>
        <anim:set smil:begin="0.2" smil:dur="0.8" smil:attributeName="skewX" smil:to="-1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-magnify">
        <anim:transitionFilter smil:begin="1.23" smil:dur="0.77" smil:accelerate="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animateTransform smil:begin="1.23" smil:dur="0.77" smil:accelerate="1" smil:from="2,4.5" smil:to="0.1,0.1" svg:type="scale"/>
        <anim:animateTransform smil:dur="1.23" smil:decelerate="1" smil:from="1,1" smil:to="2,4.5" svg:type="scale"/>
        <anim:animate smil:dur="1.23" smil:decelerate="1" smil:attributeName="x" smil:from="(x)" smil:to="(0.5)"/>
        <anim:animate smil:begin="1.23" smil:dur="0.77" smil:attributeName="x" smil:from="(0.5)" smil:to="(0.5)"/>
        <anim:animate smil:dur="1.23" smil:decelerate="1" smil:attributeName="y" smil:from="(y)" smil:to="(y+0.4)"/>
        <anim:animate smil:begin="1.23" smil:dur="0.77" smil:attributeName="y" smil:from="(y)" smil:to="(y)"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-pinwheel">
        <anim:transitionFilter smil:dur="2" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:animate smil:dur="2" smil:attributeName="rotate" smil:values="0;720" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="2" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="2" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:set smil:begin="1.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-sling">
        <anim:animate smil:dur="1" smil:attributeName="rotate" smil:values="0;90;90;90" smil:keyTimes="0;0.2;0.2;1"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;0.949999988079071;-1" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:transitionFilter smil:dur="1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-spiral-out">
        <anim:animate smil:dur="1" smil:attributeName="width" smil:values="width;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="height" smil:values="height;0" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="1" smil:attributeName="x" smil:values="x;x+-0.0500*(x*0.9511+(1-y)*0.3090);x+-0.1000*(x*0.8090+(1-y)*0.5878);x+-0.1500*(x*0.5878+(1-y)*0.8090);x+-0.2000*(x*0.3090+(1-y)*0.9511);x+-0.2500*(x*-0.0000+(1-y)*1.0000);x+-0.3000*(x*-0.3090+(1-y)*0.9511);x+-0.3500*(x*-0.5878+(1-y)*0.8090);x+-0.4000*(x*-0.8090+(1-y)*0.5878);x+-0.4500*(x*-0.9511+(1-y)*0.3090);x+-0.5000*(x*-1.0000+(1-y)*-0.0000);x+-0.5500*(x*-0.9511+(1-y)*-0.3090);x+-0.6000*(x*-0.8090+(1-y)*-0.5878);x+-0.6500*(x*-0.5878+(1-y)*-0.8090);x+-0.7000*(x*-0.3090+(1-y)*-0.9511);x+-0.7500*(x*0.0000+(1-y)*-1.0000);x+-0.8000*(x*0.3090+(1-y)*-0.9511);x+-0.8500*(x*0.5878+(1-y)*-0.8090);x+-0.9000*(x*0.8090+(1-y)*-0.5878);x+-0.9500*(x*0.9511+(1-y)*-0.3090);x+-1.0000*(x*1.0000+(1-y)*0.0000)" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:animate smil:dur="1" smil:attributeName="y" smil:values="y;y+-0.0500*(x*0.3090-(1-y)*0.9511);y+-0.1000*(x*0.5878-(1-y)*0.8090);y+-0.1500*(x*0.8090-(1-y)*0.5878);y+-0.2000*(x*0.9511-(1-y)*0.3090);y+-0.2500*(x*1.0000-(1-y)*-0.0000);y+-0.3000*(x*0.9511-(1-y)*-0.3090);y+-0.3500*(x*0.8090-(1-y)*-0.5878);y+-0.4000*(x*0.5878-(1-y)*-0.8090);y+-0.4500*(x*0.3090-(1-y)*-0.9511);y+-0.5000*(x*-0.0000-(1-y)*-1.0000);y+-0.5500*(x*-0.3090-(1-y)*-0.9511);y+-0.6000*(x*-0.5878-(1-y)*-0.8090);y+-0.6500*(x*-0.8090-(1-y)*-0.5878);y+-0.7000*(x*-0.9511-(1-y)*-0.3090);y+-0.7500*(x*-1.0000-(1-y)*0.0000);y+-0.8000*(x*-0.9511-(1-y)*0.3090);y+-0.8500*(x*-0.8090-(1-y)*0.5878);y+-0.9000*(x*-0.5878-(1-y)*0.8090);y+-0.9500*(x*-0.3090-(1-y)*0.9511);y+-1.0000*(x*0.0000-(1-y)*1.0000)" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" smil:accelerate="0.5" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-swish" anim:iterate-type="by-letter" anim:iterate-interval="0.5s">
        <anim:animate smil:begin="0" smil:dur="1" smil:attributeName="rotate" smil:values="0;45" smil:keyTimes="0;1"/>
        <anim:animate smil:begin="0" smil:dur="1" smil:attributeName="y" smil:values="y;y+1" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par pres:preset-property="Direction" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-swivel" pres:preset-sub-type="vertical">
        <anim:animate smil:dur="5" smil:attributeName="height" smil:values="height;height" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="5" smil:attributeName="width" smil:values="width;0.92*width;0.71*width;0.38*width;0;-0.38*width;-0.71*width;-0.92*width;-width;-0.92*width;-0.71*width;-0.38*width;0;0.38*width;0.71*width;0.92*width;width;0.92*width;0.71*width;0.38*width;0" smil:keyTimes="0;0.05;0.1;0.15;0.2;0.25;0.3;0.35;0.4;0.45;0.5;0.55;0.6;0.65;0.7;0.75;0.8;0.85;0.9;0.95;1"/>
        <anim:set smil:begin="4.999" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:decelerate="1" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-thread">
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;height/20;height/20" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;width+.3;width+.3" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x;x-.3" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:iterate pres:text-only="true" smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="exit" pres:preset-id="ooo-exit-whip" anim:iterate-type="by-letter" anim:iterate-interval="0.05s">
        <anim:animate smil:dur="0.5" smil:attributeName="x" smil:values="x;x+.1;x" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="y" smil:values="y;y" smil:keyTimes="0;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="height" smil:values="height;height+.01;height/10" smil:keyTimes="0;0.5;1"/>
        <anim:animate smil:dur="0.5" smil:attributeName="width" smil:values="width;width+.01;width/10" smil:keyTimes="0;0.5;1"/>
        <anim:transitionFilter smil:dur="0.5" smil:keySplines="0,0;0.5,0;1,1" smil:type="fade" smil:subtype="crossfade" smil:mode="out"/>
        <anim:set smil:begin="0.499" smil:dur="0.001" smil:fill="hold" smil:attributeName="visibility" smil:to="hidden"/>
    </anim:iterate>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-4-point-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.091 -0.0453  L 0.125 -0.16655  L 0.158 -0.0453  L 0.249 0  L 0.158 0.0453  L 0.125 0.16655  L 0.091 0.0453  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-5-point-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.029 0.12125  L 0.125 0.12125  L 0.048 0.19586  L 0.077 0.31711  L 0 0.2425  L -0.077 0.31711  L -0.048 0.19586  L -0.125 0.12125  L -0.029 0.12125  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-6-point-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.036 0.08261  L 0.108 0.08261  L 0.072 0.16655  L 0.108 0.24916  L 0.036 0.24916  L 0 0.3331  L -0.036 0.24916  L -0.108 0.24916  L -0.072 0.16655  L -0.108 0.08261  L -0.036 0.08261  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-8-point-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.052 0  L 0.089 -0.0493  L 0.125 0  L 0.177 0  L 0.177 0.06929  L 0.213 0.11858  L 0.177 0.16655  L 0.177 0.23584  L 0.125 0.23584  L 0.089 0.2838  L 0.052 0.23584  L 0 0.23584  L 0 0.16655  L -0.037 0.11858  L 0 0.06929  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-circle">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.069 0  0.125 0.07461  0.125 0.16655  C 0.125 0.25849  0.069 0.3331  0 0.3331  C -0.069 0.3331  -0.125 0.25849  -0.125 0.16655  C -0.125 0.07461  -0.069 0  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-crescent-moon">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.014 -0.00666  -0.029 -0.01199  -0.044 -0.01199  C -0.114 -0.01199  -0.169 0.06396  -0.169 0.15589  C -0.169 0.2465  -0.114 0.32111  -0.044 0.32111  C -0.029 0.32111  -0.014 0.31711  0 0.31045  C -0.047 0.28647  -0.08 0.22651  -0.08 0.15589  C -0.08 0.08394  -0.047 0.02398  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-diamond">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 -0.11192  L 0.25 0  L 0.125 0.11192  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-equal-triangle">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 0.2878  L -0.125 0.2878  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-oval">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.03 -0.05063  0.075 -0.08261  0.125 -0.08261  C 0.175 -0.08261  0.22 -0.05063  0.25 0  C 0.22 0.05063  0.175 0.08261  0.125 0.08261  C 0.075 0.08261  0.03 0.05063  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-heart">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.012 -0.02398  0.033 -0.05863  0.058 -0.05863  C 0.095 -0.05863  0.125 -0.02265  0.125 0.02265  C 0.125 0.03731  0.122 0.05063  0.116 0.06262  C 0.117 0.06262  0 0.2425  0 0.24383  C 0 0.2425  -0.117 0.06262  -0.116 0.06262  C -0.122 0.05063  -0.125 0.03731  -0.125 0.02265  C -0.125 -0.02265  -0.095 -0.05863  -0.057 -0.05863  C -0.033 -0.05863  -0.012 -0.02398  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-hexagon">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 0  L 0.188 0.14523  L 0.125 0.28913  L 0 0.28913  L -0.063 0.14523  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-octagon">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.073 -0.09727  L 0.177 -0.09727  L 0.25 0  L 0.25 0.13857  L 0.177 0.23584  L 0.073 0.23584  L 0 0.13857  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-parallelogram">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.178 0  L 0.25 0.16122  L 0.072 0.16122  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-pentagon">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 0.12125  L 0.077 0.31711  L -0.077 0.31711  L -0.125 0.12125  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-right-triangle">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0 -0.19586  L 0.25 0  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-square">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.25 0  L 0.25 0.3331  L 0 0.3331  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-teardrop">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.001 0.0453  0.011 0.08661  0.028 0.11325  C 0.028 0.11459  0.055 0.15056  0.055 0.14923  C 0.07 0.16922  0.079 0.1972  0.079 0.22651  C 0.079 0.28514  0.044 0.33177  0 0.3331  C -0.044 0.33177  -0.079 0.28514  -0.079 0.22651  C -0.079 0.1972  -0.07 0.16922  -0.055 0.14923  C -0.055 0.15056  -0.028 0.11459  -0.028 0.11325  C -0.011 0.08661  -0.001 0.0453  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-trapezoid">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.167 0  L 0.21 0.22251  L -0.04 0.22251  L 0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-arc-down">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.067 0.0533  C 0.081 0.06529  0.102 0.07195  0.124 0.07195  C 0.149 0.07195  0.169 0.06529  0.183 0.0533  L 0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-arc-left">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L -0.04 0.08927  C -0.049 0.10793  -0.054 0.13591  -0.054 0.16522  C -0.054 0.19853  -0.049 0.22518  -0.04 0.24383  L 0 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-arc-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.04 0.08927  C 0.049 0.10793  0.054 0.13591  0.054 0.16522  C 0.054 0.19853  0.049 0.22518  0.04 0.24383  L 0 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-arc-up">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.067 -0.0533  C 0.081 -0.06529  0.102 -0.07195  0.124 -0.07195  C 0.149 -0.07195  0.169 -0.06529  0.183 -0.0533  L 0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-bounce-left">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  c -0.004 -0.01066  -0.018 -0.02132  -0.023 -0.02132  c -0.031 0  -0.063 0.16655  -0.063 0.3331  c 0 -0.08394  -0.016 -0.16655  -0.031 -0.16655  c -0.016 0  -0.031 0.08394  -0.031 0.16655  c 0 -0.0413  -0.008 -0.08394  -0.016 -0.08394  c -0.008 0  -0.016 0.0413  -0.016 0.08394  c 0 -0.02132  -0.004 -0.0413  -0.008 -0.0413  c -0.004 0  -0.008 0.02132  -0.008 0.0413  c 0 -0.01066  -0.002 -0.02132  -0.004 -0.02132  c -0.001 0  -0.004 0.01066  -0.004 0.02132  c 0 -0.00533  -0.001 -0.01066  -0.002 -0.01066  c 0 -0.00133  -0.002 0.00533  -0.002 0.01066  c 0 -0.00266  0 -0.00533  -0.001 -0.00533  c 0 0.00133  -0.001 0.00266  -0.001 0.00533  c 0 -0.00133  0 -0.00266  0 -0.004  c -0.001 0  -0.001 0.00133  -0.001 0.00266  c -0.001 0  -0.001 -0.00133  -0.001 -0.00266  c -0.001 0  -0.001 0.00133  -0.001 0.00266"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-bounce-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  c 0.004 -0.01066  0.018 -0.02132  0.023 -0.02132  c 0.031 0  0.063 0.16655  0.063 0.3331  c 0 -0.08394  0.016 -0.16655  0.031 -0.16655  c 0.016 0  0.031 0.08394  0.031 0.16655  c 0 -0.0413  0.008 -0.08394  0.016 -0.08394  c 0.008 0  0.016 0.0413  0.016 0.08394  c 0 -0.02132  0.004 -0.0413  0.008 -0.0413  c 0.004 0  0.008 0.02132  0.008 0.0413  c 0 -0.01066  0.002 -0.02132  0.004 -0.02132  c 0.001 0  0.004 0.01066  0.004 0.02132  c 0 -0.00533  0.001 -0.01066  0.002 -0.01066  c 0 0.00133  0.002 0.00533  0.002 0.01066  c 0 -0.00266  0 -0.00533  0.001 -0.00533  c 0 0.00133  0.001 0.00266  0.001 0.00533  c 0 -0.00133  0 -0.00266  0 -0.004  c 0.001 0  0.001 0.00133  0.001 0.00266  c 0.001 0  0.001 -0.00133  0.001 -0.00266  c 0.001 0  0.001 0.00133  0.001 0.00266"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-curvy-left">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.008 0.01066  0.017 0.02132  0.021 0.03464  C 0.025 0.0493  0.027 0.06662  0.029 0.08394  C 0.031 0.10126  0.029 0.11592  0.027 0.13191  C 0.025 0.14656  0.022 0.16255  0.015 0.17588  C 0.009 0.1892  -0.001 0.19986  -0.012 0.20786  C -0.022 0.21585  -0.034 0.22118  -0.046 0.22384  C -0.058 0.22651  -0.07 0.22651  -0.081 0.22384  C -0.093 0.22118  -0.104 0.21452  -0.113 0.20386  C -0.122 0.19453  -0.13 0.18254  -0.134 0.16788  C -0.139 0.15456  -0.141 0.13591  -0.141 0.12125  C -0.142 0.10659  -0.141 0.08927  -0.136 0.07461  C -0.131 0.06129  -0.122 0.05063  -0.11 0.0453  C -0.098 0.0413  -0.086 0.04663  -0.078 0.05596  C -0.071 0.06529  -0.066 0.07994  -0.065 0.09727  C -0.065 0.11459  -0.066 0.13058  -0.071 0.1439  C -0.076 0.15722  -0.075 0.15989  -0.095 0.17721  C -0.113 0.19586  -0.131 0.19053  -0.142 0.19187  C -0.153 0.19187  -0.162 0.18654  -0.173 0.18121  C -0.185 0.17455  -0.195 0.16255  -0.202 0.15189  C -0.209 0.14124  -0.212 0.12791  -0.216 0.10659  C -0.219 0.08527  -0.219 0.07461  -0.219 0.05863  C -0.219 0.04264  -0.219 0.02665  -0.219 0.01066"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-curvy-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.008 0.01066  -0.017 0.02132  -0.021 0.03464  C -0.025 0.0493  -0.027 0.06662  -0.029 0.08394  C -0.031 0.10126  -0.029 0.11592  -0.027 0.13191  C -0.025 0.14656  -0.022 0.16255  -0.015 0.17588  C -0.009 0.1892  0.001 0.19986  0.012 0.20786  C 0.022 0.21585  0.034 0.22118  0.046 0.22384  C 0.058 0.22651  0.07 0.22651  0.081 0.22384  C 0.093 0.22118  0.104 0.21452  0.113 0.20386  C 0.122 0.19453  0.13 0.18254  0.134 0.16788  C 0.139 0.15456  0.141 0.13591  0.141 0.12125  C 0.142 0.10659  0.141 0.08927  0.136 0.07461  C 0.131 0.06129  0.122 0.05063  0.11 0.0453  C 0.098 0.0413  0.086 0.04663  0.078 0.05596  C 0.071 0.06529  0.066 0.07994  0.065 0.09727  C 0.065 0.11459  0.066 0.13058  0.071 0.1439  C 0.076 0.15722  0.075 0.15989  0.095 0.17721  C 0.113 0.19586  0.131 0.19053  0.142 0.19187  C 0.153 0.19187  0.162 0.18654  0.173 0.18121  C 0.185 0.17455  0.195 0.16255  0.202 0.15189  C 0.209 0.14124  0.212 0.12791  0.216 0.10659  C 0.219 0.08527  0.219 0.07461  0.219 0.05863  C 0.219 0.04264  0.219 0.02665  0.219 0.01066"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-decaying-wave">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.002 0.07062  0.007 0.16922  0.025 0.16788  C 0.051 0.16788  0.053 -0.16255  0.084 -0.16389  C 0.112 -0.16389  0.097 0.12525  0.124 0.12391  C 0.152 0.12391  0.137 -0.08527  0.167 -0.08527  C 0.194 -0.08527  0.179 0.05596  0.203 0.05596  C 0.226 0.05596  0.214 -0.05196  0.235 -0.05196  C 0.247 -0.05196  0.248 -0.02265  0.249 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-diagonal-down-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.25 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-diagonal-up-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.25 -0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-down">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-funnel">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.001 0.03331  0.06 0.06262  0.137 0.06396  C 0.198 0.06662  0.248 0.05063  0.249 0.03065  C 0.249 0.01066  0.2 -0.00799  0.138 -0.00933  C 0.107 -0.00933  0.079 -0.00666  0.059 0  C 0.03 0.00933  0.013 0.02398  0.013 0.0413  C 0.013 0.05063  0.018 0.05996  0.027 0.06795  C 0.048 0.08527  0.089 0.09727  0.136 0.0986  C 0.191 0.10126  0.236 0.08661  0.236 0.06929  C 0.237 0.05063  0.192 0.03464  0.137 0.03198  C 0.109 0.03198  0.084 0.03464  0.065 0.03997  C 0.04 0.0493  0.024 0.06396  0.024 0.07861  C 0.024 0.08661  0.029 0.0946  0.037 0.1026  C 0.056 0.11725  0.092 0.12924  0.135 0.13058  C 0.185 0.13191  0.225 0.11858  0.225 0.1026  C 0.226 0.08661  0.186 0.07195  0.136 0.07062  C 0.111 0.06929  0.088 0.07195  0.071 0.07728  C 0.048 0.08527  0.035 0.09727  0.035 0.11192  C 0.035 0.11858  0.039 0.12658  0.046 0.13324  C 0.063 0.14656  0.096 0.15722  0.134 0.15856  C 0.179 0.15856  0.215 0.1479  0.215 0.13324  C 0.215 0.11858  0.18 0.10526  0.135 0.10393  C 0.113 0.10393  0.092 0.10659  0.077 0.11059  C 0.056 0.11725  0.044 0.12924  0.043 0.14124  C 0.043 0.1479  0.048 0.15456  0.054 0.15989  C 0.069 0.17321  0.099 0.18254  0.133 0.18254  C 0.173 0.18387  0.206 0.17455  0.206 0.16122  C 0.207 0.1479  0.174 0.13591  0.134 0.13457  C 0.114 0.13457  0.095 0.13591  0.082 0.14124  C 0.063 0.14656  0.052 0.15722  0.052 0.16788  C 0.052 0.17455  0.055 0.17988  0.061 0.1852  C 0.075 0.1972  0.101 0.20519  0.132 0.20652  C 0.169 0.20652  0.198 0.19853  0.198 0.18654  C 0.199 0.17455  0.17 0.16389  0.133 0.16255  C 0.115 0.16255  0.099 0.16389  0.087 0.16788  C 0.07 0.17321  0.06 0.18254  0.06 0.1932  C 0.06 0.19853  0.063 0.20253  0.068 0.20786  C 0.08 0.21851  0.104 0.22518  0.132 0.22651  C 0.165 0.22784  0.191 0.21985  0.191 0.20786  C 0.191 0.19853  0.166 0.18787  0.133 0.18787  C 0.116 0.18654  0.101 0.1892  0.09 0.19187  C 0.075 0.1972  0.066 0.20519  0.066 0.21452  C 0.066 0.21985  0.069 0.22384  0.074 0.22784  C 0.085 0.23717  0.107 0.24383  0.131 0.24516  C 0.161 0.2465  0.185 0.2385  0.185 0.22917  C 0.185 0.21851  0.161 0.21052  0.132 0.20919  C 0.118 0.20919  0.104 0.21052  0.094 0.21452  C 0.08 0.21851  0.072 0.22518  0.072 0.2345  C 0.072 0.2385  0.075 0.2425  0.079 0.2465  C 0.089 0.25449  0.108 0.26115  0.131 0.26115  C 0.157 0.26248  0.179 0.25582  0.179 0.2465  C 0.179 0.2385  0.158 0.23051  0.131 0.23051  C 0.119 0.22917  0.106 0.23051  0.097 0.23317  C 0.085 0.2385  0.078 0.24516  0.078 0.25183  C 0.078 0.25582  0.08 0.25982  0.084 0.26248  C 0.093 0.27048  0.11 0.27581  0.131 0.27714  C 0.155 0.27714  0.174 0.27048  0.174 0.26382  C 0.174 0.25582  0.155 0.24783  0.131 0.24783  C 0.119 0.24783  0.108 0.24916  0.101 0.25183  C 0.089 0.25449  0.083 0.26115  0.083 0.26781  C 0.083 0.27048  0.085 0.27448  0.088 0.27714  C 0.096 0.28514  0.112 0.28913  0.13 0.29046  C 0.152 0.29046  0.169 0.28514  0.169 0.27847  C 0.169 0.27048  0.152 0.26515  0.131 0.26382  C 0.12 0.26382  0.11 0.26515  0.103 0.26781  C 0.093 0.27048  0.087 0.27581  0.087 0.28247  C 0.087 0.28514  0.089 0.2878  0.092 0.29046"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-heartbeat">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.017 0  C 0.025 0  0.034 -0.01865  0.042 -0.02132  C 0.048 -0.02132  0.059 -0.004  0.064 -0.004  C 0.071 -0.004  0.078 -0.00933  0.091 -0.00933  L 0.1 -0.21585  L 0.11 0.03331  L 0.122 0  L 0.132 -0.00933  L 0.156 -0.00133  C 0.167 -0.00533  0.176 -0.02265  0.187 -0.02931  C 0.191 -0.03065  0.2 -0.03198  0.206 -0.02931  C 0.212 -0.02665  0.217 -0.00799  0.219 -0.00666  C 0.222 -0.00133  0.229 -0.00666  0.233 -0.004  L 0.239 0  L 0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-left">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L -0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-s-curve-1">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0 -0.04663  0.028 -0.08261  0.062 -0.08261  C 0.097 -0.08261  0.125 -0.04663  0.125 0  C 0.125 0.04663  0.153 0.08261  0.188 0.08261  C 0.222 0.08261  0.25 0.04663  0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-s-curve-2">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0 0.04663  0.028 0.08261  0.062 0.08261  C 0.097 0.08261  0.125 0.04663  0.125 0  C 0.125 -0.04663  0.153 -0.08261  0.188 -0.08261  C 0.222 -0.08261  0.25 -0.04663  0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-sine-wave">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.003 -0.02532  0.007 -0.0493  0.015 -0.0493  C 0.024 -0.0493  0.027 -0.02532  0.03 0  C 0.034 0.02798  0.037 0.05596  0.047 0.05596  C 0.056 0.05596  0.059 0.02798  0.063 0  C 0.065 -0.02532  0.069 -0.0493  0.078 -0.0493  C 0.086 -0.0493  0.09 -0.02532  0.093 0  C 0.096 0.02798  0.1 0.05596  0.109 0.05596  C 0.118 0.05596  0.125 0  0.125 0  C 0.128 -0.02532  0.131 -0.0493  0.14 -0.0493  C 0.149 -0.0493  0.152 -0.02532  0.155 0  C 0.159 0.02798  0.162 0.05596  0.172 0.05596  C 0.181 0.05596  0.184 0.02798  0.187 0  C 0.191 -0.02532  0.194 -0.0493  0.203 -0.0493  C 0.211 -0.0493  0.215 -0.02532  0.218 0  C 0.221 0.02798  0.225 0.05596  0.234 0.05596  C 0.243 0.05596  0.246 0.02798  0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-spiral-left">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.004 -0.08927  -0.046 -0.16655  -0.113 -0.17188  C -0.177 -0.17854  -0.237 -0.11858  -0.241 -0.03198  C -0.246 0.04797  -0.204 0.12258  -0.144 0.12791  C -0.089 0.13191  -0.037 0.08261  -0.033 0.00799  C -0.029 -0.05996  -0.064 -0.12391  -0.115 -0.12924  C -0.162 -0.13324  -0.206 -0.09194  -0.209 -0.02931  C -0.212 0.02665  -0.184 0.08128  -0.142 0.08394  C -0.104 0.08794  -0.068 0.05596  -0.065 0.00533  C -0.063 -0.03997  -0.084 -0.08394  -0.117 -0.08661  C -0.146 -0.08927  -0.175 -0.06529  -0.177 -0.02665  C -0.179 0.00666  -0.164 0.03864  -0.14 0.0413  C -0.12 0.04397  -0.099 0.02931  -0.098 0.00266  C -0.096 -0.01865  -0.104 -0.0413  -0.119 -0.04397  C -0.131 -0.04397  -0.143 -0.03864  -0.145 -0.02398  C -0.146 -0.01466  -0.144 -0.00533  -0.138 -0.00133  C -0.135 0  -0.133 0  -0.13 -0.00133"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-spiral-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.004 -0.08927  0.046 -0.16655  0.113 -0.17188  C 0.177 -0.17854  0.237 -0.11858  0.241 -0.03198  C 0.246 0.04797  0.204 0.12258  0.144 0.12791  C 0.089 0.13191  0.037 0.08261  0.033 0.00799  C 0.029 -0.05996  0.064 -0.12391  0.115 -0.12924  C 0.162 -0.13324  0.206 -0.09194  0.209 -0.02931  C 0.212 0.02665  0.184 0.08128  0.142 0.08394  C 0.104 0.08794  0.068 0.05596  0.065 0.00533  C 0.063 -0.03997  0.084 -0.08394  0.117 -0.08661  C 0.146 -0.08927  0.175 -0.06529  0.177 -0.02665  C 0.179 0.00666  0.164 0.03864  0.14 0.0413  C 0.12 0.04397  0.099 0.02931  0.098 0.00266  C 0.096 -0.01865  0.104 -0.0413  0.119 -0.04397  C 0.131 -0.04397  0.143 -0.03864  0.145 -0.02398  C 0.146 -0.01466  0.144 -0.00533  0.138 -0.00133  C 0.135 0  0.133 0  0.13 -0.00133"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-spring">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.066 0.00799  -0.115 0.02798  -0.115 0.04397  C -0.115 0.05863  -0.067 0.06929  -0.003 0.06929  C 0.061 0.06929  0.115 0.05863  0.115 0.04397  C 0.115 0.02798  0.059 0.02398  -0.005 0.03464  C -0.068 0.04663  -0.115 0.06662  -0.115 0.08128  C -0.115 0.09593  -0.066 0.10793  -0.003 0.10793  C 0.061 0.10793  0.115 0.09593  0.115 0.08128  C 0.115 0.06662  0.059 0.06262  -0.004 0.07328  C -0.068 0.08394  -0.115 0.10393  -0.115 0.11858  C -0.115 0.13457  -0.066 0.14656  -0.002 0.14656  C 0.061 0.14656  0.115 0.13457  0.115 0.11858  C 0.115 0.10526  0.059 0.10126  -0.004 0.11059  C -0.067 0.12125  -0.115 0.14257  -0.115 0.15722  C -0.115 0.17188  -0.065 0.18387  -0.002 0.18387  C 0.063 0.18387  0.115 0.17188  0.115 0.15722  C 0.115 0.14257  0.06 0.13857  -0.003 0.14923  C -0.066 0.15989  -0.115 0.17988  -0.115 0.19453  C -0.115 0.21052  -0.065 0.22118  -0.001 0.22118  C 0.063 0.22118  0.115 0.20919  0.115 0.19453  C 0.115 0.17988  0.06 0.17588  -0.003 0.18654  C -0.066 0.1972  -0.115 0.21851  -0.115 0.23184  C -0.115 0.2465  -0.064 0.25849  -0.001 0.25849  C 0.063 0.25849  0.115 0.2465  0.115 0.23184  C 0.115 0.21851  0.061 0.21452  -0.003 0.22384  C -0.066 0.2345  -0.115 0.25582  -0.115 0.27048  C -0.115 0.2838  -0.064 0.29713  0 0.29713  C 0.064 0.29713  0.115 0.28514  0.115 0.27048  C 0.115 0.25582  0.061 0.25183  -0.002 0.26248  C -0.065 0.27314  -0.116 0.29313  -0.115 0.30779  C -0.114 0.32244  -0.064 0.3331  0 0.3331  C 0.064 0.3331  0.115 0.32111  0.115 0.30645  C 0.115 0.29313  0.063 0.28913  0 0.30112"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-stairs-down">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797  l 0.036 0  l 0 0.04797"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-turn-down">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 0  C 0.181 0  0.25 0.09194  0.25 0.16655  L 0.25 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-turn-down-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0 0.16655  C 0 0.24117  0.069 0.3331  0.125 0.3331  L 0.25 0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-turn-up">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.125 0  C 0.181 0  0.25 -0.09194  0.25 -0.16655  L 0.25 -0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-turn-up-right">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0 -0.16655  C 0 -0.24117  0.069 -0.3331  0.125 -0.3331  L 0.25 -0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-up">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0 -0.3331"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-wave">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.002 0.08394  0.009 0.1439  0.016 0.1439  C 0.023 0.1439  0.029 0.08394  0.031 0  C 0.034 0.08394  0.04 0.1439  0.047 0.1439  C 0.054 0.1439  0.06 0.08394  0.062 0  C 0.065 0.08394  0.071 0.1439  0.078 0.1439  C 0.085 0.1439  0.092 0.08394  0.094 0  C 0.096 0.08394  0.102 0.1439  0.11 0.1439  C 0.116 0.1439  0.123 0.08394  0.125 0  C 0.127 0.08394  0.134 0.1439  0.141 0.1439  C 0.148 0.1439  0.154 0.08394  0.156 0  C 0.159 0.08394  0.165 0.1439  0.172 0.1439  C 0.179 0.1439  0.185 0.08394  0.188 0  C 0.19 0.08394  0.196 0.1439  0.203 0.1439  C 0.21 0.1439  0.217 0.08394  0.219 0  C 0.221 0.08394  0.227 0.1439  0.235 0.1439  C 0.242 0.1439  0.248 0.08394  0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-zigzag">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  L 0.016 0.13191  L 0.031 0  L 0.047 0.13191  L 0.063 0  L 0.078 0.13191  L 0.094 0  L 0.109 0.13191  L 0.125 0  L 0.141 0.13191  L 0.156 0  L 0.172 0.13191  L 0.187 0  L 0.203 0.13191  L 0.219 0  L 0.234 0.13191  L 0.25 0"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-bean">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.002 -0.004  0.012 -0.0453  0.037 -0.04264  C 0.075 -0.03864  0.09 -0.00933  0.125 -0.03864  C 0.147 -0.05596  0.173 -0.09993  0.192 -0.0986  C 0.235 -0.09727  0.244 -0.05196  0.244 -0.01066  C 0.245 0.04797  0.189 0.09727  0.121 0.1026  C 0.052 0.10659  -0.005 0.04397  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-buzz-saw">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.022 -0.02265  -0.033 -0.06129  -0.027 -0.09993  C -0.024 -0.11325  -0.02 -0.12658  -0.014 -0.13724  C -0.01 -0.10659  0.004 -0.07861  0.025 -0.06129  C 0.025 -0.0986  0.041 -0.13457  0.068 -0.15056  C 0.077 -0.15722  0.087 -0.15989  0.097 -0.16122  C 0.082 -0.13857  0.074 -0.10659  0.077 -0.07328  C 0.099 -0.09727  0.13 -0.1026  0.157 -0.08527  C 0.166 -0.07994  0.175 -0.07062  0.181 -0.06129  C 0.158 -0.06396  0.134 -0.05196  0.117 -0.02798  C 0.144 -0.01999  0.167 0.00799  0.174 0.04663  C 0.176 0.05996  0.176 0.07328  0.174 0.08661  C 0.161 0.06129  0.139 0.04397  0.115 0.0413  C 0.127 0.07461  0.124 0.11592  0.106 0.14656  C 0.099 0.15722  0.091 0.16655  0.082 0.17188  C 0.089 0.14257  0.085 0.10926  0.072 0.08261  C 0.06 0.11592  0.034 0.13857  0.004 0.13857  C -0.007 0.13857  -0.017 0.13591  -0.026 0.13058  C -0.004 0.11992  0.013 0.0946  0.021 0.06396  C -0.007 0.07195  -0.036 0.05996  -0.055 0.02931  C -0.062 0.01732  -0.066 0.00533  -0.069 -0.00799  C -0.049 0.00933  -0.023 0.01199  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-curved-square">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0 -0.04264  0.026 -0.07728  0.058 -0.07728  L 0.192 -0.07728  C 0.224 -0.07728  0.25 -0.04264  0.25 0  L 0.25 0.17588  C 0.25 0.21851  0.224 0.25449  0.192 0.25449  L 0.058 0.25449  C 0.026 0.25449  0 0.21851  0 0.17588  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-curved-x">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.006 0.00799  0.011 0.01466  0.015 0.02265  C 0.02 0.01466  0.024 0.00799  0.03 0  C 0.065 -0.04663  0.107 -0.06662  0.124 -0.0453  C 0.14 -0.02265  0.125 0.03331  0.09 0.07994  C 0.084 0.08661  0.079 0.09327  0.073 0.09993  C 0.079 0.10526  0.084 0.11192  0.09 0.11992  C 0.125 0.16655  0.14 0.22251  0.124 0.24383  C 0.107 0.26648  0.065 0.2465  0.03 0.19986  C 0.024 0.19187  0.02 0.1852  0.015 0.17721  C 0.011 0.1852  0.006 0.19187  0 0.19986  C -0.035 0.2465  -0.077 0.26648  -0.094 0.24383  C -0.11 0.22251  -0.095 0.16655  -0.06 0.11992  C -0.054 0.11192  -0.049 0.10526  -0.043 0.09993  C -0.049 0.09327  -0.054 0.08661  -0.06 0.07994  C -0.095 0.03331  -0.11 -0.02265  -0.094 -0.0453  C -0.077 -0.06662  -0.035 -0.04663  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-curvy-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.072 0.07728  0.1 0.20253  0.077 0.31711  C -0.015 0.31045  -0.093 0.23051  -0.125 0.12125  C -0.047 0.0533  0.051 0.05729  0.125 0.12125  C 0.092 0.23717  0.011 0.31045  -0.077 0.31711  C -0.101 0.1972  -0.068 0.07461  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-figure-8-four">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.017 0  0.031 0.01865  0.031 0.0413  C 0.031 0.06529  0.017 0.08394  0 0.08394  C -0.017 0.08394  -0.031 0.1026  -0.031 0.12525  C -0.031 0.1479  -0.017 0.16655  0 0.16655  C 0.017 0.16655  0.031 0.1852  0.031 0.20786  C 0.031 0.23051  0.017 0.24916  0 0.24916  C -0.017 0.24916  -0.031 0.26781  -0.031 0.2918  C -0.031 0.31445  -0.017 0.3331  0 0.3331  C 0.017 0.3331  0.031 0.31445  0.031 0.2918  C 0.031 0.26781  0.017 0.24916  0 0.24916  C -0.017 0.24916  -0.031 0.23051  -0.031 0.20786  C -0.031 0.1852  -0.017 0.16655  0 0.16655  C 0.017 0.16655  0.031 0.1479  0.031 0.12525  C 0.031 0.1026  0.017 0.08394  0 0.08394  C -0.017 0.08394  -0.031 0.06529  -0.031 0.0413  C -0.031 0.01865  -0.017 0  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-horizontal-figure-8">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0 0.04397  0.027 0.07994  0.06 0.07994  C 0.099 0.07994  0.113 0.03997  0.119 0.01599  L 0.125 -0.01599  C 0.131 -0.03997  0.146 -0.07994  0.19 -0.07994  C 0.218 -0.07994  0.25 -0.04397  0.25 0  C 0.25 0.04397  0.218 0.07994  0.19 0.07994  C 0.146 0.07994  0.131 0.03997  0.125 0.01599  L 0.119 -0.01599  C 0.113 -0.03997  0.099 -0.07994  0.06 -0.07994  C 0.027 -0.07994  0 -0.04397  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-inverted-square">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.004 -0.00533  0.01 -0.00799  0.015 -0.00799  C 0.022 -0.00799  0.029 -0.004  0.033 0.00266  C 0.05 0.02931  0.063 0.08794  0.063 0.15722  C 0.063 0.15722  0.063 0.15856  0.063 0.15856  C 0.063 0.15856  0.063 0.15989  0.063 0.15989  C 0.063 0.22917  0.05 0.28913  0.033 0.31578  C 0.029 0.32111  0.022 0.32511  0.015 0.32511  C 0.01 0.32511  0.004 0.32244  0 0.31711  C -0.004 0.31178  -0.006 0.30512  -0.006 0.29713  C -0.006 0.2878  -0.003 0.27981  0.002 0.27448  C 0.022 0.25049  0.066 0.23317  0.118 0.23317  C 0.118 0.23317  0.119 0.23317  0.119 0.23317  C 0.119 0.23317  0.12 0.23317  0.12 0.23317  C 0.172 0.23317  0.217 0.25049  0.237 0.27448  C 0.241 0.27981  0.244 0.2878  0.244 0.29713  C 0.244 0.30512  0.242 0.31178  0.238 0.31711  C 0.234 0.32244  0.229 0.32511  0.223 0.32511  C 0.216 0.32511  0.21 0.32111  0.206 0.31578  C 0.188 0.28913  0.175 0.22917  0.175 0.15989  C 0.175 0.15989  0.175 0.15856  0.175 0.15856  C 0.175 0.15856  0.175 0.15722  0.175 0.15722  C 0.175 0.08794  0.188 0.02931  0.206 0.00133  C 0.21 -0.004  0.216 -0.00799  0.223 -0.00799  C 0.229 -0.00799  0.234 -0.00533  0.238 0  C 0.242 0.00533  0.244 0.01332  0.244 0.01999  C 0.244 0.02931  0.241 0.03731  0.237 0.04397  C 0.217 0.06662  0.172 0.08394  0.12 0.08394  C 0.12 0.08394  0.12 0.08394  0.119 0.08394  C 0.119 0.08394  0.118 0.08394  0.118 0.08394  C 0.066 0.08394  0.022 0.06662  0.002 0.04397  C -0.003 0.03731  -0.006 0.02931  -0.006 0.01999  C -0.006 0.01332  -0.004 0.00533  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-inverted-triangle">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.015 0.03198  0.037 0.06529  0.055 0.07861  C 0.082 0.09993  0.108 0.10793  0.113 0.09727  C 0.117 0.08661  0.099 0.05996  0.072 0.03864  C 0.054 0.02532  0.021 0.01599  -0.008 0.01466  C -0.036 0.01599  -0.07 0.02532  -0.088 0.03864  C -0.115 0.05996  -0.133 0.08661  -0.128 0.09727  C -0.123 0.10793  -0.097 0.09993  -0.071 0.07861  C -0.053 0.06529  -0.03 0.03198  -0.016 0  C -0.001 -0.03331  0.009 -0.07728  0.009 -0.10526  C 0.009 -0.1479  0.002 -0.18121  -0.008 -0.18121  C -0.017 -0.18121  -0.025 -0.1479  -0.025 -0.10526  C -0.025 -0.07728  -0.014 -0.03331  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-loop-de-loop">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.023 0.00133  0.042 0.01199  0.052 0.02798  L 0.075 0.06529  C 0.08 0.07328  0.088 0.07728  0.098 0.07728  C 0.112 0.07728  0.124 0.06662  0.125 0.05063  C 0.124 0.03731  0.112 0.02532  0.098 0.02532  C 0.088 0.02532  0.08 0.03065  0.075 0.03731  L 0.052 0.07461  C 0.042 0.0906  0.023 0.10126  0 0.1026  C -0.023 0.10126  -0.042 0.0906  -0.052 0.07461  L -0.075 0.03731  C -0.08 0.03065  -0.088 0.02532  -0.098 0.02532  C -0.112 0.02532  -0.124 0.03731  -0.125 0.05063  C -0.124 0.06662  -0.112 0.07728  -0.098 0.07728  C -0.088 0.07728  -0.08 0.07328  -0.075 0.06529  L -0.052 0.02798  C -0.042 0.01199  -0.023 0.00133  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-neutron">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.007 -0.01332  0.014 -0.02798  0.021 -0.04663  C 0.04 -0.09993  0.045 -0.15189  0.031 -0.15989  C 0.017 -0.16922  -0.01 -0.13191  -0.029 -0.07861  C -0.039 -0.05063  -0.045 -0.02398  -0.047 -0.004  C -0.05 0.01199  -0.051 0.02798  -0.051 0.04663  C -0.051 0.10659  -0.038 0.15589  -0.023 0.15589  C -0.008 0.15589  0.005 0.10659  0.005 0.04663  C 0.005 0.01865  0.002 -0.00799  -0.003 -0.02665  C -0.005 -0.04264  -0.01 -0.05996  -0.016 -0.07728  C -0.036 -0.13191  -0.063 -0.16922  -0.077 -0.15989  C -0.091 -0.15056  -0.086 -0.09993  -0.066 -0.0453  C -0.058 -0.01999  -0.047 0.00133  -0.036 0.01599  C -0.028 0.02931  -0.019 0.0413  -0.007 0.0533  C 0.029 0.09194  0.065 0.10926  0.075 0.09327  C 0.084 0.07728  0.064 0.03331  0.028 -0.004  C 0.013 -0.01999  -0.003 -0.03198  -0.016 -0.03997  C -0.028 -0.04797  -0.043 -0.05463  -0.059 -0.05863  C -0.103 -0.07195  -0.141 -0.06795  -0.144 -0.04663  C -0.148 -0.02665  -0.115 0  -0.071 0.01332  C -0.051 0.01865  -0.032 0.02132  -0.017 0.01999  C -0.004 0.01999  0.01 0.01732  0.025 0.01332  C 0.069 0  0.102 -0.02798  0.098 -0.04797  C 0.095 -0.06795  0.057 -0.07328  0.013 -0.05996  C -0.008 -0.0533  -0.027 -0.04397  -0.04 -0.03331  C -0.051 -0.02532  -0.062 -0.01599  -0.074 -0.004  C -0.109 0.03464  -0.13 0.07728  -0.12 0.09327  C -0.111 0.10926  -0.074 0.09194  -0.039 0.05463  C -0.022 0.03598  -0.008 0.01732  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-peanut">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.038 0  0.069 0.0413  0.069 0.09194  C 0.069 0.12525  0.056 0.15456  0.037 0.17188  C 0.037 0.17188  0.036 0.17188  0.036 0.17188  C 0.029 0.17854  0.025 0.1892  0.025 0.20119  C 0.025 0.21185  0.029 0.22118  0.034 0.22784  C 0.042 0.2385  0.047 0.25449  0.047 0.27048  C 0.047 0.30512  0.026 0.3331  0 0.3331  C -0.026 0.3331  -0.047 0.30512  -0.047 0.27048  C -0.047 0.25449  -0.042 0.2385  -0.034 0.22784  C -0.029 0.22118  -0.026 0.21185  -0.026 0.20119  C -0.026 0.1892  -0.03 0.17854  -0.036 0.17188  C -0.036 0.17188  -0.037 0.17188  -0.037 0.17188  C -0.057 0.15456  -0.07 0.12525  -0.07 0.09194  C -0.07 0.0413  -0.039 0  0 0  C 0 0  0 0  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-clover">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C -0.118 -0.15722  0.132 -0.15722  0.011 0  C 0.132 -0.15722  0.132 0.17588  0.011 0.01466  C 0.132 0.17588  -0.118 0.17588  0 0.01466  C -0.118 0.17588  -0.118 -0.15722  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold">
    <anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-pointy-star">
        <anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.069 0  0.124 -0.07461  0.124 -0.16655  C 0.124 -0.07461  0.179 -0.00133  0.248 -0.00133  C 0.179 -0.00133  0.125 0.07461  0.125 0.16655  C 0.125 0.07461  0.069 0  0 0  Z"/>
    </anim:par>
    </anim:par>
    </anim:par>
	<anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
			<anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-swoosh">
				<anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0 0  0.017 -0.08661  0.017 -0.08661  C 0.034 -0.15722  0.061 -0.1852  0.1 -0.1852  C 0.12 -0.1852  0.138 -0.17455  0.152 -0.15722  C 0.162 -0.14523  0.174 -0.13857  0.187 -0.13857  C 0.212 -0.13857  0.233 -0.16255  0.241 -0.1972  C 0.241 -0.1972  0.25 -0.2385  0.25 -0.2385  C 0.25 -0.2385  0.232 -0.15056  0.232 -0.15056  C 0.215 -0.08128  0.188 -0.0533  0.15 -0.0533  C 0.13 -0.0533  0.111 -0.06396  0.096 -0.08261  C 0.087 -0.09327  0.075 -0.09993  0.063 -0.09993  C 0.038 -0.09993  0.017 -0.07595  0.009 -0.0413  C 0.009 -0.0413  0 0  0 0  Z"/>
			</anim:par>
		</anim:par>
	</anim:par>
    <anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
			<anim:par smil:begin="0" smil:fill="hold" smil:accelerate="0.5" smil:decelerate="0.5" pres:node-type="on-click" pres:preset-class="motion-path" pres:preset-id="ooo-motionpath-vertical-figure-8">
				<anim:animateMotion smil:dur="2" smil:fill="hold" svg:path="M 0 0  C 0.033 0  0.06 0.03598  0.06 0.07994  C 0.06 0.13191  0.03 0.15056  0.012 0.15856  L -0.012 0.16655  C -0.03 0.17455  -0.06 0.19453  -0.06 0.25316  C -0.06 0.29046  -0.033 0.3331  0 0.3331  C 0.033 0.3331  0.06 0.29046  0.06 0.25316  C 0.06 0.19453  0.03 0.17455  0.012 0.16655  L -0.012 0.15856  C -0.03 0.15056  -0.06 0.13191  -0.06 0.07994  C -0.06 0.03598  -0.033 0  0 0  Z"/>
			</anim:par>
		</anim:par>
	</anim:par>
	<anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
		    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-media-start">
                <anim:command smil:begin="0" smil:dur="0.001" smil:fill="hold" anim:command="play"/>
			</anim:par>
		</anim:par>
    </anim:par>
	<anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
		    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-media-stop">
                <anim:command smil:begin="0" smil:dur="0.001" smil:fill="hold" anim:command="stop"/>
			</anim:par>
		</anim:par>
    </anim:par>
	<anim:par smil:begin="indefinite" smil:fill="hold">
		<anim:par smil:begin="0" smil:fill="hold">
		    <anim:par smil:begin="0" smil:fill="hold" pres:node-type="on-click" pres:preset-class="entrance" pres:preset-id="ooo-media-toggle-pause">
                <anim:command smil:begin="0" smil:dur="0.001" smil:fill="hold" anim:command="toggle-pause"/>
			</anim:par>
		</anim:par>
    </anim:par>
</anim:seq>
