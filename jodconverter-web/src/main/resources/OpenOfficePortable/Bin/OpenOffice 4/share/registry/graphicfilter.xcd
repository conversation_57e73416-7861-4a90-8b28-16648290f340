<?xml version="1.0"?>
<oor:data xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:oor="http://openoffice.org/2001/registry"><dependency file="main"/><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="BMP - MS Windows" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">BMP - Windows Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>bmp_MS_Windows</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="DXF - AutoCAD Interchange" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">DXF - AutoCAD Interchange Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>dxf_AutoCAD_Interchange</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="EMF - MS Windows Metafile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EMF - Enhanced Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>emf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="EPS - Encapsulated PostScript" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EPS - Encapsulated PostScript</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>eps_Encapsulated_PostScript</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="GIF - Graphics Interchange" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">GIF - Graphics Interchange Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>gif_Graphics_Interchange</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="JPG - JPEG" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>jpg_JPEG</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="MET - OS/2 Metafile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">MET - OS/2 Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>met_OS2_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PBM - Portable Bitmap" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PBM - Portable Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pbm_Portable_Bitmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PCT - Mac Pict" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCT - Mac Pict</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pct_Mac_Pict</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PCX - Zsoft Paintbrush" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCX - Zsoft Paintbrush</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pcx_Zsoft_Paintbrush</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PGM - Portable Graymap" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PGM - Portable Graymap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pgm_Portable_Graymap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PNG - Portable Network Graphic" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PNG - Portable Network Graphic</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>png_Portable_Network_Graphic</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PPM - Portable Pixelmap" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PPM - Portable Pixelmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ppm_Portable_Pixelmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="PSD - Adobe Photoshop" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PSD - Adobe Photoshop</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>psd_Adobe_Photoshop</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="RAS - Sun Rasterfile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">RAS - Sun Raster Image</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ras_Sun_Rasterfile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="SGF - StarOffice Writer SGF" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SGF - StarWriter Graphics Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>sgf_StarOffice_Writer_SGF</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="SGV - StarDraw 2.0" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SGV - StarDraw 2.0</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>sgv_StarDraw_20</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="SVG - Scalable Vector Graphics" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVG - Scalable Vector Graphics</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svg_Scalable_Vector_Graphics</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="SVM - StarView Metafile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVM - StarView Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svm_StarView_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="TGA - Truevision TARGA" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">TGA - Truevision Targa</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>tga_Truevision_TARGA</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="TIF - Tag Image File" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">TIFF - Tagged Image File Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>tif_Tag_Image_File</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="WMF - MS Windows Metafile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">WMF - Windows Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>wmf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="XBM - X-Consortium" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">XBM - X Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>xbm_X_Consortium</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="XPM" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">XPM - X PixMap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>xpm_XPM</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_PCD_Photo_CD_Base" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCD - Kodak Photo CD (768x512)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pcd_Photo_CD_Base</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_PCD_Photo_CD_Base16" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCD - Kodak Photo CD (192x128)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pcd_Photo_CD_Base16</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_PCD_Photo_CD_Base4" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCD - Kodak Photo CD (384x256)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pcd_Photo_CD_Base4</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_bmp_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">BMP - Windows Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>bmp_MS_Windows</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_emf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EMF - Enhanced Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>emf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_eps_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EPS - Encapsulated PostScript</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>eps_Encapsulated_PostScript</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_flash_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Impress.FlashExportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">Macromedia Flash (SWF)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>graphic_SWF</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_gif_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">GIF - Graphics Interchange Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>gif_Graphics_Interchange</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_html_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.draw.SdHtmlOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>graphic_HTML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_jpg_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>jpg_JPEG</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_met_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">MET - OS/2 Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>met_OS2_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_pbm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PBM - Portable Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pbm_Portable_Bitmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_pct_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCT - Mac Pict</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pct_Mac_Pict</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_pgm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PGM - Portable Graymap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pgm_Portable_Graymap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_png_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PNG - Portable Network Graphic</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>png_Portable_Network_Graphic</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_ppm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PPM - Portable Pixelmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ppm_Portable_Pixelmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_ras_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">RAS - Sun Raster Image</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ras_Sun_Rasterfile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_svg_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Draw.SVGFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVG - Scalable Vector Graphics</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svg_Scalable_Vector_Graphics</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_svm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVM - StarView Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svm_StarView_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_tif_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">TIFF - Tagged Image File Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>tif_Tag_Image_File</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_wmf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">WMF - Windows Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>wmf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node><node oor:name="draw_xpm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">XPM - X PixMap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>xpm_XPM</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.drawing.DrawingDocument</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="bmp_MS_Windows" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>bmp</value></prop><prop oor:name="MediaType"><value>image/x-MS-bmp</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>BMP - MS Windows</value></prop><prop oor:name="UIName"><value>BMP - Windows Bitmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="dxf_AutoCAD_Interchange" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>dxf</value></prop><prop oor:name="MediaType"><value>image/vnd.dxf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>DXF - AutoCAD Interchange</value></prop><prop oor:name="UIName"><value>DXF - AutoCAD Interchange Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="emf_MS_Windows_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>emf</value></prop><prop oor:name="MediaType"><value>image/x-emf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>EMF - MS Windows Metafile</value></prop><prop oor:name="UIName"><value>EMF - Enhanced Meta File</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="eps_Encapsulated_PostScript" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>eps</value></prop><prop oor:name="MediaType"><value>image/x-eps</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>EPS - Encapsulated PostScript</value></prop><prop oor:name="UIName"><value>EPS - Encapsulated PostScript</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="gif_Graphics_Interchange" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>gif</value></prop><prop oor:name="MediaType"><value>image/gif</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>GIF - Graphics Interchange</value></prop><prop oor:name="UIName"><value>GIF - Graphics Interchange</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="graphic_HTML" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>html htm</value></prop><prop oor:name="MediaType"><value>text/html</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>HTML</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="graphic_SWF" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>swf</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Macromedia Flash (SWF)</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="jpg_JPEG" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>jpg jpeg jfif jif jpe</value></prop><prop oor:name="MediaType"><value>image/jpeg</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>JPG - JPEG</value></prop><prop oor:name="UIName"><value>JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="met_OS2_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>met</value></prop><prop oor:name="MediaType"><value>image/x-met</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MET - OS/2 Metafile</value></prop><prop oor:name="UIName"><value>MET - OS/2 Metafile</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pbm_Portable_Bitmap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pbm</value></prop><prop oor:name="MediaType"><value>image/x-portable-bitmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PBM - Portable Bitmap</value></prop><prop oor:name="UIName"><value>PBM - Portable Bitmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pcd_Photo_CD_Base" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pcd</value></prop><prop oor:name="MediaType"><value>image/x-photo-cd</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>draw_PCD_Photo_CD_Base</value></prop><prop oor:name="UIName"><value>PCD - Photo CD Base</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pcd_Photo_CD_Base16" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pcd</value></prop><prop oor:name="MediaType"><value>image/x-photo-cd</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>draw_PCD_Photo_CD_Base16</value></prop><prop oor:name="UIName"><value>PCD - Photo CD Base16</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pcd_Photo_CD_Base4" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pcd</value></prop><prop oor:name="MediaType"><value>image/x-photo-cd</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>draw_PCD_Photo_CD_Base4</value></prop><prop oor:name="UIName"><value>PCD - Photo CD Base4</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pct_Mac_Pict" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pct pict</value></prop><prop oor:name="MediaType"><value>image/x-pict</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PCT - Mac Pict</value></prop><prop oor:name="UIName"><value>PCT - Mac Pict</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pcx_Zsoft_Paintbrush" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pcx</value></prop><prop oor:name="MediaType"><value>image/x-pcx</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PCX - Zsoft Paintbrush</value></prop><prop oor:name="UIName"><value>PCX - Zsoft Paintbrush</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pgm_Portable_Graymap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pgm</value></prop><prop oor:name="MediaType"><value>image/x-portable-graymap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PGM - Portable Graymap</value></prop><prop oor:name="UIName"><value>PGM - Portable Graymap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="png_Portable_Network_Graphic" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>png</value></prop><prop oor:name="MediaType"><value>image/png</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PNG - Portable Network Graphic</value></prop><prop oor:name="UIName"><value>PNG - Portable Network Graphic</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="ppm_Portable_Pixelmap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ppm</value></prop><prop oor:name="MediaType"><value>image/x-portable-pixmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PPM - Portable Pixelmap</value></prop><prop oor:name="UIName"><value>PPM - Portable Pixelmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="psd_Adobe_Photoshop" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>psd</value></prop><prop oor:name="MediaType"><value>image/vnd.adobe.photoshop</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PSD - Adobe Photoshop</value></prop><prop oor:name="UIName"><value>PSD - Adobe Photoshop</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="ras_Sun_Rasterfile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ras</value></prop><prop oor:name="MediaType"><value>image/x-cmu-raster</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>RAS - Sun Rasterfile</value></prop><prop oor:name="UIName"><value>RAS - Sun Raster Image</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="sgf_StarOffice_Writer_SGF" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sgf</value></prop><prop oor:name="MediaType"><value>image/x-sgf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SGF - StarOffice Writer SGF</value></prop><prop oor:name="UIName"><value>SGF - StarWriter SGF</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="sgv_StarDraw_20" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>sgv</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SGV - StarDraw 2.0</value></prop><prop oor:name="UIName"><value>SGV - StarDraw 2.0</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="svg_Scalable_Vector_Graphics" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>svg</value></prop><prop oor:name="MediaType"><value>image/svg+xml</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SVG - Scalable Vector Graphics</value></prop><prop oor:name="UIName"><value>SVG - Scalable Vector Graphics</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="svm_StarView_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>svm</value></prop><prop oor:name="MediaType"><value>image/x-svm</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SVM - StarView Metafile</value></prop><prop oor:name="UIName"><value>SVM - StarView Meta File</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="tga_Truevision_TARGA" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>tga</value></prop><prop oor:name="MediaType"><value>image/x-targa</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>TGA - Truevision TARGA</value></prop><prop oor:name="UIName"><value>TGA - Truevision Targa</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="tif_Tag_Image_File" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>tif tiff</value></prop><prop oor:name="MediaType"><value>image/tiff</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>TIF - Tag Image File</value></prop><prop oor:name="UIName"><value>TIFF - Tagged Image File Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="wmf_MS_Windows_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wmf</value></prop><prop oor:name="MediaType"><value>image/x-wmf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>WMF - MS Windows Metafile</value></prop><prop oor:name="UIName"><value>WMF - Windows Metafile</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="xbm_X_Consortium" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xbm</value></prop><prop oor:name="MediaType"><value>image/x-xbitmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>XBM - X-Consortium</value></prop><prop oor:name="UIName"><value>XBM - X Bitmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="xpm_XPM" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xpm</value></prop><prop oor:name="MediaType"><value>image/x-xpixmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>XPM</value></prop><prop oor:name="UIName"><value>XPM - X PixMap</value></prop><prop oor:name="ClipboardFormat"/></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Filter"><node oor:name="Filters"><node oor:name="CGM - Computer Graphics Metafile" oor:op="replace"><prop oor:name="Flags"><value>IMPORT ALIEN</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value>icg</value></prop><prop oor:name="UIName"><value xml:lang="x-default">CGM - Computer Graphics Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>impress_CGM_Computer_Graphics_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_bmp_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">BMP - Windows Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>bmp_MS_Windows</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_emf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EMF - Enhanced Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>emf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_eps_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">EPS - Encapsulated PostScript</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>eps_Encapsulated_PostScript</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_flash_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Impress.FlashExportFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">Macromedia Flash (SWF)</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>graphic_SWF</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_gif_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">GIF - Graphics Interchange Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>gif_Graphics_Interchange</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_html_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN</value></prop><prop oor:name="UIComponent"><value>com.sun.star.comp.draw.SdHtmlOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>graphic_HTML</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_jpg_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>jpg_JPEG</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_met_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">MET - OS/2 Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>met_OS2_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_pbm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PBM - Portable Bitmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pbm_Portable_Bitmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_pct_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PCT - Mac Pict</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pct_Mac_Pict</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_pgm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PGM - Portable Graymap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>pgm_Portable_Graymap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_png_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PNG - Portable Network Graphic</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>png_Portable_Network_Graphic</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_ppm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">PPM - Portable Pixelmap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ppm_Portable_Pixelmap</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_ras_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">RAS - Sun Raster Image</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>ras_Sun_Rasterfile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_svg_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN 3RDPARTYFILTER</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"><value>com.sun.star.comp.Draw.SVGFilter</value></prop><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVG - Scalable Vector Graphics</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svg_Scalable_Vector_Graphics</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_svm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">SVM - StarView Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>svm_StarView_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_tif_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">TIFF - Tagged Image File Format</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>tif_Tag_Image_File</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_wmf_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"><value>com.sun.star.svtools.SvFilterOptionsDialog</value></prop><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">WMF - Windows Metafile</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>wmf_MS_Windows_Metafile</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node><node oor:name="impress_xpm_Export" oor:op="replace"><prop oor:name="Flags"><value>EXPORT ALIEN SUPPORTSSELECTION</value></prop><prop oor:name="UIComponent"/><prop oor:name="FilterService"/><prop oor:name="UserData"><value/></prop><prop oor:name="UIName"><value xml:lang="x-default">XPM - X PixMap</value></prop><prop oor:name="FileFormatVersion"><value>0</value></prop><prop oor:name="Type"><value>xpm_XPM</value></prop><prop oor:name="TemplateName"/><prop oor:name="DocumentService"><value>com.sun.star.presentation.PresentationDocument</value></prop></node></node></oor:component-data><oor:component-data oor:package="org.openoffice.TypeDetection" oor:name="Types"><node oor:name="Types"><node oor:name="bmp_MS_Windows" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>bmp</value></prop><prop oor:name="MediaType"><value>image/x-MS-bmp</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>BMP - MS Windows</value></prop><prop oor:name="UIName"><value>BMP - Windows Bitmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="emf_MS_Windows_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>emf</value></prop><prop oor:name="MediaType"><value>image/x-emf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>EMF - MS Windows Metafile</value></prop><prop oor:name="UIName"><value>EMF - Enhanced Meta File</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="eps_Encapsulated_PostScript" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>eps</value></prop><prop oor:name="MediaType"><value>image/x-eps</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>EPS - Encapsulated PostScript</value></prop><prop oor:name="UIName"><value>EPS - Encapsulated PostScript</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="gif_Graphics_Interchange" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>gif</value></prop><prop oor:name="MediaType"><value>image/gif</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>GIF - Graphics Interchange</value></prop><prop oor:name="UIName"><value>GIF - Graphics Interchange</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="graphic_HTML" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>html htm</value></prop><prop oor:name="MediaType"><value>text/html</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>HTML</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="graphic_SWF" oor:op="replace"><prop oor:name="DetectService"/><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>swf</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"/><prop oor:name="UIName"><value>Macromedia Flash (SWF)</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="impress_CGM_Computer_Graphics_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>cgm</value></prop><prop oor:name="MediaType"/><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>CGM - Computer Graphics Metafile</value></prop><prop oor:name="UIName"><value>CGM - Computer Graphics Metafile</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="jpg_JPEG" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>jpg jpeg jfif jif jpe</value></prop><prop oor:name="MediaType"><value>image/jpeg</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>JPG - JPEG</value></prop><prop oor:name="UIName"><value>JPEG - Joint Photographic Experts Group</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="met_OS2_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>met</value></prop><prop oor:name="MediaType"><value>image/x-met</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>MET - OS/2 Metafile</value></prop><prop oor:name="UIName"><value>MET - OS/2 Metafile</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pbm_Portable_Bitmap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pbm</value></prop><prop oor:name="MediaType"><value>image/x-portable-bitmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PBM - Portable Bitmap</value></prop><prop oor:name="UIName"><value>PBM - Portable Bitmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pct_Mac_Pict" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pct pict</value></prop><prop oor:name="MediaType"><value>image/x-pict</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PCT - Mac Pict</value></prop><prop oor:name="UIName"><value>PCT - Mac Pict</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="pgm_Portable_Graymap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>pgm</value></prop><prop oor:name="MediaType"><value>image/x-portable-graymap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PGM - Portable Graymap</value></prop><prop oor:name="UIName"><value>PGM - Portable Graymap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="png_Portable_Network_Graphic" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>png</value></prop><prop oor:name="MediaType"><value>image/png</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PNG - Portable Network Graphic</value></prop><prop oor:name="UIName"><value>PNG - Portable Network Graphic</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="ppm_Portable_Pixelmap" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ppm</value></prop><prop oor:name="MediaType"><value>image/x-portable-pixmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>PPM - Portable Pixelmap</value></prop><prop oor:name="UIName"><value>PPM - Portable Pixelmap</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="ras_Sun_Rasterfile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>ras</value></prop><prop oor:name="MediaType"><value>image/x-cmu-raster</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>RAS - Sun Rasterfile</value></prop><prop oor:name="UIName"><value>RAS - Sun Raster Image</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="svg_Scalable_Vector_Graphics" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>svg</value></prop><prop oor:name="MediaType"><value>image/svg+xml</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SVG - Scalable Vector Graphics</value></prop><prop oor:name="UIName"><value>SVG - Scalable Vector Graphics</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="svm_StarView_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>svm</value></prop><prop oor:name="MediaType"><value>image/x-svm</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>SVM - StarView Metafile</value></prop><prop oor:name="UIName"><value>SVM - StarView Meta File</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="tif_Tag_Image_File" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>tif tiff</value></prop><prop oor:name="MediaType"><value>image/tiff</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>TIF - Tag Image File</value></prop><prop oor:name="UIName"><value>TIFF - Tagged Image File Format</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="wmf_MS_Windows_Metafile" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>wmf</value></prop><prop oor:name="MediaType"><value>image/x-wmf</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>WMF - MS Windows Metafile</value></prop><prop oor:name="UIName"><value>WMF - Windows Metafile</value></prop><prop oor:name="ClipboardFormat"/></node><node oor:name="xpm_XPM" oor:op="replace"><prop oor:name="DetectService"><value>com.sun.star.comp.draw.FormatDetector</value></prop><prop oor:name="URLPattern"/><prop oor:name="Extensions"><value>xpm</value></prop><prop oor:name="MediaType"><value>image/x-xpixmap</value></prop><prop oor:name="Preferred"><value>false</value></prop><prop oor:name="PreferredFilter"><value>XPM</value></prop><prop oor:name="UIName"><value>XPM - X PixMap</value></prop><prop oor:name="ClipboardFormat"/></node></node></oor:component-data></oor:data>
