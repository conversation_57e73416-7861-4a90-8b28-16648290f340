<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 3.2//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta>
<style>
			#Copyright_Trademark * {font-size: .7em;}
			.note {border: 1px solid #993333; padding: 3px;}
			p { margin-top: .1em; margin-bottom: .1em;}
			.code {font-family: monospace;}
		</style>
</head>
<body>
<div id="Intro">
<h1>OpenOffice 4.1.2 讀我檔案</h1>
<p>如需此讀我檔案的最近更新，請參閱 <a href="http://www.openoffice.org/welcome/readme.html">http://www.openoffice.org/welcome/readme.html</a></p>
<p>本檔案包含有關此程式的重要資訊。請仔細閱讀此資訊，再開始工作。</p>
<p>負責開發本產品的 Apache OpenOffice 社群想邀請您加入成為社群成員。身為新使用者，您可以檢閱下列 OpenOffice 網站，以取得有用的使用者資訊：<a href="http://openoffice.apache.org">http://openoffice.apache.org</a></p>
<p>另請參閱有關如何參與 Apache OpenOffice 專案的以下各章節。</p>
<h3>是否任何使用者真的都可免費使用 OpenOffice？ </h3>
<p>每個人皆可免費使用 OpenOffice。您可以取得 OpenOffice 軟體，安裝在不限數目的電腦上，進行多用途的使用 (包括商用、政府、公共事業管理及教育之用)。如需進一步的詳細資訊，請參閱 OpenOffice 隨附的授權文字或 <a href="http://www.openoffice.org/license.html">http://www.openoffice.org/license.html</a></p>
<h3>為何所有使用者皆可免費使用 OpenOffice？</h3>
<p>您今天之所以可免費使用 OpenOffice，是因為個人參與者及企業贊助者的設計、開發、測試、翻譯、記載、支援、行銷，以及透過許多其他方式的協助，OpenOffice 才有今天的成就，成為世界領先的開放原始碼辦公室軟體。</p>
<p>如果您欣賞並感謝他們的貢獻，並希望 Apache OpenOffice 未來能夠繼續運作，請考慮貢獻這個專案 - 請看此網頁 <a href="http://openoffice.apache.org/get-involved.html">http://openoffice.apache.org/get-involved.html</a> 獲得更多有關貢獻細節和捐款細節網頁 <a href="http://www.apache.org/foundation/contributing.html">http://www.apache.org/foundation/contributing.html</a> 每個人都能做出貢獻。</p>
</div>
<div id="Installation">
<h2>安裝注意事項</h2>
<p class="note">OpenOffice 需要最新的JAVA版本來支援完整的功能。JAVA可以從此下載： <a href="http://java.com">http://java.com</a> 。</p>
<h3>系統需求</h3>
<div id="SystemRequirements_WIN">
<ul>
<li><p>Microsoft Windows XP 、 Vista 、 Windows 7 或是 Windows 8</p></li>
<li><p>Pentium III 或是更新的處理器</p></li>
<li><p>256 MB 記憶體 (建議使用 512 MB 記憶體)</p></li>
<li><p>最大 1.5 GB 的可用硬碟空間</p></li>
<li><p>1024x768 解析度 (建議使用更高解析度)，至少 256 色</p></li>
</ul>
<p class="note">請注意，安裝程序需要管理員權限。</p>
<p>執行安裝程式時使用以下指令行開關，可強制或不讓 OpenOffice 註冊為 Microsoft Office 格式的預設應用程式：</p>
<ul>
<li><p><tt>/msoreg=1</tt> 會強制註冊 OpenOffice 為 Microsoft Office 格式的預設應用程式。</p></li>
<li><p><tt>/msoreg=0</tt> 則不會註冊 OpenOffice 為 Microsoft Office 格式的預設應用程式。</p></li>
</ul>
<p>如果使用setup /a 執行管理安裝，您必須確定系統上已安裝檔案 msvcr100.dll。管理安裝之後，OpenOffice 需要此檔案才能啟動。您可以從 <a href="http://www.microsoft.com/en-us/download/details.aspx?id=5555">http://www.microsoft.com/en-us/download/details.aspx?id=5555</a>取得檔案。</p>
<p class="note">請注意，安裝程序需要管理員權限。</p>
</div>
<p>請確定系統上的暫存目錄有足夠的可用記憶體，並已授與讀取、寫入和執行存取權。請關閉所有其他程式，再開始安裝。</p>
</div>
<div id="StartupProblems">
<h2>程式啟動期間發生問題</h2>
<p>無法啟動 OpenOffice (例如應用程式停滯) 以及螢幕顯示發生問題，通常是由於圖形卡驅動程式所致。若發生這些問題，請更新您的圖形卡驅動程式，或嘗試使用作業系統所提供的圖形驅動程式。停用「工具」-「選項」-「OpenOffice」-「檢視」-「3D 檢視」下的「採用 OpenGL」選項，通常可解決無法顯示 3D 物件的問題。</p>
</div>
<div id="Scroll">
<h2>Windows 中的 ALPS/Synaptics 筆記型電腦觸控板</h2>
<p>由於 Windows 驅動程式發生問題，您無法在 ALPS/Synaptics 觸控板上滑動手指時捲動 OpenOffice 文件。</p>
<p>若要啟用觸控板捲動功能，請將以下行加入「<tt>C:\Program Files\Synaptics\SynTP\SynTPEnh.ini</tt>」配置檔案，然後重新啟動電腦：</p>
<p class="code">[OpenOffice]</p>
<p class="code">FC = "SALFRAME"</p>
<p class="code">SF = 0x10000000</p>
<p class="code">SF |= 0x00004000</p>
<p class="note">配置檔案的位置會隨 Windows 版本而異。</p>
</div>
<div id="Keyboard">
<h2>捷徑鍵</h2>
<p>OpenOffice 中只能使用作業系統未使用的捷徑鍵 (按鍵組合)。如果 OpenOffice 中的按鍵組合執行方式與 OpenOffice 說明中的說明不相符，請檢查作業系統是否已使用該捷徑。若要修正此衝突，您可以變更作業系統所指定的按鍵。或是變更 OpenOffice 中幾乎所有的按鍵指定。如需有關此主題的更多資訊，請參閱 OpenOffice 說明或作業系統的說明文件。</p>
</div>
<div id="Mapi">
<h2>從 OpenOffice 以電子郵件傳送文件時發生問題</h2>
<p>透過「檔案」-「傳送」-「作為電子郵件的文件」或「文件當作 PDF 附件」傳送文件時，可能會發生問題 (程式故障或停滯)。這是由於在某些檔案版本中導致問題的 Windows 系統檔案「Mapi」(訊息應用程式發展介面) 所致。不過，此問題無法縮小範圍到特定的版本編號。如需更多資訊，請造訪 <a href="http://www.microsoft.com">http://www.microsoft.com</a> 並在 Microsoft 知識庫中搜尋「mapi dll」。</p>
</div>
<div id="A11y">
<h2>重要協助工具備註</h2>
<p>如需有關 OpenOffice 之無障礙功能的更多資訊，請參閱 <a href="http://www.openoffice.org/access/">http://www.openoffice.org/access/</a></p>
</div>
<div id="UserSupport">
<h2>使用者支援</h2>
<p>主要支援頁面 <a href="http://support.openoffice.org/">http://support.openoffice.org/</a> 提供各種可能的 OpenOffice 協助。您的問題可能已經有答覆 - 請檢閱社群論壇 (<a href="http://forum.openoffice.org">http://forum.openoffice.org</a>) 或搜尋「<EMAIL>」郵件收件人清單的歸檔 (<a href="http://openoffice.apache.org/mailing-lists.html">http://openoffice.apache.org/mailing-lists.html</a>)。您也可以將問題傳送至<a href="mailto:<EMAIL>"> 。下列頁面會說明如何訂閱該清單 (以取得電子郵件回覆)</a>：<a href="http://openoffice.apache.org/mailing-lists.html">http://openoffice.apache.org/mailing-lists.html</a> 。</p>
<p>另請檢閱 <a href="http://wiki.openoffice.org/wiki/Documentation/FAQ">http://wiki.openoffice.org/wiki/Documentation/FAQ</a> 的常見問題集部分。</p>
</div>
<div id="ReportBugsIssues">
<h2>報告錯誤與問題</h2>
<p>OpenOffice 網站託管的 <a href="https://issues.apache.org/ooo/">BugZilla</a> 是報告、追蹤及解決錯誤與問題的機制。建議並歡迎所有使用者報告發生在其特定平台上的問題。積極報告問題是使用者社群得以繼續開發與改善套裝軟體的重要貢獻之一。</p>
</div>
<div id="GettingInvolved">
<h2>參與 </h2>
<p>您對開發此重要之開放原始碼專案的積極參與，將有益於 OpenOffice 社群。</p>
<p>身為使用者，您已經是套裝軟體開發過程的重要一份子，建議您扮演更積極的角色，成為社群的長期貢獻者。請從下列網址加入並檢閱使用者頁面：<a href="http://openoffice.apache.org/get-involved.html">http://openoffice.apache.org/get-involved.html</a></p>
<h3>如何開始</h3>
<p>想要開始貢獻，最好的方式就是訂閱一個或多個郵件論壇。您可以潛伏純閱讀討論一段時間，當逐漸熟悉郵件論壇的使用方式後，您可以開始寄一封信做自我介紹，並加入論壇討論。這些郵件論壇文章打包從 OpenOffice 2000年十月的原始碼釋出開始。</p>
<h3>訂閱</h3>
<p>以下是一些您可以從下列網址訂閱 OpenOffice 的專案郵件收信人清單：<a href="http://openoffice.apache.org/mailing-lists.html">http://openoffice.apache.org/mailing-lists.html</a></p>
<ul>
<li><p>新聞：<EMAIL> *建議給所有使用者* (低流量) </p></li>
<li><p>主要使用者論壇：<EMAIL>  *觀察討論的簡單方式* (高流量) </p></li>
<li><p>一般的專案開發與討論列表：<EMAIL> （高流量）</p></li>
</ul>
<h3>加入這個專案</h3>
<p>即使您的軟體設計或編碼經驗有限，也可對此重要的開放原始碼專案做出卓越的貢獻。沒錯，就是您！</p>
<p>在 <a href="http://openoffice.apache.org/get-involved.html">http://openoffice.apache.org/get-involved.html</a> ，您將發現從本土化、移植及群組軟體專案，到一些實際核心的編碼專案。如果您不是開發人員，請嘗試文件或行銷專案。OpenOffice 行銷專案同時採用游擊戰術與傳統商業手法，行銷開放原始碼軟體，超越語言與文化的藩籬，您只要告知好友這套辦公室套裝軟體並為其美言幾句，便可協助行銷。</p>
<p>您可以透過行銷郵件論壇 <EMAIL> 來幫忙宣傳，透過出版物、媒體、政府機構、顧問、Linux使用者社群或是您的國家的開發者與地方性社群。</p>
</div>
<div id="Credits">
<p>希望您對使用新的 OpenOffice 4.1.2 感到滿意，並願意線上加入我們。</p>
<p>Apache OpenOffice 社群</p>
</div>
<div id="ModifiedSourceCode"></div>
</body>
</html>
