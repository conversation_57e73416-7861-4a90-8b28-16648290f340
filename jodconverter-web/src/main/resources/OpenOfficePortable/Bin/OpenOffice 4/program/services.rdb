<?xml version="1.0"?>
<components xmlns="http://openoffice.org/2010/uno-components"><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/acceptor.uno.dll"><implementation name="com.sun.star.comp.io.Acceptor"><service name="com.sun.star.connection.Acceptor"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/binaryurp.uno.dll"><implementation name="com.sun.star.comp.bridge.BridgeFactory"><service name="com.sun.star.bridge.BridgeFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/bootstrap.uno.dll"><implementation name="com.sun.star.comp.stoc.DLLComponentLoader"><service name="com.sun.star.loader.SharedLibrary"/></implementation><implementation name="com.sun.star.comp.stoc.ImplementationRegistration"><service name="com.sun.star.registry.ImplementationRegistration"/></implementation><implementation name="com.sun.star.comp.stoc.NestedRegistry"><service name="com.sun.star.registry.NestedRegistry"/></implementation><implementation name="com.sun.star.comp.stoc.ORegistryServiceManager"><service name="com.sun.star.lang.RegistryServiceManager"/></implementation><implementation name="com.sun.star.comp.stoc.OServiceManager"><service name="com.sun.star.lang.ServiceManager"/></implementation><implementation name="com.sun.star.comp.stoc.OServiceManagerWrapper"/><implementation name="com.sun.star.comp.stoc.RegistryTypeDescriptionProvider"><service name="com.sun.star.reflection.TypeDescriptionProvider"/></implementation><implementation name="com.sun.star.comp.stoc.SimpleRegistry"><service name="com.sun.star.registry.SimpleRegistry"/></implementation><implementation name="com.sun.star.comp.stoc.TypeDescriptionManager"><service name="com.sun.star.reflection.TypeDescriptionManager"/></implementation><implementation name="com.sun.star.security.comp.stoc.AccessController"><service name="com.sun.star.security.AccessController"/></implementation><implementation name="com.sun.star.security.comp.stoc.FilePolicy"><service name="com.sun.star.security.Policy"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/connector.uno.dll"><implementation name="com.sun.star.comp.io.Connector"><service name="com.sun.star.connection.Connector"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/introspection.uno.dll"><implementation name="com.sun.star.comp.stoc.Introspection"><service name="com.sun.star.beans.Introspection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/invocadapt.uno.dll"><implementation name="com.sun.star.comp.stoc.InvocationAdapterFactory"><service name="com.sun.star.script.InvocationAdapterFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/invocation.uno.dll"><implementation name="com.sun.star.comp.stoc.Invocation"><service name="com.sun.star.script.Invocation"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/javaloader.uno.dll"><implementation name="com.sun.star.comp.stoc.JavaComponentLoader"><service name="com.sun.star.loader.Java"/><service name="com.sun.star.loader.Java2"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/javavm.uno.dll"><implementation name="com.sun.star.comp.stoc.JavaVirtualMachine"><service name="com.sun.star.java.JavaVirtualMachine"/><singleton name="com.sun.star.java.theJavaVirtualMachine"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$URE_INTERNAL_JAVA_DIR/juh.jar"><implementation name="com.sun.star.comp.juhtest.SmoketestCommandEnvironment"><service name="com.sun.star.deployment.test.SmoketestCommandEnvironment"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/namingservice.uno.dll"><implementation name="com.sun.star.comp.stoc.NamingService"><service name="com.sun.star.uno.NamingService"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/proxyfac.uno.dll"><implementation name="com.sun.star.comp.reflection.ProxyFactory"><service name="com.sun.star.reflection.ProxyFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/reflection.uno.dll"><implementation name="com.sun.star.comp.stoc.CoreReflection"><service name="com.sun.star.reflection.CoreReflection"/><singleton name="com.sun.star.reflection.theCoreReflection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/stocservices.uno.dll"><implementation name="com.sun.star.comp.stoc.TypeConverter"><service name="com.sun.star.script.Converter"/></implementation><implementation name="com.sun.star.comp.uri.ExternalUriReferenceTranslator"><service name="com.sun.star.uri.ExternalUriReferenceTranslator"/></implementation><implementation name="com.sun.star.comp.uri.UriReferenceFactory"><service name="com.sun.star.uri.UriReferenceFactory"/></implementation><implementation name="com.sun.star.comp.uri.UriSchemeParser_vndDOTsunDOTstarDOTexpand"><service name="com.sun.star.uri.UriSchemeParser_vndDOTsunDOTstarDOTexpand"/></implementation><implementation name="com.sun.star.comp.uri.UriSchemeParser_vndDOTsunDOTstarDOTscript"><service name="com.sun.star.uri.UriSchemeParser_vndDOTsunDOTstarDOTscript"/></implementation><implementation name="com.sun.star.comp.uri.VndSunStarPkgUrlReferenceFactory"><service name="com.sun.star.uri.VndSunStarPkgUrlReferenceFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/streams.uno.dll"><implementation name="com.sun.star.comp.io.Pump"><service name="com.sun.star.io.Pump"/></implementation><implementation name="com.sun.star.comp.io.stm.DataInputStream"><service name="com.sun.star.io.DataInputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.DataOutputStream"><service name="com.sun.star.io.DataOutputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.MarkableInputStream"><service name="com.sun.star.io.MarkableInputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.MarkableOutputStream"><service name="com.sun.star.io.MarkableOutputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.ObjectInputStream"><service name="com.sun.star.io.ObjectInputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.ObjectOutputStream"><service name="com.sun.star.io.ObjectOutputStream"/></implementation><implementation name="com.sun.star.comp.io.stm.Pipe"><service name="com.sun.star.io.Pipe"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/textinstream.uno.dll"><implementation name="com.sun.star.comp.io.TextInputStream"><service name="com.sun.star.io.TextInputStream"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/textoutstream.uno.dll"><implementation name="com.sun.star.comp.io.TextOutputStream"><service name="com.sun.star.io.TextOutputStream"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$URE_INTERNAL_LIB_DIR/uuresolver.uno.dll"><implementation name="com.sun.star.comp.bridge.UnoUrlResolver"><service name="com.sun.star.bridge.UnoUrlResolver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/abp.dll"><implementation name="org.openoffice.comp.abp.OAddressBookSourcePilot"><service name="com.sun.star.ui.dialogs.AddressBookSourcePilot"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/adabasui.dll"><implementation name="org.openoffice.comp.adabasui.AdabasCreateDialog"><service name="com.sun.star.sdb.AdabasCreationDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/analysis.dll"><implementation name="com.sun.star.sheet.addin.AnalysisImpl"><service name="com.sun.star.sheet.AddIn"/><service name="com.sun.star.sheet.addin.Analysis"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/animcore.dll"><implementation name="animcore::Animate"><service name="com.sun.star.animations.Animate"/></implementation><implementation name="animcore::AnimateColor"><service name="com.sun.star.animations.AnimateColor"/></implementation><implementation name="animcore::AnimateMotion"><service name="com.sun.star.animations.AnimateMotion"/></implementation><implementation name="animcore::AnimateSet"><service name="com.sun.star.animations.AnimateSet"/></implementation><implementation name="animcore::AnimateTransform"><service name="com.sun.star.animations.AnimateTransform"/></implementation><implementation name="animcore::Audio"><service name="com.sun.star.animations.Audio"/></implementation><implementation name="animcore::Command"><service name="com.sun.star.animations.Command"/></implementation><implementation name="animcore::IterateContainer"><service name="com.sun.star.animations.IterateContainer"/></implementation><implementation name="animcore::ParallelTimeContainer"><service name="com.sun.star.animations.ParallelTimeContainer"/></implementation><implementation name="animcore::SequenceTimeContainer"><service name="com.sun.star.animations.SequenceTimeContainer"/></implementation><implementation name="animcore::TargetPropertiesCreator"><service name="com.sun.star.animations.TargetPropertiesCreator"/></implementation><implementation name="animcore::TransitionFilter"><service name="com.sun.star.animations.TransitionFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/avmedia.dll"><implementation name="com.sun.star.comp.framework.SoundHandler"><service name="com.sun.star.frame.ContentHandler"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/basctl.dll"><implementation name="com.sun.star.comp.basic.BasicIDE"><service name="com.sun.star.script.BasicIDE"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/basprov.uno.dll"><implementation name="com.sun.star.comp.scripting.ScriptProviderForBasic"><service name="com.sun.star.script.browse.BrowseNode"/><service name="com.sun.star.script.provider.LanguageScriptProvider"/><service name="com.sun.star.script.provider.ScriptProvider"/><service name="com.sun.star.script.provider.ScriptProviderForBasic"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/bib.dll"><implementation name="com.sun.star.extensions.Bibliography"><service name="com.sun.star.frame.Bibliography"/><service name="com.sun.star.frame.FrameLoader"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/cached1.dll"><implementation name="com.sun.star.comp.ucb.CachedContentResultSetFactory"><service name="com.sun.star.ucb.CachedContentResultSetFactory"/></implementation><implementation name="com.sun.star.comp.ucb.CachedContentResultSetStubFactory"><service name="com.sun.star.ucb.CachedContentResultSetStubFactory"/></implementation><implementation name="com.sun.star.comp.ucb.CachedDynamicResultSetFactory"><service name="com.sun.star.ucb.CachedDynamicResultSetFactory"/></implementation><implementation name="com.sun.star.comp.ucb.CachedDynamicResultSetStubFactory"><service name="com.sun.star.ucb.CachedDynamicResultSetStubFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/calc.dll"><implementation name="com.sun.star.comp.sdbc.calc.ODriver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/canvasfactory.uno.dll"><implementation name="com.sun.star.comp.rendering.CanvasFactory"><service name="com.sun.star.rendering.CanvasFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/chartcontroller.dll"><implementation name="com.sun.star.comp.chart.ElementSelectorToolbarController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.chart2.ChartController"><service name="com.sun.star.chart2.ChartController"/><service name="com.sun.star.frame.Controller"/></implementation><implementation name="com.sun.star.comp.chart2.ChartDocumentWrapper"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart.ChartDocument"/><service name="com.sun.star.chart2.ChartDocumentWrapper"/><service name="com.sun.star.xml.UserDefinedAttributeSupplier"/></implementation><implementation name="com.sun.star.comp.chart2.ChartFrameLoader"><service name="com.sun.star.frame.SynchronousFrameLoader"/></implementation><implementation name="com.sun.star.comp.chart2.ChartTypeDialog"><service name="com.sun.star.chart2.ChartTypeDialog"/></implementation><implementation name="com.sun.star.comp.chart2.ShapeToolbarController"><service name="com.sun.star.chart2.ShapeToolbarController"/></implementation><implementation name="com.sun.star.comp.chart2.WizardDialog"><service name="com.sun.star.chart2.WizardDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/chartmodel.dll"><implementation name="com.sun.star.comp.chart.AreaChartType"><service name="com.sun.star.chart2.AreaChartType"/><service name="com.sun.star.chart2.ChartType"/></implementation><implementation name="com.sun.star.comp.chart.BarChartType"><service name="com.sun.star.chart2.BarChartType"/><service name="com.sun.star.chart2.ChartType"/></implementation><implementation name="com.sun.star.comp.chart.BubbleChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.BubbleChartType"/><service name="com.sun.star.chart2.ChartType"/></implementation><implementation name="com.sun.star.comp.chart.CandleStickChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.CandleStickChartType"/><service name="com.sun.star.chart2.ChartType"/></implementation><implementation name="com.sun.star.comp.chart.ChartTypeManager"><service name="com.sun.star.chart2.ChartTypeManager"/></implementation><implementation name="com.sun.star.comp.chart.ColumnChartType"><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.ColumnChartType"/></implementation><implementation name="com.sun.star.comp.chart.DataSeries"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.DataPointProperties"/><service name="com.sun.star.chart2.DataSeries"/></implementation><implementation name="com.sun.star.comp.chart.FilledNetChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.FilledNetChartType"/></implementation><implementation name="com.sun.star.comp.chart.FormattedString"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.FormattedString"/></implementation><implementation name="com.sun.star.comp.chart.LineChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.LineChartType"/></implementation><implementation name="com.sun.star.comp.chart.NetChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.NetChartType"/></implementation><implementation name="com.sun.star.comp.chart.PieChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.PieChartType"/></implementation><implementation name="com.sun.star.comp.chart.ScatterChartType"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.ChartType"/><service name="com.sun.star.chart2.ScatterChartType"/></implementation><implementation name="com.sun.star.comp.chart2.Axis"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.Axis"/></implementation><implementation name="com.sun.star.comp.chart2.CartesianCoordinateSystem2d"><service name="com.sun.star.chart2.CartesianCoordinateSystem2d"/><service name="com.sun.star.chart2.CoordinateSystems.Cartesian"/></implementation><implementation name="com.sun.star.comp.chart2.CartesianCoordinateSystem3d"><service name="com.sun.star.chart2.CartesianCoordinateSystem3d"/><service name="com.sun.star.chart2.CoordinateSystems.Cartesian"/></implementation><implementation name="com.sun.star.comp.chart2.ChartModel"><service name="com.sun.star.chart.ChartDocument"/><service name="com.sun.star.chart2.ChartDocument"/><service name="com.sun.star.document.OfficeDocument"/></implementation><implementation name="com.sun.star.comp.chart2.Diagram"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.Diagram"/><service name="com.sun.star.layout.LayoutElement"/></implementation><implementation name="com.sun.star.comp.chart2.GridProperties"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.GridProperties"/></implementation><implementation name="com.sun.star.comp.chart2.Legend"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.Legend"/><service name="com.sun.star.drawing.FillProperties"/><service name="com.sun.star.drawing.LineProperties"/><service name="com.sun.star.layout.LayoutElement"/><service name="com.sun.star.style.CharacterProperties"/></implementation><implementation name="com.sun.star.comp.chart2.PageBackground"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.PageBackground"/></implementation><implementation name="com.sun.star.comp.chart2.PolarCoordinateSystem2d"><service name="com.sun.star.chart2.CoordinateSystems.Polar"/><service name="com.sun.star.chart2.PolarCoordinateSystem2d"/></implementation><implementation name="com.sun.star.comp.chart2.PolarCoordinateSystem3d"><service name="com.sun.star.chart2.CoordinateSystems.Polar"/><service name="com.sun.star.chart2.PolarCoordinateSystem3d"/></implementation><implementation name="com.sun.star.comp.chart2.Title"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.Title"/><service name="com.sun.star.layout.LayoutElement"/><service name="com.sun.star.style.ParagraphProperties"/></implementation><implementation name="com.sun.star.comp.chart2.XMLFilter"><service name="com.sun.star.document.ExportFilter"/><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.chart2.report.XMLFilter"><service name="com.sun.star.document.ExportFilter"/><service name="com.sun.star.document.ImportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/charttools.dll"><implementation name="com.sun.star.chart2.ExponentialScaling"><service name="com.sun.star.chart2.ExponentialScaling"/></implementation><implementation name="com.sun.star.chart2.LinearScaling"><service name="com.sun.star.chart2.LinearScaling"/></implementation><implementation name="com.sun.star.chart2.LogarithmicScaling"><service name="com.sun.star.chart2.LogarithmicScaling"/></implementation><implementation name="com.sun.star.chart2.PowerScaling"><service name="com.sun.star.chart2.PowerScaling"/></implementation><implementation name="com.sun.star.comp.chart.CachedDataSequence"><service name="com.sun.star.chart2.data.DataSequence"/><service name="com.sun.star.chart2.data.NumericalDataSequence"/><service name="com.sun.star.chart2.data.TextualDataSequence"/><service name="com.sun.star.comp.chart.CachedDataSequence"/></implementation><implementation name="com.sun.star.comp.chart.DataSource"><service name="com.sun.star.chart2.data.DataSource"/></implementation><implementation name="com.sun.star.comp.chart.InternalDataProvider"><service name="com.sun.star.chart2.data.DataProvider"/></implementation><implementation name="com.sun.star.comp.chart2.ConfigDefaultColorScheme"><service name="com.sun.star.chart2.ColorScheme"/></implementation><implementation name="com.sun.star.comp.chart2.ErrorBar"><service name="com.sun.star.chart2.ErrorBar"/><service name="com.sun.star.comp.chart2.ErrorBar"/></implementation><implementation name="com.sun.star.comp.chart2.ExponentialRegressionCurve"><service name="com.sun.star.chart2.ExponentialRegressionCurve"/><service name="com.sun.star.chart2.RegressionCurve"/></implementation><implementation name="com.sun.star.comp.chart2.LabeledDataSequence"><service name="com.sun.star.chart2.data.LabeledDataSequence"/></implementation><implementation name="com.sun.star.comp.chart2.LinearRegressionCurve"><service name="com.sun.star.chart2.LinearRegressionCurve"/><service name="com.sun.star.chart2.RegressionCurve"/></implementation><implementation name="com.sun.star.comp.chart2.LogarithmicRegressionCurve"><service name="com.sun.star.chart2.LogarithmicRegressionCurve"/><service name="com.sun.star.chart2.RegressionCurve"/></implementation><implementation name="com.sun.star.comp.chart2.MeanValueRegressionCurve"><service name="com.sun.star.chart2.MeanValueRegressionCurve"/><service name="com.sun.star.chart2.RegressionCurve"/></implementation><implementation name="com.sun.star.comp.chart2.PotentialRegressionCurve"><service name="com.sun.star.chart2.PotentialRegressionCurve"/><service name="com.sun.star.chart2.RegressionCurve"/></implementation><implementation name="com.sun.star.comp.chart2.RegressionEquation"><service name="com.sun.star.beans.PropertySet"/><service name="com.sun.star.chart2.RegressionEquation"/><service name="com.sun.star.drawing.FillProperties"/><service name="com.sun.star.drawing.LineProperties"/><service name="com.sun.star.style.CharacterProperties"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/chartview.dll"><implementation name="com.sun.star.comp.chart2.ChartView"><service name="com.sun.star.chart2.ChartView"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/comphelpMSC.dll"><implementation name="AnyCompareFactory"><service name="com.sun.star.ucb.AnyCompareFactory"/></implementation><implementation name="IndexedPropertyValuesContainer"><service name="com.sun.star.document.IndexedPropertyValues"/></implementation><implementation name="NamedPropertyValuesContainer"><service name="com.sun.star.document.NamedPropertyValues"/></implementation><implementation name="com.sun.star.comp.MemoryStream"><service name="com.sun.star.comp.MemoryStream"/></implementation><implementation name="com.sun.star.comp.SequenceInputStreamService"><service name="com.sun.star.io.SequenceInputStream"/></implementation><implementation name="com.sun.star.comp.SequenceOutputStreamService"><service name="com.sun.star.io.SequenceOutputStream"/></implementation><implementation name="com.sun.star.comp.comphelper.OPropertyBag"><service name="com.sun.star.beans.PropertyBag"/></implementation><implementation name="com.sun.star.comp.embed.InstanceLocker"><service name="com.sun.star.embed.InstanceLocker"/></implementation><implementation name="com.sun.star.comp.logging.SimpleLogRing"><service name="com.sun.star.logging.SimpleLogRing"/><singleton name="com.sun.star.logging.DocumentIOLogRing"/></implementation><implementation name="com.sun.star.comp.task.OfficeRestartManager"><service name="com.sun.star.comp.task.OfficeRestartManager"/><singleton name="com.sun.star.task.OfficeRestartManager"/></implementation><implementation name="com.sun.star.comp.util.OfficeInstallationDirectories"><service name="com.sun.star.util.OfficeInstallationDirectories"/><singleton name="com.sun.star.util.theOfficeInstallationDirectories"/></implementation><implementation name="org.openoffice.comp.comphelper.EnumerableMap"><service name="com.sun.star.container.EnumerableMap"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/cui.dll"><implementation name="com.sun.star.cui.ColorPicker"><service name="com.sun.star.ui.dialogs.ColorPicker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/drawinglayer.dll"><implementation name="drawinglayer::unorenderer::XPrimitive2DRenderer"><service name="com.sun.star.graphic.Primitive2DTools"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fwk.dll"><implementation name="com.sun.star.comp.frame.SessionListener"><service name="com.sun.star.frame.SessionListener"/></implementation><implementation name="com.sun.star.comp.framework.AddonsToolBarFactory"><service name="com.sun.star.ui.ToolBarFactory"/></implementation><implementation name="com.sun.star.comp.framework.AutoRecovery"><service name="com.sun.star.frame.AutoRecovery"/></implementation><implementation name="com.sun.star.comp.framework.BackingComp"><service name="com.sun.star.frame.StartModule"/></implementation><implementation name="com.sun.star.comp.framework.ControlMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.Desktop"><service name="com.sun.star.frame.Desktop"/></implementation><implementation name="com.sun.star.comp.framework.DocumentAcceleratorConfiguration"><service name="com.sun.star.ui.DocumentAcceleratorConfiguration"/></implementation><implementation name="org.apache.openoffice.comp.framework.ContextChangeEventMultiplexer"><service name="com.sun.star.ui.ContextChangeEventMultiplexer"/><singleton name="com.sun.star.ui.ContextChangeEventMultiplexer"/></implementation><implementation name="com.sun.star.comp.framework.Frame"><service name="com.sun.star.frame.Frame"/></implementation><implementation name="com.sun.star.comp.framework.GlobalAcceleratorConfiguration"><service name="com.sun.star.ui.GlobalAcceleratorConfiguration"/></implementation><implementation name="com.sun.star.comp.framework.ImageManager"><service name="com.sun.star.ui.ImageManager"/></implementation><implementation name="com.sun.star.comp.framework.JobExecutor"><service name="com.sun.star.task.JobExecutor"/></implementation><implementation name="com.sun.star.comp.framework.LangSelectionStatusbarController"><service name="com.sun.star.frame.StatusbarController"/></implementation><implementation name="com.sun.star.comp.framework.LayoutManager"><service name="com.sun.star.frame.LayoutManager"/></implementation><implementation name="com.sun.star.comp.framework.MenuBarFactory"><service name="com.sun.star.ui.UIElementFactory"/></implementation><implementation name="com.sun.star.comp.framework.ModuleAcceleratorConfiguration"><service name="com.sun.star.ui.ModuleAcceleratorConfiguration"/></implementation><implementation name="com.sun.star.comp.framework.ModuleManager"><service name="com.sun.star.frame.ModuleManager"/></implementation><implementation name="com.sun.star.comp.framework.ModuleUIConfigurationManager"><service name="com.sun.star.ui.ModuleUIConfigurationManager"/></implementation><implementation name="com.sun.star.comp.framework.ModuleUIConfigurationManagerSupplier"><service name="com.sun.star.ui.ModuleUIConfigurationManagerSupplier"/></implementation><implementation name="com.sun.star.comp.framework.ObjectMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.PathSettings"><service name="com.sun.star.util.PathSettings"/></implementation><implementation name="com.sun.star.comp.framework.PathSubstitution"><service name="com.sun.star.util.PathSubstitution"/></implementation><implementation name="com.sun.star.comp.framework.PopupMenuControllerFactory"><service name="com.sun.star.frame.PopupMenuControllerFactory"/></implementation><implementation name="com.sun.star.comp.framework.RecentFilesMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.StatusBarControllerFactory"><service name="com.sun.star.frame.StatusbarControllerFactory"/></implementation><implementation name="com.sun.star.comp.framework.StatusBarFactory"><service name="com.sun.star.ui.StatusBarFactory"/></implementation><implementation name="com.sun.star.comp.framework.StatusIndicatorFactory"><service name="com.sun.star.task.StatusIndicatorFactory"/></implementation><implementation name="com.sun.star.comp.framework.TaskCreator"><service name="com.sun.star.frame.TaskCreator"/></implementation><implementation name="com.sun.star.comp.framework.ToolBarControllerFactory"><service name="com.sun.star.frame.ToolbarControllerFactory"/></implementation><implementation name="com.sun.star.comp.framework.ToolBarFactory"><service name="com.sun.star.ui.ToolBarFactory"/></implementation><implementation name="com.sun.star.comp.framework.UICategoryDescription"><service name="com.sun.star.ui.UICategoryDescription"/></implementation><implementation name="com.sun.star.comp.framework.UICommandDescription"><service name="com.sun.star.frame.UICommandDescription"/></implementation><implementation name="com.sun.star.comp.framework.UIConfigurationManager"><service name="com.sun.star.ui.UIConfigurationManager"/></implementation><implementation name="com.sun.star.comp.framework.UIElementFactoryManager"><service name="com.sun.star.ui.UIElementFactoryManager"/></implementation><implementation name="com.sun.star.comp.framework.URLTransformer"><service name="com.sun.star.util.URLTransformer"/></implementation><implementation name="com.sun.star.comp.framework.WindowContentFactoryManager"><service name="com.sun.star.ui.WindowContentFactoryManager"/></implementation><implementation name="com.sun.star.comp.framework.WindowStateConfiguration"><service name="com.sun.star.ui.WindowStateConfiguration"/></implementation><implementation name="com.sun.star.comp.framework.jobs.JobDispatch"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.framework.TabWindowService"><service name="com.sun.star.ui.dialogs.TabContainerWindow"/></implementation><implementation name="org.apache.openoffice.comp.framework.OpenToolbarController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="org.apache.openoffice.comp.framework.NewToolbarController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="org.apache.openoffice.comp.framework.WizardsToolbarController"><service name="com.sun.star.frame.ToolbarController"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fwl.dll"><implementation name="com.sum.star.comp.framework.LanguageSelectionMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.DispatchRecorder"><service name="com.sun.star.frame.DispatchRecorder"/></implementation><implementation name="com.sun.star.comp.framework.DispatchRecorderSupplier"><service name="com.sun.star.frame.DispatchRecorderSupplier"/></implementation><implementation name="com.sun.star.comp.framework.FontMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.FontSizeMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.FooterMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.HeaderMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.License"><service name="com.sun.star.task.Job"/></implementation><implementation name="com.sun.star.comp.framework.MacrosMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.MailToDispatcher"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.framework.MediaTypeDetectionHelper"><service name="com.sun.star.frame.MediaTypeDetectionHelper"/></implementation><implementation name="com.sun.star.comp.framework.NewMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.OXTFileHandler"><service name="com.sun.star.frame.ContentHandler"/></implementation><implementation name="com.sun.star.comp.framework.PopupMenuControllerDispatcher"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.framework.ServiceHandler"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.framework.ToolBarsMenuController"><service name="com.sun.star.frame.PopupMenuController"/></implementation><implementation name="com.sun.star.comp.framework.UriAbbreviation"><service name="com.sun.star.util.UriAbbreviation"/></implementation><implementation name="com.sun.star.comp.framework.services.DispatchHelper"><service name="com.sun.star.frame.DispatchHelper"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fwm.dll"><implementation name="com.sun.star.comp.framework.HelpOnStartup"><service name="com.sun.star.task.Job"/></implementation><implementation name="com.sun.star.comp.framework.ShellJob"><service name="com.sun.star.task.Job"/></implementation><implementation name="com.sun.star.comp.framework.SystemExecute"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.framework.TabWindowFactory"><service name="com.sun.star.frame.TabWindowFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/msforms.uno.dll"><implementation name="ControlProviderImpl"><service name="ooo.vba.ControlProvider"/></implementation><implementation name="ScVbaUserForm"><service name="ooo.vba.msforms.UserForm"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sfx.dll"><implementation name="SfxDocumentMetaData"><service name="com.sun.star.document.DocumentProperties"/></implementation><implementation name="com.sun.star.comp.desktop.QuickstartWrapper"><service name="com.sun.star.office.Quickstart"/></implementation><implementation name="com.sun.star.comp.document.OwnSubFilter"><service name="com.sun.star.comp.document.OwnSubFilter"/><service name="com.sun.star.document.OwnSubFilter"/></implementation><implementation name="com.sun.star.comp.embed.PackageStructureCreator"><service name="com.sun.star.comp.embed.PackageStructureCreator"/><service name="com.sun.star.embed.PackageStructureCreator"/></implementation><implementation name="com.sun.star.comp.office.FrameLoader"><service name="com.sun.star.frame.SynchronousFrameLoader"/></implementation><implementation name="com.sun.star.comp.sfx2.AppDispatchProvider"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.sfx2.ApplicationDialogLibraryContainer"><service name="com.sun.star.script.ApplicationDialogLibraryContainer"/></implementation><implementation name="com.sun.star.comp.sfx2.ApplicationScriptLibraryContainer"><service name="com.sun.star.script.ApplicationScriptLibraryContainer"/></implementation><implementation name="com.sun.star.comp.sfx2.DocumentTemplates"><service name="com.sun.star.frame.DocumentTemplates"/></implementation><implementation name="com.sun.star.comp.sfx2.GlobalEventBroadcaster"><service name="com.sun.star.frame.GlobalEventBroadcaster"/></implementation><implementation name="com.sun.star.comp.sfx2.IFrameObject"><service name="com.sun.star.frame.SpecialEmbeddedObject"/></implementation><implementation name="com.sun.star.comp.sfx2.PluginObject"><service name="com.sun.star.frame.SpecialEmbeddedObject"/></implementation><implementation name="com.sun.star.comp.sfx2.SfxMacroLoader"><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.sfx2.StandaloneDocumentInfo"><service name="com.sun.star.document.StandaloneDocumentInfo"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sot.dll"><implementation name="com.sun.star.comp.embed.OLESimpleStorage"><service name="com.sun.star.embed.OLESimpleStorage"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svgio.dll"><implementation name="svgio::svgreader::XSvgParser"><service name="com.sun.star.graphic.SvgTools"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fsstorage.uno.dll"><implementation name="com.sun.star.comp.embed.FileSystemStorageFactory"><service name="com.sun.star.comp.embed.FileSystemStorageFactory"/><service name="com.sun.star.embed.FileSystemStorageFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/passwordcontainer.uno.dll"><implementation name="stardiv.svl.PasswordContainer"><service name="com.sun.star.task.PasswordContainer"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svl.dll"><implementation name="com.sun.star.comp.svl.PathService"><service name="com.sun.star.config.SpecialConfigManager"/></implementation><implementation name="com.sun.star.uno.util.numbers.SvNumberFormatsSupplierServiceObject"><service name="com.sun.star.util.NumberFormatsSupplier"/></implementation><implementation name="com.sun.star.uno.util.numbers.SvNumberFormatterServiceObject"><service name="com.sun.star.util.NumberFormatter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/hatchwindowfactory.uno.dll"><implementation name="com.sun.star.comp.embed.DocumentCloser"><service name="com.sun.star.embed.DocumentCloser"/></implementation><implementation name="com.sun.star.comp.embed.HatchWindowFactory"><service name="com.sun.star.comp.embed.HatchWindowFactory"/><service name="com.sun.star.embed.HatchWindowFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svt.dll"><implementation name="com.sun.star.comp.graphic.GraphicProvider"><service name="com.sun.star.graphic.GraphicProvider"/></implementation><implementation name="com.sun.star.comp.graphic.GraphicRendererVCL"><service name="com.sun.star.graphic.GraphicRendererVCL"/></implementation><implementation name="com.sun.star.comp.svtools.OAddressBookSourceDialogUno"><service name="com.sun.star.ui.AddressBookSourceDialog"/></implementation><implementation name="com.sun.star.comp.svtools.uno.Wizard"><service name="com.sun.star.ui.dialogs.Wizard"/></implementation><implementation name="com.sun.star.graphic.GraphicObject"><service name="com.sun.star.graphic.GraphicObject"/></implementation><implementation name="com.sun.star.svtools.SvFilterOptionsDialog"><service name="com.sun.star.ui.dialogs.FilterOptionsDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svx.dll"><implementation name="com.sun.star.comp.Draw.GraphicExporter"><service name="com.sun.star.drawing.GraphicExportFilter"/></implementation><implementation name="com.sun.star.comp.Svx.GraphicExportHelper"><service name="com.sun.star.document.BinaryStreamResolver"/><service name="com.sun.star.document.GraphicObjectResolver"/></implementation><implementation name="com.sun.star.comp.Svx.GraphicImportHelper"><service name="com.sun.star.document.BinaryStreamResolver"/><service name="com.sun.star.document.GraphicObjectResolver"/></implementation><implementation name="com.sun.star.comp.gallery.GalleryThemeProvider"><service name="com.sun.star.gallery.GalleryThemeProvider"/></implementation><implementation name="com.sun.star.comp.graphic.PrimitiveFactory2D"><service name="com.sun.star.graphic.PrimitiveFactory2D"/></implementation><implementation name="com.sun.star.comp.svx.Impl.FindbarDispatcher"><service name="com.sun.star.comp.svx.FindbarDispatcher"/><service name="com.sun.star.frame.ProtocolHandler"/></implementation><implementation name="com.sun.star.comp.svx.RecoveryUI"><service name="com.sun.star.dialog.RecoveryUI"/></implementation><implementation name="com.sun.star.drawing.EnhancedCustomShapeEngine"><service name="com.sun.star.drawing.CustomShapeEngine"/></implementation><implementation name="com.sun.star.drawing.SvxShapeCollection"><service name="com.sun.star.drawing.ShapeCollection"/></implementation><implementation name="com.sun.star.drawing.SvxUnoColorTable"><service name="com.sun.star.drawing.ColorTable"/></implementation><implementation name="com.sun.star.svx.DownSearchToolboxController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.svx.FindTextToolboxController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.svx.FontHeightToolBoxController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.svx.UpSearchToolboxController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="org.apache.openoffice.comp.svx.sidebar.PanelFactory"><service name="com.sun.star.ui.UIElementFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svxcore.dll"><implementation name="com.sun.star.comp.svx.ExtrusionDepthController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.svx.ExtrusionDirectionController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.svx.ExtrusionLightingController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.svx.ExtrusionSurfaceController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.svx.FontworkAlignmentController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.svx.FontworkCharacterSpacingController"><service name="com.sun.star.frame.ToolbarController"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/textconversiondlgs.dll"><implementation name="com.sun.star.comp.linguistic2.ChineseTranslationDialog"><service name="com.sun.star.linguistic2.ChineseTranslationDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/msword.dll"><implementation name="com.sun.star.comp.Writer.RtfExport"><service name="com.sun.star.comp.Writer.RtfExport"/></implementation><implementation name="com.sun.star.comp.Writer.RtfImport"><service name="com.sun.star.comp.Writer.RtfImport"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sw.dll"><implementation name="SwXAutoTextContainer"><service name="com.sun.star.text.AutoTextContainer"/></implementation><implementation name="SwXMailMerge"><service name="com.sun.star.sdb.DataAccessDescriptor"/><service name="com.sun.star.text.MailMerge"/></implementation><implementation name="SwXModule"><service name="com.sun.star.text.GlobalSettings"/></implementation><implementation name="com.sun.star.comp.Writer.FilterOptionsDialog"><service name="com.sun.star.ui.dialogs.FilterOptionsDialog"/></implementation><implementation name="com.sun.star.comp.Writer.GlobalDocument"><service name="com.sun.star.text.GlobalDocument"/></implementation><implementation name="com.sun.star.comp.Writer.TextDocument"><service name="com.sun.star.text.TextDocument"/></implementation><implementation name="com.sun.star.comp.Writer.WebDocument"><service name="com.sun.star.text.WebDocument"/></implementation><implementation name="com.sun.star.comp.Writer.WriterModule"><service name="com.sun.star.text.ModuleDispatcher"/></implementation><implementation name="com.sun.star.comp.Writer.XMLContentExporter"><service name="com.sun.star.comp.Writer.XMLContentExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLExporter"><service name="com.sun.star.comp.Writer.XMLExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLMetaExporter"><service name="com.sun.star.comp.Writer.XMLMetaExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisContentExporter"><service name="com.sun.star.comp.Writer.XMLOasisContentExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisContentImporter"><service name="com.sun.star.comp.Writer.XMLOasisContentImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisExporter"><service name="com.sun.star.comp.Writer.XMLOasisExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisImporter"><service name="com.sun.star.comp.Writer.XMLOasisImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisMetaExporter"><service name="com.sun.star.comp.Writer.XMLOasisMetaExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisMetaImporter"><service name="com.sun.star.comp.Writer.XMLOasisMetaImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisSettingsExporter"><service name="com.sun.star.comp.Writer.XMLOasisSettingsExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisSettingsImporter"><service name="com.sun.star.comp.Writer.XMLOasisSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisStylesExporter"><service name="com.sun.star.comp.Writer.XMLOasisStylesExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisStylesImporter"><service name="com.sun.star.comp.Writer.XMLOasisStylesImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLSettingsExporter"><service name="com.sun.star.comp.Writer.XMLSettingsExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLStylesExporter"><service name="com.sun.star.comp.Writer.XMLStylesExporter"/></implementation><implementation name="com.sun.star.util.comp.FinalThreadManager"><service name="com.sun.star.util.JobManager"/></implementation><implementation name="org.apache.openoffice.comp.sw.sidebar.SwPanelFactory"><service name="com.sun.star.ui.UIElementFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/swd.dll"><implementation name="com.sun.star.comp.writer.FormatDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/><service name="com.sun.star.text.FormatDetector"/><service name="com.sun.star.text.W4WFormatDetector"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/vbaswobj.uno.dll"><implementation name="SwVbaDocument"><service name="ooo.vba.word.Document"/></implementation><implementation name="SwVbaEventsHelper"><service name="com.sun.star.document.vba.VBATextEventProcessor"/></implementation><implementation name="SwVbaGlobals"><service name="ooo.vba.word.Globals"/></implementation><implementation name="SwVbaWrapFormat"><service name="ooo.vba.word.WrapFormat"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ootk.dll"><implementation name="com.sun.star.awt.comp.AsyncCallback"><service name="com.sun.star.awt.AsyncCallback"/></implementation><implementation name="com.sun.star.comp.awt.Layout"><service name="com.sun.star.awt.Layout"/><service name="com.sun.star.comp.awt.Layout"/></implementation><implementation name="stardiv.Toolkit.UnoControlTabPage"><service name="com.sun.star.awt.tab.UnoControlTabPage"/></implementation><implementation name="stardiv.Toolkit.UnoControlTabPageModel"><service name="com.sun.star.awt.tab.UnoControlTabPageModel"/></implementation><implementation name="stardiv.Toolkit.UnoControlTabPageContainerModel"><service name="com.sun.star.awt.tab.UnoControlTabPageContainerModel"/></implementation><implementation name="stardiv.Toolkit.UnoControlTabPageContainer"><service name="com.sun.star.awt.tab.UnoControlTabPageContainer"/></implementation><implementation name="stardiv.Toolkit.DefaultGridColumnModel"><service name="com.sun.star.awt.grid.DefaultGridColumnModel"/></implementation><implementation name="stardiv.Toolkit.DefaultGridDataModel"><service name="com.sun.star.awt.grid.DefaultGridDataModel"/></implementation><implementation name="org.openoffice.comp.toolkit.GridColumn"><service name="com.sun.star.awt.grid.GridColumn"/></implementation><implementation name="org.openoffice.comp.toolkit.SortableGridDataModel"><service name="com.sun.star.awt.grid.SortableGridDataModel"/></implementation><implementation name="stardiv.Toolkit.GridControl"><service name="com.sun.star.awt.grid.UnoControlGrid"/></implementation><implementation name="stardiv.Toolkit.GridControlModel"><service name="com.sun.star.awt.grid.UnoControlGridModel"/></implementation><implementation name="stardiv.Toolkit.MutableTreeDataModel"><service name="com.sun.star.awt.tree.MutableTreeDataModel"/></implementation><implementation name="stardiv.Toolkit.StdTabController"><service name="com.sun.star.awt.TabController"/><service name="stardiv.vcl.control.TabController"/></implementation><implementation name="stardiv.Toolkit.StdTabControllerModel"><service name="com.sun.star.awt.TabControllerModel"/><service name="stardiv.vcl.controlmodel.TabController"/></implementation><implementation name="stardiv.Toolkit.TreeControl"><service name="com.sun.star.awt.tree.TreeControl"/></implementation><implementation name="stardiv.Toolkit.TreeControlModel"><service name="com.sun.star.awt.tree.TreeControlModel"/></implementation><implementation name="stardiv.Toolkit.UnoButtonControl"><service name="com.sun.star.awt.UnoControlButton"/><service name="stardiv.vcl.control.Button"/></implementation><implementation name="stardiv.Toolkit.UnoCheckBoxControl"><service name="com.sun.star.awt.UnoControlCheckBox"/><service name="stardiv.vcl.control.CheckBox"/></implementation><implementation name="stardiv.Toolkit.UnoComboBoxControl"><service name="com.sun.star.awt.UnoControlComboBox"/><service name="stardiv.vcl.control.ComboBox"/></implementation><implementation name="stardiv.Toolkit.UnoControlButtonModel"><service name="com.sun.star.awt.UnoControlButtonModel"/><service name="stardiv.vcl.controlmodel.Button"/></implementation><implementation name="stardiv.Toolkit.UnoControlCheckBoxModel"><service name="com.sun.star.awt.UnoControlCheckBoxModel"/><service name="stardiv.vcl.controlmodel.CheckBox"/></implementation><implementation name="stardiv.Toolkit.UnoControlComboBoxModel"><service name="com.sun.star.awt.UnoControlComboBoxModel"/><service name="stardiv.vcl.controlmodel.ComboBox"/></implementation><implementation name="stardiv.Toolkit.UnoControlContainer"><service name="com.sun.star.awt.UnoControlContainer"/><service name="stardiv.vcl.control.ControlContainer"/></implementation><implementation name="stardiv.Toolkit.UnoControlContainerModel"><service name="com.sun.star.awt.UnoControlContainerModel"/><service name="stardiv.vcl.controlmodel.ControlContainer"/></implementation><implementation name="stardiv.Toolkit.UnoControlCurrencyFieldModel"><service name="com.sun.star.awt.UnoControlCurrencyFieldModel"/><service name="stardiv.vcl.controlmodel.CurrencyField"/></implementation><implementation name="stardiv.Toolkit.UnoControlDateFieldModel"><service name="com.sun.star.awt.UnoControlDateFieldModel"/><service name="stardiv.vcl.controlmodel.DateField"/></implementation><implementation name="stardiv.Toolkit.UnoControlDialogModel"><service name="com.sun.star.awt.UnoControlDialogModel"/><service name="stardiv.vcl.controlmodel.Dialog"/></implementation><implementation name="stardiv.Toolkit.UnoControlEditModel"><service name="com.sun.star.awt.UnoControlEditModel"/><service name="stardiv.vcl.controlmodel.Edit"/></implementation><implementation name="stardiv.Toolkit.UnoControlFileControlModel"><service name="com.sun.star.awt.UnoControlFileControlModel"/><service name="stardiv.vcl.controlmodel.FileControl"/></implementation><implementation name="stardiv.Toolkit.UnoControlFixedHyperlinkModel"><service name="com.sun.star.awt.UnoControlFixedHyperlinkModel"/></implementation><implementation name="stardiv.Toolkit.UnoControlFixedLineModel"><service name="com.sun.star.awt.UnoControlFixedLineModel"/><service name="stardiv.vcl.controlmodel.FixedLine"/></implementation><implementation name="stardiv.Toolkit.UnoControlFixedTextModel"><service name="com.sun.star.awt.UnoControlFixedTextModel"/><service name="stardiv.vcl.controlmodel.FixedText"/></implementation><implementation name="stardiv.Toolkit.UnoControlFormattedFieldModel"><service name="com.sun.star.awt.UnoControlFormattedFieldModel"/><service name="stardiv.vcl.controlmodel.FormattedField"/></implementation><implementation name="stardiv.Toolkit.UnoControlGroupBoxModel"><service name="com.sun.star.awt.UnoControlGroupBoxModel"/><service name="stardiv.vcl.controlmodel.GroupBox"/></implementation><implementation name="stardiv.Toolkit.UnoControlImageControlModel"><service name="com.sun.star.awt.UnoControlImageButtonModel"/><service name="com.sun.star.awt.UnoControlImageControlModel"/><service name="stardiv.vcl.controlmodel.ImageButton"/><service name="stardiv.vcl.controlmodel.ImageControl"/></implementation><implementation name="stardiv.Toolkit.UnoControlListBoxModel"><service name="com.sun.star.awt.UnoControlListBoxModel"/><service name="stardiv.vcl.controlmodel.ListBox"/></implementation><implementation name="stardiv.Toolkit.UnoControlNumericFieldModel"><service name="com.sun.star.awt.UnoControlNumericFieldModel"/><service name="stardiv.vcl.controlmodel.NumericField"/></implementation><implementation name="stardiv.Toolkit.UnoControlPatternFieldModel"><service name="com.sun.star.awt.UnoControlPatternFieldModel"/><service name="stardiv.vcl.controlmodel.PatternField"/></implementation><implementation name="stardiv.Toolkit.UnoControlProgressBarModel"><service name="com.sun.star.awt.UnoControlProgressBarModel"/><service name="stardiv.vcl.controlmodel.ProgressBar"/></implementation><implementation name="stardiv.Toolkit.UnoControlRadioButtonModel"><service name="com.sun.star.awt.UnoControlRadioButtonModel"/><service name="stardiv.vcl.controlmodel.RadioButton"/></implementation><implementation name="stardiv.Toolkit.UnoControlRoadmapModel"><service name="com.sun.star.awt.UnoControlRoadmapModel"/><service name="stardiv.vcl.controlmodel.Roadmap"/></implementation><implementation name="stardiv.Toolkit.UnoControlScrollBarModel"><service name="com.sun.star.awt.UnoControlScrollBarModel"/><service name="stardiv.vcl.controlmodel.ScrollBar"/></implementation><implementation name="stardiv.Toolkit.UnoControlTimeFieldModel"><service name="com.sun.star.awt.UnoControlTimeFieldModel"/><service name="stardiv.vcl.controlmodel.TimeField"/></implementation><implementation name="stardiv.Toolkit.UnoCurrencyFieldControl"><service name="com.sun.star.awt.UnoControlCurrencyField"/><service name="stardiv.vcl.control.CurrencyField"/></implementation><implementation name="stardiv.Toolkit.UnoDateFieldControl"><service name="com.sun.star.awt.UnoControlDateField"/><service name="stardiv.vcl.control.DateField"/></implementation><implementation name="stardiv.Toolkit.UnoDialogControl"><service name="com.sun.star.awt.UnoControlDialog"/><service name="stardiv.vcl.control.Dialog"/></implementation><implementation name="stardiv.Toolkit.UnoEditControl"><service name="com.sun.star.awt.UnoControlEdit"/><service name="stardiv.vcl.control.Edit"/></implementation><implementation name="stardiv.Toolkit.UnoFileControl"><service name="com.sun.star.awt.UnoControlFileControl"/><service name="stardiv.vcl.control.FileControl"/></implementation><implementation name="stardiv.Toolkit.UnoFixedHyperlinkControl"><service name="com.sun.star.awt.UnoControlFixedHyperlink"/></implementation><implementation name="stardiv.Toolkit.UnoFixedLineControl"><service name="com.sun.star.awt.UnoControlFixedLine"/><service name="stardiv.vcl.control.FixedLine"/></implementation><implementation name="stardiv.Toolkit.UnoFixedTextControl"><service name="com.sun.star.awt.UnoControlFixedText"/><service name="stardiv.vcl.control.FixedText"/></implementation><implementation name="stardiv.Toolkit.UnoFormattedFieldControl"><service name="com.sun.star.awt.UnoControlFormattedField"/><service name="stardiv.vcl.control.FormattedField"/></implementation><implementation name="stardiv.Toolkit.UnoGroupBoxControl"><service name="com.sun.star.awt.UnoControlGroupBox"/><service name="stardiv.vcl.control.GroupBox"/></implementation><implementation name="stardiv.Toolkit.UnoImageControlControl"><service name="com.sun.star.awt.UnoControlImageButton"/><service name="com.sun.star.awt.UnoControlImageControl"/><service name="stardiv.vcl.control.ImageButton"/><service name="stardiv.vcl.control.ImageControl"/></implementation><implementation name="stardiv.Toolkit.UnoListBoxControl"><service name="com.sun.star.awt.UnoControlListBox"/><service name="stardiv.vcl.control.ListBox"/></implementation><implementation name="stardiv.Toolkit.UnoNumericFieldControl"><service name="com.sun.star.awt.UnoControlNumericField"/><service name="stardiv.vcl.control.NumericField"/></implementation><implementation name="stardiv.Toolkit.UnoPatternFieldControl"><service name="com.sun.star.awt.UnoControlPatternField"/><service name="stardiv.vcl.control.PatternField"/></implementation><implementation name="stardiv.Toolkit.UnoProgressBarControl"><service name="com.sun.star.awt.UnoControlProgressBar"/><service name="stardiv.vcl.control.ProgressBar"/></implementation><implementation name="stardiv.Toolkit.UnoRadioButtonControl"><service name="com.sun.star.awt.UnoControlRadioButton"/><service name="stardiv.vcl.control.RadioButton"/></implementation><implementation name="stardiv.Toolkit.UnoRoadmapControl"><service name="com.sun.star.awt.UnoControlRoadmap"/><service name="stardiv.vcl.control.Roadmap"/></implementation><implementation name="stardiv.Toolkit.UnoScrollBarControl"><service name="com.sun.star.awt.UnoControlScrollBar"/><service name="stardiv.vcl.control.ScrollBar"/></implementation><implementation name="org.openoffice.comp.toolkit.SpinningProgressControlModel"><service name="com.sun.star.awt.SpinningProgressControlModel"/></implementation><implementation name="org.openoffice.comp.toolkit.AnimatedImagesControlModel"><service name="com.sun.star.awt.AnimatedImagesControlModel"/></implementation><implementation name="org.openoffice.comp.toolkit.AnimatedImagesControl"><service name="com.sun.star.awt.AnimatedImagesControl"/></implementation><implementation name="stardiv.Toolkit.UnoSpinButtonControl"><service name="com.sun.star.awt.UnoControlSpinButton"/></implementation><implementation name="stardiv.Toolkit.UnoSpinButtonModel"><service name="com.sun.star.awt.UnoControlSpinButtonModel"/></implementation><implementation name="stardiv.Toolkit.UnoTimeFieldControl"><service name="com.sun.star.awt.UnoControlTimeField"/><service name="stardiv.vcl.control.TimeField"/></implementation><implementation name="stardiv.Toolkit.VCLXMenuBar"><service name="com.sun.star.awt.MenuBar"/><service name="stardiv.vcl.MenuBar"/></implementation><implementation name="stardiv.Toolkit.VCLXPointer"><service name="com.sun.star.awt.Pointer"/><service name="stardiv.vcl.Pointer"/></implementation><implementation name="stardiv.Toolkit.VCLXPopupMenu"><service name="com.sun.star.awt.PopupMenu"/><service name="stardiv.vcl.PopupMenu"/></implementation><implementation name="stardiv.Toolkit.VCLXPrinterServer"><service name="com.sun.star.awt.PrinterServer"/><service name="stardiv.vcl.PrinterServer"/></implementation><implementation name="stardiv.Toolkit.VCLXToolkit"><service name="com.sun.star.awt.Toolkit"/><service name="stardiv.vcl.VclToolkit"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/unordf.dll"><implementation name="CBlankNode"><service name="com.sun.star.rdf.BlankNode"/></implementation><implementation name="CLiteral"><service name="com.sun.star.rdf.Literal"/></implementation><implementation name="CURI"><service name="com.sun.star.rdf.URI"/></implementation><implementation name="librdf_Repository"><service name="com.sun.star.rdf.Repository"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/unoxml.dll"><implementation name="com.sun.star.comp.xml.dom.DocumentBuilder"><service name="com.sun.star.xml.dom.DocumentBuilder"/></implementation><implementation name="com.sun.star.comp.xml.dom.SAXDocumentBuilder"><service name="com.sun.star.xml.dom.SAXDocumentBuilder"/></implementation><implementation name="com.sun.star.comp.xml.dom.events.TestListener"><service name="com.sun.star.comp.xml.dom.events.TestListener"/></implementation><implementation name="com.sun.star.comp.xml.xpath.XPathAPI"><service name="com.sun.star.xml.xpath.XPathAPI"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xof.dll"><implementation name="com.sun.star.comp.Calc.XMLContentImporter"><service name="com.sun.star.comp.Calc.XMLContentImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLImporter"><service name="com.sun.star.comp.Calc.XMLImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLMetaImporter"><service name="com.sun.star.comp.Calc.XMLMetaImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLSettingsImporter"><service name="com.sun.star.comp.Calc.XMLSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLStylesImporter"><service name="com.sun.star.comp.Calc.XMLStylesImporter"/></implementation><implementation name="com.sun.star.comp.Chart.XMLContentImporter"><service name="com.sun.star.comp.Chart.XMLContentImporter"/></implementation><implementation name="com.sun.star.comp.Chart.XMLImporter"><service name="com.sun.star.comp.Chart.XMLImporter"/></implementation><implementation name="com.sun.star.comp.Chart.XMLStylesImporter"><service name="com.sun.star.comp.Chart.XMLStylesImporter"/></implementation><implementation name="com.sun.star.comp.Draw.XMLContentImporter"><service name="com.sun.star.comp.Draw.XMLContentImporter"/></implementation><implementation name="com.sun.star.comp.Draw.XMLImporter"><service name="com.sun.star.comp.Draw.XMLImporter"/></implementation><implementation name="com.sun.star.comp.Draw.XMLMetaImporter"><service name="com.sun.star.comp.Draw.XMLMetaImporter"/></implementation><implementation name="com.sun.star.comp.Draw.XMLSettingsImporter"><service name="com.sun.star.comp.Draw.XMLSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Draw.XMLStylesImporter"><service name="com.sun.star.comp.Draw.XMLStylesImporter"/></implementation><implementation name="com.sun.star.comp.Impress.XMLContentImporter"><service name="com.sun.star.comp.Impress.XMLContentImporter"/></implementation><implementation name="com.sun.star.comp.Impress.XMLImporter"><service name="com.sun.star.comp.Impress.XMLImporter"/></implementation><implementation name="com.sun.star.comp.Impress.XMLMetaImporter"><service name="com.sun.star.comp.Impress.XMLMetaImporter"/></implementation><implementation name="com.sun.star.comp.Impress.XMLSettingsImporter"><service name="com.sun.star.comp.Impress.XMLSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Impress.XMLStylesImporter"><service name="com.sun.star.comp.Impress.XMLStylesImporter"/></implementation><implementation name="com.sun.star.comp.Math.XMLMetaImporter"><service name="com.sun.star.comp.Math.XMLMetaImporter"/></implementation><implementation name="com.sun.star.comp.Math.XMLSettingsImporter"><service name="com.sun.star.comp.Math.XMLSettingsImporter"/></implementation><implementation name="com.sun.star.comp.OOo2OasisTransformer"><service name="com.sun.star.comp.OOo2OasisTransformer"/></implementation><implementation name="com.sun.star.comp.Oasis2OOoTransformer"><service name="com.sun.star.comp.Oasis2OOoTransformer"/></implementation><implementation name="com.sun.star.comp.Writer.XMLAutotextEventsImporter"><service name="com.sun.star.comp.Writer.XMLAutotextEventsImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLContentImporter"><service name="com.sun.star.comp.Writer.XMLContentImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLImporter"><service name="com.sun.star.comp.Writer.XMLImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLMetaImporter"><service name="com.sun.star.comp.Writer.XMLMetaImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLSettingsImporter"><service name="com.sun.star.comp.Writer.XMLSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLStylesImporter"><service name="com.sun.star.comp.Writer.XMLStylesImporter"/></implementation><implementation name="com.sun.star.document.XMLMetaImporter"><service name="com.sun.star.document.XMLMetaImporter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xo.dll"><implementation name="SchXMLExport.Compact"><service name="com.sun.star.comp.Chart.XMLExporter"/></implementation><implementation name="SchXMLExport.Content"><service name="com.sun.star.comp.Chart.XMLContentExporter"/></implementation><implementation name="SchXMLExport.Oasis.Compact"><service name="com.sun.star.comp.Chart.XMLOasisExporter"/></implementation><implementation name="SchXMLExport.Oasis.Content"><service name="com.sun.star.comp.Chart.XMLOasisContentExporter"/></implementation><implementation name="SchXMLExport.Oasis.Meta"><service name="com.sun.star.comp.Chart.XMLOasisMetaExporter"/></implementation><implementation name="SchXMLExport.Oasis.Styles"><service name="com.sun.star.comp.Chart.XMLOasisStylesExporter"/></implementation><implementation name="SchXMLExport.Styles"><service name="com.sun.star.comp.Chart.XMLStylesExporter"/></implementation><implementation name="SchXMLImport"><service name="com.sun.star.comp.Chart.XMLOasisImporter"/></implementation><implementation name="SchXMLImport.Content"><service name="com.sun.star.comp.Chart.XMLOasisContentImporter"/></implementation><implementation name="SchXMLImport.Meta"><service name="com.sun.star.comp.Chart.XMLOasisMetaImporter"/></implementation><implementation name="SchXMLImport.Styles"><service name="com.sun.star.comp.Chart.XMLOasisStylesImporter"/></implementation><implementation name="XMLDrawContentExportOOO"><service name="com.sun.star.comp.Draw.XMLContentExporter"/></implementation><implementation name="XMLDrawContentExportOasis"><service name="com.sun.star.comp.Draw.XMLOasisContentExporter"/></implementation><implementation name="XMLDrawExportOOO"><service name="com.sun.star.comp.Draw.XMLExporter"/></implementation><implementation name="XMLDrawExportOasis"><service name="com.sun.star.comp.Draw.XMLOasisExporter"/></implementation><implementation name="XMLDrawImportOasis"><service name="com.sun.star.comp.Draw.XMLOasisImporter"/></implementation><implementation name="XMLDrawMetaExportOOO"><service name="com.sun.star.comp.Draw.XMLMetaExporter"/></implementation><implementation name="XMLDrawMetaExportOasis"><service name="com.sun.star.comp.Draw.XMLOasisMetaExporter"/></implementation><implementation name="XMLDrawSettingsExportOOO"><service name="com.sun.star.comp.Draw.XMLSettingsExporter"/></implementation><implementation name="XMLDrawSettingsExportOasis"><service name="com.sun.star.comp.Draw.XMLOasisSettingsExporter"/></implementation><implementation name="XMLDrawStylesExportOOO"><service name="com.sun.star.comp.Draw.XMLStylesExporter"/></implementation><implementation name="XMLDrawStylesExportOasis"><service name="com.sun.star.comp.Draw.XMLOasisStylesExporter"/></implementation><implementation name="XMLDrawingLayerExport"><service name="com.sun.star.comp.DrawingLayer.XMLExporter"/></implementation><implementation name="XMLImpressClipboardExport"><service name="com.sun.star.comp.Impress.XMLClipboardExporter"/></implementation><implementation name="XMLImpressContentExportOOO"><service name="com.sun.star.comp.Impress.XMLContentExporter"/></implementation><implementation name="XMLImpressContentExportOasis"><service name="com.sun.star.comp.Impress.XMLOasisContentExporter"/></implementation><implementation name="XMLImpressContentImportOasis"><service name="com.sun.star.comp.Draw.XMLOasisContentImporter"/><service name="com.sun.star.comp.Impress.XMLOasisContentImporter"/></implementation><implementation name="XMLImpressExportOOO"><service name="com.sun.star.comp.Impress.XMLExporter"/></implementation><implementation name="XMLImpressExportOasis"><service name="com.sun.star.comp.Impress.XMLOasisExporter"/></implementation><implementation name="XMLImpressImportOasis"><service name="com.sun.star.comp.Impress.XMLOasisImporter"/></implementation><implementation name="XMLImpressMetaExportOOO"><service name="com.sun.star.comp.Impress.XMLMetaExporter"/></implementation><implementation name="XMLImpressMetaExportOasis"><service name="com.sun.star.comp.Impress.XMLOasisMetaExporter"/></implementation><implementation name="XMLImpressMetaImportOasis"><service name="com.sun.star.comp.Draw.XMLOasisMetaImporter"/><service name="com.sun.star.comp.Impress.XMLOasisMetaImporter"/></implementation><implementation name="XMLImpressSettingsExportOOO"><service name="com.sun.star.comp.Impress.XMLSettingsExporter"/></implementation><implementation name="XMLImpressSettingsExportOasis"><service name="com.sun.star.comp.Impress.XMLOasisSettingsExporter"/></implementation><implementation name="XMLImpressSettingsImportOasis"><service name="com.sun.star.comp.Draw.XMLOasisSettingsImporter"/><service name="com.sun.star.comp.Impress.XMLOasisSettingsImporter"/></implementation><implementation name="XMLImpressStylesExportOOO"><service name="com.sun.star.comp.Impress.XMLStylesExporter"/></implementation><implementation name="XMLImpressStylesExportOasis"><service name="com.sun.star.comp.Impress.XMLOasisStylesExporter"/></implementation><implementation name="XMLImpressStylesImportOasis"><service name="com.sun.star.comp.Draw.XMLOasisStylesImporter"/><service name="com.sun.star.comp.Impress.XMLOasisStylesImporter"/></implementation><implementation name="XMLMetaExportComponent"><service name="com.sun.star.document.XMLOasisMetaExporter"/></implementation><implementation name="XMLMetaExportOOo"><service name="com.sun.star.document.XMLMetaExporter"/></implementation><implementation name="XMLMetaImportComponent"><service name="com.sun.star.document.XMLOasisMetaImporter"/></implementation><implementation name="XMLVersionListPersistence"><service name="com.sun.star.document.DocumentRevisionListPersistence"/></implementation><implementation name="com.sun.star.comp.Writer.XMLAutotextEventsExporter"><service name="com.sun.star.comp.Writer.XMLAutotextEventsExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisAutotextEventsExporter"><service name="com.sun.star.comp.Writer.XMLOasisAutotextEventsExporter"/></implementation><implementation name="com.sun.star.comp.Writer.XMLOasisAutotextEventsImporter"><service name="com.sun.star.comp.Writer.XMLOasisAutotextEventsImporter"/></implementation><implementation name="xmloff::AnimationsImport"><service name="com.sun.star.comp.Xmloff.AnimationsImport"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/configmgr.uno.dll"><implementation name="com.sun.star.comp.configuration.ConfigurationProvider"><service name="com.sun.star.configuration.ConfigurationProvider"/></implementation><implementation name="com.sun.star.comp.configuration.ConfigurationRegistry"><service name="com.sun.star.configuration.ConfigurationRegistry"/></implementation><implementation name="com.sun.star.comp.configuration.DefaultProvider"><service name="com.sun.star.configuration.DefaultProvider"/><singleton name="com.sun.star.configuration.theDefaultProvider"/></implementation><implementation name="com.sun.star.comp.configuration.Update"><service name="com.sun.star.configuration.Update_Service"/><singleton name="com.sun.star.configuration.Update"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ctl.dll"><implementation name="stardiv.UnoControls.FrameControl"><service name="com.sun.star.frame.FrameControl"/></implementation><implementation name="stardiv.UnoControls.ProgressBar"><service name="com.sun.star.awt.XProgressBar"/></implementation><implementation name="stardiv.UnoControls.ProgressMonitor"><service name="com.sun.star.awt.XProgressMonitor"/></implementation><implementation name="stardiv.UnoControls.StatusIndicator"><service name="com.sun.star.task.XStatusIndicator"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/date.dll"><implementation name="com.sun.star.sheet.addin.DateFunctionsImpl"><service name="com.sun.star.sheet.AddIn"/><service name="com.sun.star.sheet.addin.DateFunctions"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dba.dll"><implementation name="com.sun.star.comp.chart2.data.DatabaseDataProvider"><service name="com.sun.star.chart2.data.DatabaseDataProvider"/></implementation><implementation name="com.sun.star.comp.dba.DataAccessDescriptorFactory"><service name="com.sun.star.sdb.DataAccessDescriptorFactory"/><singleton name="com.sun.star.sdb.DataAccessDescriptorFactory"/></implementation><implementation name="com.sun.star.comp.dba.OCommandDefinition"><service name="com.sun.star.sdb.CommandDefinition"/><service name="com.sun.star.sdb.QueryDefinition"/></implementation><implementation name="com.sun.star.comp.dba.OComponentDefinition"><service name="com.sun.star.sdb.TableDefinition"/></implementation><implementation name="com.sun.star.comp.dba.ODatabaseContext"><service name="com.sun.star.sdb.DatabaseContext"/></implementation><implementation name="com.sun.star.comp.dba.ODatabaseDocument"><service name="com.sun.star.document.OfficeDocument"/><service name="com.sun.star.sdb.OfficeDatabaseDocument"/></implementation><implementation name="com.sun.star.comp.dba.ODatabaseSource"><service name="com.sun.star.sdb.DataSource"/><service name="com.sun.star.sdb.DocumentDataSource"/></implementation><implementation name="com.sun.star.comp.dba.ORowSet"><service name="com.sun.star.sdb.ResultSet"/><service name="com.sun.star.sdb.RowSet"/><service name="com.sun.star.sdbc.ResultSet"/><service name="com.sun.star.sdbc.RowSet"/><service name="com.sun.star.sdbcx.ResultSet"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbase.dll"><implementation name="com.sun.star.comp.sdbc.dbase.ODriver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbaxml.dll"><implementation name="com.sun.star.comp.sdb.DBExportFilter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.sdb.DBFilter"><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.sdb.XMLFullExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.sdb.XMLSettingsExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="org.openoffice.comp.dbflt.DBContentLoader2"><service name="com.sun.star.frame.FrameLoader"/></implementation><implementation name="org.openoffice.comp.dbflt.DBTypeDetection"><service name="com.sun.star.document.ExtendedTypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbmm.dll"><implementation name="com.sun.star.comp.dbaccess.macromigration.MacroMigrationDialogService"><service name="com.sun.star.sdb.application.MacroMigrationWizard"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbp.dll"><implementation name="org.openoffice.comp.dbp.OGridWizard"><service name="com.sun.star.sdb.GridControlAutoPilot"/></implementation><implementation name="org.openoffice.comp.dbp.OGroupBoxWizard"><service name="com.sun.star.sdb.GroupBoxAutoPilot"/></implementation><implementation name="org.openoffice.comp.dbp.OListComboWizard"><service name="com.sun.star.sdb.ListComboBoxAutoPilot"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbpool2.dll"><implementation name="com.sun.star.sdbc.OConnectionPool"><service name="com.sun.star.sdbc.ConnectionPool"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbtools.dll"><implementation name="org.openoffice.comp.helper.DatabaseMetaDataResultSet"><service name="com.sun.star.sdbc.ResultSet"/></implementation><implementation name="org.openoffice.comp.helper.ParameterSubstitution"><service name="com.sun.star.sdb.ParameterSubstitution"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dbu.dll"><implementation name="com.sun.star.comp.dbaccess.DatabaseInteractionHandler"><service name="com.sun.star.sdb.DatabaseInteractionHandler"/></implementation><implementation name="com.sun.star.comp.dbaccess.LegacyInteractionHandler"><service name="com.sun.star.sdb.InteractionHandler"/></implementation><implementation name="com.sun.star.comp.dbaccess.OTextConnectionSettingsDialog"><service name="com.sun.star.sdb.TextConnectionSettings"/></implementation><implementation name="com.sun.star.comp.dbu.OColumnControl"><service name="com.sun.star.sdb.ColumnDescriptorControl"/></implementation><implementation name="com.sun.star.comp.dbu.OColumnControlModel"><service name="com.sun.star.sdb.ColumnDescriptorControlModel"/></implementation><implementation name="com.sun.star.comp.dbu.SbaXGridControl"><service name="com.sun.star.form.control.GridControl"/><service name="com.sun.star.form.control.InteractionGridControl"/></implementation><implementation name="com.sun.star.comp.sdb.DirectSQLDialog"><service name="org.openoffice.comp.dbu.DirectSqlDialog"/></implementation><implementation name="com.sun.star.sdb.ApplicationToolboxController"><service name="com.sun.star.frame.ToolboxController"/></implementation><implementation name="com.sun.star.uno.comp.sdb.RowsetFilterDialog"><service name="com.sun.star.sdb.FilterDialog"/></implementation><implementation name="com.sun.star.uno.comp.sdb.RowsetOrderDialog"><service name="com.sun.star.sdb.OrderDialog"/></implementation><implementation name="org.openoffice.comp.dbu.CopyTableWizard"><service name="com.sun.star.sdb.application.CopyTableWizard"/></implementation><implementation name="org.openoffice.comp.dbu.DBContentLoader"><service name="com.sun.star.frame.FrameLoader"/><service name="com.sun.star.sdb.ContentLoader"/></implementation><implementation name="org.openoffice.comp.dbu.OAdvancedSettingsDialog"><service name="com.sun.star.sdb.AdvancedDatabaseSettingsDialog"/></implementation><implementation name="org.openoffice.comp.dbu.OApplicationController"><service name="com.sun.star.sdb.application.DefaultViewController"/></implementation><implementation name="org.openoffice.comp.dbu.ODBTypeWizDialog"><service name="com.sun.star.sdb.DataSourceTypeChangeDialog"/></implementation><implementation name="org.openoffice.comp.dbu.ODBTypeWizDialogSetup"><service name="com.sun.star.sdb.DatabaseWizardDialog"/></implementation><implementation name="org.openoffice.comp.dbu.ODatasourceAdministrationDialog"><service name="com.sun.star.sdb.DatasourceAdministrationDialog"/></implementation><implementation name="org.openoffice.comp.dbu.ODatasourceBrowser"><service name="com.sun.star.sdb.DataSourceBrowser"/></implementation><implementation name="org.openoffice.comp.dbu.OFormGridView"><service name="com.sun.star.sdb.FormGridView"/></implementation><implementation name="org.openoffice.comp.dbu.OQueryDesign"><service name="com.sun.star.sdb.QueryDesign"/></implementation><implementation name="org.openoffice.comp.dbu.ORelationDesign"><service name="com.sun.star.sdb.RelationDesign"/></implementation><implementation name="org.openoffice.comp.dbu.OSQLMessageDialog"><service name="com.sun.star.sdb.ErrorMessageDialog"/></implementation><implementation name="org.openoffice.comp.dbu.OTableDesign"><service name="com.sun.star.sdb.TableDesign"/></implementation><implementation name="org.openoffice.comp.dbu.OTableFilterDialog"><service name="com.sun.star.sdb.TableFilterDialog"/></implementation><implementation name="org.openoffice.comp.dbu.OUserSettingsDialog"><service name="com.sun.star.sdb.UserAdministrationDialog"/></implementation><implementation name="org.openoffice.comp.dbu.OViewDesign"><service name="com.sun.star.sdb.ViewDesign"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/deployment.uno.dll"><implementation name="com.sun.star.comp.deployment.ExtensionManager"><service name="com.sun.star.comp.deployment.ExtensionManager"/><singleton name="com.sun.star.deployment.ExtensionManager"/></implementation><implementation name="com.sun.star.comp.deployment.PackageInformationProvider"><service name="com.sun.star.comp.deployment.PackageInformationProvider"/><singleton name="com.sun.star.deployment.PackageInformationProvider"/></implementation><implementation name="com.sun.star.comp.deployment.PackageManagerFactory"><service name="com.sun.star.comp.deployment.PackageManagerFactory"/><singleton name="com.sun.star.deployment.thePackageManagerFactory"/></implementation><implementation name="com.sun.star.comp.deployment.ProgressLog"><service name="com.sun.star.comp.deployment.ProgressLog"/></implementation><implementation name="com.sun.star.comp.deployment.component.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation><implementation name="com.sun.star.comp.deployment.configuration.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation><implementation name="com.sun.star.comp.deployment.executable.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation><implementation name="com.sun.star.comp.deployment.help.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation><implementation name="com.sun.star.comp.deployment.script.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation><implementation name="com.sun.star.comp.deployment.sfwk.PackageRegistryBackend"><service name="com.sun.star.deployment.PackageRegistryBackend"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/deploymentgui.uno.dll"><implementation name="com.sun.star.comp.deployment.ui.LicenseDialog"><service name="com.sun.star.deployment.ui.LicenseDialog"/></implementation><implementation name="com.sun.star.comp.deployment.ui.PackageManagerDialog"><service name="com.sun.star.deployment.ui.PackageManagerDialog"/></implementation><implementation name="com.sun.star.comp.deployment.ui.UpdateRequiredDialog"><service name="com.sun.star.deployment.ui.UpdateRequiredDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dlgprov.uno.dll"><implementation name="com.sun.star.comp.scripting.DialogProvider"><service name="com.sun.star.awt.ContainerWindowProvider"/><service name="com.sun.star.awt.DialogProvider"/><service name="com.sun.star.awt.DialogProvider2"/></implementation><implementation name="com.sun.star.comp.scripting.DialogModelProvider"><service name="com.sun.star.awt.UnoControlDialogModelProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/embobj.dll"><implementation name="com.sun.star.comp.embed.EmbeddedObjectCreator"><service name="com.sun.star.comp.embed.EmbeddedObjectCreator"/><service name="com.sun.star.embed.EmbeddedObjectCreator"/></implementation><implementation name="com.sun.star.comp.embed.OOoEmbeddedObjectFactory"><service name="com.sun.star.comp.embed.OOoEmbeddedObjectFactory"/><service name="com.sun.star.embed.OOoEmbeddedObjectFactory"/></implementation><implementation name="com.sun.star.comp.embed.OOoSpecialEmbeddedObjectFactory"><service name="com.sun.star.comp.embed.OOoSpecialEmbeddedObjectFactory"/><service name="com.sun.star.embed.OOoSpecialEmbeddedObjectFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/evtatt.dll"><implementation name="com.sun.star.comp.EventAttacher"><service name="com.sun.star.script.EventAttacher"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fastsax.uno.dll"><implementation name="com.sun.star.comp.extensions.xml.sax.FastParser"><service name="com.sun.star.xml.sax.FastParser"/></implementation><implementation name="com.sun.star.comp.extensions.xml.sax.FastSerializer"><service name="com.sun.star.xml.sax.FastSerializer"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fileacc.dll"><implementation name="com.sun.star.comp.ucb.SimpleFileAccess"><service name="com.sun.star.ucb.SimpleFileAccess"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/filterconfig1.dll"><implementation name="com.sun.star.comp.filter.config.ConfigFlush"><service name="com.sun.star.document.FilterConfigRefresh"/></implementation><implementation name="com.sun.star.comp.filter.config.ContentHandlerFactory"><service name="com.sun.star.frame.ContentHandlerFactory"/></implementation><implementation name="com.sun.star.comp.filter.config.FilterFactory"><service name="com.sun.star.document.FilterFactory"/></implementation><implementation name="com.sun.star.comp.filter.config.FrameLoaderFactory"><service name="com.sun.star.frame.FrameLoaderFactory"/></implementation><implementation name="com.sun.star.comp.filter.config.TypeDetection"><service name="com.sun.star.document.TypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/flash.dll"><implementation name="com.sun.star.Impress.FlashExportDialog"><service name="com.sun.star.Impress.FlashExportDialog"/></implementation><implementation name="com.sun.star.comp.Impress.FlashExportFilter"><service name="com.sun.star.document.ExportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/flat.dll"><implementation name="com.sun.star.comp.sdbc.flat.ODriver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/for.dll"><implementation name="simple.formula.FormulaOpCodeMapperObj"><service name="com.sun.star.sheet.FormulaOpCodeMapper"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fpicker.uno.dll"><implementation name="com.sun.star.comp.fpicker.FilePicker"><service name="com.sun.star.ui.dialogs.FilePicker"/></implementation><implementation name="com.sun.star.comp.fpicker.FolderPicker"><service name="com.sun.star.ui.dialogs.FolderPicker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fps_office.uno.dll"><implementation name="com.sun.star.svtools.OfficeFilePicker"><service name="com.sun.star.ui.dialogs.OfficeFilePicker"/></implementation><implementation name="com.sun.star.svtools.OfficeFolderPicker"><service name="com.sun.star.ui.dialogs.OfficeFolderPicker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/frm.dll"><implementation name="com.sun.star.comp.form.ONavigationBarControl"><service name="com.sun.star.form.control.NavigationToolBar"/></implementation><implementation name="com.sun.star.comp.form.ONavigationBarModel"><service name="com.sun.star.form.FormComponent"/><service name="com.sun.star.form.FormControlModel"/><service name="com.sun.star.form.component.NavigationToolBar"/></implementation><implementation name="com.sun.star.comp.form.ORichTextControl"><service name="com.sun.star.form.control.RichTextControl"/></implementation><implementation name="com.sun.star.comp.forms.FormOperations"><service name="com.sun.star.form.runtime.FormOperations"/></implementation><implementation name="com.sun.star.comp.forms.ODatabaseForm"><service name="com.sun.star.form.FormComponent"/><service name="com.sun.star.form.FormComponents"/><service name="com.sun.star.form.component.DataForm"/><service name="com.sun.star.form.component.Form"/><service name="com.sun.star.form.component.HTMLForm"/><service name="stardiv.one.form.component.Form"/></implementation><implementation name="com.sun.star.comp.forms.OFilterControl"><service name="com.sun.star.awt.UnoControl"/><service name="com.sun.star.form.control.FilterControl"/></implementation><implementation name="com.sun.star.comp.forms.OFormattedFieldWrapper_ForcedFormatted"><service name="com.sun.star.form.binding.BindableDatabaseFormattedField"/><service name="com.sun.star.form.component.DatabaseFormattedField"/><service name="com.sun.star.form.component.FormattedField"/><service name="stardiv.one.form.component.FormattedField"/></implementation><implementation name="com.sun.star.comp.forms.ORichTextModel"><service name="com.sun.star.form.FormComponent"/><service name="com.sun.star.form.FormControlModel"/><service name="com.sun.star.form.component.RichTextControl"/><service name="com.sun.star.style.CharacterProperties"/><service name="com.sun.star.style.CharacterPropertiesAsian"/><service name="com.sun.star.style.CharacterPropertiesComplex"/><service name="com.sun.star.style.ParagraphProperties"/><service name="com.sun.star.style.ParagraphPropertiesComplex"/><service name="com.sun.star.text.TextRange"/></implementation><implementation name="com.sun.star.comp.forms.OScrollBarModel"><service name="com.sun.star.form.FormComponent"/><service name="com.sun.star.form.FormControlModel"/><service name="com.sun.star.form.binding.BindableIntegerValueRange"/><service name="com.sun.star.form.component.ScrollBar"/></implementation><implementation name="com.sun.star.comp.forms.OSpinButtonModel"><service name="com.sun.star.form.FormComponent"/><service name="com.sun.star.form.FormControlModel"/><service name="com.sun.star.form.binding.BindableIntegerValueRange"/><service name="com.sun.star.form.component.SpinButton"/></implementation><implementation name="com.sun.star.comp.xml.xpath.XFormsExtension"><service name="com.sun.star.xml.xpath.XPathExtension"/></implementation><implementation name="com.sun.star.form.ImageProducer"><service name="com.sun.star.awt.ImageProducer"/></implementation><implementation name="com.sun.star.form.Model"><service name="com.sun.star.xforms.Model"/></implementation><implementation name="com.sun.star.form.OButtonControl"><service name="com.sun.star.form.control.CommandButton"/><service name="stardiv.one.form.control.CommandButton"/></implementation><implementation name="com.sun.star.form.OButtonModel"><service name="com.sun.star.form.component.CommandButton"/><service name="stardiv.one.form.component.CommandButton"/></implementation><implementation name="com.sun.star.form.OCheckBoxControl"><service name="com.sun.star.form.control.CheckBox"/><service name="stardiv.one.form.control.CheckBox"/></implementation><implementation name="com.sun.star.form.OCheckBoxModel"><service name="com.sun.star.form.binding.BindableDatabaseCheckBox"/><service name="com.sun.star.form.component.CheckBox"/><service name="com.sun.star.form.component.DatabaseCheckBox"/><service name="stardiv.one.form.component.CheckBox"/></implementation><implementation name="com.sun.star.form.OComboBoxControl"><service name="com.sun.star.form.control.ComboBox"/><service name="stardiv.one.form.control.ComboBox"/></implementation><implementation name="com.sun.star.form.OComboBoxModel"><service name="com.sun.star.form.binding.BindableDatabaseComboBox"/><service name="com.sun.star.form.component.ComboBox"/><service name="com.sun.star.form.component.DatabaseComboBox"/><service name="stardiv.one.form.component.ComboBox"/></implementation><implementation name="com.sun.star.form.OCurrencyControl"><service name="com.sun.star.form.control.CurrencyField"/><service name="stardiv.one.form.control.CurrencyField"/></implementation><implementation name="com.sun.star.form.OCurrencyModel"><service name="com.sun.star.form.component.CurrencyField"/><service name="com.sun.star.form.component.DatabaseCurrencyField"/><service name="stardiv.one.form.component.CurrencyField"/></implementation><implementation name="com.sun.star.form.ODateControl"><service name="com.sun.star.form.control.DateField"/><service name="stardiv.one.form.control.DateField"/></implementation><implementation name="com.sun.star.form.ODateModel"><service name="com.sun.star.form.component.DatabaseDateField"/><service name="com.sun.star.form.component.DateField"/><service name="stardiv.one.form.component.DateField"/></implementation><implementation name="com.sun.star.form.OEditControl"><service name="com.sun.star.form.control.TextField"/><service name="stardiv.one.form.control.Edit"/><service name="stardiv.one.form.control.TextField"/></implementation><implementation name="com.sun.star.form.OEditModel"><service name="com.sun.star.form.binding.BindableDatabaseTextField"/><service name="com.sun.star.form.component.DatabaseTextField"/><service name="com.sun.star.form.component.TextField"/><service name="stardiv.one.form.component.TextField"/></implementation><implementation name="com.sun.star.form.OFileControlModel"><service name="com.sun.star.form.component.FileControl"/><service name="stardiv.one.form.component.FileControl"/></implementation><implementation name="com.sun.star.form.OFixedTextModel"><service name="com.sun.star.form.component.FixedText"/><service name="stardiv.one.form.component.FixedText"/></implementation><implementation name="com.sun.star.form.OFormattedControl"><service name="com.sun.star.form.control.FormattedField"/><service name="stardiv.one.form.control.FormattedField"/></implementation><implementation name="com.sun.star.form.OFormattedFieldWrapper"><service name="stardiv.one.form.component.Edit"/></implementation><implementation name="com.sun.star.form.OFormsCollection"><service name="com.sun.star.form.Forms"/></implementation><implementation name="com.sun.star.form.OGridControlModel"><service name="com.sun.star.form.component.GridControl"/><service name="stardiv.one.form.component.Grid"/><service name="stardiv.one.form.component.GridControl"/></implementation><implementation name="com.sun.star.form.OGroupBoxControl"><service name="com.sun.star.form.control.GroupBox"/><service name="stardiv.one.form.control.GroupBox"/></implementation><implementation name="com.sun.star.form.OGroupBoxModel"><service name="com.sun.star.form.component.GroupBox"/><service name="stardiv.one.form.component.GroupBox"/></implementation><implementation name="com.sun.star.form.OHiddenModel"><service name="com.sun.star.form.component.HiddenControl"/><service name="stardiv.one.form.component.Hidden"/><service name="stardiv.one.form.component.HiddenControl"/></implementation><implementation name="com.sun.star.form.OImageButtonControl"><service name="com.sun.star.form.control.ImageButton"/><service name="stardiv.one.form.control.ImageButton"/></implementation><implementation name="com.sun.star.form.OImageButtonModel"><service name="com.sun.star.form.component.ImageButton"/><service name="stardiv.one.form.component.ImageButton"/></implementation><implementation name="com.sun.star.form.OImageControlControl"><service name="com.sun.star.form.control.ImageControl"/><service name="stardiv.one.form.control.ImageControl"/></implementation><implementation name="com.sun.star.form.OImageControlModel"><service name="com.sun.star.form.component.DatabaseImageControl"/><service name="stardiv.one.form.component.ImageControl"/></implementation><implementation name="com.sun.star.form.OListBoxControl"><service name="com.sun.star.form.control.ListBox"/><service name="stardiv.one.form.control.ListBox"/></implementation><implementation name="com.sun.star.form.OListBoxModel"><service name="com.sun.star.form.binding.BindableDatabaseListBox"/><service name="com.sun.star.form.component.DatabaseListBox"/><service name="com.sun.star.form.component.ListBox"/><service name="stardiv.one.form.component.ListBox"/></implementation><implementation name="com.sun.star.form.ONumericControl"><service name="com.sun.star.form.control.NumericField"/><service name="stardiv.one.form.control.NumericField"/></implementation><implementation name="com.sun.star.form.ONumericModel"><service name="com.sun.star.form.binding.BindableDatabaseNumericField"/><service name="com.sun.star.form.component.DatabaseNumericField"/><service name="com.sun.star.form.component.NumericField"/><service name="stardiv.one.form.component.NumericField"/></implementation><implementation name="com.sun.star.form.OPatternControl"><service name="com.sun.star.form.control.PatternField"/><service name="stardiv.one.form.control.PatternField"/></implementation><implementation name="com.sun.star.form.OPatternModel"><service name="com.sun.star.form.component.DatabasePatternField"/><service name="com.sun.star.form.component.PatternField"/><service name="stardiv.one.form.component.PatternField"/></implementation><implementation name="com.sun.star.form.ORadioButtonControl"><service name="com.sun.star.form.control.RadioButton"/><service name="stardiv.one.form.control.RadioButton"/></implementation><implementation name="com.sun.star.form.ORadioButtonModel"><service name="com.sun.star.form.binding.BindableDatabaseRadioButton"/><service name="com.sun.star.form.component.DatabaseRadioButton"/><service name="com.sun.star.form.component.RadioButton"/><service name="stardiv.one.form.component.RadioButton"/></implementation><implementation name="com.sun.star.form.OTimeControl"><service name="com.sun.star.form.control.TimeField"/><service name="stardiv.one.form.control.TimeField"/></implementation><implementation name="com.sun.star.form.OTimeModel"><service name="com.sun.star.form.component.DatabaseTimeField"/><service name="com.sun.star.form.component.TimeField"/><service name="stardiv.one.form.component.TimeField"/></implementation><implementation name="com.sun.star.form.XForms"><service name="com.sun.star.xforms.XForms"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/guesslang.dll"><implementation name="com.sun.star.lingu2.LanguageGuessing"><service name="com.sun.star.linguistic2.LanguageGuessing"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/i18npool.uno.dll"><implementation name="com.sun.star.i18n.BreakIterator"><service name="com.sun.star.i18n.BreakIterator"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_Unicode"><service name="com.sun.star.i18n.BreakIterator_Unicode"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_ja"><service name="com.sun.star.i18n.BreakIterator_ja"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_ko"><service name="com.sun.star.i18n.BreakIterator_ko"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_th"><service name="com.sun.star.i18n.BreakIterator_th"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_zh"><service name="com.sun.star.i18n.BreakIterator_zh"/></implementation><implementation name="com.sun.star.i18n.BreakIterator_zh_TW"><service name="com.sun.star.i18n.BreakIterator_zh_TW"/></implementation><implementation name="com.sun.star.i18n.CalendarImpl"><service name="com.sun.star.i18n.LocaleCalendar"/></implementation><implementation name="com.sun.star.i18n.Calendar_ROC"><service name="com.sun.star.i18n.Calendar_ROC"/></implementation><implementation name="com.sun.star.i18n.Calendar_buddhist"><service name="com.sun.star.i18n.Calendar_buddhist"/></implementation><implementation name="com.sun.star.i18n.Calendar_gengou"><service name="com.sun.star.i18n.Calendar_gengou"/></implementation><implementation name="com.sun.star.i18n.Calendar_gregorian"><service name="com.sun.star.i18n.Calendar_gregorian"/></implementation><implementation name="com.sun.star.i18n.Calendar_hanja"><service name="com.sun.star.i18n.Calendar_hanja"/></implementation><implementation name="com.sun.star.i18n.Calendar_hanja_yoil"><service name="com.sun.star.i18n.Calendar_hanja_yoil"/></implementation><implementation name="com.sun.star.i18n.Calendar_hijri"><service name="com.sun.star.i18n.Calendar_hijri"/></implementation><implementation name="com.sun.star.i18n.Calendar_jewish"><service name="com.sun.star.i18n.Calendar_jewish"/></implementation><implementation name="com.sun.star.i18n.ChapterCollator"><service name="com.sun.star.i18n.ChapterCollator"/></implementation><implementation name="com.sun.star.i18n.CharacterClassification"><service name="com.sun.star.i18n.CharacterClassification"/></implementation><implementation name="com.sun.star.i18n.CharacterClassification_Unicode"><service name="com.sun.star.i18n.CharacterClassification_Unicode"/></implementation><implementation name="com.sun.star.i18n.Collator"><service name="com.sun.star.i18n.Collator"/></implementation><implementation name="com.sun.star.i18n.Collator_Unicode"><service name="com.sun.star.i18n.Collator_Unicode"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier"><service name="com.sun.star.i18n.IndexEntrySupplier"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_Unicode"><service name="com.sun.star.i18n.IndexEntrySupplier_Unicode"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_asian"><service name="com.sun.star.i18n.IndexEntrySupplier_asian"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first)"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first) (grouped by consonant)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first) (grouped by consonant)"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first) (grouped by syllable)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric first) (grouped by syllable)"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last)"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last) (grouped by consonant)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last) (grouped by consonant)"/></implementation><implementation name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last) (grouped by syllable)"><service name="com.sun.star.i18n.IndexEntrySupplier_ja_phonetic (alphanumeric last) (grouped by syllable)"/></implementation><implementation name="com.sun.star.i18n.InputSequenceChecker"><service name="com.sun.star.i18n.InputSequenceChecker"/></implementation><implementation name="com.sun.star.i18n.InputSequenceChecker_hi"><service name="com.sun.star.i18n.InputSequenceChecker_hi"/></implementation><implementation name="com.sun.star.i18n.InputSequenceChecker_th"><service name="com.sun.star.i18n.InputSequenceChecker_th"/></implementation><implementation name="com.sun.star.i18n.LocaleData"><service name="com.sun.star.i18n.LocaleData"/></implementation><implementation name="com.sun.star.i18n.NativeNumberSupplier"><service name="com.sun.star.i18n.NativeNumberSupplier"/></implementation><implementation name="com.sun.star.i18n.NumberFormatCodeMapper"><service name="com.sun.star.i18n.NumberFormatMapper"/></implementation><implementation name="com.sun.star.i18n.OrdinalSuffix"><service name="com.sun.star.i18n.OrdinalSuffix"/></implementation><implementation name="com.sun.star.i18n.ScriptTypeDetector"><service name="com.sun.star.i18n.ScriptTypeDetector"/></implementation><implementation name="com.sun.star.i18n.TextConversion"><service name="com.sun.star.i18n.TextConversion"/></implementation><implementation name="com.sun.star.i18n.TextConversion_ko"><service name="com.sun.star.i18n.TextConversion_ko"/></implementation><implementation name="com.sun.star.i18n.TextConversion_zh"><service name="com.sun.star.i18n.TextConversion_zh"/></implementation><implementation name="com.sun.star.i18n.Transliteration"><service name="com.sun.star.i18n.Transliteration"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumEastIndic_ar"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumFullwidth"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumIndic_ar"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumIndic_hi"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumKanjiShort_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumKanjiTraditional_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumLower_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumLower_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumUpper_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNumUpper_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.CharToNum_th"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.FULLWIDTHKATAKANA_HALFWIDTHKATAKANA"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.FULLWIDTH_HALFWIDTH"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.FULLWIDTH_HALFWIDTH_LIKE_ASC"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.HALFWIDTHKATAKANA_FULLWIDTHKATAKANA"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.HALFWIDTH_FULLWIDTH"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.HALFWIDTH_FULLWIDTH_LIKE_JIS"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.HIRAGANA_KATAKANA"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.IGNORE_CASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.IGNORE_KANA"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.IGNORE_WIDTH"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.KATAKANA_HIRAGANA"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.LOWERCASE_UPPERCASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharEastIndic_ar"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharFullwidth"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharHalfwidth"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharIndic_ar"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharIndic_hi"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharKanjiShort_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharKanjiTraditional_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharLower_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharLower_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharUpper_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToCharUpper_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToChar_th"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextAIUFullWidth_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextAIUHalfWidth_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextCircledNumber"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextDate_zh"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextDiZi_zh"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFormalHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFormalLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFormalUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFullwidth_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFullwidth_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFullwidth_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextFullwidth_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextHangulCircledJamo_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextHangulCircledSyllable_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextHangulJamo_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextHangulSyllable_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextIROHAFullWidth_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextIROHAHalfWidth_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextInformalHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextInformalLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextInformalUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextKanjiLongModern_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextKanjiLongTraditional_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextKanjiShortModern_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextKanjiShortTraditional_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextLower_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextLower_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextTianGan_zh"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextUpper_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.NumToTextUpper_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.SENTENCE_CASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TITLE_CASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TOGGLE_CASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToChuyin_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumFormalHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumFormalLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumFormalUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumInformalHangul_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumInformalLower_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumInformalUpper_ko"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumKanjiLongModern_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumKanjiLongTraditional_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumLower_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumLower_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumUpper_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToNumUpper_zh_TW"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.TextToPinyin_zh_CN"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.UPPERCASE_LOWERCASE"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreBaFa_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreHyuByu_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreIandEfollowedByYa_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreIterationMark_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreKana"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreKiKuFollowedBySa_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreMiddleDot_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreMinusSign_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreProlongedSoundMark_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreSeZe_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreSeparator_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreSize_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreSpace_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreTiJi_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreTraditionalKana_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreTraditionalKanji_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.ignoreZiZu_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.largeToSmall_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.i18n.Transliteration.smallToLarge_ja_JP"><service name="com.sun.star.i18n.Transliteration.l10n"/></implementation><implementation name="com.sun.star.text.DefaultNumberingProvider"><service name="com.sun.star.text.DefaultNumberingProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/i18nsearch.uno.dll"><implementation name="com.sun.star.util.TextSearch_i18n"><service name="com.sun.star.util.TextSearch"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/lng.dll"><implementation name="com.sun.star.lingu2.ConvDicList"><service name="com.sun.star.linguistic2.ConversionDictionaryList"/></implementation><implementation name="com.sun.star.lingu2.DicList"><service name="com.sun.star.linguistic2.DictionaryList"/></implementation><implementation name="com.sun.star.lingu2.LinguProps"><service name="com.sun.star.linguistic2.LinguProperties"/></implementation><implementation name="com.sun.star.lingu2.LngSvcMgr"><service name="com.sun.star.linguistic2.LinguServiceManager"/></implementation><implementation name="com.sun.star.lingu2.ProofreadingIterator"><service name="com.sun.star.linguistic2.ProofreadingIterator"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/lnth.uno.dll"><implementation name="org.openoffice.lingu.new.Thesaurus"><service name="com.sun.star.linguistic2.Thesaurus"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/localebe1.uno.dll"><implementation name="com.sun.star.comp.configuration.backend.LocaleBackend"><service name="com.sun.star.configuration.backend.LocaleBackend"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/log.dll"><implementation name="com.sun.star.comp.extensions.ConsoleHandler"><service name="com.sun.star.logging.ConsoleHandler"/></implementation><implementation name="com.sun.star.comp.extensions.CsvFormatter"><service name="com.sun.star.logging.CsvFormatter"/></implementation><implementation name="com.sun.star.comp.extensions.FileHandler"><service name="com.sun.star.logging.FileHandler"/></implementation><implementation name="com.sun.star.comp.extensions.LoggerPool"><service name="com.sun.star.logging.LoggerPool"/><singleton name="com.sun.star.logging.LoggerPool"/></implementation><implementation name="com.sun.star.comp.extensions.PlainTextFormatter"><service name="com.sun.star.logging.PlainTextFormatter"/></implementation></component><component loader="com.sun.star.loader.Python" uri="vnd.openoffice.pymodule:mailmerge"><implementation name="org.openoffice.pyuno.MailMessage"><service name="com.sun.star.mail.MailMessage"/></implementation><implementation name="org.openoffice.pyuno.MailServiceProvider"><service name="com.sun.star.mail.MailServiceProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/mcnttype.dll"><implementation name="com.sun.star.datatransfer.MimeCntTypeFactory"><service name="com.sun.star.datatransfer.MimeContentTypeFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/migrationoo2.uno.dll"><implementation name="com.sun.star.comp.desktop.migration.Basic"><service name="com.sun.star.migration.Basic"/></implementation><implementation name="com.sun.star.comp.desktop.migration.Wordbooks"><service name="com.sun.star.migration.Wordbooks"/></implementation><implementation name="com.sun.star.comp.desktop.migration.OOo3Extensions"><service name="com.sun.star.migration.Extensions"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/msfilter.dll"><implementation name="com.sun.star.comp.vba.VBAMacroResolver"><service name="com.sun.star.script.vba.VBAMacroResolver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/mysql.uno.dll"><implementation name="org.openoffice.comp.drivers.MySQL.Driver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/odbc.uno.dll"><implementation name="com.sun.star.comp.sdbc.ODBCDriver"><service name="com.sun.star.sdbc.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/offacc.dll"><implementation name="com.sun.star.office.comp.Acceptor"><service name="com.sun.star.office.Acceptor"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/oooimprovecore.dll"><implementation name="com.sun.star.comp.extensions.oooimprovecore.Core"><service name="com.sun.star.oooimprovement.Core"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/oox.dll"><implementation name="com.sun.star.comp.oox.core.FastTokenHandler"><service name="com.sun.star.xml.sax.FastTokenHandler"/></implementation><implementation name="com.sun.star.comp.oox.FormatDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/></implementation><implementation name="com.sun.star.comp.oox.docprop.DocumentPropertiesImporter"><service name="com.sun.star.document.OOXMLDocumentPropertiesImporter"/></implementation><implementation name="com.sun.star.comp.oox.WordVbaProjectFilter"><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.oox.ppt.PowerPointImport"><service name="com.sun.star.document.ImportFilter"/><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.oox.ShapeContextHandler"><service name="com.sun.star.xml.sax.FastShapeContextHandler"/></implementation><implementation name="com.sun.star.comp.oox.xls.BiffDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/></implementation><implementation name="com.sun.star.comp.oox.xls.ExcelFilter"><service name="com.sun.star.document.ImportFilter"/><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.oox.xls.ExcelBiffFilter"><service name="com.sun.star.document.ImportFilter"/><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.oox.xls.ExcelVbaProjectFilter"><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.oox.xls.FormulaParser"><service name="com.sun.star.sheet.FilterFormulaParser"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/package2.dll"><implementation name="com.sun.star.comp.package.zip.ZipFileAccess"><service name="com.sun.star.comp.packages.zip.ZipFileAccess"/><service name="com.sun.star.packages.zip.ZipFileAccess"/></implementation><implementation name="com.sun.star.packages.comp.ZipPackage"><service name="com.sun.star.packages.Package"/></implementation><implementation name="com.sun.star.packages.manifest.comp.ManifestReader"><service name="com.sun.star.packages.manifest.ManifestReader"/></implementation><implementation name="com.sun.star.packages.manifest.comp.ManifestWriter"><service name="com.sun.star.packages.manifest.ManifestWriter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/pcr.dll"><implementation name="StringRepresentation"><service name="com.sun.star.inspection.StringRepresentation"/></implementation><implementation name="com.sun.star.comp.extensions.ButtonNavigationHandler"><service name="com.sun.star.form.inspection.ButtonNavigationHandler"/></implementation><implementation name="com.sun.star.comp.extensions.CellBindingPropertyHandler"><service name="com.sun.star.form.inspection.CellBindingPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.EFormsPropertyHandler"><service name="com.sun.star.form.inspection.XMLFormsPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.EditPropertyHandler"><service name="com.sun.star.form.inspection.EditPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.EventHandler"><service name="com.sun.star.form.inspection.EventHandler"/></implementation><implementation name="com.sun.star.comp.extensions.FormComponentPropertyHandler"><service name="com.sun.star.form.inspection.FormComponentPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.FormGeometryHandler"><service name="com.sun.star.form.inspection.FormGeometryHandler"/></implementation><implementation name="com.sun.star.comp.extensions.GenericPropertyHandler"><service name="com.sun.star.inspection.GenericPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.SubmissionPropertyHandler"><service name="com.sun.star.form.inspection.SubmissionPropertyHandler"/></implementation><implementation name="com.sun.star.comp.extensions.XSDValidationPropertyHandler"><service name="com.sun.star.form.inspection.XSDValidationPropertyHandler"/></implementation><implementation name="org.openoffice.comp.extensions.DefaultFormComponentInspectorModel"><service name="com.sun.star.form.inspection.DefaultFormComponentInspectorModel"/></implementation><implementation name="org.openoffice.comp.extensions.DefaultHelpProvider"><service name="com.sun.star.inspection.DefaultHelpProvider"/></implementation><implementation name="org.openoffice.comp.extensions.DialogController"><service name="com.sun.star.awt.PropertyBrowserController"/></implementation><implementation name="org.openoffice.comp.extensions.FormController"><service name="com.sun.star.form.PropertyBrowserController"/></implementation><implementation name="org.openoffice.comp.extensions.ObjectInspector"><service name="com.sun.star.inspection.ObjectInspector"/></implementation><implementation name="org.openoffice.comp.extensions.ObjectInspectorModel"><service name="com.sun.star.inspection.ObjectInspectorModel"/></implementation><implementation name="org.openoffice.comp.form.ui.MasterDetailLinkDialog"><service name="com.sun.star.form.MasterDetailLinkDialog"/></implementation><implementation name="org.openoffice.comp.form.ui.OControlFontDialog"><service name="com.sun.star.form.ControlFontDialog"/></implementation><implementation name="org.openoffice.comp.form.ui.OTabOrderDialog"><service name="com.sun.star.form.ui.TabOrderDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/pdffilter.dll"><implementation name="com.sun.star.comp.PDF.PDFDialog"><service name="com.sun.star.document.PDFDialog"/></implementation><implementation name="com.sun.star.comp.PDF.PDFFilter"><service name="com.sun.star.document.PDFFilter"/></implementation><implementation name="com.sun.star.comp.PDF.PDFExportInteractionHandler"><service name="com.sun.star.filter.pdfexport.PDFExportInteractionHandler"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/placeware.dll"><implementation name="com.sun.star.comp.Impress.PlaceWareExportFilter"><service name="com.sun.star.document.ExportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/preload.dll"><implementation name="org.openoffice.comp.preload.OEMPreloadWizard"><service name="org.openoffice.comp.preload.OEMPreloadWizard"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/protocolhandler.dll"><implementation name="com.sun.star.comp.ScriptProtocolHandler"><service name="com.sun.star.frame.ProtocolHandler"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/pythonloader.uno.dll"><implementation name="org.openoffice.comp.pyuno.Loader"><service name="com.sun.star.loader.Python"/></implementation></component><component loader="com.sun.star.loader.Python" uri="vnd.openoffice.pymodule:pythonscript"><implementation name="org.openoffice.pyuno.LanguageScriptProviderForPython"><service name="com.sun.star.script.provider.LanguageScriptProvider"/><service name="com.sun.star.script.provider.ScriptProviderForPython"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/res.dll"><implementation name="com.sun.star.comp.extensions.ResourceService"><service name="com.sun.star.resource.VclStringResourceLoader"/></implementation><implementation name="com.sun.star.comp.resource.OpenOfficeResourceLoader"><service name="com.sun.star.resource.OfficeResourceLoader"/><singleton name="com.sun.star.resource.OfficeResourceLoader"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/rpt.dll"><implementation name="com.sun.star.comp.report.OFixedLine"><service name="com.sun.star.report.FixedLine"/></implementation><implementation name="com.sun.star.comp.report.OFixedText"><service name="com.sun.star.report.FixedText"/></implementation><implementation name="com.sun.star.comp.report.OFormatCondition"><service name="com.sun.star.report.FormatCondition"/></implementation><implementation name="com.sun.star.comp.report.OFormattedField"><service name="com.sun.star.report.FormattedField"/></implementation><implementation name="com.sun.star.comp.report.OFunction"><service name="com.sun.star.report.Function"/></implementation><implementation name="com.sun.star.comp.report.OImageControl"><service name="com.sun.star.report.ImageControl"/></implementation><implementation name="com.sun.star.comp.report.OReportDefinition"><service name="com.sun.star.report.ReportDefinition"/></implementation><implementation name="com.sun.star.comp.report.OReportEngineJFree"><service name="com.sun.star.report.ReportEngine"/></implementation><implementation name="com.sun.star.comp.report.Shape"><service name="com.sun.star.report.Shape"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/rptui.dll"><implementation name="com.sun.star.comp.report.DataProviderHandler"><service name="com.sun.star.report.inspection.DataProviderHandler"/></implementation><implementation name="com.sun.star.comp.report.DefaultComponentInspectorModel"><service name="com.sun.star.report.inspection.DefaultComponentInspectorModel"/></implementation><implementation name="com.sun.star.comp.report.GeometryHandler"><service name="com.sun.star.report.inspection.GeometryHandler"/></implementation><implementation name="com.sun.star.comp.report.ReportComponentHandler"><service name="com.sun.star.report.inspection.ReportComponentHandler"/></implementation><implementation name="com.sun.star.report.comp.ReportDesign"><service name="com.sun.star.sdb.ReportDesign"/></implementation><implementation name="com.sun.star.report.comp.ReportToolboxController"><service name="com.sun.star.report.ReportToolboxController"/></implementation><implementation name="com.sun.star.report.comp.StatusbarController"><service name="com.sun.star.frame.StatusbarController"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/rptxml.dll"><implementation name="com.sun.star.comp.Report.XMLOasisContentImporter"><service name="com.sun.star.comp.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.Report.XMLOasisMetaImporter"><service name="com.sun.star.comp.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.Report.XMLOasisSettingsImporter"><service name="com.sun.star.comp.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.Report.XMLOasisStylesImporter"><service name="com.sun.star.comp.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.report.ExportDocumentHandler"><service name="com.sun.star.report.ExportDocumentHandler"/></implementation><implementation name="com.sun.star.comp.report.ExportFilter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.report.ImportDocumentHandler"><service name="com.sun.star.report.ImportDocumentHandler"/></implementation><implementation name="com.sun.star.comp.report.OReportFilter"><service name="com.sun.star.comp.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.report.ORptTypeDetection"><service name="com.sun.star.document.ExtendedTypeDetection"/></implementation><implementation name="com.sun.star.comp.report.XMLContentExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.report.XMLFullExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.report.XMLMetaExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.report.XMLSettingsExporter"><service name="com.sun.star.document.ExportFilter"/></implementation><implementation name="com.sun.star.comp.report.XMLStylesExporter"><service name="com.sun.star.document.ExportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sax.uno.dll"><implementation name="com.sun.star.comp.extensions.xml.sax.ParserExpat"><service name="com.sun.star.xml.sax.Parser"/></implementation><implementation name="com.sun.star.extensions.xml.sax.Writer"><service name="com.sun.star.xml.sax.Writer"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sb.dll"><implementation name="com.sun.star.comp.sfx2.DialogLibraryContainer"><service name="com.sun.star.script.DialogLibraryContainer"/><service name="com.sun.star.script.DocumentDialogLibraryContainer"/></implementation><implementation name="com.sun.star.comp.sfx2.ScriptLibraryContainer"><service name="com.sun.star.script.DocumentScriptLibraryContainer"/><service name="com.sun.star.script.ScriptLibraryContainer"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sc.dll"><implementation name="com.sun.star.comp.Calc.FilterOptionsDialog"><service name="com.sun.star.ui.dialogs.FilterOptionsDialog"/></implementation><implementation name="com.sun.star.comp.Calc.SpreadsheetDocument"><service name="com.sun.star.sheet.SpreadsheetDocument"/></implementation><implementation name="com.sun.star.comp.Calc.XMLContentExporter"><service name="com.sun.star.comp.Calc.XMLContentExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLExporter"><service name="com.sun.star.comp.Calc.XMLExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLMetaExporter"><service name="com.sun.star.comp.Calc.XMLMetaExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisContentExporter"><service name="com.sun.star.comp.Calc.XMLOasisContentExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisContentImporter"><service name="com.sun.star.comp.Calc.XMLOasisContentImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisExporter"><service name="com.sun.star.comp.Calc.XMLOasisExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisImporter"><service name="com.sun.star.comp.Calc.XMLOasisImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisMetaExporter"><service name="com.sun.star.comp.Calc.XMLOasisMetaExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisMetaImporter"><service name="com.sun.star.comp.Calc.XMLOasisMetaImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisSettingsExporter"><service name="com.sun.star.comp.Calc.XMLOasisSettingsExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisSettingsImporter"><service name="com.sun.star.comp.Calc.XMLOasisSettingsImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisStylesExporter"><service name="com.sun.star.comp.Calc.XMLOasisStylesExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLOasisStylesImporter"><service name="com.sun.star.comp.Calc.XMLOasisStylesImporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLSettingsExporter"><service name="com.sun.star.comp.Calc.XMLSettingsExporter"/></implementation><implementation name="com.sun.star.comp.Calc.XMLStylesExporter"><service name="com.sun.star.comp.Calc.XMLStylesExporter"/></implementation><implementation name="stardiv.StarCalc.ScAutoFormatsObj"><service name="com.sun.star.sheet.TableAutoFormats"/></implementation><implementation name="stardiv.StarCalc.ScFunctionAccess"><service name="com.sun.star.sheet.FunctionAccess"/></implementation><implementation name="stardiv.StarCalc.ScFunctionListObj"><service name="com.sun.star.sheet.FunctionDescriptions"/></implementation><implementation name="stardiv.StarCalc.ScRecentFunctionsObj"><service name="com.sun.star.sheet.RecentFunctions"/></implementation><implementation name="stardiv.StarCalc.ScSpreadsheetSettings"><service name="com.sun.star.sheet.GlobalSheetSettings"/></implementation><implementation name="org.apache.openoffice.comp.sc.sidebar.ScPanelFactory"><service name="com.sun.star.ui.UIElementFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/scd.dll"><implementation name="com.sun.star.comp.calc.FormatDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/scn.dll"><implementation name="com.sun.star.scanner.ScannerManager"><service name="com.sun.star.scanner.ScannerManager"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/scriptframe.dll"><implementation name="com.sun.star.script.browse.BrowseNodeFactory"><service name="com.sun.star.script.browse.BrowseNodeFactory"/><singleton name="com.sun.star.script.browse.theBrowseNodeFactory"/></implementation><implementation name="com.sun.star.script.provider.MasterScriptProvider"><service name="com.sun.star.script.browse.BrowseNode"/><service name="com.sun.star.script.provider.MasterScriptProvider"/><service name="com.sun.star.script.provider.ScriptProvider"/></implementation><implementation name="com.sun.star.script.provider.MasterScriptProviderFactory"><service name="com.sun.star.script.provider.MasterScriptProviderFactory"/><singleton name="com.sun.star.script.provider.theMasterScriptProviderFactory"/></implementation><implementation name="com.sun.star.script.provider.ScriptURIHelper"><service name="com.sun.star.script.provider.ScriptURIHelper"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sd.dll"><implementation name="com.sun.star.comp.Draw.DrawingDocument"><service name="com.sun.star.drawing.DrawingDocument"/><service name="com.sun.star.drawing.DrawingDocumentFactory"/></implementation><implementation name="com.sun.star.comp.Draw.DrawingModule"><service name="com.sun.star.drawing.ModuleDispatcher"/></implementation><implementation name="com.sun.star.comp.Draw.PresentationDocument"><service name="com.sun.star.drawing.DrawingDocumentFactory"/><service name="com.sun.star.presentation.PresentationDocument"/></implementation><implementation name="com.sun.star.comp.Draw.PresenterCanvasFactory"><service name="com.sun.star.rendering.Canvas"/></implementation><implementation name="com.sun.star.comp.Draw.PresenterHelper"><service name="com.sun.star.drawing.PresenterHelper"/></implementation><implementation name="com.sun.star.comp.Draw.PresenterPreviewCache"><service name="com.sun.star.drawing.PresenterPreviewCache"/></implementation><implementation name="com.sun.star.comp.Draw.PresenterTextView"><service name="com.sun.star.drawing.PresenterTextView"/></implementation><implementation name="com.sun.star.comp.Draw.SlideRenderer"><service name="com.sun.star.drawing.SlideRenderer"/></implementation><implementation name="com.sun.star.comp.Draw.SlideSorter"><service name="com.sun.star.drawing.SlideSorter"/></implementation><implementation name="com.sun.star.comp.Draw.framework.BasicPaneFactory"><service name="com.sun.star.drawing.framework.BasicPaneFactory"/></implementation><implementation name="com.sun.star.comp.Draw.framework.BasicToolBarFactory"><service name="com.sun.star.drawing.framework.BasicToolBarFactory"/></implementation><implementation name="com.sun.star.comp.Draw.framework.BasicViewFactory"><service name="com.sun.star.drawing.framework.BasicViewFactory"/></implementation><implementation name="com.sun.star.comp.Draw.framework.PresentationFactoryProvider"><service name="com.sun.star.drawing.framework.PresentationFactoryProvider"/></implementation><implementation name="com.sun.star.comp.Draw.framework.ResourceId"><service name="com.sun.star.drawing.framework.ResourceId"/></implementation><implementation name="org.openoffice.comp.Draw.framework.PanelFactory"><service name="com.sun.star.drawing.framework.PanelFactory"/></implementation><implementation name="com.sun.star.comp.Draw.framework.configuration.Configuration"><service name="com.sun.star.drawing.framework.Configuration"/></implementation><implementation name="com.sun.star.comp.Draw.framework.configuration.ConfigurationController"><service name="com.sun.star.drawing.framework.ConfigurationController"/></implementation><implementation name="com.sun.star.comp.Draw.framework.module.ModuleController"><service name="com.sun.star.drawing.framework.ModuleController"/></implementation><implementation name="com.sun.star.comp.draw.SdHtmlOptionsDialog"><service name="com.sun.star.ui.dialog.FilterOptionsDialog"/></implementation><implementation name="com.sun.star.comp.sd.InsertSlideController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="com.sun.star.comp.sd.SlideLayoutController"><service name="com.sun.star.frame.ToolbarController"/></implementation><implementation name="sd::RandomAnimationNode"><service name="com.sun.star.comp.sd.RandomAnimationNode"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sdbc2.dll"><implementation name="com.sun.star.comp.sdbc.OSDBCDriverManager"><service name="com.sun.star.sdbc.DriverManager"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sdbt.dll"><implementation name="com.sun.star.comp.dbaccess.ConnectionTools"><service name="com.sun.star.sdb.tools.ConnectionTools"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sdd.dll"><implementation name="com.sun.star.comp.draw.FormatDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/simplecanvas.uno.dll"><implementation name="com.sun.star.comp.rendering.SimpleCanvas"><service name="com.sun.star.rendering.SimpleCanvas"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/slideshow.uno.dll"><implementation name="com.sun.star.comp.presentation.SlideShow"><service name="com.sun.star.presentation.SlideShow"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sm.dll"><implementation name="com.sun.star.comp.Math.FormulaDocument"><service name="com.sun.star.formula.FormulaProperties"/></implementation><implementation name="com.sun.star.comp.Math.XMLContentExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLImporter"><service name="com.sun.star.xml.XMLImportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLMetaExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLOasisMetaExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLOasisMetaImporter"><service name="com.sun.star.xml.XMLImportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLOasisSettingsExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLOasisSettingsImporter"><service name="com.sun.star.xml.XMLImportFilter"/></implementation><implementation name="com.sun.star.comp.Math.XMLSettingsExporter"><service name="com.sun.star.xml.XMLExportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/smd.dll"><implementation name="com.sun.star.comp.math.FormatDetector"><service name="com.sun.star.frame.ExtendedTypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/spl.dll"><implementation name="com.sun.star.comp.desktop.FirstStart"><service name="com.sun.star.task.Job"/></implementation><implementation name="com.sun.star.office.comp.SplashScreen"><service name="com.sun.star.office.SplashScreen"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/srtrs1.dll"><implementation name="com.sun.star.comp.ucb.SortedDynamicResultSetFactory"><service name="com.sun.star.ucb.SortedDynamicResultSetFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/stringresource.uno.dll"><implementation name="com.sun.star.comp.scripting.StringResource"><service name="com.sun.star.resource.StringResource"/></implementation><implementation name="com.sun.star.comp.scripting.StringResourceWithLocation"><service name="com.sun.star.resource.StringResourceWithLocation"/></implementation><implementation name="com.sun.star.comp.scripting.StringResourceWithStorage"><service name="com.sun.star.resource.StringResourceWithStorage"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/svgfilter.dll"><implementation name="com.sun.star.comp.Draw.SVGFilter"><service name="com.sun.star.document.SVGFilter"/></implementation><implementation name="com.sun.star.comp.Draw.SVGWriter"><service name="com.sun.star.svg.SVGWriter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/syssh.uno.dll"><implementation name="com.sun.star.system.SystemShellExecute"><service name="com.sun.star.system.SystemShellExecute"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/t602filter.dll"><implementation name="com.sun.star.comp.Writer.T602ImportFilter"><service name="com.sun.star.document.ExtendedTypeDetection"/><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.Writer.T602ImportFilterDialog"><service name="com.sun.star.ui.dialogs.FilterOptionsDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/tvhlp1.dll"><implementation name="com.sun.star.help.TreeViewImpl"><service name="com.sun.star.help.TreeView"/><service name="com.sun.star.ucb.HiearchyDataSource"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucb1.dll"><implementation name="com.sun.star.comp.ucb.CommandEnvironment"><service name="com.sun.star.ucb.CommandEnvironment"/></implementation><implementation name="com.sun.star.comp.ucb.UcbContentProviderProxyFactory"><service name="com.sun.star.ucb.ContentProviderProxyFactory"/></implementation><implementation name="com.sun.star.comp.ucb.UcbPropertiesManager"><service name="com.sun.star.ucb.PropertiesManager"/></implementation><implementation name="com.sun.star.comp.ucb.UcbStore"><service name="com.sun.star.ucb.Store"/></implementation><implementation name="com.sun.star.comp.ucb.UniversalContentBroker"><service name="com.sun.star.ucb.UniversalContentBroker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpchelp1.dll"><implementation name="CHelpContentProvider"><service name="com.sun.star.help.XMLHelp"/><service name="com.sun.star.ucb.HelpContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpdav1.dll"><implementation name="com.sun.star.comp.WebDAVContentProvider"><service name="com.sun.star.ucb.WebDAVContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpexpand1.uno.dll"><implementation name="com.sun.star.comp.ucb.ExpandContentProvider"><service name="com.sun.star.ucb.ExpandContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpext.uno.dll"><implementation name="org.openoffice.comp.ucp.ext.ContentProvider"><service name="com.sun.star.ucb.ExtensionContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpfile1.dll"><implementation name="com.sun.star.comp.ucb.FileProvider"><service name="com.sun.star.ucb.FileContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucpftp1.dll"><implementation name="com.sun.star.comp.FTPContentProvider"><service name="com.sun.star.ucb.FTPContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucphier1.dll"><implementation name="com.sun.star.comp.ucb.HierarchyContentProvider"><service name="com.sun.star.ucb.HierarchyContentProvider"/></implementation><implementation name="com.sun.star.comp.ucb.HierarchyDataSource"><service name="com.sun.star.ucb.DefaultHierarchyDataSource"/><service name="com.sun.star.ucb.HierarchyDataSource"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucppkg1.dll"><implementation name="com.sun.star.comp.ucb.PackageContentProvider"><service name="com.sun.star.ucb.PackageContentProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ucptdoc1.uno.dll"><implementation name="com.sun.star.comp.ucb.TransientDocumentsContentProvider"><service name="com.sun.star.ucb.TransientDocumentsContentProvider"/></implementation><implementation name="com.sun.star.comp.ucb.TransientDocumentsDocumentContentFactory"><service name="com.sun.star.frame.TransientDocumentsDocumentContentFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/updatefeed.uno.dll"><implementation name="vnd.sun.UpdateInformationProvider"><service name="com.sun.star.deployment.UpdateInformationProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/updchk.dll"><implementation name="vnd.sun.UpdateCheckUI"><service name="com.sun.star.setup.UpdateCheckUI"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/utl.dll"><implementation name="com.sun.star.io.comp.TempFile"><service name="com.sun.star.io.TempFile"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/uui.dll"><implementation name="com.sun.star.comp.uui.PasswordContainerInteractionHandler"><service name="com.sun.star.task.PasswordContainerInteractionHandler"/></implementation><implementation name="com.sun.star.comp.uui.UUIInteractionHandler"><service name="com.sun.star.configuration.backend.InteractionHandler"/><service name="com.sun.star.task.InteractionHandler"/><service name="com.sun.star.uui.InteractionHandler"/></implementation><implementation name="com.sun.star.comp.uui.UUIInteractionRequestStringResolver"><service name="com.sun.star.task.InteractionRequestStringResolver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/vbaevents.uno.dll"><implementation name="ooo.vba.EventListener"><service name="ooo.vba.EventListener"/></implementation><implementation name="ooo.vba.VBAToOOEventDesc"><service name="ooo.vba.VBAToOOEventDesc"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/vbaobj.uno.dll"><implementation name="ScVbaApplication"><service name="ooo.vba.excel.Application"/></implementation><implementation name="ScVbaEventsHelper"><service name="com.sun.star.script.vba.VBASpreadsheetEventProcessor"/></implementation><implementation name="ScVbaGlobals"><service name="ooo.vba.excel.Globals"/></implementation><implementation name="ScVbaHyperlink"><service name="ooo.vba.excel.Hyperlink"/></implementation><implementation name="ScVbaTextFrame"><service name="ooo.vba.excel.TextFrame"/></implementation><implementation name="ScVbaWindow"><service name="ooo.vba.excel.Window"/></implementation><implementation name="ScVbaWorkbook"><service name="ooo.vba.excel.Workbook"/></implementation><implementation name="ScVbaWorksheet"><service name="ooo.vba.excel.Worksheet"/></implementation><implementation name="SvVbaRange"><service name="ooo.vba.excel.Range"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/vclcanvas.uno.dll"><implementation name="com.sun.star.comp.rendering.Canvas.VCL"><service name="com.sun.star.rendering.Canvas.VCL"/></implementation><implementation name="com.sun.star.comp.rendering.SpriteCanvas.VCL"><service name="com.sun.star.rendering.SpriteCanvas.VCL"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/writerfilter.dll"><implementation name="com.sun.star.comp.Writer.WriterFilter"><service name="com.sun.star.document.ExportFilter"/><service name="com.sun.star.document.ImportFilter"/></implementation><implementation name="com.sun.star.comp.Writer.WriterFilterDetector"><service name="com.sun.star.document.ExtendedTypeDetection"/></implementation><implementation name="com.sun.star.comp.Writer.RtfFilter"><service name="com.sun.star.document.ImportFilter"/><service name="com.sun.star.document.ExportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xcr.dll"><implementation name="com.sun.star.comp.xml.input.SaxDocumentHandler"><service name="com.sun.star.xml.input.SaxDocumentHandler"/></implementation><implementation name="com.sun.star.comp.xmlscript.XMLBasicExporter"><service name="com.sun.star.document.XMLBasicExporter"/></implementation><implementation name="com.sun.star.comp.xmlscript.XMLBasicImporter"><service name="com.sun.star.document.XMLBasicImporter"/></implementation><implementation name="com.sun.star.comp.xmlscript.XMLOasisBasicExporter"><service name="com.sun.star.document.XMLOasisBasicExporter"/></implementation><implementation name="com.sun.star.comp.xmlscript.XMLOasisBasicImporter"><service name="com.sun.star.document.XMLOasisBasicImporter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xmlfa.dll"><implementation name="com.sun.star.comp.Writer.XmlFilterAdaptor"><service name="com.sun.star.document.ExportFilter"/><service name="com.sun.star.document.ImportFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xmlfd.dll"><implementation name="com.sun.star.comp.filters.XMLFilterDetect"><service name="com.sun.star.document.ExtendedTypeDetection"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xmx.dll"><implementation name="com.sun.star.comp.io.XMLExtractor"><service name="com.sun.star.io.XMLExtractor"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xsltdlg.dll"><implementation name="XMLFilterDialogComponent"><service name="com.sun.star.comp.ui.XSLTFilterDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xstor.dll"><implementation name="com.sun.star.comp.embed.StorageFactory"><service name="com.sun.star.comp.embed.StorageFactory"/><service name="com.sun.star.embed.StorageFactory"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/PresentationMinimizer.uno.dll"><implementation name="com.sun.star.comp.presentation.PresentationOptimizer"><service name="com.sun.star.presentation.PresentationOptimizer"/></implementation><implementation name="com.sun.star.comp.ui.dialogs.PresentationMinimizerDialog"><service name="com.sun.star.ui.dialogs.PresentationMinimizerDialog"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/PresenterScreen.uno.dll"><implementation name="com.sun.star.comp.presentation.PresenterScreenJob"><service name="com.sun.star.presentation.PresenterScreenJob"/></implementation><implementation name="com.sun.star.comp.presentation.PresenterProtocolHandler"><service name="com.sun.star.frame.ProtocolHandler"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/vcl.dll"><implementation name="com.sun.star.frame.VCLSessionManagerClient"><service name="com.sun.star.frame.SessionManagerClient"/></implementation><implementation name="vcl::DisplayAccess"><service name="com.sun.star.awt.DisplayAccess"/></implementation><implementation name="vcl::FontIdentificator"><service name="com.sun.star.awt.FontIdentificator"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/updchk.uno.dll"><implementation name="vnd.sun.UpdateCheck"><service name="com.sun.star.setup.UpdateCheck"/></implementation><implementation name="vnd.sun.UpdateCheckConfig"><service name="com.sun.star.setup.UpdateCheckConfig"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/solver.dll"><implementation name="com.sun.star.comp.Calc.Solver"><service name="com.sun.star.sheet.Solver"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/XSLTFilter.jar"><implementation name="XSLTransformer"><service name="com.sun.star.comp.JAXTHelper"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xsltfilter.dll"><implementation name="com.sun.star.comp.documentconversion.XSLTFilter"><service name="com.sun.star.documentconversion.XSLTFilter"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/hyphen.uno.dll"><implementation name="org.openoffice.lingu.LibHnjHyphenator"><service name="com.sun.star.linguistic2.Hyphenator"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/spell.uno.dll"><implementation name="org.openoffice.lingu.MySpellSpellChecker"><service name="com.sun.star.linguistic2.SpellChecker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/emboleobj.dll"><implementation name="com.sun.star.comp.embed.MSOLEObjectSystemCreator"><service name="com.sun.star.comp.embed.MSOLEObjectSystemCreator"/><service name="com.sun.star.embed.MSOLEObjectSystemCreator"/></implementation><implementation name="com.sun.star.comp.embed.OLEEmbeddedObjectFactory"><service name="com.sun.star.comp.embed.OLEEmbeddedObjectFactory"/><service name="com.sun.star.embed.OLEEmbeddedObjectFactory"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/LuceneHelpWrapper.jar"><implementation name="com.sun.star.help.HelpIndexer"><service name="com.sun.star.help.HelpIndexer"/></implementation><implementation name="com.sun.star.help.HelpSearch$_HelpSearch"><service name="com.sun.star.help.HelpSearch"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/ScriptFramework.jar"><implementation name="com.sun.star.script.framework.security.SecurityDialog"><service name="com.sun.star.script.framework.security.SecurityDialog"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/ScriptProviderForJava.jar"><implementation name="com.sun.star.script.framework.provider.java.ScriptProviderForJava$_ScriptProviderForJava"><service name="com.sun.star.script.browse.BrowseNode"/><service name="com.sun.star.script.provider.LanguageScriptProvider"/><service name="com.sun.star.script.provider.ScriptProvider"/><service name="com.sun.star.script.provider.ScriptProviderForJava"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/XMergeBridge.jar"><implementation name="XMergeBridge$_XMergeBridge"><service name="com.sun.star.documentconversion.XMergeBridge"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/XSLTValidate.jar"><implementation name="XSLTValidate$_XSLTValidate"><service name="com.sun.star.documentconversion.XSLTValidate"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/agenda.jar"><implementation name="com.sun.star.wizards.agenda.CallWizard$WizardImplementation"><service name="com.sun.star.wizards.agenda.CallWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/fax.jar"><implementation name="com.sun.star.wizards.fax.CallWizard$WizardImplementation"><service name="com.sun.star.wizards.fax.CallWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/form.jar"><implementation name="com.sun.star.wizards.form.CallFormWizard$FormWizardImplementation"><service name="com.sun.star.wizards.form.CallFormWizard"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/hsqldb.dll"><implementation name="com.sun.star.sdbcx.comp.hsqldb.Driver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/jdbc.dll"><implementation name="com.sun.star.comp.sdbc.JDBCDriver"><service name="com.sun.star.sdbc.Driver"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/letter.jar"><implementation name="com.sun.star.wizards.letter.CallWizard$WizardImplementation"><service name="com.sun.star.wizards.letter.CallWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/query.jar"><implementation name="com.sun.star.wizards.query.CallQueryWizard$QueryWizardImplementation"><service name="com.sun.star.wizards.query.CallQueryWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/report.jar"><implementation name="com.sun.star.wizards.report.CallReportWizard$ReportWizardImplementation"><service name="com.sun.star.wizards.report.CallReportWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/table.jar"><implementation name="com.sun.star.wizards.table.CallTableWizard$TableWizardImplementation"><service name="com.sun.star.wizards.table.CallTableWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/web.jar"><implementation name="com.sun.star.wizards.web.CallWizard$WizardImplementation"><service name="com.sun.star.wizards.web.CallWizard"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/ScriptProviderForBeanShell.jar"><implementation name="com.sun.star.script.framework.provider.beanshell.ScriptProviderForBeanShell$_ScriptProviderForBeanShell"><service name="com.sun.star.script.browse.BrowseNode"/><service name="com.sun.star.script.provider.LanguageScriptProvider"/><service name="com.sun.star.script.provider.ScriptProvider"/><service name="com.sun.star.script.provider.ScriptProviderForBeanShell"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/ScriptProviderForJavaScript.jar"><implementation name="com.sun.star.script.framework.provider.javascript.ScriptProviderForJavaScript$_ScriptProviderForJavaScript"><service name="com.sun.star.script.browse.BrowseNode"/><service name="com.sun.star.script.provider.LanguageScriptProvider"/><service name="com.sun.star.script.provider.ScriptProvider"/><service name="com.sun.star.script.provider.ScriptProviderForJavaScript"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xmlsecurity.dll"><implementation name="com.sun.star.security.CertificateContainer"><service name="com.sun.star.security.CertificateContainer"/></implementation><implementation name="com.sun.star.security.DocumentDigitalSignatures"><service name="com.sun.star.security.DocumentDigitalSignatures"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xsec_fw.dll"><implementation name="com.sun.star.xml.security.framework.DecryptorImpl"><service name="com.sun.star.xml.crypto.sax.Decryptor"/></implementation><implementation name="com.sun.star.xml.security.framework.EncryptorImpl"><service name="com.sun.star.xml.crypto.sax.Encryptor"/></implementation><implementation name="com.sun.star.xml.security.framework.SAXEventKeeperImpl"><service name="com.sun.star.xml.crypto.sax.SAXEventKeeper"/></implementation><implementation name="com.sun.star.xml.security.framework.SignatureCreatorImpl"><service name="com.sun.star.xml.crypto.sax.SignatureCreator"/></implementation><implementation name="com.sun.star.xml.security.framework.SignatureVerifierImpl"><service name="com.sun.star.xml.crypto.sax.SignatureVerifier"/></implementation><implementation name="com.sun.star.xml.security.framework.XMLEncryptionTemplateImpl"><service name="com.sun.star.xml.crypto.XMLEncryptionTemplate"/></implementation><implementation name="com.sun.star.xml.security.framework.XMLSignatureTemplateImpl"><service name="com.sun.star.xml.crypto.XMLSignatureTemplate"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/xsec_xmlsec.dll"><implementation name="com.sun.star.security.SerialNumberAdapter"><service name="com.sun.star.security.SerialNumberAdapter"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.NSSInitializer_NssImpl"><service name="com.sun.star.xml.crypto.NSSInitializer"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.SEInitializer_MSCryptImpl"><service name="com.sun.star.xml.crypto.SEInitializer"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.SecurityEnvironment_MSCryptImpl"><service name="com.sun.star.xml.crypto.SecurityEnvironment"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.XMLDocumentWrapper_XmlSecImpl"><service name="com.sun.star.xml.wrapper.XMLDocumentWrapper"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.XMLElementWrapper_XmlSecImpl"><service name="com.sun.star.xml.wrapper.XMLElementWrapper"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.XMLEncryption_MSCryptImpl"><service name="com.sun.star.xml.crypto.XMLEncryption"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.XMLSecurityContext_MSCryptImpl"><service name="com.sun.star.xml.crypto.XMLSecurityContext"/></implementation><implementation name="com.sun.star.xml.security.bridge.xmlsec.XMLSignature_MSCryptImpl"><service name="com.sun.star.xml.crypto.XMLSignature"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ado.dll"><implementation name="com.sun.star.comp.sdbc.ado.ODriver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dnd.dll"><implementation name="com.sun.star.comp.datatransfer.dnd.OleDragSource_V1"><service name="com.sun.star.datatransfer.dnd.OleDragSource"/></implementation><implementation name="com.sun.star.comp.datatransfer.dnd.OleDropTarget_V1"><service name="com.sun.star.datatransfer.dnd.OleDropTarget"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/dtrans.dll"><implementation name="com.sun.star.comp.datatransfer.ClipboardManager"><service name="com.sun.star.datatransfer.clipboard.ClipboardManager"/></implementation><implementation name="com.sun.star.comp.datatransfer.clipboard.GenericClipboard"><service name="com.sun.star.datatransfer.clipboard.GenericClipboard"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fop.dll"><implementation name="com.sun.star.ui.dialogs.Win32FolderPicker"><service name="com.sun.star.ui.dialogs.SystemFolderPicker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/fps.dll"><implementation name="com.sun.star.ui.dialogs.Win32FilePicker"><service name="com.sun.star.ui.dialogs.SystemFilePicker"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/ftransl.dll"><implementation name="com.sun.star.datatransfer.DataFormatTranslator"><service name="com.sun.star.datatransfer.DataFormatTranslator"/></implementation></component><component loader="com.sun.star.loader.Java2" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/classes/java_uno_accessbridge.jar"><implementation name="org.openoffice.accessibility.AccessBridge"><service name="com.sun.star.accessibility.AccessBridge"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sysmail.uno.dll"><implementation name="com.sun.star.comp.system.win.SystemMailProvider"><service name="com.sun.star.system.SystemMailProvider"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/sysdtrans.dll"><implementation name="com.sun.star.datatransfer.clipboard.ClipboardW32"><service name="com.sun.star.datatransfer.clipboard.SystemClipboard"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/winaccessibility.dll"><implementation name="com.sun.star.accessibility.my_sc_implementation.MSAAService"><service name="com.sun.star.accessibility.MSAAService"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/wininetbe1.uno.dll"><implementation name="com.sun.star.comp.configuration.backend.WinInetBackend"><service name="com.sun.star.configuration.backend.WinInetBackend"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/emser.dll"><implementation name="com.sun.star.comp.ole.EmbedServer"><service name="com.sun.star.document.OleEmbeddedServerRegistration"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/oleautobridge.uno.dll"><implementation name="com.sun.star.comp.ole.OleClient"><service name="com.sun.star.bridge.OleObjectFactory"/><service name="com.sun.star.bridge.oleautomation.Factory"/></implementation><implementation name="com.sun.star.comp.ole.OleConverter2"><service name="com.sun.star.bridge.OleBridgeSupplier2"/><service name="com.sun.star.bridge.oleautomation.BridgeSupplier"/></implementation><implementation name="com.sun.star.comp.ole.OleConverterVar1"><service name="com.sun.star.bridge.OleBridgeSupplierVar1"/></implementation><implementation name="com.sun.star.comp.ole.OleServer"><service name="com.sun.star.bridge.OleApplicationRegistration"/><service name="com.sun.star.bridge.oleautomation.ApplicationRegistration"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/avmediawin.dll"><implementation name="com.sun.star.comp.avmedia.Manager_DirectX"><service name="com.sun.star.media.Manager_DirectX"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/directx9canvas.uno.dll"><implementation name="com.sun.star.comp.rendering.SpriteCanvas.DX9"><service name="com.sun.star.rendering.SpriteCanvas.DX9"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/gdipluscanvas.uno.dll"><implementation name="com.sun.star.comp.rendering.BitmapCanvas.GDI+"><service name="com.sun.star.rendering.BitmapCanvas.GDI+"/></implementation><implementation name="com.sun.star.comp.rendering.Canvas.GDI+"><service name="com.sun.star.rendering.Canvas.GDI+"/></implementation></component><component loader="com.sun.star.loader.SharedLibrary" uri="vnd.sun.star.expand:$OOO_BASE_DIR/program/adabas.dll"><implementation name="com.sun.star.comp.sdbcx.adabas.ODriver"><service name="com.sun.star.sdbc.Driver"/><service name="com.sun.star.sdbcx.Driver"/></implementation></component></components>
