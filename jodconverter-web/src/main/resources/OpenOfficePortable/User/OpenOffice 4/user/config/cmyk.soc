<?xml version="1.0" encoding="UTF-8"?>

<office:color-table xmlns:office="http://openoffice.org/2000/office" xmlns:style="http://openoffice.org/2000/style" xmlns:text="http://openoffice.org/2000/text" xmlns:table="http://openoffice.org/2000/table" xmlns:draw="http://openoffice.org/2000/drawing" xmlns:fo="http://www.w3.org/1999/XSL/Format" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:meta="http://openoffice.org/2000/meta" xmlns:number="http://openoffice.org/2000/datastyle" xmlns:svg="http://www.w3.org/2000/svg" xmlns:chart="http://openoffice.org/2000/chart" xmlns:dr3d="http://openoffice.org/2000/dr3d" xmlns:math="http://www.w3.org/1998/Math/MathML" xmlns:form="http://openoffice.org/2000/form" xmlns:script="http://openoffice.org/2000/script">
 <draw:color draw:name="0 0 0" draw:color="#ffffff"/>
 <draw:color draw:name="0 20 0" draw:color="#ffccff"/>
 <draw:color draw:name="0 40 0" draw:color="#ff99ff"/>
 <draw:color draw:name="0 60 0" draw:color="#ff66ff"/>
 <draw:color draw:name="0 80 0" draw:color="#ff33ff"/>
 <draw:color draw:name="0 100 0" draw:color="#ff00ff"/>
 <draw:color draw:name="20 0 0" draw:color="#ccffff"/>
 <draw:color draw:name="20 20 0" draw:color="#ccccff"/>
 <draw:color draw:name="20 40 0" draw:color="#cc99ff"/>
 <draw:color draw:name="20 60 0" draw:color="#cc66ff"/>
 <draw:color draw:name="20 80 0" draw:color="#cc33ff"/>
 <draw:color draw:name="20 100 0" draw:color="#cc00ff"/>
 <draw:color draw:name="40 0 0" draw:color="#99ffff"/>
 <draw:color draw:name="40 20 0" draw:color="#99ccff"/>
 <draw:color draw:name="40 40 0" draw:color="#9999ff"/>
 <draw:color draw:name="40 60 0" draw:color="#9966ff"/>
 <draw:color draw:name="40 80 0" draw:color="#9933ff"/>
 <draw:color draw:name="40 100 0" draw:color="#9900ff"/>
 <draw:color draw:name="60 0 0" draw:color="#66ffff"/>
 <draw:color draw:name="60 20 0" draw:color="#66ccff"/>
 <draw:color draw:name="60 40 0" draw:color="#6699ff"/>
 <draw:color draw:name="60 60 0" draw:color="#6666ff"/>
 <draw:color draw:name="60 80 0" draw:color="#6633ff"/>
 <draw:color draw:name="60 100 0" draw:color="#6600ff"/>
 <draw:color draw:name="80 0 0" draw:color="#33ffff"/>
 <draw:color draw:name="80 20 0" draw:color="#33ccff"/>
 <draw:color draw:name="80 40 0" draw:color="#3399ff"/>
 <draw:color draw:name="80 60 0" draw:color="#3366ff"/>
 <draw:color draw:name="80 80 0" draw:color="#3333ff"/>
 <draw:color draw:name="80 100 0" draw:color="#3300ff"/>
 <draw:color draw:name="100 0 0" draw:color="#00ffff"/>
 <draw:color draw:name="100 20 0" draw:color="#00ccff"/>
 <draw:color draw:name="100 40 0" draw:color="#0099ff"/>
 <draw:color draw:name="100 60 0" draw:color="#0066ff"/>
 <draw:color draw:name="100 80 0" draw:color="#0033ff"/>
 <draw:color draw:name="100 100 0" draw:color="#0000ff"/>
 <draw:color draw:name="0 0 20" draw:color="#ffffcc"/>
 <draw:color draw:name="0 20 20" draw:color="#ffcccc"/>
 <draw:color draw:name="0 40 20" draw:color="#ff99cc"/>
 <draw:color draw:name="0 60 20" draw:color="#ff66cc"/>
 <draw:color draw:name="0 80 20" draw:color="#ff33cc"/>
 <draw:color draw:name="0 100 20" draw:color="#ff00cc"/>
 <draw:color draw:name="20 0 20" draw:color="#ccffcc"/>
 <draw:color draw:name="20 20 20" draw:color="#cccccc"/>
 <draw:color draw:name="20 40 20" draw:color="#cc99cc"/>
 <draw:color draw:name="20 60 20" draw:color="#cc66cc"/>
 <draw:color draw:name="20 80 20" draw:color="#cc33cc"/>
 <draw:color draw:name="20 100 20" draw:color="#cc00cc"/>
 <draw:color draw:name="40 0 20" draw:color="#99ffcc"/>
 <draw:color draw:name="40 20 20" draw:color="#99cccc"/>
 <draw:color draw:name="40 40 20" draw:color="#9999cc"/>
 <draw:color draw:name="40 60 20" draw:color="#9966cc"/>
 <draw:color draw:name="40 80 20" draw:color="#9933cc"/>
 <draw:color draw:name="40 100 20" draw:color="#9900cc"/>
 <draw:color draw:name="60 0 20" draw:color="#66ffcc"/>
 <draw:color draw:name="60 20 20" draw:color="#66cccc"/>
 <draw:color draw:name="60 40 20" draw:color="#6699cc"/>
 <draw:color draw:name="60 60 20" draw:color="#6666cc"/>
 <draw:color draw:name="60 80 20" draw:color="#6633cc"/>
 <draw:color draw:name="60 100 20" draw:color="#6600cc"/>
 <draw:color draw:name="80 0 20" draw:color="#33ffcc"/>
 <draw:color draw:name="80 20 20" draw:color="#33cccc"/>
 <draw:color draw:name="80 40 20" draw:color="#3399cc"/>
 <draw:color draw:name="80 60 20" draw:color="#3366cc"/>
 <draw:color draw:name="80 80 20" draw:color="#3333cc"/>
 <draw:color draw:name="80 100 20" draw:color="#3300cc"/>
 <draw:color draw:name="100 0 20" draw:color="#00ffcc"/>
 <draw:color draw:name="100 20 20" draw:color="#00cccc"/>
 <draw:color draw:name="100 40 20" draw:color="#0099cc"/>
 <draw:color draw:name="100 60 20" draw:color="#0066cc"/>
 <draw:color draw:name="100 80 20" draw:color="#0033cc"/>
 <draw:color draw:name="100 100 20" draw:color="#0000cc"/>
 <draw:color draw:name="0 0 40" draw:color="#ffff99"/>
 <draw:color draw:name="0 20 40" draw:color="#ffcc99"/>
 <draw:color draw:name="0 40 40" draw:color="#ff9999"/>
 <draw:color draw:name="0 60 40" draw:color="#ff6699"/>
 <draw:color draw:name="0 80 40" draw:color="#ff3399"/>
 <draw:color draw:name="0 100 40" draw:color="#ff0099"/>
 <draw:color draw:name="20 0 40" draw:color="#ccff99"/>
 <draw:color draw:name="20 20 40" draw:color="#cccc99"/>
 <draw:color draw:name="20 40 40" draw:color="#cc9999"/>
 <draw:color draw:name="20 60 40" draw:color="#cc6699"/>
 <draw:color draw:name="20 80 40" draw:color="#cc3399"/>
 <draw:color draw:name="20 100 40" draw:color="#cc0099"/>
 <draw:color draw:name="40 0 40" draw:color="#99ff99"/>
 <draw:color draw:name="40 20 40" draw:color="#99cc99"/>
 <draw:color draw:name="40 40 40" draw:color="#999999"/>
 <draw:color draw:name="40 60 40" draw:color="#996699"/>
 <draw:color draw:name="40 80 40" draw:color="#993399"/>
 <draw:color draw:name="40 100 40" draw:color="#990099"/>
 <draw:color draw:name="60 0 40" draw:color="#66ff99"/>
 <draw:color draw:name="60 20 40" draw:color="#66cc99"/>
 <draw:color draw:name="60 40 40" draw:color="#669999"/>
 <draw:color draw:name="60 60 40" draw:color="#666699"/>
 <draw:color draw:name="60 80 40" draw:color="#663399"/>
 <draw:color draw:name="60 100 40" draw:color="#660099"/>
 <draw:color draw:name="80 0 40" draw:color="#33ff99"/>
 <draw:color draw:name="80 20 40" draw:color="#33cc99"/>
 <draw:color draw:name="80 40 40" draw:color="#339999"/>
 <draw:color draw:name="80 60 40" draw:color="#336699"/>
 <draw:color draw:name="80 80 40" draw:color="#333399"/>
 <draw:color draw:name="80 100 40" draw:color="#330099"/>
 <draw:color draw:name="100 0 40" draw:color="#00ff99"/>
 <draw:color draw:name="100 20 40" draw:color="#00cc99"/>
 <draw:color draw:name="100 40 40" draw:color="#009999"/>
 <draw:color draw:name="100 60 40" draw:color="#006699"/>
 <draw:color draw:name="100 80 40" draw:color="#003399"/>
 <draw:color draw:name="100 100 40" draw:color="#000099"/>
 <draw:color draw:name="0 0 60" draw:color="#ffff66"/>
 <draw:color draw:name="0 20 60" draw:color="#ffcc66"/>
 <draw:color draw:name="0 40 60" draw:color="#ff9966"/>
 <draw:color draw:name="0 60 60" draw:color="#ff6666"/>
 <draw:color draw:name="0 80 60" draw:color="#ff3366"/>
 <draw:color draw:name="0 100 60" draw:color="#ff0066"/>
 <draw:color draw:name="20 0 60" draw:color="#ccff66"/>
 <draw:color draw:name="20 20 60" draw:color="#cccc66"/>
 <draw:color draw:name="20 40 60" draw:color="#cc9966"/>
 <draw:color draw:name="20 60 60" draw:color="#cc6666"/>
 <draw:color draw:name="20 80 60" draw:color="#cc3366"/>
 <draw:color draw:name="20 100 60" draw:color="#cc0066"/>
 <draw:color draw:name="40 0 60" draw:color="#99ff66"/>
 <draw:color draw:name="40 20 60" draw:color="#99cc66"/>
 <draw:color draw:name="40 40 60" draw:color="#999966"/>
 <draw:color draw:name="40 60 60" draw:color="#996666"/>
 <draw:color draw:name="40 80 60" draw:color="#993366"/>
 <draw:color draw:name="40 100 60" draw:color="#990066"/>
 <draw:color draw:name="60 0 60" draw:color="#66ff66"/>
 <draw:color draw:name="60 20 60" draw:color="#66cc66"/>
 <draw:color draw:name="60 40 60" draw:color="#669966"/>
 <draw:color draw:name="60 60 60" draw:color="#666666"/>
 <draw:color draw:name="60 80 60" draw:color="#663366"/>
 <draw:color draw:name="60 100 60" draw:color="#660066"/>
 <draw:color draw:name="80 0 60" draw:color="#33ff66"/>
 <draw:color draw:name="80 20 60" draw:color="#33cc66"/>
 <draw:color draw:name="80 40 60" draw:color="#339966"/>
 <draw:color draw:name="80 60 60" draw:color="#336666"/>
 <draw:color draw:name="80 80 60" draw:color="#333366"/>
 <draw:color draw:name="80 100 60" draw:color="#330066"/>
 <draw:color draw:name="100 0 60" draw:color="#00ff66"/>
 <draw:color draw:name="100 20 60" draw:color="#00cc66"/>
 <draw:color draw:name="100 40 60" draw:color="#009966"/>
 <draw:color draw:name="100 60 60" draw:color="#006666"/>
 <draw:color draw:name="100 80 60" draw:color="#003366"/>
 <draw:color draw:name="100 100 60" draw:color="#000066"/>
 <draw:color draw:name="0 0 80" draw:color="#ffff33"/>
 <draw:color draw:name="0 20 80" draw:color="#ffcc33"/>
 <draw:color draw:name="0 40 80" draw:color="#ff9933"/>
 <draw:color draw:name="0 60 80" draw:color="#ff6633"/>
 <draw:color draw:name="0 80 80" draw:color="#ff3333"/>
 <draw:color draw:name="0 100 80" draw:color="#ff0033"/>
 <draw:color draw:name="20 0 80" draw:color="#ccff33"/>
 <draw:color draw:name="20 20 80" draw:color="#cccc33"/>
 <draw:color draw:name="20 40 80" draw:color="#cc9933"/>
 <draw:color draw:name="20 60 80" draw:color="#cc6633"/>
 <draw:color draw:name="20 80 80" draw:color="#cc3333"/>
 <draw:color draw:name="20 100 80" draw:color="#cc0033"/>
 <draw:color draw:name="40 0 80" draw:color="#99ff33"/>
 <draw:color draw:name="40 20 80" draw:color="#99cc33"/>
 <draw:color draw:name="40 40 80" draw:color="#999933"/>
 <draw:color draw:name="40 60 80" draw:color="#996633"/>
 <draw:color draw:name="40 80 80" draw:color="#993333"/>
 <draw:color draw:name="40 100 80" draw:color="#990033"/>
 <draw:color draw:name="60 0 80" draw:color="#66ff33"/>
 <draw:color draw:name="60 20 80" draw:color="#66cc33"/>
 <draw:color draw:name="60 40 80" draw:color="#669933"/>
 <draw:color draw:name="60 60 80" draw:color="#666633"/>
 <draw:color draw:name="60 80 80" draw:color="#663333"/>
 <draw:color draw:name="60 100 80" draw:color="#660033"/>
 <draw:color draw:name="80 0 80" draw:color="#33ff33"/>
 <draw:color draw:name="80 20 80" draw:color="#33cc33"/>
 <draw:color draw:name="80 40 80" draw:color="#339933"/>
 <draw:color draw:name="80 60 80" draw:color="#336633"/>
 <draw:color draw:name="80 80 80" draw:color="#333333"/>
 <draw:color draw:name="80 100 80" draw:color="#330033"/>
 <draw:color draw:name="100 0 80" draw:color="#00ff33"/>
 <draw:color draw:name="100 20 80" draw:color="#00cc33"/>
 <draw:color draw:name="100 40 80" draw:color="#009933"/>
 <draw:color draw:name="100 60 80" draw:color="#006633"/>
 <draw:color draw:name="100 80 80" draw:color="#003333"/>
 <draw:color draw:name="100 100 80" draw:color="#000033"/>
 <draw:color draw:name="0 0 100" draw:color="#ffff00"/>
 <draw:color draw:name="0 20 100" draw:color="#ffcc00"/>
 <draw:color draw:name="0 40 100" draw:color="#ff9900"/>
 <draw:color draw:name="0 60 100" draw:color="#ff6600"/>
 <draw:color draw:name="0 80 100" draw:color="#ff3300"/>
 <draw:color draw:name="0 100 100" draw:color="#ff0000"/>
 <draw:color draw:name="20 0 100" draw:color="#ccff00"/>
 <draw:color draw:name="20 20 100" draw:color="#cccc00"/>
 <draw:color draw:name="20 40 100" draw:color="#cc9900"/>
 <draw:color draw:name="20 60 100" draw:color="#cc6600"/>
 <draw:color draw:name="20 80 100" draw:color="#cc3300"/>
 <draw:color draw:name="20 100 100" draw:color="#cc0000"/>
 <draw:color draw:name="40 0 100" draw:color="#99ff00"/>
 <draw:color draw:name="40 20 100" draw:color="#99cc00"/>
 <draw:color draw:name="40 40 100" draw:color="#999900"/>
 <draw:color draw:name="40 60 100" draw:color="#996600"/>
 <draw:color draw:name="40 80 100" draw:color="#993300"/>
 <draw:color draw:name="40 100 100" draw:color="#990000"/>
 <draw:color draw:name="60 0 100" draw:color="#66ff00"/>
 <draw:color draw:name="60 20 100" draw:color="#66cc00"/>
 <draw:color draw:name="60 40 100" draw:color="#669900"/>
 <draw:color draw:name="60 60 100" draw:color="#666600"/>
 <draw:color draw:name="60 80 100" draw:color="#663300"/>
 <draw:color draw:name="60 100 100" draw:color="#660000"/>
 <draw:color draw:name="80 0 100" draw:color="#33ff00"/>
 <draw:color draw:name="80 20 100" draw:color="#33cc00"/>
 <draw:color draw:name="80 40 100" draw:color="#339900"/>
 <draw:color draw:name="80 60 100" draw:color="#336600"/>
 <draw:color draw:name="80 80 100" draw:color="#333300"/>
 <draw:color draw:name="80 100 100" draw:color="#330000"/>
 <draw:color draw:name="100 0 100" draw:color="#00ff00"/>
 <draw:color draw:name="100 20 100" draw:color="#00cc00"/>
 <draw:color draw:name="100 40 100" draw:color="#009900"/>
 <draw:color draw:name="100 60 100" draw:color="#006600"/>
 <draw:color draw:name="100 80 100" draw:color="#003300"/>
 <draw:color draw:name="100 100 100" draw:color="#000000"/>
</office:color-table>