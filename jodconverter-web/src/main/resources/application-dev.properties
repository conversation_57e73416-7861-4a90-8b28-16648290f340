#redisè¿æ¥
spring.redisson.address = 127.0.0.1:6379
##èµæºæ å°è·¯å¾
file.dir = D:/Users/<USER>/test/
spring.resources.static-locations = classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.dir}
server.tomcat.uri-encoding = UTF-8
converted.file.charset = GBK
#æä»¶ä¸ä¼ éå¶
spring.http.multipart.max-file-size=100MB
spring.http.multipart.max-request-size=100MB
##ææ¬ç±»å
simText = txt,html,xml,java,properties,sql
#å¤åªä½ç±»å
media=mp3,mp4,flv,rmvb

########################################################
###PageOffice 
########################################################
posyspath=d:/PageOfficeLic/
popassword=111111
