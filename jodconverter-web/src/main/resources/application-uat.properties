#=============================================#spring Redissonï¿½ï¿½ï¿½ï¿½#===================================#
spring.redisson.address = *************:63793
spring.redisson.database = 3
##ï¿½ï¿½Ô´Ó³ï¿½ï¿½Â·ï¿½ï¿½(ï¿½ï¿½Îªjarï¿½ï¿½Ê½ï¿½ï¿½ï¿½Ðµï¿½Ô­ï¿½ï¿½)
file.dir = /data/filepreview/
spring.resources.static-locations = classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,file:${file.dir}
## openofficeï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
openOfficePath = 123
office.home = /opt/openoffice4
server.tomcat.uri-encoding = utf-8
converted.file.charset = utf-8
spring.http.multipart.max-file-size = 100MB
## Ö§ï¿½Öµï¿½ï¿½ï¿½ï¿½Ä±ï¿½ï¿½ï¿½Ê½ï¿½ï¿½ï¿½Ä¼ï¿½ï¿½ï¿½ï¿½ï¿½
simText = txt,html,xml,java,properties,sql,js,md,json,conf,ini,vue,php,py,bat,gitignore,log,htm,css,cnf
media=mp3,mp4,flv